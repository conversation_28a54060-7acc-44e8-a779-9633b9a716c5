# config.py - Конфигурационный файл для Telegram юзербота
"""
Конфигурационный файл для хранения API ключей и настроек
"""

# Telegram API конфигурация
TELEGRAM_API_ID = ...
TELEGRAM_API_HASH = "..."

# Gemini API конфигурация
GEMINI_API_KEY = "..."
GEMINI_MODEL = "gemini-2.5-pro"
GEMINI_MAX_TOKENS = 65536

# Настройки thinking для Gemini 2.5 Pro
GEMINI_THINKING_BUDGET = 32768  # Максимальный thinking budget для 2.5 Pro
GEMINI_INCLUDE_THOUGHTS = True  # Включить резюме размышлений в ответах
GEMINI_TEMPERATURE = 0.7  # Температура для более креативных ответов

# Настройки сессии
SESSION_NAME = "extra_userbot_session"

# Настройки логирования
LOG_LEVEL = "INFO"
LOG_FORMAT = "[%(levelname)s %(asctime)s] %(name)s: %(message)s"

# Настройки для хранения функций
FUNCTIONS_DB_FILE = "functions_db.json"

# Полные встроенные функции для динамического выполнения - БЕЗ ОГРАНИЧЕНИЙ
SAFE_BUILTINS = {
    '__builtins__': {
        # Базовые типы данных
        'len': len,
        'str': str,
        'int': int,
        'float': float,
        'bool': bool,
        'list': list,
        'dict': dict,
        'tuple': tuple,
        'set': set,
        'frozenset': frozenset,
        'bytes': bytes,
        'bytearray': bytearray,
        'memoryview': memoryview,
        'complex': complex,

        # Функции для работы с итерируемыми объектами
        'range': range,
        'enumerate': enumerate,
        'zip': zip,
        'map': map,
        'filter': filter,
        'sorted': sorted,
        'reversed': reversed,
        'any': any,
        'all': all,

        # Математические функции
        'min': min,
        'max': max,
        'sum': sum,
        'abs': abs,
        'round': round,
        'pow': pow,
        'divmod': divmod,

        # Функции для работы с объектами
        'type': type,
        'isinstance': isinstance,
        'issubclass': issubclass,
        'hasattr': hasattr,
        'getattr': getattr,
        'setattr': setattr,
        'delattr': delattr,
        'dir': dir,
        'id': id,
        'hash': hash,
        'repr': repr,
        'format': format,
        'vars': vars,
        'locals': locals,
        'globals': globals,

        # Исключения (КРИТИЧЕСКИ ВАЖНО!)
        'Exception': Exception,
        'ValueError': ValueError,
        'TypeError': TypeError,
        'AttributeError': AttributeError,
        'KeyError': KeyError,
        'IndexError': IndexError,
        'RuntimeError': RuntimeError,
        'StopIteration': StopIteration,
        'GeneratorExit': GeneratorExit,
        'SystemExit': SystemExit,
        'KeyboardInterrupt': KeyboardInterrupt,
        'ImportError': ImportError,
        'ModuleNotFoundError': ModuleNotFoundError,
        'FileNotFoundError': FileNotFoundError,
        'PermissionError': PermissionError,
        'OSError': OSError,
        'IOError': IOError,

        # Функции ввода-вывода
        'print': print,
        'input': input,
        'open': open,

        # Функции для работы со строками и числами
        'ord': ord,
        'chr': chr,
        'hex': hex,
        'oct': oct,
        'bin': bin,
        'ascii': ascii,

        # Импорт и выполнение
        '__import__': __import__,
        'exec': exec,
        'eval': eval,
        'compile': compile,

        # Другие полезные функции
        'slice': slice,
        'object': object,
        'property': property,
        'staticmethod': staticmethod,
        'classmethod': classmethod,
        'super': super,
        'callable': callable,
        'iter': iter,
        'next': next,
        'exit': exit,
        'quit': quit,
        'help': help,
        'copyright': copyright,
        'credits': credits,
        'license': license,
    }
}

# Префикс для команд
COMMAND_PREFIX = "."

# Настройки валидации кода - ОТКЛЮЧЕНЫ для полной функциональности
# FORBIDDEN_IMPORTS = [
#     'os', 'sys', 'subprocess', 'eval', 'exec', 'compile',
#     'open', 'file', 'globals', 'locals', 'vars'
# ]

# FORBIDDEN_KEYWORDS = [
#     'import os', 'import sys', 'import subprocess', 'exec(',
#     'eval(', 'compile(', 'open(', '__import__(', 'globals()',
#     'locals()', 'vars()', 'delattr', 'exit', 'quit'
# ]

# Разрешенные импорты и ключевые слова - теперь без ограничений
ALLOWED_IMPORTS = ['*']  # Разрешены все импорты
ALLOWED_KEYWORDS = ['*']  # Разрешены все ключевые слова