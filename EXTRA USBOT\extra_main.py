# main.py - Основной файл Telegram юзербота с динамической генерацией функций
"""
Telegram юзербот с возможностью динамической генерации функций через Gemini API
"""

import asyncio
import logging
import sys
import traceback
from datetime import datetime
from typing import Dict, Any
from telethon import TelegramClient, events
from telethon.errors import SessionPasswordNeededError, FloodWaitError, ChatAdminRequiredError, UserAdminInvalidError

from extra_config import (
    TELEGRAM_API_ID, TELEGRAM_API_HASH, SESSION_NAME,
    LOG_LEVEL, LOG_FORMAT, COMMAND_PREFIX, SAFE_BUILTINS
)
from functions_manager import FunctionsManager
from gemini_handler import GeminiHandler

# Настройка логирования
logging.basicConfig(
    format=LOG_FORMAT,
    level=getattr(logging, LOG_LEVEL),
    stream=sys.stdout
)
logger = logging.getLogger(__name__)

class TelegramUserBot:
    """Основной класс Telegram юзербота"""
    
    def __init__(self):
        self.client = None
        self.functions_manager = FunctionsManager()
        self.gemini_handler = GeminiHandler()
        self.dynamic_handlers = {}
        self.is_running = False
        # Хранилище для команды extraban
        self.extraban_data = {}
        
    async def initialize_client(self):
        """Инициализация Telegram клиента"""
        try:
            self.client = TelegramClient(
                SESSION_NAME,
                TELEGRAM_API_ID,
                TELEGRAM_API_HASH
            )
            
            await self.client.start()
            logger.info("Telegram клиент успешно инициализирован")
            
            # Получение информации о пользователе
            me = await self.client.get_me()
            logger.info(f"Авторизован как: {me.first_name} (@{me.username})")
            
            return True
            
        except SessionPasswordNeededError:
            logger.error("Требуется двухфакторная аутентификация")
            return False
        except Exception as e:
            logger.error(f"Ошибка инициализации клиента: {e}")
            return False
    
    def setup_base_handlers(self):
        """Настройка базовых обработчиков команд"""
        
        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}add', outgoing=True))
        async def add_function_handler(event):
            """Обработчик команды добавления функции"""
            try:
                # Парсинг аргументов команды
                args = event.raw_text.split(' ', 2)
                if len(args) < 3:
                    await event.reply("❌ Использование: .add <команда> <описание>")
                    return
                
                command = args[1].strip()
                description = args[2].strip()
                
                # Проверка на корректность команды
                if not command.startswith(COMMAND_PREFIX):
                    command = COMMAND_PREFIX + command
                
                await event.reply(f"🤖 Генерирую функцию для команды {command}...")
                
                # Генерация кода через Gemini
                success, code = self.gemini_handler.generate_function_code(command, description)
                
                if not success:
                    await event.reply(f"❌ Ошибка генерации: {code}")
                    return
                
                # Валидация сгенерированного кода - ОТКЛЮЧЕНА
                # is_valid, validation_message = self.gemini_handler.validate_generated_code(code, command)
                # if not is_valid:
                #     await event.reply(f"❌ Код не прошел валидацию: {validation_message}")
                #     return
                
                # Добавление функции в менеджер
                success, message = self.functions_manager.add_function(command, description, code)
                
                if success:
                    # Динамическая регистрация функции
                    await self.register_dynamic_function(command, code)
                    await event.reply(f"✅ {message}")
                else:
                    await event.reply(f"❌ {message}")
                    
            except Exception as e:
                logger.error(f"Ошибка в add_function_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")
        
        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}list', outgoing=True))
        async def list_functions_handler(event):
            """Обработчик команды списка функций"""
            try:
                functions = self.functions_manager.list_functions()
                
                if not functions:
                    await event.reply("📝 Нет сохраненных функций")
                    return
                
                # Формирование списка функций
                message_parts = ["📝 **Список функций:**\n"]
                
                for i, func in enumerate(functions, 1):
                    message_parts.append(
                        f"{i}. `{func['command']}` - {func['description']}\n"
                        f"   ID: `{func['id']}`\n"
                        f"   Создано: {func['created_at'][:19]}\n"
                    )
                
                # Разбиение на части если сообщение слишком длинное
                current_message = ""
                for part in message_parts:
                    if len(current_message + part) > 4000:
                        await event.reply(current_message)
                        current_message = part
                    else:
                        current_message += part
                
                if current_message:
                    await event.reply(current_message)
                    
            except Exception as e:
                logger.error(f"Ошибка в list_functions_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")
        
        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}remove', outgoing=True))
        async def remove_function_handler(event):
            """Обработчик команды удаления функции"""
            try:
                args = event.raw_text.split(' ', 1)
                if len(args) < 2:
                    await event.reply("❌ Использование: .remove <id|команда>")
                    return
                
                identifier = args[1].strip()
                
                # Удаление из менеджера
                success, message = self.functions_manager.remove_function(identifier)
                
                if success:
                    # Удаление динамического обработчика
                    await self.unregister_dynamic_function(identifier)
                    await event.reply(f"✅ {message}")
                else:
                    await event.reply(f"❌ {message}")
                    
            except Exception as e:
                logger.error(f"Ошибка в remove_function_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")
        
        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}help', outgoing=True))
        async def help_handler(event):
            """Обработчик команды помощи"""
            try:
                help_text = """
🤖 **Telegram UserBot - Помощь**

**Основные команды:**
• `.add <команда> <описание>` - Добавить новую функцию
• `.list` - Показать все функции
• `.remove <id|команда>` - Удалить функцию
• `.help` - Показать эту справку
• `.status` - Показать статус бота
• `.reload` - Перезагрузить все функции

**Примеры:**
• `.add ping Отправить pong в ответ`
• `.add time Показать текущее время`
• `.remove ping` или `.remove <id>`

**Информация:**
• Функции сохраняются автоматически
• Код генерируется через Gemini AI
• Поддерживается безопасное выполнение
• Функции загружаются при старте бота

Всего функций: {count}
"""
                count = self.functions_manager.get_functions_count()
                await event.reply(help_text.format(count=count))
                
            except Exception as e:
                logger.error(f"Ошибка в help_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")
        
        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}status', outgoing=True))
        async def status_handler(event):
            """Обработчик команды статуса"""
            try:
                # Тест соединения с Gemini
                gemini_status = self.gemini_handler.test_client_connection()
                
                status_text = f"""
🤖 **Статус бота**

**Общая информация:**
• Статус: {'🟢 Работает' if self.is_running else '🔴 Остановлен'}
• Функций загружено: {len(self.dynamic_handlers)}
• Функций сохранено: {self.functions_manager.get_functions_count()}

**Gemini API:**
• Статус: {'🟢 Подключен' if gemini_status[0] else '🔴 Ошибка'}
• Модель: {self.gemini_handler.model}
• Макс. токенов: {self.gemini_handler.max_tokens}

**Telegram API:**
• Статус: 🟢 Подключен
• Сессия: {SESSION_NAME}
"""
                await event.reply(status_text)
                
            except Exception as e:
                logger.error(f"Ошибка в status_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")
        
        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}reload', outgoing=True))
        async def reload_handler(event):
            """Обработчик команды перезагрузки функций"""
            try:
                await event.reply("🔄 Перезагружаю функции...")
                
                # Очистка существующих обработчиков
                self.dynamic_handlers.clear()
                
                # Перезагрузка функций
                await self.load_dynamic_functions()
                
                count = len(self.dynamic_handlers)
                await event.reply(f"✅ Перезагружено {count} функций")
                
            except Exception as e:
                logger.error(f"Ошибка в reload_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")

        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}extraban', outgoing=True))
        async def extraban_handler(event):
            """Обработчик команды анализа неактивных участников"""
            try:
                chat = await event.get_chat()
                chat_id = event.chat_id

                # Проверяем, что это групповой чат
                if not (hasattr(chat, 'megagroup') or hasattr(chat, 'broadcast')):
                    await event.reply("❌ Команда работает только в группах и каналах")
                    return

                await event.reply("🔍 Начинаю анализ участников чата...")

                # Проверяем права администратора
                try:
                    permissions = await self.client.get_permissions(chat, 'me')
                    if not permissions.ban_users:
                        await event.reply("❌ У вас нет прав для бана пользователей в этом чате")
                        return
                except ChatAdminRequiredError:
                    await event.reply("❌ Для использования этой команды нужны права администратора")
                    return
                except Exception as e:
                    await event.reply(f"❌ Ошибка проверки прав: {e}")
                    return

                # Получаем всех участников чата
                await event.reply("📋 Получаю список участников...")
                participants = []
                try:
                    participant_count = 0
                    async for participant in self.client.iter_participants(chat):
                        participants.append(participant)
                        participant_count += 1

                        # Лимит на количество участников для безопасности
                        if participant_count > 5000:
                            await event.reply("⚠️ Слишком много участников (>5000). Операция прервана для безопасности.")
                            return

                except Exception as e:
                    await event.reply(f"❌ Ошибка получения участников: {e}")
                    return

                if len(participants) == 0:
                    await event.reply("❌ Не удалось получить список участников")
                    return

                await event.reply(f"👥 Найдено {len(participants)} участников. Анализирую активность...")

                # Создаем множество ID всех отправителей сообщений
                message_senders = set()
                message_count = 0

                try:
                    # Анализируем историю сообщений (последние 10000 сообщений)
                    async for message in self.client.iter_messages(chat, limit=10000):
                        if message.sender_id:
                            message_senders.add(message.sender_id)
                        message_count += 1

                        # Показываем прогресс каждые 1000 сообщений
                        if message_count % 1000 == 0:
                            await event.edit(f"📊 Проанализировано {message_count} сообщений...")

                except Exception as e:
                    await event.reply(f"❌ Ошибка анализа сообщений: {e}")
                    return

                # Находим неактивных участников
                inactive_users = []
                admin_count = 0
                me = await self.client.get_me()

                for participant in participants:
                    # Пропускаем ботов
                    if participant.bot:
                        continue

                    # Пропускаем себя
                    if participant.id == me.id:
                        continue

                    # Пропускаем администраторов и создателей
                    try:
                        user_permissions = await self.client.get_permissions(chat, participant)
                        if user_permissions.is_admin or user_permissions.is_creator:
                            admin_count += 1
                            continue
                    except:
                        pass

                    # Если пользователь не писал сообщения, добавляем в список неактивных
                    if participant.id not in message_senders:
                        inactive_users.append({
                            'id': participant.id,
                            'username': participant.username,
                            'first_name': participant.first_name,
                            'last_name': participant.last_name
                        })

                # Сохраняем результаты для команды .ban
                self.extraban_data[chat_id] = {
                    'inactive_users': inactive_users,
                    'chat_title': chat.title if hasattr(chat, 'title') else 'Личные сообщения',
                    'total_participants': len(participants),
                    'analyzed_messages': message_count,
                    'timestamp': datetime.now().isoformat()
                }

                # Формируем отчет
                report = f"""
📊 **Анализ неактивных участников завершен**

🏷️ **Чат:** {chat.title if hasattr(chat, 'title') else 'Личные сообщения'}
👥 **Всего участников:** {len(participants)}
👑 **Администраторов:** {admin_count}
📝 **Проанализировано сообщений:** {message_count}
🔍 **Уникальных отправителей:** {len(message_senders)}

❌ **Неактивных участников:** {len(inactive_users)}

Для выполнения бана неактивных участников используйте команду `.ban`
"""

                await event.reply(report)

            except Exception as e:
                logger.error(f"Ошибка в extraban_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")

        @self.client.on(events.NewMessage(pattern=f'^\\{COMMAND_PREFIX}ban', outgoing=True))
        async def ban_handler(event):
            """Обработчик команды бана неактивных участников"""
            try:
                chat_id = event.chat_id

                # Проверяем наличие данных от команды .extraban
                if chat_id not in self.extraban_data:
                    await event.reply("❌ Сначала выполните команду `.extraban` для анализа участников")
                    return

                ban_data = self.extraban_data[chat_id]
                inactive_users = ban_data['inactive_users']

                if not inactive_users:
                    await event.reply("✅ Нет неактивных участников для бана")
                    return

                # Показываем информацию и запрашиваем подтверждение
                confirmation_msg = f"""
⚠️ **ПОДТВЕРЖДЕНИЕ БАНА**

🏷️ **Чат:** {ban_data['chat_title']}
👥 **Всего участников:** {ban_data['total_participants']}
📝 **Проанализировано сообщений:** {ban_data['analyzed_messages']}
❌ **К бану:** {len(inactive_users)} участников

**ВНИМАНИЕ!** Это действие нельзя отменить!

Для подтверждения отправьте: `.ban confirm`
Для отмены отправьте: `.ban cancel`
"""

                # Парсим аргументы команды
                args = event.raw_text.split(' ', 1)
                if len(args) < 2:
                    await event.reply(confirmation_msg)
                    return

                action = args[1].strip().lower()

                if action == 'cancel':
                    # Очищаем данные и отменяем
                    del self.extraban_data[chat_id]
                    await event.reply("✅ Операция бана отменена")
                    return

                elif action == 'confirm':
                    # Выполняем бан
                    await event.reply("🚫 Начинаю процесс бана неактивных участников...")

                    chat = await event.get_chat()
                    banned_count = 0
                    error_count = 0

                    # Создаем сообщение для отслеживания прогресса
                    progress_msg = await event.reply(f"📊 Прогресс: 0/{len(inactive_users)}")

                    for i, user_data in enumerate(inactive_users):
                        try:
                            # Пытаемся забанить пользователя
                            await self.client.kick_participant(chat, user_data['id'])
                            banned_count += 1

                            # Обновляем прогресс каждые 5 банов
                            if (i + 1) % 5 == 0 or i == len(inactive_users) - 1:
                                await progress_msg.edit(f"📊 Прогресс: {i + 1}/{len(inactive_users)} (забанено: {banned_count}, ошибок: {error_count})")

                            # Небольшая задержка чтобы не попасть в rate limit
                            await asyncio.sleep(1.0)

                        except FloodWaitError as e:
                            # Обрабатываем rate limit от Telegram
                            await event.reply(f"⏳ Rate limit от Telegram. Жду {e.seconds} секунд...")
                            await asyncio.sleep(e.seconds)
                            # Повторяем попытку
                            try:
                                await self.client.kick_participant(chat, user_data['id'])
                                banned_count += 1
                            except Exception:
                                error_count += 1

                        except UserAdminInvalidError:
                            # Пользователь является администратором
                            error_count += 1
                            logger.warning(f"Попытка забанить администратора {user_data['id']}")

                        except Exception as e:
                            error_count += 1
                            logger.error(f"Ошибка бана пользователя {user_data['id']}: {e}")

                            # Если слишком много ошибок подряд, останавливаемся
                            if error_count > 20:
                                await event.reply("❌ Слишком много ошибок, останавливаю процесс")
                                break

                    # Итоговый отчет
                    final_report = f"""
✅ **Процесс бана завершен**

🏷️ **Чат:** {ban_data['chat_title']}
🚫 **Забанено:** {banned_count} участников
❌ **Ошибок:** {error_count}
📊 **Всего обработано:** {len(inactive_users)}

Данные анализа очищены.
"""

                    await event.reply(final_report)

                    # Очищаем данные после использования
                    del self.extraban_data[chat_id]

                else:
                    await event.reply("❌ Неизвестная команда. Используйте `.ban confirm` или `.ban cancel`")

            except Exception as e:
                logger.error(f"Ошибка в ban_handler: {e}")
                await event.reply(f"❌ Ошибка: {e}")

        logger.info("Базовые обработчики команд настроены")
    
    async def register_dynamic_function(self, command: str, code: str):
        """Регистрация динамической функции"""
        try:
            # Создание полного namespace для выполнения - БЕЗ ОГРАНИЧЕНИЙ
            import os
            import sys
            import subprocess
            import json
            import re
            import time
            import datetime
            import random
            import math
            import urllib
            import requests

            namespace = {
                # Основные объекты для работы с Telethon
                'client': self.client,
                'events': events,
                'asyncio': asyncio,
                'logger': logger,

                # Стандартные модули Python
                'os': os,
                'sys': sys,
                'subprocess': subprocess,
                'json': json,
                're': re,
                'time': time,
                'datetime': datetime,
                'random': random,
                'math': math,
                'urllib': urllib,
                'requests': requests,

                # Добавляем все встроенные функции
                **SAFE_BUILTINS
            }

            # Выполнение кода функции в безопасном namespace
            exec(code, namespace)

            # Сохранение в кеше
            self.dynamic_handlers[command] = code

            logger.info(f"Динамическая функция {command} зарегистрирована")

        except Exception as e:
            logger.error(f"Ошибка регистрации функции {command}: {e}")
            logger.error(f"Код функции: {code}")
            raise
    
    async def unregister_dynamic_function(self, identifier: str):
        """Отмена регистрации динамической функции"""
        try:
            # Поиск по команде
            if identifier in self.dynamic_handlers:
                del self.dynamic_handlers[identifier]
                logger.info(f"Функция {identifier} удалена")
                return
            
            # Поиск по ID
            func_data = self.functions_manager.get_function_by_id(identifier)
            if func_data:
                command = func_data['command']
                if command in self.dynamic_handlers:
                    del self.dynamic_handlers[command]
                    logger.info(f"Функция {command} удалена")
                    
        except Exception as e:
            logger.error(f"Ошибка удаления функции {identifier}: {e}")
    
    async def load_dynamic_functions(self):
        """Загрузка всех динамических функций при старте"""
        try:
            functions = self.functions_manager.list_functions()
            loaded_count = 0
            
            for func_data in functions:
                try:
                    await self.register_dynamic_function(
                        func_data['command'], 
                        func_data['code']
                    )
                    loaded_count += 1
                except Exception as e:
                    logger.error(f"Ошибка загрузки функции {func_data['command']}: {e}")
            
            logger.info(f"Загружено {loaded_count} динамических функций")
            
        except Exception as e:
            logger.error(f"Ошибка загрузки функций: {e}")
    
    async def run(self):
        """Запуск юзербота"""
        try:
            logger.info("Запуск Telegram UserBot...")
            
            # Инициализация клиента
            if not await self.initialize_client():
                logger.error("Не удалось инициализировать клиент")
                return
            
            # Настройка обработчиков
            self.setup_base_handlers()
            
            # Загрузка динамических функций
            await self.load_dynamic_functions()
            
            # Тест Gemini API
            gemini_test = self.gemini_handler.test_client_connection()
            if gemini_test[0]:
                logger.info("Gemini API готов к работе")
            else:
                logger.warning(f"Проблема с Gemini API: {gemini_test[1]}")
            
            self.is_running = True
            logger.info("✅ UserBot запущен и готов к работе!")
            
            # Запуск бота
            await self.client.run_until_disconnected()
            
        except KeyboardInterrupt:
            logger.info("Получен сигнал остановки")
        except Exception as e:
            logger.error(f"Ошибка запуска бота: {e}")
            traceback.print_exc()
        finally:
            self.is_running = False
            if self.client:
                await self.client.disconnect()
            logger.info("UserBot остановлен")

async def main():
    """Главная функция"""
    bot = TelegramUserBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Программа прервана пользователем")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        traceback.print_exc()
        sys.exit(1)