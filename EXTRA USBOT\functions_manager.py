# functions_manager.py - Управление сгенерированными функциями
"""
Модуль для управления сгенерированными функциями юзербота
"""

import json
import os
import uuid
import ast
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging
from extra_config import FUNCTIONS_DB_FILE

logger = logging.getLogger(__name__)

class FunctionsManager:
    """Класс для управления сгенерированными функциями"""
    
    def __init__(self, db_file: str = FUNCTIONS_DB_FILE):
        self.db_file = db_file
        self.functions: Dict[str, Dict[str, Any]] = {}
        self.load_functions()
    
    def load_functions(self) -> None:
        """Загрузка функций из JSON файла"""
        try:
            if os.path.exists(self.db_file):
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'functions' in data:
                        # Преобразуем список в словарь для быстрого доступа
                        for func_data in data['functions']:
                            self.functions[func_data['id']] = func_data
                        logger.info(f"Загружено {len(self.functions)} функций")
                    else:
                        logger.warning("Некорректная структура файла функций")
            else:
                logger.info("Файл функций не найден, создается новый")
                self._save_functions()
        except Exception as e:
            logger.error(f"Ошибка при загрузке функций: {e}")
            self.functions = {}
    
    def _save_functions(self) -> None:
        """Сохранение функций в JSON файл"""
        try:
            data = {
                "functions": list(self.functions.values())
            }
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"Сохранено {len(self.functions)} функций")
        except Exception as e:
            logger.error(f"Ошибка при сохранении функций: {e}")
    
    def validate_code(self, code: str) -> tuple[bool, str]:
        """Валидация кода перед сохранением"""
        try:
            # Проверка на запрещенные импорты и ключевые слова - ОТКЛЮЧЕНА
            # code_lower = code.lower()
            #
            # for forbidden in FORBIDDEN_IMPORTS:
            #     if forbidden in code_lower:
            #         return False, f"Запрещенный импорт: {forbidden}"
            #
            # for forbidden in FORBIDDEN_KEYWORDS:
            #     if forbidden in code_lower:
            #         return False, f"Запрещенное ключевое слово: {forbidden}"
            
            # Проверка синтаксиса Python
            ast.parse(code)
            
            # Проверка на наличие async def
            if 'async def' not in code:
                return False, "Функция должна быть async"
            
            # Проверка на наличие декоратора для события
            if '@client.on(' not in code and '@app.on(' not in code:
                return False, "Функция должна иметь декоратор для обработки событий"
            
            return True, "Код прошел валидацию"
            
        except SyntaxError as e:
            return False, f"Ошибка синтаксиса: {e}"
        except Exception as e:
            return False, f"Ошибка валидации: {e}"
    
    def add_function(self, command: str, description: str, code: str) -> tuple[bool, str]:
        """Добавление новой функции"""
        try:
            # Валидация кода
            is_valid, message = self.validate_code(code)
            if not is_valid:
                return False, message
            
            # Проверка на существование команды
            for func_id, func_data in self.functions.items():
                if func_data['command'] == command:
                    return False, f"Команда {command} уже существует"
            
            # Создание новой функции
            func_id = str(uuid.uuid4())
            function_data = {
                "id": func_id,
                "command": command,
                "description": description,
                "code": code,
                "created_at": datetime.now().isoformat()
            }
            
            self.functions[func_id] = function_data
            self._save_functions()
            
            logger.info(f"Добавлена функция: {command}")
            return True, f"Функция {command} успешно добавлена"
            
        except Exception as e:
            logger.error(f"Ошибка при добавлении функции: {e}")
            return False, f"Ошибка при добавлении функции: {e}"
    
    def remove_function(self, identifier: str) -> tuple[bool, str]:
        """Удаление функции по ID или команде"""
        try:
            # Поиск по ID
            if identifier in self.functions:
                command = self.functions[identifier]['command']
                del self.functions[identifier]
                self._save_functions()
                logger.info(f"Удалена функция: {command}")
                return True, f"Функция {command} успешно удалена"
            
            # Поиск по команде
            for func_id, func_data in self.functions.items():
                if func_data['command'] == identifier:
                    del self.functions[func_id]
                    self._save_functions()
                    logger.info(f"Удалена функция: {identifier}")
                    return True, f"Функция {identifier} успешно удалена"
            
            return False, f"Функция {identifier} не найдена"
            
        except Exception as e:
            logger.error(f"Ошибка при удалении функции: {e}")
            return False, f"Ошибка при удалении функции: {e}"
    
    def list_functions(self) -> List[Dict[str, Any]]:
        """Получение списка всех функций"""
        return list(self.functions.values())
    
    def get_function_by_id(self, func_id: str) -> Optional[Dict[str, Any]]:
        """Получение функции по ID"""
        return self.functions.get(func_id)
    
    def get_function_by_command(self, command: str) -> Optional[Dict[str, Any]]:
        """Получение функции по команде"""
        for func_data in self.functions.values():
            if func_data['command'] == command:
                return func_data
        return None
    
    def get_functions_count(self) -> int:
        """Получение количества функций"""
        return len(self.functions)
    
    def clear_all_functions(self) -> tuple[bool, str]:
        """Очистка всех функций"""
        try:
            count = len(self.functions)
            self.functions.clear()
            self._save_functions()
            logger.info(f"Очищено {count} функций")
            return True, f"Очищено {count} функций"
        except Exception as e:
            logger.error(f"Ошибка при очистке функций: {e}")
            return False, f"Ошибка при очистке функций: {e}"
    
    def export_functions(self, export_file: str) -> tuple[bool, str]:
        """Экспорт функций в файл"""
        try:
            data = {
                "functions": list(self.functions.values()),
                "exported_at": datetime.now().isoformat(),
                "total_functions": len(self.functions)
            }
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Экспортировано {len(self.functions)} функций в {export_file}")
            return True, f"Экспортировано {len(self.functions)} функций в {export_file}"
            
        except Exception as e:
            logger.error(f"Ошибка при экспорте функций: {e}")
            return False, f"Ошибка при экспорте функций: {e}"
    
    def import_functions(self, import_file: str) -> tuple[bool, str]:
        """Импорт функций из файла"""
        try:
            if not os.path.exists(import_file):
                return False, f"Файл {import_file} не найден"
            
            with open(import_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'functions' not in data:
                return False, "Некорректная структура файла для импорта"
            
            imported_count = 0
            for func_data in data['functions']:
                if 'command' in func_data and 'code' in func_data:
                    # Проверяем, что команда не существует
                    if not self.get_function_by_command(func_data['command']):
                        is_valid, _ = self.validate_code(func_data['code'])
                        if is_valid:
                            func_id = str(uuid.uuid4())
                            self.functions[func_id] = {
                                "id": func_id,
                                "command": func_data['command'],
                                "description": func_data.get('description', ''),
                                "code": func_data['code'],
                                "created_at": datetime.now().isoformat()
                            }
                            imported_count += 1
            
            self._save_functions()
            logger.info(f"Импортировано {imported_count} функций из {import_file}")
            return True, f"Импортировано {imported_count} функций из {import_file}"
            
        except Exception as e:
            logger.error(f"Ошибка при импорте функций: {e}")
            return False, f"Ошибка при импорте функций: {e}"