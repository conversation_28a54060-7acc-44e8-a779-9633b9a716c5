# gemini_handler.py - Обработчик для работы с Gemini API
"""
Модуль для работы с Gemini API для генерации кода функций
"""

import logging
import re
from typing import Optional, Tuple
from google import genai
from google.genai import types
from extra_config import (
    GEMINI_API_KEY, GEMINI_MODEL, GEMINI_MAX_TOKENS,
    GEMINI_THINKING_BUDGET, GEMINI_INCLUDE_THOUGHTS, GEMINI_TEMPERATURE
)

logger = logging.getLogger(__name__)

class GeminiHandler:
    """Класс для работы с Gemini API"""
    
    def __init__(self, api_key: str = GEMINI_API_KEY):
        self.api_key = api_key
        self.model = GEMINI_MODEL
        self.max_tokens = GEMINI_MAX_TOKENS
        self.thinking_budget = GEMINI_THINKING_BUDGET
        self.include_thoughts = GEMINI_INCLUDE_THOUGHTS
        self.temperature = GEMINI_TEMPERATURE
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Инициализация клиента Gemini"""
        try:
            self.client = genai.Client(api_key=self.api_key)
            logger.info("Gemini клиент успешно инициализирован")
        except Exception as e:
            logger.error(f"Ошибка инициализации Gemini клиента: {e}")
            raise
    
    def _create_function_prompt(self, command: str, description: str) -> str:
        """Создание промпта для генерации функции"""
        prompt = f"""
Сгенерируй Python функцию для Telethon юзербота.

КОНТЕКСТ ВЫПОЛНЕНИЯ:
Код будет выполняться через exec() в ограниченном, но безопасном namespace с доступом к:
- client: объект TelegramClient из библиотеки Telethon
- events: модуль telethon.events для работы с событиями
- asyncio: модуль для асинхронного программирования
- logger: объект для логирования
- Все стандартные Python функции: len, str, int, Exception, ValueError, etc.

ЗАДАЧА:
Команда: "{command}"
Описание: {description}

СТРОГИЕ ТРЕБОВАНИЯ К КОДУ:

1. СТРУКТУРА ФУНКЦИИ:
   - Используй ТОЧНО этот декоратор: @client.on(events.NewMessage(pattern=r'\\{command}', outgoing=True))
   - Функция ОБЯЗАТЕЛЬНО должна быть async
   - Единственный параметр: event
   - Имя функции: {command.replace('.', '').replace('-', '_')}_handler

2. ОБРАБОТКА ОШИБОК:
   - ВЕСЬ код функции должен быть в блоке try/except
   - Используй ТОЛЬКО Exception (не BaseException или другие)
   - В except блоке логируй ошибку через logger.error()
   - Отправляй пользователю сообщение об ошибке через await event.reply()

3. РАБОТА С TELETHON:
   - Используй await для ВСЕХ асинхронных вызовов
   - Для ответов используй await event.reply() или await event.respond()
   - Для получения текста команды: event.raw_text
   - Для получения аргументов: event.raw_text.split()

4. ФУНКЦИОНАЛЬНОСТЬ:
   - Можешь использовать любые import statements при необходимости
   - Доступны все стандартные модули Python: os, sys, subprocess, etc.
   - Можешь использовать eval, exec, open, file при необходимости
   - Доступны globals(), locals(), vars() и другие встроенные функции
   - Используй любые функции Python для реализации требуемой функциональности

ШАБЛОН КОДА:
```python
@client.on(events.NewMessage(pattern=r'\\{command}', outgoing=True))
async def {command.replace('.', '').replace('-', '_')}_handler(event):
    try:
        # Получение аргументов команды (если нужно)
        args = event.raw_text.split(' ', 1)

        # ТВОЯ ЛОГИКА ЗДЕСЬ
        # Реализуй функциональность согласно описанию

        # Пример базового ответа
        result = "Команда выполнена успешно"
        await event.reply(result)

    except Exception as e:
        logger.error(f"Ошибка в {command}_handler: {{e}}")
        await event.reply(f"❌ Ошибка: {{e}}")
```

ВАЖНЫЕ ДЕТАЛИ:
- Если команда требует аргументы, проверяй их наличие
- Используй эмодзи для улучшения UX (✅, ❌, 🔄, etc.)
- Логируй важные действия через logger.info()
- Для длинных операций можешь показать прогресс

ФОРМАТ ОТВЕТА:
Верни ТОЛЬКО исполняемый Python код без:
- Объяснений
- Комментариев (кроме необходимых в коде)
- Markdown разметки (```)
- Дополнительного текста
"""
        return prompt
    
    def generate_function_code(self, command: str, description: str) -> Tuple[bool, str]:
        """Генерация кода функции через Gemini API"""
        try:
            if not self.client:
                return False, "Gemini клиент не инициализирован"
            
            prompt = self._create_function_prompt(command, description)
            
            # Настройка конфигурации генерации с максимальными параметрами thinking
            config = types.GenerateContentConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                thinking_config=types.ThinkingConfig(
                    thinking_budget=self.thinking_budget,  # Максимальный thinking budget (32768)
                    include_thoughts=self.include_thoughts  # Включаем резюме размышлений
                )
            )
            
            response = self.client.models.generate_content(
                model=self.model,
                contents=prompt,
                config=config
            )
            
            if response and response.text:
                generated_code = self._extract_code_from_response(response.text)
                if generated_code:
                    logger.info(f"Сгенерирован код для команды: {command}")
                    return True, generated_code
                else:
                    logger.error("Не удалось извлечь код из ответа Gemini")
                    return False, "Не удалось извлечь код из ответа"
            else:
                logger.error("Пустой ответ от Gemini API")
                return False, "Пустой ответ от API"
                
        except Exception as e:
            logger.error(f"Ошибка генерации кода: {e}")
            return False, f"Ошибка генерации кода: {e}"
    
    def _extract_code_from_response(self, response_text: str) -> Optional[str]:
        """Извлечение кода из ответа Gemini"""
        try:
            # Убираем markdown разметку если есть
            response_text = response_text.strip()
            
            # Ищем код в блоках ```python
            python_code_pattern = r'```python\n(.*?)```'
            match = re.search(python_code_pattern, response_text, re.DOTALL)
            if match:
                return match.group(1).strip()
            
            # Ищем код в блоках ```
            code_pattern = r'```\n(.*?)```'
            match = re.search(code_pattern, response_text, re.DOTALL)
            if match:
                return match.group(1).strip()
            
            # Если нет блоков кода, проверяем весь текст
            if '@client.on(' in response_text and 'async def' in response_text:
                return response_text.strip()
            
            return None
            
        except Exception as e:
            logger.error(f"Ошибка извлечения кода: {e}")
            return None
    
    def validate_generated_code(self, code: str, command: str) -> Tuple[bool, str]:
        """Валидация сгенерированного кода"""
        try:
            # Базовые проверки
            if not code:
                return False, "Пустой код"

            if 'async def' not in code:
                return False, "Отсутствует async функция"

            if '@client.on(' not in code:
                return False, "Отсутствует декоратор для обработки событий"

            if f'pattern=r\'\\{command}\'' not in code and f'pattern=r"\\{command}"' not in code:
                return False, f"Неверный паттерн для команды {command}"

            # Проверка на наличие обработки исключений
            if 'try:' not in code or 'except' not in code:
                return False, "Отсутствует обработка исключений (try/except)"

            # Проверка на наличие await event.reply
            if 'await event.reply' not in code and 'await event.respond' not in code:
                return False, "Отсутствует await event.reply() или await event.respond()"

            # Проверка на опасные функции ОТКЛЮЧЕНА
            # dangerous_patterns = [
            #     r'import\s+os',
            #     r'import\s+sys',
            #     r'import\s+subprocess',
            #     r'exec\s*\(',
            #     r'eval\s*\(',
            #     r'open\s*\(',
            #     r'file\s*\(',
            #     r'globals\s*\(',
            #     r'locals\s*\(',
            #     r'vars\s*\(',
            #     r'compile\s*\(',
            # ]
            #
            # for pattern in dangerous_patterns:
            #     if re.search(pattern, code, re.IGNORECASE):
            #         return False, f"Обнаружен опасный код: {pattern}"
            
            return True, "Код прошел валидацию"
            
        except Exception as e:
            logger.error(f"Ошибка валидации кода: {e}")
            return False, f"Ошибка валидации: {e}"
    
    def improve_function_code(self, code: str, feedback: str) -> Tuple[bool, str]:
        """Улучшение кода функции на основе обратной связи"""
        try:
            if not self.client:
                return False, "Gemini клиент не инициализирован"
            
            prompt = f"""
Улучши следующий код функции для Telethon юзербота на основе обратной связи:

Текущий код:
```python
{code}
```

Обратная связь: {feedback}

Требования:
- Сохрани структуру и функциональность
- Исправь указанные проблемы
- Используй только безопасные функции Python
- Используй только методы Telethon
- Верни ТОЛЬКО исполняемый Python код без объяснений

Улучшенный код:
"""
            
            config = types.GenerateContentConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                thinking_config=types.ThinkingConfig(
                    thinking_budget=self.thinking_budget,
                    include_thoughts=self.include_thoughts
                )
            )
            
            response = self.client.models.generate_content(
                model=self.model,
                contents=prompt,
                config=config
            )
            
            if response and response.text:
                improved_code = self._extract_code_from_response(response.text)
                if improved_code:
                    logger.info("Код успешно улучшен")
                    return True, improved_code
                else:
                    logger.error("Не удалось извлечь улучшенный код")
                    return False, "Не удалось извлечь улучшенный код"
            else:
                logger.error("Пустой ответ от Gemini API при улучшении")
                return False, "Пустой ответ от API"
                
        except Exception as e:
            logger.error(f"Ошибка улучшения кода: {e}")
            return False, f"Ошибка улучшения кода: {e}"
    
    def generate_function_documentation(self, code: str, command: str) -> Tuple[bool, str]:
        """Генерация документации для функции"""
        try:
            if not self.client:
                return False, "Gemini клиент не инициализирован"
            
            prompt = f"""
Проанализируй следующий код функции для Telethon юзербота и создай краткую документацию:

```python
{code}
```

Создай документацию в формате:
- Команда: {command}
- Описание: [что делает функция]
- Использование: [как использовать команду]
- Параметры: [если есть]
- Примеры: [если применимо]

Верни только текст документации без дополнительных объяснений.
"""
            
            config = types.GenerateContentConfig(
                temperature=0.1,  # Низкая температура для документации
                max_output_tokens=1000,
                thinking_config=types.ThinkingConfig(
                    thinking_budget=8192,  # Средний thinking budget для документации
                    include_thoughts=False  # Не включаем размышления в документацию
                )
            )
            
            response = self.client.models.generate_content(
                model=self.model,
                contents=prompt,
                config=config
            )
            
            if response and response.text:
                logger.info(f"Создана документация для команды: {command}")
                return True, response.text.strip()
            else:
                logger.error("Пустой ответ от Gemini API при создании документации")
                return False, "Пустой ответ от API"
                
        except Exception as e:
            logger.error(f"Ошибка создания документации: {e}")
            return False, f"Ошибка создания документации: {e}"
    
    def test_client_connection(self) -> Tuple[bool, str]:
        """Тестирование соединения с Gemini API"""
        try:
            if not self.client:
                return False, "Клиент не инициализирован"
            
            # Простой тест запрос
            response = self.client.models.generate_content(
                model=self.model,
                contents="Ответь 'OK' если получил это сообщение",
                config=types.GenerateContentConfig(
                    max_output_tokens=10,
                    thinking_config=types.ThinkingConfig(
                        thinking_budget=128,  # Минимальный thinking budget для теста
                        include_thoughts=False
                    )
                )
            )
            
            if response and response.text:
                logger.info("Соединение с Gemini API успешно")
                return True, "Соединение установлено"
            else:
                logger.error("Не удалось получить ответ от API")
                return False, "Не удалось получить ответ"
                
        except Exception as e:
            logger.error(f"Ошибка тестирования соединения: {e}")
            return False, f"Ошибка соединения: {e}"

    def _extract_thinking_summary(self, response) -> Optional[str]:
        """Извлечение резюме размышлений из ответа"""
        try:
            if not response or not response.candidates:
                return None

            thinking_parts = []
            for candidate in response.candidates:
                if candidate.content and candidate.content.parts:
                    for part in candidate.content.parts:
                        if hasattr(part, 'thought') and part.thought and part.text:
                            thinking_parts.append(part.text)

            return "\n".join(thinking_parts) if thinking_parts else None

        except Exception as e:
            logger.error(f"Ошибка извлечения thinking summary: {e}")
            return None

    def generate_with_thinking_analysis(self, command: str, description: str) -> Tuple[bool, str, Optional[str]]:
        """Генерация кода с анализом процесса размышления"""
        try:
            if not self.client:
                return False, "Gemini клиент не инициализирован", None

            prompt = self._create_function_prompt(command, description)

            # Конфигурация с включенными размышлениями
            config = types.GenerateContentConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                thinking_config=types.ThinkingConfig(
                    thinking_budget=self.thinking_budget,
                    include_thoughts=True  # Принудительно включаем для анализа
                )
            )

            response = self.client.models.generate_content(
                model=self.model,
                contents=prompt,
                config=config
            )

            if response and response.text:
                generated_code = self._extract_code_from_response(response.text)
                thinking_summary = self._extract_thinking_summary(response)

                if generated_code:
                    logger.info(f"Сгенерирован код с thinking analysis для команды: {command}")
                    return True, generated_code, thinking_summary
                else:
                    logger.error("Не удалось извлечь код из ответа Gemini")
                    return False, "Не удалось извлечь код из ответа", thinking_summary
            else:
                logger.error("Пустой ответ от Gemini API")
                return False, "Пустой ответ от API", None

        except Exception as e:
            logger.error(f"Ошибка генерации с thinking analysis: {e}")
            return False, f"Ошибка генерации: {e}", None