# run_bot.py - Скрипт для запуска бота с дополнительными функциями
"""
Расширенный скрипт запуска Telegram UserBot с дополнительными возможностями
"""

import asyncio
import sys
import argparse
import logging
import os
from datetime import datetime

# Импорт основных модулей
from extra_main import TelegramUserBot
from test_components import run_all_tests
from functions_manager import FunctionsManager
from gemini_handler import GeminiHandler

def setup_logging(log_level="INFO"):
    """Настройка логирования"""
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='[%(levelname)s %(asctime)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('userbot.log', encoding='utf-8')
        ]
    )

def print_banner():
    """Печать баннера приложения"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════════╗
║                                                                                  ║
║  ████████╗███████╗██╗     ███████╗ ██████╗ ██████╗  █████╗ ███╗   ███╗          ║
║  ╚══██╔══╝██╔════╝██║     ██╔════╝██╔════╝ ██╔══██╗██╔══██╗████╗ ████║          ║
║     ██║   █████╗  ██║     █████╗  ██║  ███╗██████╔╝███████║██╔████╔██║          ║
║     ██║   ██╔══╝  ██║     ██╔══╝  ██║   ██║██╔══██╗██╔══██║██║╚██╔╝██║          ║
║     ██║   ███████╗███████╗███████╗╚██████╔╝██║  ██║██║  ██║██║ ╚═╝ ██║          ║
║     ╚═╝   ╚══════╝╚══════╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝          ║
║                                                                                  ║
║                           USER BOT with AI Functions                            ║
║                                                                                  ║
╚══════════════════════════════════════════════════════════════════════════════════╝

    🤖 Telegram UserBot с динамической генерацией функций
    🧠 Powered by Gemini AI
    📚 Telethon Library
    🔧 Создано для автоматизации и расширения функциональности

"""
    print(banner)

def check_dependencies():
    """Проверка установленных зависимостей"""
    print("🔍 Проверка зависимостей...")
    
    try:
        import telethon
        print(f"✅ Telethon: {telethon.__version__}")
    except ImportError:
        print("❌ Telethon не установлен")
        return False
    
    try:
        from google import genai
        print("✅ Google GenAI: установлен")
    except ImportError:
        print("❌ Google GenAI не установлен")
        return False
    
    try:
        import cryptg
        print("✅ CryptG: установлен (ускорение)")
    except ImportError:
        print("⚠️ CryptG не установлен (рекомендуется для производительности)")
    
    return True

def check_config():
    """Проверка конфигурации"""
    print("🔧 Проверка конфигурации...")
    
    try:
        from extra_config import TELEGRAM_API_ID, TELEGRAM_API_HASH, GEMINI_API_KEY
        
        if not TELEGRAM_API_ID or not TELEGRAM_API_HASH:
            print("❌ Telegram API данные не настроены")
            return False
        
        if not GEMINI_API_KEY:
            print("❌ Gemini API ключ не настроен")
            return False
        
        print("✅ Конфигурация корректна")
        return True
        
    except ImportError as e:
        print(f"❌ Ошибка импорта конфигурации: {e}")
        return False

def backup_functions():
    """Создание резервной копии функций"""
    try:
        manager = FunctionsManager()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"functions_backup_{timestamp}.json"
        
        success, message = manager.export_functions(backup_file)
        if success:
            print(f"✅ Резервная копия создана: {backup_file}")
            return backup_file
        else:
            print(f"❌ Ошибка создания резервной копии: {message}")
            return None
            
    except Exception as e:
        print(f"❌ Ошибка при создании резервной копии: {e}")
        return None

def restore_functions(backup_file):
    """Восстановление функций из резервной копии"""
    try:
        manager = FunctionsManager()
        success, message = manager.import_functions(backup_file)
        
        if success:
            print(f"✅ Функции восстановлены из {backup_file}")
            return True
        else:
            print(f"❌ Ошибка восстановления: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при восстановлении: {e}")
        return False

def show_functions_info():
    """Показать информацию о функциях"""
    try:
        manager = FunctionsManager()
        functions = manager.list_functions()
        
        print(f"\n📊 Статистика функций:")
        print(f"Всего функций: {len(functions)}")
        
        if functions:
            print(f"\nСписок функций:")
            for i, func in enumerate(functions, 1):
                print(f"{i}. {func['command']} - {func['description']}")
                print(f"   Создано: {func['created_at'][:19]}")
                print(f"   ID: {func['id']}")
                print()
        else:
            print("Нет сохраненных функций")
            
    except Exception as e:
        print(f"❌ Ошибка получения информации о функциях: {e}")

async def interactive_setup():
    """Интерактивная настройка бота"""
    print("\n🛠️ Интерактивная настройка")
    print("Нажмите Enter для значений по умолчанию")
    
    # Проверка Gemini API
    try:
        handler = GeminiHandler()
        success, message = handler.test_client_connection()
        
        if success:
            print("✅ Gemini API: подключение установлено")
        else:
            print(f"⚠️ Gemini API: {message}")
            
            response = input("Продолжить без Gemini API? (y/N): ").strip().lower()
            if response != 'y':
                print("Настройте Gemini API в config.py и повторите попытку")
                return False
    
    except Exception as e:
        print(f"❌ Ошибка Gemini API: {e}")
        return False
    
    # Создание резервной копии
    response = input("Создать резервную копию функций? (Y/n): ").strip().lower()
    if response != 'n':
        backup_functions()
    
    return True

async def main():
    """Главная функция"""
    parser = argparse.ArgumentParser(description="Telegram UserBot с AI функциями")
    parser.add_argument("--test", action="store_true", help="Запустить тесты")
    parser.add_argument("--info", action="store_true", help="Показать информацию о функциях")
    parser.add_argument("--backup", action="store_true", help="Создать резервную копию функций")
    parser.add_argument("--restore", type=str, help="Восстановить функции из файла")
    parser.add_argument("--setup", action="store_true", help="Интерактивная настройка")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       help="Уровень логирования")
    parser.add_argument("--no-banner", action="store_true", help="Не показывать баннер")
    
    args = parser.parse_args()
    
    # Настройка логирования
    setup_logging(args.log_level)
    
    # Показать баннер
    if not args.no_banner:
        print_banner()
    
    # Обработка команд
    if args.test:
        print("🧪 Запуск тестов...")
        await run_all_tests()
        return
    
    if args.info:
        show_functions_info()
        return
    
    if args.backup:
        backup_functions()
        return
    
    if args.restore:
        if os.path.exists(args.restore):
            restore_functions(args.restore)
        else:
            print(f"❌ Файл {args.restore} не найден")
        return
    
    if args.setup:
        if not await interactive_setup():
            return
    
    # Проверка системы
    if not check_dependencies():
        print("❌ Не все зависимости установлены")
        print("Выполните: pip install -r requirements.txt")
        return
    
    if not check_config():
        print("❌ Конфигурация не настроена")
        print("Настройте файл config.py и повторите попытку")
        return
    
    # Запуск бота
    print("🚀 Запуск UserBot...")
    bot = TelegramUserBot()
    
    try:
        await bot.run()
    except KeyboardInterrupt:
        print("\n👋 Получен сигнал остановки")
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        logging.error(f"Критическая ошибка: {e}", exc_info=True)
    finally:
        print("🛑 UserBot остановлен")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Программа прервана пользователем")
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        sys.exit(1)