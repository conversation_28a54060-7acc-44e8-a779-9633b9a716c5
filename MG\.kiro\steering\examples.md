---
inclusion: manual
description: "Примеры использования steering файлов"
---

# Примеры использования Steering

## Пример 1: Стандарты кодирования для всего проекта

**Файл: `.kiro/steering/coding-standards.md`**

```markdown
---
inclusion: always
---

# Стандарты кодирования

## Python
- Используйте type hints
- Максимальная длина строки: 88 символов
- Docstrings в формате Google Style
- Используйте black для форматирования

## Именование
- Переменные и функции: snake_case
- Классы: PascalCase
- Константы: UPPER_CASE

## Импорты
Группируйте импорты в следующем порядке:
1. Стандартные библиотеки
2. Сторонние библиотеки
3. Локальные модули
```

## Пример 2: Инструкции для работы с README файлами

**Файл: `.kiro/steering/readme-guidelines.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: 'README*'
---

# Инструкции для README файлов

При работе с README файлами:

## Структура
1. Заголовок проекта
2. Краткое описание
3. Установка
4. Использование
5. API документация
6. Примеры
7. Лицензия

## Стиль
- Используйте четкие заголовки
- Добавляйте примеры кода
- Включайте скриншоты если нужно
- Поддерживайте актуальность

## Шаблон
```markdown
# Название проекта

Краткое описание проекта.

## Установка

\```bash
pip install package-name
\```

## Использование

\```python
import package
result = package.function()
\```
```
```

## Пример 3: Специальные инструкции для деплоя

**Файл: `.kiro/steering/deployment.md`**

```markdown
---
inclusion: manual
description: "Инструкции для деплоя"
---

# Инструкции по деплою

## Перед деплоем
1. Запустите все тесты
2. Обновите версию в setup.py
3. Создайте git tag
4. Обновите CHANGELOG.md

## Команды деплоя
```bash
# Тестирование
pytest tests/ --cov=src

# Сборка
python setup.py sdist bdist_wheel

# Деплой в staging
docker-compose -f docker-compose.staging.yml up -d

# Деплой в production
docker-compose -f docker-compose.prod.yml up -d
```

## Откат
В случае проблем:
```bash
docker-compose -f docker-compose.prod.yml down
git checkout previous-tag
docker-compose -f docker-compose.prod.yml up -d
```
```

## Пример 4: Инструкции для тестов

**Файл: `.kiro/steering/testing.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: '*test*.py'
---

# Инструкции для тестов

## Структура тестов
- Один тест файл на модуль
- Группируйте тесты в классы
- Используйте описательные имена

## Fixtures
```python
@pytest.fixture
def sample_data():
    return {"key": "value"}
```

## Моки
```python
@patch('module.external_api')
def test_with_mock(mock_api):
    mock_api.return_value = "mocked_response"
    # тест
```

## Команды
```bash
# Все тесты
pytest

# С покрытием
pytest --cov=src

# Конкретный тест
pytest tests/test_module.py::test_function
```
```

## Пример 5: Работа с API документацией

**Файл: `.kiro/steering/api-docs.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: '*api*.py'
---

# API документация

При работе с API модулями используйте следующую OpenAPI спецификацию:

#[[file:api/openapi.yaml]]

## Стандарты API
- Используйте RESTful принципы
- Версионирование через URL (/api/v1/)
- Стандартные HTTP коды ответов
- JSON формат для всех ответов

## Обработка ошибок
```python
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500
```

## Валидация
Используйте Pydantic для валидации входных данных:
```python
from pydantic import BaseModel

class UserCreate(BaseModel):
    name: str
    email: str
```
```

## Пример 6: Конфигурация для разных окружений

**Файл: `.kiro/steering/environment-config.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: '*config*.py'
---

# Конфигурация окружений

## Переменные окружения
Используйте следующие переменные:

### Обязательные
- `DATABASE_URL` - URL базы данных
- `SECRET_KEY` - секретный ключ приложения
- `API_KEY` - ключ внешнего API

### Опциональные
- `DEBUG` - режим отладки (default: False)
- `LOG_LEVEL` - уровень логирования (default: INFO)

## Структура конфигурации
```python
import os
from typing import Optional

class Config:
    """Базовая конфигурация."""
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key')
    DATABASE_URL = os.getenv('DATABASE_URL')
    
class DevelopmentConfig(Config):
    """Конфигурация для разработки."""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    
class ProductionConfig(Config):
    """Конфигурация для продакшн."""
    DEBUG = False
    LOG_LEVEL = 'INFO'
```

## Загрузка конфигурации
```python
def get_config() -> Config:
    env = os.getenv('ENVIRONMENT', 'development')
    if env == 'production':
        return ProductionConfig()
    return DevelopmentConfig()
```
```

## Пример 7: Инструкции для работы с базой данных

**Файл: `.kiro/steering/database.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: '*{model,db,database}*.py'
---

# Работа с базой данных

## ORM модели
Используйте SQLAlchemy для определения моделей:

```python
from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

## Миграции
```bash
# Создание миграции
alembic revision --autogenerate -m "Add user table"

# Применение миграций
alembic upgrade head

# Откат миграции
alembic downgrade -1
```

## Запросы
```python
# Создание
user = User(name="John", email="<EMAIL>")
session.add(user)
session.commit()

# Чтение
user = session.query(User).filter_by(email="<EMAIL>").first()

# Обновление
user.name = "John Doe"
session.commit()

# Удаление
session.delete(user)
session.commit()
```

Схема базы данных:
#[[file:database/schema.sql]]
```

## Пример 8: Инструкции для фронтенда

**Файл: `.kiro/steering/frontend.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: '{*.js,*.ts,*.jsx,*.tsx,*.vue}'
---

# Фронтенд разработка

## Стандарты кодирования
- Используйте TypeScript
- ESLint + Prettier для форматирования
- Компоненты в PascalCase
- Хуки начинаются с "use"

## Структура компонентов
```typescript
interface Props {
  title: string;
  onClick: () => void;
}

const Button: React.FC<Props> = ({ title, onClick }) => {
  return (
    <button onClick={onClick} className="btn">
      {title}
    </button>
  );
};

export default Button;
```

## Стили
Используйте CSS модули или styled-components:
```typescript
import styles from './Button.module.css';

const Button = () => (
  <button className={styles.button}>
    Click me
  </button>
);
```

## Тестирование
```typescript
import { render, fireEvent } from '@testing-library/react';
import Button from './Button';

test('calls onClick when clicked', () => {
  const handleClick = jest.fn();
  const { getByText } = render(
    <Button title="Click me" onClick={handleClick} />
  );
  
  fireEvent.click(getByText('Click me'));
  expect(handleClick).toHaveBeenCalledTimes(1);
});
```
```

## Пример 9: Инструкции для документации

**Файл: `.kiro/steering/documentation.md`**

```markdown
---
inclusion: fileMatch
fileMatchPattern: '*.md'
---

# Стандарты документации

## Структура документов
1. Заголовок (H1)
2. Краткое описание
3. Содержание (для длинных документов)
4. Основной контент
5. Примеры
6. Ссылки

## Стиль написания
- Используйте активный залог
- Короткие предложения
- Конкретные примеры
- Актуальная информация

## Форматирование кода
```python
# Хорошо
def calculate_total(items: List[Item]) -> float:
    """Вычисляет общую стоимость товаров."""
    return sum(item.price for item in items)
```

## Ссылки
- [Внутренние ссылки](./other-doc.md)
- [Внешние ссылки](https://example.com)

## Изображения
![Описание](./images/screenshot.png)
```

Эти примеры показывают различные способы использования steering файлов для автоматизации и стандартизации разработки в Kiro.