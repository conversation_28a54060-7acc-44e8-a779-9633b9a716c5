---
inclusion: always
---

# Стандарты проекта медиа-бота

## Архитектура проекта

Проект состоит из следующих модулей:
- `media_bot.py` - основной Telegram бот
- `media_handlers.py` - обработчики различных типов медиа
- `media_openai.py` - интеграция с OpenAI API
- `media_utils.py` - вспомогательные утилиты
- `media_config.py` - конфигурация приложения

## Стандарты кодирования Python

### Общие правила
- Используйте type hints для всех функций и методов
- Максимальная длина строки: 88 символов
- Используйте black для автоматического форматирования
- Все публичные функции должны иметь docstrings в формате Google Style

### Пример правильного оформления функции
```python
def process_media_file(file_path: str, media_type: str) -> dict[str, Any]:
    """Обрабатывает медиа файл и возвращает метаданные.
    
    Args:
        file_path: Путь к медиа файлу
        media_type: Тип медиа (image, video, audio)
        
    Returns:
        Словарь с метаданными файла
        
    Raises:
        FileNotFoundError: Если файл не найден
        ValueError: Если неподдерживаемый тип медиа
    """
    # Реализация функции
    pass
```

### Обработка ошибок
- Всегда используйте специфичные исключения
- Логируйте ошибки с соответствующим уровнем
- Предоставляйте понятные сообщения об ошибках пользователю

### Структура импортов
```python
# Стандартные библиотеки
import os
import logging
from typing import Any, Dict, List, Optional

# Сторонние библиотеки
import openai
from telegram import Update
from telegram.ext import Application

# Локальные модули
from media_config import Config
from media_utils import validate_file
```

## Конфигурация и переменные окружения

### Обязательные переменные
- `TELEGRAM_BOT_TOKEN` - токен Telegram бота
- `OPENAI_API_KEY` - ключ API OpenAI

### Опциональные переменные
- `LOG_LEVEL` - уровень логирования (по умолчанию INFO)
- `MAX_FILE_SIZE` - максимальный размер файла в байтах
- `SUPPORTED_FORMATS` - поддерживаемые форматы файлов

## Логирование

Используйте структурированное логирование:

```python
import logging

logger = logging.getLogger(__name__)

# Информационные сообщения
logger.info("Обработка файла: %s", filename)

# Предупреждения
logger.warning("Файл превышает рекомендуемый размер: %s", file_size)

# Ошибки
logger.error("Ошибка обработки файла: %s", str(error))
```

## Тестирование

- Покрытие кода тестами должно быть не менее 80%
- Используйте pytest для написания тестов
- Мокайте внешние API (OpenAI, Telegram)
- Тестируйте граничные случаи и обработку ошибок

Спецификация проекта:
#[[file:sh_media_plan.txt]]