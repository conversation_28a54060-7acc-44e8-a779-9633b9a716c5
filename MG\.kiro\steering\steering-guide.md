# Руководство по Steering в Kiro

## Что такое Steering?

Steering - это мощная система контекстных инструкций в Kiro, которая позволяет включать дополнительный контекст и инструкции во все или некоторые взаимодействия пользователя с ИИ-ассистентом.

## Основные возможности

- **Стандарты команды**: Определение норм и стандартов кодирования для всей команды
- **Информация о проекте**: Полезная информация о структуре и особенностях проекта
- **Инструкции по задачам**: Дополнительная информация о том, как выполнять задачи (сборка, тестирование и т.д.)
- **Автоматическое включение**: Контекст может включаться автоматически или по условию

## Расположение файлов

Все steering файлы должны находиться в папке `.kiro/steering/*.md`

## Типы включения

### 1. Always (Всегда) - По умолчанию

Файлы без front-matter или с `inclusion: always` включаются во все взаимодействия.

```markdown
---
inclusion: always
---

# Стандарты кодирования команды

Всегда используйте TypeScript для новых проектов...
```

### 2. FileMatch (По совпадению файла)

Включается только когда определенный файл читается в контекст.

```markdown
---
inclusion: fileMatch
fileMatchPattern: 'README*'
---

# Инструкции для README файлов

При работе с README файлами всегда...
```

### 3. Manual (Ручное)

Включается только когда пользователь явно указывает через контекстный ключ ('#' в чате).

```markdown
---
inclusion: manual
---

# Специальные инструкции для деплоя

Эти инструкции используются только при деплое...
```

## Ссылки на файлы

В steering файлах можно ссылаться на другие файлы проекта:

```markdown
# Стандарты API

Используйте следующую OpenAPI спецификацию:
#[[file:api/openapi.yaml]]

Для GraphQL схемы:
#[[file:schema/schema.graphql]]
```

## Примеры использования

### Стандарты кодирования

```markdown
---
inclusion: always
---

# Стандарты кодирования проекта

## Python
- Используйте type hints для всех функций
- Максимальная длина строки: 88 символов
- Используйте black для форматирования
- Все функции должны иметь docstrings

## Структура проекта
- Модели в папке `models/`
- Утилиты в папке `utils/`
- Тесты в папке `tests/`
```

### Инструкции для тестирования

```markdown
---
inclusion: fileMatch
fileMatchPattern: '*test*.py'
---

# Инструкции для тестов

При написании тестов:
1. Используйте pytest
2. Покрытие должно быть не менее 80%
3. Мокайте внешние зависимости
4. Используйте fixtures для общих данных

Команда для запуска тестов:
```bash
pytest tests/ -v --cov=src
```
```

### Специфичные для проекта инструкции

```markdown
---
inclusion: always
---

# Особенности медиа-бота

## Архитектура
Проект состоит из следующих модулей:
- `media_bot.py` - основной бот
- `media_handlers.py` - обработчики медиа
- `media_openai.py` - интеграция с OpenAI
- `media_utils.py` - утилиты

## Конфигурация
Все настройки в `media_config.py`

## API ключи
- OpenAI API ключ в переменной окружения `OPENAI_API_KEY`
- Telegram Bot Token в `TELEGRAM_BOT_TOKEN`

Спецификация API:
#[[file:sh_media_plan.txt]]
```

## Лучшие практики

### 1. Организация файлов
- Используйте описательные имена файлов
- Группируйте связанные инструкции
- Не дублируйте информацию между файлами

### 2. Написание инструкций
- Будьте конкретными и четкими
- Используйте примеры кода
- Обновляйте инструкции при изменении проекта

### 3. Условное включение
- Используйте `fileMatch` для специфичных инструкций
- `manual` для редко используемых инструкций
- `always` только для критически важной информации

### 4. Ссылки на файлы
- Ссылайтесь на актуальные файлы проекта
- Используйте относительные пути
- Проверяйте существование файлов

## Управление steering файлами

### Создание нового файла
```bash
# Создать новый steering файл
touch .kiro/steering/new-instructions.md
```

### Редактирование
Просто отредактируйте файл в `.kiro/steering/` и изменения применятся автоматически.

### Отладка
Если steering не работает:
1. Проверьте синтаксис front-matter
2. Убедитесь, что файл в правильной папке
3. Проверьте паттерны fileMatch

## Примеры front-matter

### Базовый always
```yaml
---
inclusion: always
---
```

### FileMatch с паттерном
```yaml
---
inclusion: fileMatch
fileMatchPattern: '*.py'
---
```

### Множественные паттерны
```yaml
---
inclusion: fileMatch
fileMatchPattern: '{*.js,*.ts,*.jsx,*.tsx}'
---
```

### Manual с описанием
```yaml
---
inclusion: manual
description: "Инструкции для деплоя в продакшн"
---
```

Эта система позволяет создать мощный и гибкий контекст для работы с Kiro, адаптированный под специфику вашего проекта и команды.