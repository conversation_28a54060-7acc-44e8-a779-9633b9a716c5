---
inclusion: fileMatch
fileMatchPattern: '*test*.py'
---

# Руководство по тестированию

## Структура тестов

Все тесты должны находиться в папке `tests/` с соответствующей структурой:

```
tests/
├── test_media_bot.py
├── test_media_handlers.py
├── test_media_openai.py
├── test_media_utils.py
└── fixtures/
    ├── sample_image.jpg
    ├── sample_video.mp4
    └── sample_audio.mp3
```

## Стандарты написания тестов

### Именование тестов
```python
def test_process_image_success():
    """Тест успешной обработки изображения."""
    pass

def test_process_image_invalid_format():
    """Тест обработки изображения неподдерживаемого формата."""
    pass

def test_process_image_file_not_found():
    """Тест обработки несуществующего файла."""
    pass
```

### Использование fixtures
```python
import pytest
from unittest.mock import Mock, patch

@pytest.fixture
def sample_image_path():
    """Путь к тестовому изображению."""
    return "tests/fixtures/sample_image.jpg"

@pytest.fixture
def mock_openai_client():
    """Мок клиента OpenAI."""
    with patch('media_openai.OpenAI') as mock:
        yield mock

@pytest.fixture
def telegram_update():
    """Мок Telegram Update объекта."""
    update = Mock()
    update.message.photo = [Mock()]
    update.message.photo[0].file_id = "test_file_id"
    return update
```

### Тестирование обработчиков медиа
```python
def test_handle_photo_message(telegram_update, mock_openai_client):
    """Тест обработки фото сообщения."""
    # Arrange
    handler = MediaHandler()
    
    # Act
    result = handler.handle_photo(telegram_update)
    
    # Assert
    assert result is not None
    assert "description" in result
    mock_openai_client.assert_called_once()
```

### Тестирование ошибок
```python
def test_process_unsupported_format():
    """Тест обработки неподдерживаемого формата."""
    with pytest.raises(ValueError, match="Неподдерживаемый формат"):
        process_media_file("test.xyz", "unknown")

def test_process_large_file():
    """Тест обработки слишком большого файла."""
    with pytest.raises(FileSizeError):
        process_media_file("large_file.jpg", "image")
```

## Моки и заглушки

### OpenAI API
```python
@patch('media_openai.OpenAI')
def test_analyze_image_with_openai(mock_openai):
    """Тест анализа изображения через OpenAI."""
    # Настройка мока
    mock_client = mock_openai.return_value
    mock_client.chat.completions.create.return_value.choices[0].message.content = "Описание изображения"
    
    # Тест
    result = analyze_image("path/to/image.jpg")
    
    # Проверки
    assert result == "Описание изображения"
    mock_client.chat.completions.create.assert_called_once()
```

### Telegram Bot API
```python
@patch('telegram.Bot.get_file')
def test_download_telegram_file(mock_get_file):
    """Тест загрузки файла из Telegram."""
    # Настройка мока
    mock_file = Mock()
    mock_file.download_to_drive.return_value = "downloaded_file.jpg"
    mock_get_file.return_value = mock_file
    
    # Тест
    result = download_file("file_id")
    
    # Проверки
    assert result == "downloaded_file.jpg"
```

## Команды для запуска тестов

### Запуск всех тестов
```bash
pytest tests/ -v
```

### Запуск с покрытием
```bash
pytest tests/ -v --cov=src --cov-report=html
```

### Запуск конкретного теста
```bash
pytest tests/test_media_handlers.py::test_handle_photo_message -v
```

### Запуск тестов с маркерами
```bash
# Только быстрые тесты
pytest -m "not slow"

# Только интеграционные тесты
pytest -m "integration"
```

## Маркеры для тестов

```python
import pytest

@pytest.mark.slow
def test_large_video_processing():
    """Медленный тест обработки большого видео."""
    pass

@pytest.mark.integration
def test_full_workflow():
    """Интеграционный тест полного workflow."""
    pass

@pytest.mark.unit
def test_utility_function():
    """Юнит-тест утилитарной функции."""
    pass
```

## Конфигурация pytest

Создайте файл `pytest.ini`:
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    slow: медленные тесты
    integration: интеграционные тесты
    unit: юнит-тесты
addopts = 
    -v
    --tb=short
    --strict-markers
```

## Лучшие практики

1. **Один тест - одна проверка**: Каждый тест должен проверять только одну функциональность
2. **Независимость тестов**: Тесты не должны зависеть друг от друга
3. **Понятные имена**: Имена тестов должны четко описывать что тестируется
4. **Arrange-Act-Assert**: Структурируйте тесты по этому паттерну
5. **Мокайте внешние зависимости**: Не делайте реальные HTTP запросы в тестах