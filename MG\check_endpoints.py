#!/usr/bin/env python3
"""
Скрипт для проверки доступных эндпойнтов API cody.su
"""

from openai import OpenAI

# API ключ из конфигурации
api_key = "cody-V_AtIw73yP1d_rK9DmlLlZgOO2PzWKONwwFZ6sr40gWRZatPF_QzoxK_HAR-6PQIEPcVkL9kSrwfJwGdoafcdA"

try:
    # Создаем клиент с правильным base_url
    client = OpenAI(base_url="https://cody.su/api/v1", api_key=api_key)
    
    print("Проверяем доступные модели...")
    models = client.models.list()
    
    print("\nДоступные модели:")
    for model in models.data:
        print(f"- {model.id}")
        
except Exception as e:
    print(f"Ошибка при получении списка моделей: {e}")
