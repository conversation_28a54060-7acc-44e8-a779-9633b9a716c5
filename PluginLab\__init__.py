# PluginLab - Модульная структура для Telegram Plugin Maker Bot
"""
PluginLab - модульная архитектура для бота создания плагинов exteraGram.

Структура модулей:
- config.py - конфигурация и константы
- data_manager.py - работа с данными, подписками и поддержкой  
- ai_client.py - работа с AI API и парсинг ответов
- handlers.py - все обработчики команд и сообщений
- middleware.py - middleware классы
- utils.py - вспомогательные функции
- main.py - главный модуль для запуска бота
"""

__version__ = "1.0.0"
__author__ = "PluginLab Team"

# Импортируем основные компоненты для удобства использования
from .main import main

__all__ = ["main"]
