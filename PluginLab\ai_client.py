# ai_client.py - Работа с AI API и парсинг ответов
import asyncio
import logging
import random
import re
import aiohttp

from . import config
from .data_manager import (
    get_user_daily_pro_usage, increment_user_daily_pro_usage, is_subscription_active,
    get_user_daily_total_usage, increment_user_daily_total_usage,
    get_user_daily_free_usage, increment_user_daily_free_usage,
    get_user_daily_custom_key_usage, increment_user_daily_custom_key_usage,
    has_user_custom_key, get_user_custom_key
)


# --- ФУНКЦИИ ДЛЯ ПАРСИНГА ОТВЕТОВ AI ---

def sanitize_html(text: str) -> str:
    """Очищает HTML теги из текста"""
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'</p>', '\n\n', text, flags=re.IGNORECASE)
    text = re.sub(r'<[^>]+>', '', text)
    return text.strip()


def parse_ai_response(response_text: str) -> tuple[str | None, str | None, str | None, bool]:
    """
    Парсит ответ AI и извлекает заголовок, описание, код плагина

    Returns:
        tuple: (title, description, code, is_ban_detected)
        - title: заголовок плагина или None
        - description: описание плагина или None
        - code: код плагина или None
        - is_ban_detected: всегда False (система автобанов отключена)
    """
    try:
        # Сначала пробуем найти стандартный markdown-блок
        code_match_markdown = re.search(r"```(?:python)?\s*\n(.*?)\n?```", response_text, re.DOTALL)

        if code_match_markdown:
            code = code_match_markdown.group(1).strip()
            before_code = response_text[:code_match_markdown.start()].strip()
            parts = before_code.split('\n', 1)
            title = parts[0].strip()
            description = parts[1].strip() if len(parts) > 1 and parts[1].strip() else "Описание не предоставлено."
            return title, description, code, False

        # Если markdown не найден, ищем код по характерным признакам плагина

        # Ищем код, который начинается с метаданных плагина
        metadata_pattern = r'(# --- Метаданные ---.*?)(?=\n\n[^#\s]|\Z)'
        metadata_match = re.search(metadata_pattern, response_text, re.DOTALL)

        if metadata_match:
            # Найдены метаданные, ищем весь код от метаданных до конца
            start_pos = metadata_match.start()
            code_text = response_text[start_pos:].strip()

            # Убираем возможный мусор в конце (например, "...")
            code_text = re.sub(r'\s*\.\.\.\s*$', '', code_text)

            # Получаем заголовок и описание из части до кода
            before_code = response_text[:start_pos].strip()
            lines = before_code.split('\n')

            title = "Плагин"
            description = "Описание не предоставлено."

            # Ищем заголовок (обычно содержит эмодзи или начинается с заглавной буквы)
            for line in lines:
                line = line.strip()
                if line and line != "💡 Информация":  # Пропускаем стандартный заголовок
                    if (any(ord(char) > 127 for char in line) or line[0].isupper()) and len(line) < 100:
                        title = line
                        break

            # Ищем описание (обычно самая длинная строка с текстом)
            for line in lines:
                line = line.strip()
                if line and len(line) > 50 and line != title:
                    description = line
                    break

            return title, description, code_text, False

        # Если и это не сработало, ищем любой Python-код
        python_code_pattern = r'((?:from|import|class|def|__\w+__\s*=).*?)(?=\n\n[^#\s]|\Z)'
        python_match = re.search(python_code_pattern, response_text, re.DOTALL)

        if python_match:
            code_text = python_match.group(1).strip()
            before_code = response_text[:python_match.start()].strip()

            title = "Код"
            description = before_code if before_code else "Описание не предоставлено."

            return title, description, code_text, False

        # Если ничего не найдено, возвращаем весь текст как описание
        logging.error("Не удалось найти код в ответе ИИ.")
        plain_text = sanitize_html(response_text)
        return "Информация", plain_text, None, False

    except Exception as e:
        logging.exception(f"Критическая ошибка парсинга ответа ИИ: {e}")
        return None, None, None, False


def add_watermark_comments(code: str) -> str:
    """Добавляет водяные знаки в код для бесплатных пользователей"""
    watermark = "# Плагин сгенерирован @UseLabBot"
    lines = code.split('\n')
    watermarked_lines = []
    for line in lines:
        watermarked_lines.append(line)
        watermarked_lines.append(watermark)
    return '\n'.join(watermarked_lines)


def get_random_gemini_key():
    """Возвращает случайный ключ API Gemini"""
    if not config.GEMINI_KEYS:
        logging.error("Нет доступных ключей API Gemini")
        return None
    key_id = random.choice(list(config.GEMINI_KEYS.keys()))
    return config.GEMINI_KEYS[key_id]


def is_user_pro(user_id: int) -> bool:
    """Проверяет, является ли пользователь Pro (админ, подписка или старая система, но НЕ пользователи с собственными ключами)"""
    return (config.is_admin(user_id) or
            is_subscription_active(user_id) or
            user_id in config.UNLIMITED_USERS)


def is_user_free(user_id: int) -> bool:
    """Проверяет, является ли пользователь бесплатным (не Pro и не админ)"""
    return not is_user_pro(user_id)


def get_max_file_size_for_user(user_id: int) -> int:
    """Возвращает максимальный размер файла для пользователя (только Pro размер, так как бот платный)"""
    # Все пользователи теперь должны быть Pro, поэтому возвращаем Pro размер
    return config.MAX_FILE_SIZE_BYTES_PRO


def can_use_plugins_today(user_id: int) -> bool:
    """Проверяет, может ли пользователь использовать плагины сегодня"""
    # Проверяем админа - только админы имеют безлимит
    if config.is_admin(user_id):
        return True

    # Проверяем пользовательский ключ - теперь с лимитом 30 плагинов в день
    if has_user_custom_key(user_id):
        custom_key_usage_today = get_user_daily_custom_key_usage(user_id)
        can_use = custom_key_usage_today < config.DAILY_CUSTOM_KEY_LIMIT
        return can_use

    # Проверяем активную подписку
    if is_subscription_active(user_id):
        # Для Pro пользователей проверяем дневной лимит 50 плагинов
        total_usage_today = get_user_daily_total_usage(user_id)
        can_use = total_usage_today < config.DAILY_PRO_LIMIT
        return can_use

    # Проверяем старую систему (для совместимости)
    if user_id in config.UNLIMITED_USERS:
        # Для Pro пользователей проверяем дневной лимит 50 плагинов
        total_usage_today = get_user_daily_total_usage(user_id)
        can_use = total_usage_today < config.DAILY_PRO_LIMIT
        return can_use

    # Бесплатные пользователи могут использовать 5 плагинов в день
    free_usage_today = get_user_daily_free_usage(user_id)
    can_use = free_usage_today < config.DAILY_FREE_LIMIT
    return can_use


def can_use_pro_today(user_id: int) -> bool:
    """Проверяет, может ли пользователь использовать Gemini 2.5 Pro сегодня (только для Pro пользователей)"""
    # Проверяем админа
    if config.is_admin(user_id):
        return True

    # Проверяем активную подписку
    if is_subscription_active(user_id):
        # Для Pro пользователей проверяем дневной лимит 50 плагинов
        total_usage_today = get_user_daily_total_usage(user_id)
        can_use = total_usage_today < config.DAILY_PRO_LIMIT
        return can_use

    # Проверяем старую систему (для совместимости)
    if user_id in config.UNLIMITED_USERS:
        # Для Pro пользователей проверяем дневной лимит 50 плагинов
        total_usage_today = get_user_daily_total_usage(user_id)
        can_use = total_usage_today < config.DAILY_PRO_LIMIT
        return can_use

    # Бесплатные пользователи больше не могут использовать бота
    return False


async def official_gemini_pro_async(prompt: str, session: aiohttp.ClientSession, image_base64: str = None) -> tuple[str | None, dict | None, str]:
    """
    Асинхронная функция для работы с Gemini 2.5 Pro через официальный API
    Возвращает (response_text, usage_stats, model_used) или (None, None, model_name) при ошибке
    """
    endpoint = f"https://generativelanguage.googleapis.com/v1beta/{config.OFFICIAL_GEMINI_PRO_MODEL}:generateContent"
    headers = {"Content-Type": "application/json"}

    # Подготавливаем части запроса
    parts = [{"text": prompt}]
    if image_base64:
        parts.append({
            "inline_data": {
                "mime_type": "image/jpeg",
                "data": image_base64
            }
        })

    payload = {
        "contents": [{"role": "user", "parts": parts}],
        "generationConfig": {
            "maxOutputTokens": 65535,
            "temperature": 0.9,
            "topP": 1.0,
            "topK": 40,
            "thinkingConfig": {
                "thinkingBudget": 128,
                "includeThoughts": False
            }
        }
    }

    # Пробуем разные ключи до успешного ответа
    available_keys = list(config.GEMINI_KEYS.keys())
    random.shuffle(available_keys)  # Перемешиваем ключи для случайного порядка

    for key_id in available_keys:
        api_key = config.GEMINI_KEYS[key_id]
        try:
            async with session.post(endpoint, json=payload, params={'key': api_key}, headers=headers, timeout=900) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    response_text = data['candidates'][0]['content']['parts'][0]['text']
                    usage_stats = data.get('usageMetadata', {})
                    return response_text, usage_stats, config.OFFICIAL_GEMINI_PRO_MODEL
                else:
                    continue
        except Exception as e:
            continue

    return None, None, config.OFFICIAL_GEMINI_PRO_MODEL


async def user_gemini_pro_async(prompt: str, session: aiohttp.ClientSession, user_id: int, image_base64: str = None) -> tuple[str | None, dict | None, str]:
    """
    Асинхронная функция для работы с Gemini 2.5 Pro используя пользовательский ключ
    Возвращает (response_text, usage_stats, model_used) или (None, None, model_name) при ошибке
    """
    user_key = get_user_custom_key(user_id)
    if not user_key:
        return None, None, config.OFFICIAL_GEMINI_PRO_MODEL

    endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/{config.OFFICIAL_GEMINI_PRO_MODEL}:generateContent"
    headers = {"Content-Type": "application/json"}

    # Подготавливаем части запроса
    parts = [{"text": prompt}]
    if image_base64:
        parts.append({
            "inline_data": {
                "mime_type": "image/jpeg",
                "data": image_base64
            }
        })

    payload = {
        "contents": [{"parts": parts}],
        "generationConfig": {
            "maxOutputTokens": 65535,
            "thinkingConfig": {"thinkingBudget": 24576}
        }
    }

    try:
        async with session.post(endpoint, json=payload, params={'key': user_key}, headers=headers, timeout=900) as resp:
            if resp.status == 200:
                data = await resp.json()
                response_text = data['candidates'][0]['content']['parts'][0]['text']
                usage_stats = data.get('usageMetadata', {})
                return response_text, usage_stats, config.OFFICIAL_GEMINI_PRO_MODEL
            else:
                return None, None, config.OFFICIAL_GEMINI_PRO_MODEL
    except Exception as e:
        return None, None, config.OFFICIAL_GEMINI_PRO_MODEL


async def user_gemini_flash_async(prompt: str, session: aiohttp.ClientSession, user_id: int, image_base64: str = None) -> tuple[str | None, dict | None, str]:
    """
    Асинхронная функция для работы с Gemini 2.5 Flash используя пользовательский ключ
    Возвращает (response_text, usage_stats, model_used) или (None, None, model_name) при ошибке
    """
    user_key = get_user_custom_key(user_id)
    if not user_key:
        return None, None, config.MODEL_NAME

    url = f"https://generativelanguage.googleapis.com/v1beta/models/{config.MODEL_NAME}:generateContent"

    # Подготавливаем части запроса
    parts = [{"text": prompt}]
    if image_base64:
        parts.append({
            "inline_data": {
                "mime_type": "image/jpeg",
                "data": image_base64
            }
        })

    payload = {
        "contents": [{"parts": parts}],
        "generationConfig": {
            "maxOutputTokens": 65535,
            "thinkingConfig": {"thinkingBudget": 24576}
        }
    }

    try:
        async with session.post(url, json=payload, params={'key': user_key}, timeout=900) as resp:
            if resp.status == 200:
                data = await resp.json()
                response_text = data['candidates'][0]['content']['parts'][0]['text']
                usage_stats = data.get('usageMetadata', {})
                return response_text, usage_stats, config.MODEL_NAME
            else:
                return None, None, config.MODEL_NAME
    except Exception as e:
        return None, None, config.MODEL_NAME


async def voidai_async(prompt: str, session: aiohttp.ClientSession, image_base64: str = None) -> tuple[str | None, dict | None, str]:
    """
    Асинхронная функция для работы с Gemini 2.5 Pro через VoidAI API
    Возвращает (response_text, usage_stats, model_used) или (None, None, model_name) при ошибке
    """
    headers = {
        "Authorization": f"Bearer {config.VOIDAI_API_KEY}",
        "Content-Type": "application/json"
    }

    # Подготавливаем сообщения для OpenAI-совместимого API
    messages = [{"role": "user", "content": prompt}]

    # Если есть изображение, добавляем его (для моделей с поддержкой vision)
    if image_base64:
        messages[0]["content"] = [
            {"type": "text", "text": prompt},
            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
        ]

    payload = {
        "model": config.VOIDAI_MODEL_NAME,
        "messages": messages,
        "max_tokens": 65000,
        "temperature": 0.7
    }

    try:
        async with session.post(config.VOIDAI_ENDPOINT, json=payload, headers=headers, timeout=900) as resp:
            if resp.status == 200:
                data = await resp.json()
                response_text = data['choices'][0]['message']['content']
                usage_stats = data.get('usage', {})
                return response_text, usage_stats, config.VOIDAI_MODEL_NAME
            else:
                error_text = await resp.text()
                return None, None, config.VOIDAI_MODEL_NAME
    except Exception as e:
        return None, None, config.VOIDAI_MODEL_NAME


async def gemini_async(prompt: str, session: aiohttp.ClientSession, image_base64: str = None) -> tuple[str | None, dict | None, str]:
    """
    Асинхронная функция для работы с Gemini 2.5 Flash Lite
    Возвращает (response_text, usage_stats, model_used) или (None, None, model_name) при ошибке
    """
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{config.MODEL_NAME}:generateContent"

    # Подготавливаем части запроса
    parts = [{"text": prompt}]
    if image_base64:
        parts.append({
            "inline_data": {
                "mime_type": "image/jpeg",
                "data": image_base64
            }
        })

    payload = {
        "contents": [{"parts": parts}],
        "generationConfig": {
            "maxOutputTokens": 65535,
            "thinkingConfig": {"thinkingBudget": 24576}
        }
    }

    # Пробуем разные ключи до успешного ответа
    available_keys = list(config.GEMINI_KEYS.keys())
    random.shuffle(available_keys)  # Перемешиваем ключи для случайного порядка

    for key_id in available_keys:
        api_key = config.GEMINI_KEYS[key_id]
        try:
            async with session.post(url, json=payload, params={'key': api_key}, timeout=900) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    response_text = data['candidates'][0]['content']['parts'][0]['text']
                    usage_stats = data.get('usageMetadata', {})
                    return response_text, usage_stats, config.MODEL_NAME
                else:
                    continue
        except Exception as e:
            continue

    return None, None, config.MODEL_NAME


async def ai_request_with_fallback(prompt: str, session: aiohttp.ClientSession, user_id: int, image_base64: str = None, status_message = None) -> tuple[str | None, dict | None, str]:
    """
    Последовательность моделей в зависимости от типа пользователя:

    Для пользователей с собственным ключом:
    1. Gemini 2.5 Pro (пользовательский ключ)
    2. Gemini 2.5 Flash (пользовательский ключ, если Pro не работает)

    Для Pro пользователей:
    1. Gemini 2.5 Pro (официальный API)
    2. VoidAI Claude Sonnet 4 (если официальный не работает)
    3. Gemini 2.5 Flash (официальный API, если VoidAI не работает)

    Для бесплатных пользователей:
    1. Gemini 2.5 Flash (официальный API)

    Возвращает (response_text, usage_stats, model_used)
    """

    # Проверяем, есть ли у пользователя собственный ключ
    if has_user_custom_key(user_id):
        # Увеличиваем счетчик использования для пользователей с собственными ключами
        increment_user_daily_custom_key_usage(user_id)
        custom_key_usage = get_user_daily_custom_key_usage(user_id)

        # 1. Пробуем Gemini 2.5 Pro с пользовательским ключом
        response_text, usage_stats, model_used = await user_gemini_pro_async(prompt, session, user_id, image_base64)
        if response_text:
            return response_text, usage_stats, model_used

        # 2. Если Pro не сработал, пробуем Flash с пользовательским ключом
        return await user_gemini_flash_async(prompt, session, user_id, image_base64)

    # Определяем тип пользователя
    is_pro = is_user_pro(user_id)

    if is_pro:
        # Увеличиваем общий счетчик использования плагинов для Pro пользователей
        increment_user_daily_total_usage(user_id)
        total_usage = get_user_daily_total_usage(user_id)

        # 1. Пробуем официальный Gemini 2.5 Pro API
        response_text, usage_stats, model_used = await official_gemini_pro_async(prompt, session, image_base64)
        if response_text:
            return response_text, usage_stats, model_used

        # 2. Пробуем VoidAI API (Claude Sonnet 4)
        response_text, usage_stats, model_used = await voidai_async(prompt, session, image_base64)
        if response_text:
            return response_text, usage_stats, model_used

        # 3. Если и VoidAI не сработал, переключаемся на Flash
        return await gemini_async(prompt, session, image_base64)

    else:
        # Для бесплатных пользователей - только Gemini 2.5 Flash
        increment_user_daily_free_usage(user_id)
        free_usage = get_user_daily_free_usage(user_id)

        return await gemini_async(prompt, session, image_base64)
