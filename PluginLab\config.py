# config.py - Конфигурация и константы для PluginLab
import os
import time
import json

# Определяем абсолютный путь к директории бота (корень проекта)
# __file__ указывает на PluginLab/config.py
# dirname(__file__) = PluginLab/
# dirname(dirname(__file__)) = корень проекта (где находятся data, docs, bot.py)
BOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# --- КОНФИГУРАЦИЯ ---
BOT_TOKEN = "7881085153:AAECWyF_4qSdW-9JAqOhS6hywzQ0pGmFbfE"  # ЗАМЕНИТЕ НА ВАШ ТОКЕН
MODEL_NAME = "gemini-2.5-flash"
OFFICIAL_GEMINI_PRO_MODEL = "gemini-2.5-pro"
VOIDAI_MODEL_NAME = "claude-sonnet-4-20250514"
VOIDAI_API_KEY = "sk-voidai-ALNCakCgzNLyam8qwQ7Gm2mGVZbWTgmN6Liu8H8MisGyQDVMjCUPAUG4IJWSONzGOEc9aHmEewJUZaWVs8zjoummjijmcdFkF4mf-basic"
VOIDAI_ENDPOINT = "https://api.voidai.app/v1/chat/completions"

# Строгая последовательность моделей (без переключения):
# 1. Gemini 2.5 Pro (официальный API)
# 2. VoidAI Claude Sonnet 4 (если официальный не работает)
# 3. Gemini 2.5 Flash (официальный API, если VoidAI не работает)

# --- ПЕРЕМЕННЫЕ ДЛЯ РАБОТЫ БОТА ---
DATA_FOLDER = os.path.join(BOT_DIR, "data")
USER_DATA_FILE = os.path.join(DATA_FOLDER, "users.json")
STATS_DATA_FILE = os.path.join(DATA_FOLDER, "stats.json")
SUPPORT_DATA_FILE = os.path.join(DATA_FOLDER, "support.json")
DAILY_LIMITS_FILE = os.path.join(DATA_FOLDER, "daily_limits.json")
DAILY_TOTAL_LIMITS_FILE = os.path.join(DATA_FOLDER, "daily_total_limits.json")
DAILY_FREE_LIMITS_FILE = os.path.join(DATA_FOLDER, "daily_free_limits.json")
SUBSCRIPTIONS_FILE = os.path.join(DATA_FOLDER, "subscriptions.json")
PENDING_SUBSCRIPTIONS_FILE = os.path.join(DATA_FOLDER, "pending_subscriptions.json")
PENDING_LIFETIME_SUBSCRIPTIONS_FILE = os.path.join(DATA_FOLDER, "pending_lifetime_subscriptions.json")
USER_CUSTOM_KEYS_FILE = os.path.join(DATA_FOLDER, "user_custom_keys.json")
USER_LANGUAGES_FILE = os.path.join(DATA_FOLDER, "user_languages.json")
DAILY_CUSTOM_KEY_LIMITS_FILE = os.path.join(DATA_FOLDER, "daily_custom_key_limits.json")

KEYS_FILE = os.path.join(BOT_DIR, "keys.json")
TRANSLATIONS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "translations.json")

# Глобальные переменные (будут инициализированы в data_manager)
ADMIN_IDS = set()  # Множество ID админов (заменяет ADMIN_ID)
BOT_STATS = []
USER_COOLDOWNS = {}
PRO_REQUEST_COOLDOWNS = {}
PRO_LAST_REQUEST = {}
UNLIMITED_USERS = set()
BLOCKED_USERS = set()
NOTIFICATION_TASKS_PENDING = set()
LAST_GENERATED_CODE = {}
GEMINI_KEYS = {}
SUPPORT_MESSAGES = []
SUPPORT_BANNED_USERS = set()
USER_DAILY_PRO_USAGE = {}
USER_DAILY_TOTAL_USAGE = {}  # Общее дневное использование плагинов
USER_DAILY_FREE_USAGE = {}  # Дневное использование бесплатными пользователями
USER_SUBSCRIPTIONS = {}
PENDING_SUBSCRIPTION_REQUESTS = {}  # Запросы на подписку через донат
PENDING_LIFETIME_SUBSCRIPTION_REQUESTS = {}  # Запросы на пожизненную подписку через донат
USER_CUSTOM_KEYS = {}  # Пользовательские API ключи Gemini
USER_DAILY_CUSTOM_KEY_USAGE = {}  # Дневное использование пользователями с собственными ключами
USER_LANGUAGES = {}  # Языки пользователей (по умолчанию 'ru')
TRANSLATIONS = {}  # Переводы из translations.json



# --- ЛИМИТЫ ---
# Лимиты для бесплатных пользователей
DAILY_FREE_LIMIT = 5  # 5 плагинов в день для бесплатных пользователей (Gemini 2.5 Flash)
FREE_SPAM_PROTECTION_SECONDS = 30  # Защита от спама для бесплатных: не больше 1 плагина в 30 секунд

# Лимиты для пользователей с собственными API ключами
DAILY_CUSTOM_KEY_LIMIT = 30  # 30 плагинов в день для пользователей с собственными ключами

# Лимиты для Pro пользователей
DAILY_PRO_LIMIT = 50  # 50 плагинов в день для Pro пользователей
PRO_SPAM_PROTECTION_SECONDS = 10  # Защита от спама для Pro: не больше 1 плагина в 10 секунд
ALLOWED_FILE_EXTENSIONS = ('.plugin', '.py', '.txt')
MAX_FILE_SIZE_BYTES_FLASH = 50 * 1024   # 50 КБ для Gemini 2.5 Flash (обычные пользователи)
MAX_FILE_SIZE_BYTES_PRO = 300 * 1024    # 300 КБ для Gemini 2.5 Pro (Pro-пользователи)
MAX_CAPTION_LENGTH = 1024

# --- ПОДПИСКИ ---
SUBSCRIPTION_DURATION_DAYS = 30  # Длительность подписки в днях
SUBSCRIPTION_PRICE_DONATION = 99  # Цена подписки через донат в рублях
LIFETIME_SUBSCRIPTION_PRICE_DONATION = 199  # Цена пожизненной подписки через донат в рублях
DONATION_URL = "https://pay.cloudtips.ru/p/469fba34"  # Ссылка на донат

# --- RETRY НАСТРОЙКИ ---
RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 2

# ОПТИМИЗАЦИЯ: Кэш для содержимого документации
DOCS_CONTENT_CACHE = ""

# --- Кнопки и клавиатура ---
BTN_INFO = "ℹ️ Информация"
BTN_SUPPORT = "💭 Поддержка"
BTN_EXIT_SUPPORT = "🚪 Выйти из чата поддержки"

def get_today_date():
    """Возвращает текущую дату в формате YYYY-MM-DD"""
    return time.strftime("%Y-%m-%d", time.localtime())


# --- ФУНКЦИИ ДЛЯ РАБОТЫ С АДМИНАМИ ---

def is_admin(user_id):
    """Проверяет, является ли пользователь админом"""
    return user_id in ADMIN_IDS

def add_admin(user_id):
    """Добавляет пользователя в список админов"""
    ADMIN_IDS.add(user_id)
    return True

def remove_admin(user_id):
    """Удаляет пользователя из списка админов (если это не последний админ)"""
    if len(ADMIN_IDS) <= 1:
        return False, "❌ Нельзя удалить последнего администратора!"

    if user_id not in ADMIN_IDS:
        return False, "❌ Пользователь не является администратором!"

    ADMIN_IDS.discard(user_id)
    return True, "✅ Администратор успешно удален!"

def get_admin_list():
    """Возвращает список ID админов"""
    return list(ADMIN_IDS)

def get_admin_count():
    """Возвращает количество админов"""
    return len(ADMIN_IDS)


# --- ФУНКЦИИ ДЛЯ РАБОТЫ С ЯЗЫКАМИ ---

def get_user_language(user_id):
    """Получает язык пользователя (по умолчанию 'ru')"""
    return USER_LANGUAGES.get(str(user_id), 'ru')

def set_user_language(user_id, language):
    """Устанавливает язык пользователя"""
    USER_LANGUAGES[str(user_id)] = language

def t(user_id, category, key, **kwargs):
    """
    Основная функция перевода - получает переведенный текст для пользователя

    Args:
        user_id: ID пользователя
        category: Категория перевода (buttons, commands, errors, etc.)
        key: Ключ перевода (поддерживает точечную нотацию для вложенных ключей, например: 'start.text')
        **kwargs: Параметры для форматирования строки

    Returns:
        str: Переведенный и отформатированный текст
    """
    lang = get_user_language(user_id)

    # Если переводы не загружены, возвращаем ключ как есть
    if not TRANSLATIONS:
        return key

    try:
        # Получаем данные для нужного языка
        lang_data = TRANSLATIONS.get(lang, TRANSLATIONS.get('ru', {}))

        # Получаем категорию
        category_data = lang_data.get(category, {})

        # Обрабатываем точечную нотацию в ключе
        if '.' in key:
            # Разбиваем ключ по точкам для доступа к вложенным объектам
            key_parts = key.split('.')
            text = category_data

            # Проходим по всем частям ключа
            for part in key_parts:
                if isinstance(text, dict) and part in text:
                    text = text[part]
                else:
                    text = key  # Если ключ не найден, возвращаем исходный ключ
                    break
        else:
            # Обычный доступ к ключу
            text = category_data.get(key, key)

        # Если текст не найден на выбранном языке, пробуем русский как fallback
        if text == key and lang != 'ru':
            ru_data = TRANSLATIONS.get('ru', {})
            ru_category = ru_data.get(category, {})

            if '.' in key:
                # Обрабатываем точечную нотацию для русского fallback
                key_parts = key.split('.')
                text = ru_category

                for part in key_parts:
                    if isinstance(text, dict) and part in text:
                        text = text[part]
                    else:
                        text = key
                        break
            else:
                text = ru_category.get(key, key)

        # Форматируем текст с переданными параметрами
        if kwargs and isinstance(text, str):
            try:
                text = text.format(**kwargs)
            except (KeyError, ValueError, TypeError):
                pass

        return text

    except (KeyError, TypeError, AttributeError):
        return key

def get_text(user_id, category, key, **kwargs):
    """Устаревшая функция - используйте t() вместо неё"""
    return t(user_id, category, key, **kwargs)

def get_button_text(user_id, button_key):
    """Получает текст кнопки для пользователя"""
    return t(user_id, 'buttons', button_key)

def get_command_text(user_id, command, subkey=None, **kwargs):
    """Получает текст команды для пользователя"""
    lang = get_user_language(user_id)

    # Если переводы не загружены, возвращаем команду как есть
    if not TRANSLATIONS:
        return command

    try:
        # Получаем данные для нужного языка
        lang_data = TRANSLATIONS.get(lang, TRANSLATIONS.get('ru', {}))
        commands_data = lang_data.get('commands', {})

        # Получаем данные команды
        command_data = commands_data.get(command, {})

        if subkey:
            # Если указан подключ, получаем его
            text = command_data.get(subkey, subkey)
        else:
            # Если подключ не указан, пробуем получить 'greeting' или первое значение
            if isinstance(command_data, dict):
                if 'greeting' in command_data:
                    text = command_data['greeting']
                else:
                    # Берем первое значение из словаря
                    text = next(iter(command_data.values()), command)
            else:
                text = command_data if command_data else command

        # Если текст не найден на выбранном языке, пробуем русский как fallback
        if text == (subkey or command) and lang != 'ru':
            ru_data = TRANSLATIONS.get('ru', {})
            ru_commands = ru_data.get('commands', {})
            ru_command_data = ru_commands.get(command, {})

            if subkey:
                text = ru_command_data.get(subkey, subkey)
            else:
                if isinstance(ru_command_data, dict):
                    if 'greeting' in ru_command_data:
                        text = ru_command_data['greeting']
                    else:
                        text = next(iter(ru_command_data.values()), command)
                else:
                    text = ru_command_data if ru_command_data else command

        # Форматируем текст с переданными параметрами
        if kwargs and isinstance(text, str):
            try:
                text = text.format(**kwargs)
            except (KeyError, ValueError, TypeError):
                pass

        return text

    except (KeyError, TypeError, AttributeError):
        return subkey or command

def get_ai_system_prompt(user_id, user_prompt):
    """
    Формирует системный промпт для ИИ с учетом языка пользователя

    Args:
        user_id: ID пользователя
        user_prompt: Запрос пользователя

    Returns:
        str: Полный системный промпт с языковыми инструкциями
    """
    lang = get_user_language(user_id)

    # Получаем базовые части промпта из переводов
    system_prompt_start = t(user_id, 'ai_prompts', 'system_prompt_start')
    format_requirements = t(user_id, 'ai_prompts', 'format_requirements')
    code_wrapping_requirement = t(user_id, 'ai_prompts', 'code_wrapping_requirement')
    main_rules = t(user_id, 'ai_prompts', 'main_rules')
    icon_rule = t(user_id, 'ai_prompts', 'icon_rule')
    critical_prohibitions = t(user_id, 'ai_prompts', 'critical_prohibitions')

    # Получаем языковую инструкцию
    if lang == 'en':
        language_instruction = t(user_id, 'ai_prompts', 'language_instruction_en')
    else:
        language_instruction = t(user_id, 'ai_prompts', 'language_instruction_ru')

    # Получаем дополнительные части промпта
    user_request_prefix = t(user_id, 'ai_prompts', 'user_request_prefix')
    final_instruction = t(user_id, 'ai_prompts', 'final_instruction')

    # Формируем полный промпт
    final_prompt = (
        f"{system_prompt_start}\n\n"
        f"{format_requirements}\n\n"
        f"{code_wrapping_requirement}\n\n"
        f"{main_rules}\n"
        f"{icon_rule}\n\n"
        f"{critical_prohibitions}\n\n"
        f"{user_request_prefix}\n{user_prompt}\n\n"
        f"{final_instruction}\n\n"
        f"{language_instruction}"
    )

    return final_prompt

def load_translations():
    """Загружает переводы из файла"""
    global TRANSLATIONS
    try:
        with open(TRANSLATIONS_FILE, 'r', encoding='utf-8') as f:
            TRANSLATIONS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        TRANSLATIONS = {}


