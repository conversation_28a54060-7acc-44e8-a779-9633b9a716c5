# data_manager.py - Управление данными, подписками и поддержкой
import json
import logging
import os
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

from . import config


def load_gemini_keys():
    """Загружает ключи API Gemini из файла keys.json"""
    try:
        with open(config.KEYS_FILE, 'r', encoding='utf-8') as f:
            config.GEMINI_KEYS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logging.error(f"Ошибка загрузки ключей API: {e}")
        config.GEMINI_KEYS = {}


def load_user_data():
    """Загружает данные пользователей"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.USER_DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

            # Миграция со старой системы одного админа
            old_admin_id = data.get("admin_id")
            admin_ids = data.get("admin_ids", [])

            if old_admin_id and not admin_ids:
                # Миграция: переносим старого админа в новую систему
                config.ADMIN_IDS = {old_admin_id}
            else:
                # Загружаем список админов
                config.ADMIN_IDS = set(admin_ids)

            config.UNLIMITED_USERS = set(data.get("pro_users", []))
            config.BLOCKED_USERS = set(data.get("blocked_users", []))
    except (FileNotFoundError, json.JSONDecodeError):
        config.ADMIN_IDS = set()
        config.UNLIMITED_USERS = set()
        config.BLOCKED_USERS = set()
        save_user_data()


def save_user_data():
    """Сохраняет данные пользователей"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.USER_DATA_FILE, 'w', encoding='utf-8') as f:
        data_to_save = {
            "admin_ids": list(config.ADMIN_IDS),  # Новая система админов
            "pro_users": list(config.UNLIMITED_USERS),
            "blocked_users": list(config.BLOCKED_USERS)
        }
        json.dump(data_to_save, f, indent=4, ensure_ascii=False)


def load_stats_data():
    """Загружает статистику бота"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.STATS_DATA_FILE, 'r', encoding='utf-8') as f:
            config.BOT_STATS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.BOT_STATS = []


def save_stats_data():
    """Сохраняет статистику бота"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.STATS_DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.BOT_STATS, f, indent=4, ensure_ascii=False)


def load_support_data():
    """Загружает данные поддержки"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.SUPPORT_DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            config.SUPPORT_MESSAGES = data.get("messages", [])
            config.SUPPORT_BANNED_USERS = set(data.get("banned_users", []))
    except (FileNotFoundError, json.JSONDecodeError):
        config.SUPPORT_MESSAGES = []
        config.SUPPORT_BANNED_USERS = set()
        save_support_data()


def save_support_data():
    """Сохраняет данные поддержки"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.SUPPORT_DATA_FILE, 'w', encoding='utf-8') as f:
        data_to_save = {
            "messages": config.SUPPORT_MESSAGES,
            "banned_users": list(config.SUPPORT_BANNED_USERS)
        }
        json.dump(data_to_save, f, indent=4, ensure_ascii=False)


def load_daily_limits():
    """Загружает дневные лимиты"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.DAILY_LIMITS_FILE, 'r', encoding='utf-8') as f:
            config.USER_DAILY_PRO_USAGE = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_DAILY_PRO_USAGE = {}


def save_daily_limits():
    """Сохраняет дневные лимиты"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.DAILY_LIMITS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_DAILY_PRO_USAGE, f, indent=4, ensure_ascii=False)


def load_daily_total_limits():
    """Загружает общие дневные лимиты"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.DAILY_TOTAL_LIMITS_FILE, 'r', encoding='utf-8') as f:
            config.USER_DAILY_TOTAL_USAGE = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_DAILY_TOTAL_USAGE = {}


def save_daily_total_limits():
    """Сохраняет общие дневные лимиты"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.DAILY_TOTAL_LIMITS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_DAILY_TOTAL_USAGE, f, indent=4, ensure_ascii=False)


def load_daily_free_limits():
    """Загружает дневные лимиты для бесплатных пользователей"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.DAILY_FREE_LIMITS_FILE, 'r', encoding='utf-8') as f:
            config.USER_DAILY_FREE_USAGE = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_DAILY_FREE_USAGE = {}


def save_daily_free_limits():
    """Сохраняет дневные лимиты для бесплатных пользователей"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.DAILY_FREE_LIMITS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_DAILY_FREE_USAGE, f, indent=4, ensure_ascii=False)


def load_daily_custom_key_limits():
    """Загружает дневные лимиты для пользователей с собственными ключами"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.DAILY_CUSTOM_KEY_LIMITS_FILE, 'r', encoding='utf-8') as f:
            config.USER_DAILY_CUSTOM_KEY_USAGE = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_DAILY_CUSTOM_KEY_USAGE = {}


def save_daily_custom_key_limits():
    """Сохраняет дневные лимиты для пользователей с собственными ключами"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.DAILY_CUSTOM_KEY_LIMITS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_DAILY_CUSTOM_KEY_USAGE, f, indent=4, ensure_ascii=False)


def load_subscriptions():
    """Загружает подписки"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.SUBSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
            config.USER_SUBSCRIPTIONS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_SUBSCRIPTIONS = {}

    # Автоматически мигрируем старых пользователей при загрузке
    migrate_old_users_to_lifetime()


def load_pending_subscriptions():
    """Загружает pending запросы на подписку"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.PENDING_SUBSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
            config.PENDING_SUBSCRIPTION_REQUESTS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.PENDING_SUBSCRIPTION_REQUESTS = {}


def save_subscriptions():
    """Сохраняет подписки"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.SUBSCRIPTIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_SUBSCRIPTIONS, f, indent=4, ensure_ascii=False)


def save_pending_subscriptions():
    """Сохраняет pending запросы на подписку"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.PENDING_SUBSCRIPTIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.PENDING_SUBSCRIPTION_REQUESTS, f, indent=4, ensure_ascii=False)


def load_pending_lifetime_subscriptions():
    """Загружает pending запросы на пожизненную подписку"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.PENDING_LIFETIME_SUBSCRIPTIONS_FILE, 'r', encoding='utf-8') as f:
            config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS = {}


def save_pending_lifetime_subscriptions():
    """Сохраняет pending запросы на пожизненную подписку"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.PENDING_LIFETIME_SUBSCRIPTIONS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS, f, indent=4, ensure_ascii=False)


def load_user_custom_keys():
    """Загружает пользовательские API ключи"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.USER_CUSTOM_KEYS_FILE, 'r', encoding='utf-8') as f:
            config.USER_CUSTOM_KEYS = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_CUSTOM_KEYS = {}


def save_user_custom_keys():
    """Сохраняет пользовательские API ключи"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.USER_CUSTOM_KEYS_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_CUSTOM_KEYS, f, indent=4, ensure_ascii=False)


def load_user_languages():
    """Загружает языки пользователей"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    try:
        with open(config.USER_LANGUAGES_FILE, 'r', encoding='utf-8') as f:
            config.USER_LANGUAGES = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        config.USER_LANGUAGES = {}


def save_user_languages():
    """Сохраняет языки пользователей"""
    os.makedirs(config.DATA_FOLDER, exist_ok=True)
    with open(config.USER_LANGUAGES_FILE, 'w', encoding='utf-8') as f:
        json.dump(config.USER_LANGUAGES, f, indent=4, ensure_ascii=False)





def load_all_data():
    """Загружает все данные при старте"""
    config.load_translations()  # Загружаем переводы
    load_gemini_keys()
    load_user_data()
    load_stats_data()
    load_support_data()
    load_daily_limits()
    load_daily_total_limits()
    load_daily_free_limits()
    load_daily_custom_key_limits()
    load_subscriptions()
    load_pending_subscriptions()
    load_pending_lifetime_subscriptions()
    load_user_custom_keys()
    load_user_languages()


# Функции для работы с подписками
def add_subscription(user_id: int, duration_days: int = config.SUBSCRIPTION_DURATION_DAYS, subscription_type: str = "monthly"):
    """Добавляет подписку пользователю"""
    user_id_str = str(user_id)
    current_time = datetime.now()

    # Если у пользователя уже есть активная подписка, продлеваем её
    if user_id_str in config.USER_SUBSCRIPTIONS:
        existing_sub = config.USER_SUBSCRIPTIONS[user_id_str]
        existing_end = datetime.fromisoformat(existing_sub['end_date'])

        # Если подписка ещё активна, продлеваем от даты окончания
        if existing_end > current_time:
            new_end_date = existing_end + timedelta(days=duration_days)
        else:
            # Если подписка истекла, начинаем с текущего момента
            new_end_date = current_time + timedelta(days=duration_days)
    else:
        # Новая подписка
        new_end_date = current_time + timedelta(days=duration_days)

    config.USER_SUBSCRIPTIONS[user_id_str] = {
        'start_date': current_time.isoformat(),
        'end_date': new_end_date.isoformat(),
        'is_active': True,
        'subscription_type': subscription_type
    }

    # Добавляем в UNLIMITED_USERS для совместимости
    config.UNLIMITED_USERS.add(user_id)
    config.BLOCKED_USERS.discard(user_id)

    save_subscriptions()
    save_user_data()

    return new_end_date


def add_lifetime_subscription(user_id: int):
    """Добавляет пожизненную подписку пользователю"""
    user_id_str = str(user_id)
    current_time = datetime.now()

    # Пожизненная подписка - устанавливаем дату окончания на 100 лет вперед
    lifetime_end_date = current_time + timedelta(days=36500)  # ~100 лет

    config.USER_SUBSCRIPTIONS[user_id_str] = {
        'start_date': current_time.isoformat(),
        'end_date': lifetime_end_date.isoformat(),
        'is_active': True,
        'subscription_type': 'lifetime'
    }

    # Добавляем в UNLIMITED_USERS для совместимости
    config.UNLIMITED_USERS.add(user_id)
    config.BLOCKED_USERS.discard(user_id)

    save_subscriptions()
    save_user_data()

    return lifetime_end_date


def is_subscription_active(user_id: int) -> bool:
    """Проверяет, активна ли подписка пользователя"""
    user_id_str = str(user_id)

    if user_id_str not in config.USER_SUBSCRIPTIONS:
        return False

    subscription = config.USER_SUBSCRIPTIONS[user_id_str]
    end_date = datetime.fromisoformat(subscription['end_date'])
    current_time = datetime.now()
    subscription_type = subscription.get('subscription_type', 'monthly')

    # Для пожизненных подписок всегда возвращаем True
    if subscription_type == 'lifetime':
        return subscription.get('is_active', True)

    is_active = end_date > current_time and subscription.get('is_active', True)

    # Если подписка истекла, удаляем из UNLIMITED_USERS (только для помесячных)
    if not is_active and user_id in config.UNLIMITED_USERS and subscription_type == 'monthly':
        config.UNLIMITED_USERS.discard(user_id)
        subscription['is_active'] = False
        save_subscriptions()
        save_user_data()

    return is_active


def get_subscription_info(user_id: int) -> dict:
    """Возвращает информацию о подписке пользователя"""
    user_id_str = str(user_id)

    if user_id_str not in config.USER_SUBSCRIPTIONS:
        return {'has_subscription': False}

    subscription = config.USER_SUBSCRIPTIONS[user_id_str]
    end_date = datetime.fromisoformat(subscription['end_date'])
    current_time = datetime.now()
    subscription_type = subscription.get('subscription_type', 'monthly')

    # Для пожизненных подписок
    if subscription_type == 'lifetime':
        is_active = subscription.get('is_active', True)
        days_left = float('inf') if is_active else 0
    else:
        is_active = end_date > current_time and subscription.get('is_active', True)
        days_left = (end_date - current_time).days if is_active else 0

    return {
        'has_subscription': True,
        'is_active': is_active,
        'end_date': end_date,
        'days_left': days_left,
        'start_date': datetime.fromisoformat(subscription['start_date']),
        'subscription_type': subscription_type
    }


def migrate_old_users_to_lifetime():
    """Мигрирует пользователей из UNLIMITED_USERS в систему пожизненных подписок"""
    migrated_count = 0

    # Создаем копию множества для итерации
    users_to_migrate = list(config.UNLIMITED_USERS)

    for user_id in users_to_migrate:
        user_id_str = str(user_id)

        # Проверяем, есть ли уже подписка у пользователя
        if user_id_str not in config.USER_SUBSCRIPTIONS:
            # Создаем пожизненную подписку для старого пользователя
            add_lifetime_subscription(user_id)
            migrated_count += 1
        else:
            # Если подписка уже есть, проверяем её тип
            subscription = config.USER_SUBSCRIPTIONS[user_id_str]
            if subscription.get('subscription_type') is None:
                # Обновляем тип на пожизненный для старых подписок без типа
                subscription['subscription_type'] = 'lifetime'
                # Устанавливаем дату окончания на 100 лет вперед
                current_time = datetime.now()
                lifetime_end_date = current_time + timedelta(days=36500)
                subscription['end_date'] = lifetime_end_date.isoformat()
                subscription['is_active'] = True
                migrated_count += 1

    if migrated_count > 0:
        save_subscriptions()
        save_user_data()

    return migrated_count


# Функции для работы с pending запросами на подписку
def add_pending_subscription_request(user_id: int, username: str = None, full_name: str = None):
    """Добавляет запрос на подписку через донат"""
    user_id_str = str(user_id)
    current_time = datetime.now()

    config.PENDING_SUBSCRIPTION_REQUESTS[user_id_str] = {
        'user_id': user_id,
        'username': username,
        'full_name': full_name,
        'request_date': current_time.isoformat(),
        'status': 'pending'  # pending, approved, rejected
    }

    save_pending_subscriptions()
    return current_time


def approve_pending_subscription(user_id: int):
    """Одобряет запрос на подписку и выдает подписку"""
    user_id_str = str(user_id)

    if user_id_str not in config.PENDING_SUBSCRIPTION_REQUESTS:
        return False, "Запрос не найден"

    request = config.PENDING_SUBSCRIPTION_REQUESTS[user_id_str]
    if request['status'] != 'pending':
        return False, f"Запрос уже обработан: {request['status']}"

    # Выдаем подписку
    end_date = add_subscription(user_id, config.SUBSCRIPTION_DURATION_DAYS, "monthly")

    # Обновляем статус запроса
    request['status'] = 'approved'
    request['approved_date'] = datetime.now().isoformat()

    save_pending_subscriptions()
    return True, end_date


def reject_pending_subscription(user_id: int, reason: str = None):
    """Отклоняет запрос на подписку"""
    user_id_str = str(user_id)

    if user_id_str not in config.PENDING_SUBSCRIPTION_REQUESTS:
        return False, "Запрос не найден"

    request = config.PENDING_SUBSCRIPTION_REQUESTS[user_id_str]
    if request['status'] != 'pending':
        return False, f"Запрос уже обработан: {request['status']}"

    # Обновляем статус запроса
    request['status'] = 'rejected'
    request['rejected_date'] = datetime.now().isoformat()
    if reason:
        request['rejection_reason'] = reason

    save_pending_subscriptions()
    return True, "Запрос отклонен"


def ban_user(user_id: int, reason: str = "Нарушение правил"):
    """Блокирует пользователя"""
    # ЗАЩИТА: Админы не могут быть забанены
    if config.is_admin(user_id):
        return False

    config.BLOCKED_USERS.add(user_id)

    # Сохраняем информацию о том, был ли пользователь Pro до бана
    was_pro_user = user_id in config.UNLIMITED_USERS

    # Удаляем из про-пользователей если есть
    config.UNLIMITED_USERS.discard(user_id)

    # Удаляем активную подписку если есть
    user_id_str = str(user_id)
    if user_id_str in config.USER_SUBSCRIPTIONS:
        config.USER_SUBSCRIPTIONS[user_id_str]['is_active'] = False
        config.USER_SUBSCRIPTIONS[user_id_str]['ban_reason'] = reason
        config.USER_SUBSCRIPTIONS[user_id_str]['ban_date'] = datetime.now().isoformat()
        # Сохраняем информацию о Pro статусе для восстановления
        config.USER_SUBSCRIPTIONS[user_id_str]['was_pro_before_ban'] = was_pro_user
    else:
        # Если подписки нет, но пользователь был Pro, создаем запись для восстановления
        if was_pro_user:
            config.USER_SUBSCRIPTIONS[user_id_str] = {
                'is_active': False,
                'ban_reason': reason,
                'ban_date': datetime.now().isoformat(),
                'was_pro_before_ban': True,
                'type': 'unlimited',  # Помечаем как безлимитный Pro
                'start_date': datetime.now().isoformat(),
                'end_date': (datetime.now() + timedelta(days=36500)).isoformat()  # 100 лет
            }

    save_user_data()
    save_subscriptions()
    return True


def unban_user(user_id: int):
    """Разблокирует пользователя"""
    if user_id not in config.BLOCKED_USERS:
        return False, "Пользователь не заблокирован"

    config.BLOCKED_USERS.discard(user_id)

    # Восстанавливаем подписку если была заблокирована
    user_id_str = str(user_id)
    if user_id_str in config.USER_SUBSCRIPTIONS:
        subscription = config.USER_SUBSCRIPTIONS[user_id_str]
        if 'ban_reason' in subscription:
            # Восстанавливаем Pro статус если пользователь был Pro до бана
            was_pro_before_ban = subscription.get('was_pro_before_ban', False)
            if was_pro_before_ban:
                config.UNLIMITED_USERS.add(user_id)

            # Проверяем, не истекла ли подписка
            end_date = datetime.fromisoformat(subscription['end_date'])
            if end_date > datetime.now():
                subscription['is_active'] = True

            # Очищаем информацию о бане
            subscription.pop('ban_reason', None)
            subscription.pop('ban_date', None)
            subscription.pop('was_pro_before_ban', None)

    save_user_data()
    save_subscriptions()
    return True, "Пользователь разблокирован"


def remove_subscription(user_id: int):
    """Удаляет подписку у пользователя"""
    user_id_str = str(user_id)

    # Удаляем из старой системы
    config.UNLIMITED_USERS.discard(user_id)

    # Деактивируем в новой системе
    if user_id_str in config.USER_SUBSCRIPTIONS:
        config.USER_SUBSCRIPTIONS[user_id_str]['is_active'] = False
        config.USER_SUBSCRIPTIONS[user_id_str]['removed_date'] = datetime.now().isoformat()
        config.USER_SUBSCRIPTIONS[user_id_str]['removed_by_admin'] = True

    save_user_data()
    save_subscriptions()
    return True


def get_user_detailed_info(user_id: int):
    """Получает детальную информацию о пользователе"""
    user_id_str = str(user_id)

    info = {
        'user_id': user_id,
        'is_blocked': user_id in config.BLOCKED_USERS,
        'is_unlimited': user_id in config.UNLIMITED_USERS,
        'has_custom_key': has_user_custom_key(user_id),
        'subscription': None,
        'daily_usage': config.USER_DAILY_PRO_USAGE.get(user_id_str, {}),
        'pending_subscription': user_id_str in config.PENDING_SUBSCRIPTION_REQUESTS
    }

    # Информация о подписке
    if user_id_str in config.USER_SUBSCRIPTIONS:
        subscription = config.USER_SUBSCRIPTIONS[user_id_str]
        info['subscription'] = {
            'type': subscription.get('subscription_type', 'unknown'),
            'start_date': subscription.get('start_date'),
            'end_date': subscription.get('end_date'),
            'is_active': subscription.get('is_active', False),
            'ban_reason': subscription.get('ban_reason'),
            'ban_date': subscription.get('ban_date'),
            'removed_by_admin': subscription.get('removed_by_admin', False)
        }

        # Проверяем, не истекла ли подписка
        if subscription.get('end_date'):
            end_date = datetime.fromisoformat(subscription['end_date'])
            info['subscription']['is_expired'] = end_date <= datetime.now()

    return info


def get_pending_subscription_requests():
    """Возвращает все pending запросы"""
    pending_requests = []
    for user_id_str, request in config.PENDING_SUBSCRIPTION_REQUESTS.items():
        if request['status'] == 'pending':
            pending_requests.append(request)
    return pending_requests


def has_pending_subscription_request(user_id: int):
    """Проверяет, есть ли у пользователя pending запрос"""
    user_id_str = str(user_id)
    if user_id_str not in config.PENDING_SUBSCRIPTION_REQUESTS:
        return False

    request = config.PENDING_SUBSCRIPTION_REQUESTS[user_id_str]
    return request['status'] == 'pending'


# Функции для работы с pending запросами на пожизненную подписку
def add_pending_lifetime_subscription_request(user_id: int, username: str = None, full_name: str = None):
    """Добавляет запрос на пожизненную подписку через донат"""
    user_id_str = str(user_id)
    current_time = datetime.now()

    config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS[user_id_str] = {
        'user_id': user_id,
        'username': username,
        'full_name': full_name,
        'request_date': current_time.isoformat(),
        'status': 'pending'  # pending, approved, rejected
    }

    save_pending_lifetime_subscriptions()
    return current_time


def approve_pending_lifetime_subscription(user_id: int):
    """Одобряет запрос на пожизненную подписку и выдает пожизненную подписку"""
    user_id_str = str(user_id)

    if user_id_str not in config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS:
        return False, "Запрос не найден"

    request = config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS[user_id_str]
    if request['status'] != 'pending':
        return False, f"Запрос уже обработан: {request['status']}"

    # Выдаем пожизненную подписку
    end_date = add_lifetime_subscription(user_id)

    # Обновляем статус запроса
    request['status'] = 'approved'
    request['approved_date'] = datetime.now().isoformat()

    save_pending_lifetime_subscriptions()
    return True, end_date


def reject_pending_lifetime_subscription(user_id: int, reason: str = None):
    """Отклоняет запрос на пожизненную подписку"""
    user_id_str = str(user_id)

    if user_id_str not in config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS:
        return False, "Запрос не найден"

    request = config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS[user_id_str]
    if request['status'] != 'pending':
        return False, f"Запрос уже обработан: {request['status']}"

    # Обновляем статус запроса
    request['status'] = 'rejected'
    request['rejected_date'] = datetime.now().isoformat()
    if reason:
        request['rejection_reason'] = reason

    save_pending_lifetime_subscriptions()
    return True, "Запрос отклонен"


def get_pending_lifetime_subscription_requests():
    """Возвращает все pending запросы на пожизненную подписку"""
    pending_requests = []
    for user_id_str, request in config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS.items():
        if request['status'] == 'pending':
            pending_requests.append(request)
    return pending_requests


def has_pending_lifetime_subscription_request(user_id: int):
    """Проверяет, есть ли у пользователя pending запрос на пожизненную подписку"""
    user_id_str = str(user_id)
    if user_id_str not in config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS:
        return False

    request = config.PENDING_LIFETIME_SUBSCRIPTION_REQUESTS[user_id_str]
    return request['status'] == 'pending'


# Функции для работы с поддержкой
def create_support_message(user, message_text: str, photo_file_id: str = None, original_message_id: int = None):
    """Создает новое сообщение поддержки"""
    message_id = str(uuid.uuid4())[:8]  # Короткий ID

    support_message = {
        "id": message_id,
        "user_id": user.id,
        "user_full_name": user.full_name,
        "user_username": user.username,
        "message": message_text,
        "photo_file_id": photo_file_id,
        "original_message_id": original_message_id,
        "timestamp": int(time.time()),
        "status": "open",
        "replies": [],
        "chat_active": True
    }

    config.SUPPORT_MESSAGES.append(support_message)
    save_support_data()
    return message_id


def get_support_message(message_id: str):
    """Получает сообщение поддержки по ID"""
    for msg in config.SUPPORT_MESSAGES:
        if msg["id"] == message_id:
            return msg
    return None


def get_active_support_chat(user_id: int):
    """Получает активный диалог поддержки для пользователя"""
    for msg in config.SUPPORT_MESSAGES:
        if msg["user_id"] == user_id and msg.get("chat_active", False) and msg["status"] == "open":
            return msg
    return None


def close_support_message(message_id: str):
    """Закрывает сообщение поддержки"""
    for msg in config.SUPPORT_MESSAGES:
        if msg["id"] == message_id:
            msg["status"] = "closed"
            save_support_data()
            return True
    return False


# Функции для работы с дневными лимитами
def get_user_daily_pro_usage(user_id: int) -> int:
    """Возвращает количество использований Gemini 2.5 Pro сегодня для пользователя"""
    today = config.get_today_date()
    user_data = config.USER_DAILY_PRO_USAGE.get(str(user_id), {})
    return user_data.get(today, 0)


def increment_user_daily_pro_usage(user_id: int):
    """Увеличивает счетчик использования Gemini 2.5 Pro для пользователя на сегодня"""
    today = config.get_today_date()
    user_id_str = str(user_id)

    if user_id_str not in config.USER_DAILY_PRO_USAGE:
        config.USER_DAILY_PRO_USAGE[user_id_str] = {}

    old_count = config.USER_DAILY_PRO_USAGE[user_id_str].get(today, 0)
    config.USER_DAILY_PRO_USAGE[user_id_str][today] = old_count + 1
    new_count = config.USER_DAILY_PRO_USAGE[user_id_str][today]

    save_daily_limits()


def get_user_daily_total_usage(user_id: int) -> int:
    """Возвращает общее количество использований плагинов сегодня для пользователя"""
    today = config.get_today_date()
    user_data = config.USER_DAILY_TOTAL_USAGE.get(str(user_id), {})
    return user_data.get(today, 0)


def increment_user_daily_total_usage(user_id: int):
    """Увеличивает счетчик общего использования плагинов для пользователя на сегодня"""
    today = config.get_today_date()
    user_id_str = str(user_id)

    if user_id_str not in config.USER_DAILY_TOTAL_USAGE:
        config.USER_DAILY_TOTAL_USAGE[user_id_str] = {}

    old_count = config.USER_DAILY_TOTAL_USAGE[user_id_str].get(today, 0)
    config.USER_DAILY_TOTAL_USAGE[user_id_str][today] = old_count + 1
    new_count = config.USER_DAILY_TOTAL_USAGE[user_id_str][today]

    save_daily_total_limits()


def get_user_daily_free_usage(user_id: int) -> int:
    """Возвращает количество использований бесплатными пользователями сегодня"""
    today = config.get_today_date()
    user_data = config.USER_DAILY_FREE_USAGE.get(str(user_id), {})
    return user_data.get(today, 0)


def increment_user_daily_free_usage(user_id: int):
    """Увеличивает счетчик использования для бесплатного пользователя на сегодня"""
    today = config.get_today_date()
    user_id_str = str(user_id)

    if user_id_str not in config.USER_DAILY_FREE_USAGE:
        config.USER_DAILY_FREE_USAGE[user_id_str] = {}

    old_count = config.USER_DAILY_FREE_USAGE[user_id_str].get(today, 0)
    config.USER_DAILY_FREE_USAGE[user_id_str][today] = old_count + 1
    new_count = config.USER_DAILY_FREE_USAGE[user_id_str][today]

    save_daily_free_limits()


def get_user_daily_custom_key_usage(user_id: int) -> int:
    """Возвращает количество использований пользователями с собственными ключами сегодня"""
    today = config.get_today_date()
    user_data = config.USER_DAILY_CUSTOM_KEY_USAGE.get(str(user_id), {})
    return user_data.get(today, 0)


def increment_user_daily_custom_key_usage(user_id: int):
    """Увеличивает счетчик использования для пользователя с собственным ключом на сегодня"""
    today = config.get_today_date()
    user_id_str = str(user_id)

    if user_id_str not in config.USER_DAILY_CUSTOM_KEY_USAGE:
        config.USER_DAILY_CUSTOM_KEY_USAGE[user_id_str] = {}

    old_count = config.USER_DAILY_CUSTOM_KEY_USAGE[user_id_str].get(today, 0)
    config.USER_DAILY_CUSTOM_KEY_USAGE[user_id_str][today] = old_count + 1
    new_count = config.USER_DAILY_CUSTOM_KEY_USAGE[user_id_str][today]

    save_daily_custom_key_limits()


def set_user_custom_key(user_id: int, api_key: str):
    """Устанавливает пользовательский API ключ"""
    user_id_str = str(user_id)
    config.USER_CUSTOM_KEYS[user_id_str] = api_key
    save_user_custom_keys()


def get_user_custom_key(user_id: int) -> str | None:
    """Получает пользовательский API ключ"""
    user_id_str = str(user_id)
    return config.USER_CUSTOM_KEYS.get(user_id_str)


def has_user_custom_key(user_id: int) -> bool:
    """Проверяет, есть ли у пользователя собственный API ключ"""
    return get_user_custom_key(user_id) is not None


def delete_user_custom_key(user_id: int) -> bool:
    """Удаляет пользовательский API ключ. Возвращает True если ключ был удален"""
    user_id_str = str(user_id)
    if user_id_str in config.USER_CUSTOM_KEYS:
        del config.USER_CUSTOM_KEYS[user_id_str]
        save_user_custom_keys()
        return True
    return False


def cleanup_old_daily_limits():
    """Удаляет данные за старые дни, оставляя только сегодняшние"""
    today = config.get_today_date()

    # Очищаем Pro лимиты
    cleaned_pro_data = {}
    for user_id, user_data in config.USER_DAILY_PRO_USAGE.items():
        if today in user_data:
            cleaned_pro_data[user_id] = {today: user_data[today]}

    config.USER_DAILY_PRO_USAGE = cleaned_pro_data
    save_daily_limits()

    # Очищаем общие лимиты
    cleaned_total_data = {}
    for user_id, user_data in config.USER_DAILY_TOTAL_USAGE.items():
        if today in user_data:
            cleaned_total_data[user_id] = {today: user_data[today]}

    config.USER_DAILY_TOTAL_USAGE = cleaned_total_data
    save_daily_total_limits()

    # Очищаем лимиты бесплатных пользователей
    cleaned_free_data = {}
    for user_id, user_data in config.USER_DAILY_FREE_USAGE.items():
        if today in user_data:
            cleaned_free_data[user_id] = {today: user_data[today]}

    config.USER_DAILY_FREE_USAGE = cleaned_free_data
    save_daily_free_limits()

    # Очищаем лимиты пользователей с собственными ключами
    cleaned_custom_key_data = {}
    for user_id, user_data in config.USER_DAILY_CUSTOM_KEY_USAGE.items():
        if today in user_data:
            cleaned_custom_key_data[user_id] = {today: user_data[today]}

    config.USER_DAILY_CUSTOM_KEY_USAGE = cleaned_custom_key_data
    save_daily_custom_key_limits()



def cleanup_expired_subscriptions():
    """Очищает истёкшие подписки (только помесячные)"""
    current_time = datetime.now()
    expired_users = []

    for user_id_str, subscription in config.USER_SUBSCRIPTIONS.items():
        subscription_type = subscription.get('subscription_type', 'monthly')

        # Пропускаем пожизненные подписки
        if subscription_type == 'lifetime':
            continue

        end_date = datetime.fromisoformat(subscription['end_date'])
        if end_date <= current_time:
            expired_users.append(int(user_id_str))
            subscription['is_active'] = False

    # Удаляем истёкших пользователей из UNLIMITED_USERS (только помесячных)
    for user_id in expired_users:
        config.UNLIMITED_USERS.discard(user_id)

    if expired_users:
        save_subscriptions()
        save_user_data()

    return expired_users
