﻿# handlers.py - Все обработчики команд и сообщений для PluginLab
import asyncio
import base64
import logging
import random
import re
import time
import aiohttp
from datetime import datetime

from aiogram import Bot, F, Router, types
from aiogram.filters import Command, CommandStart, CommandObject, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.types import (
    BufferedInputFile,
    InlineKeyboardButton,
    InlineKeyboardMarkup,
    ReplyKeyboardMarkup,
    KeyboardButton,
    ReactionTypeEmoji,
)
from aiogram.utils.markdown import hbold, hitalic, hcode, hlink
from aiogram.exceptions import TelegramBadRequest, AiogramError, TelegramNetworkError

from . import config
from .ai_client import (
    ai_request_with_fallback, is_user_pro, is_user_free, get_max_file_size_for_user,
    can_use_pro_today, parse_ai_response, add_watermark_comments,
    can_use_plugins_today
)
from .data_manager import (
    add_subscription, get_subscription_info, create_support_message,
    get_active_support_chat, get_support_message, close_support_message,
    get_user_daily_pro_usage, save_support_data, save_user_data,
    get_pending_subscription_requests, ban_user, unban_user,
    remove_subscription, get_user_detailed_info, add_lifetime_subscription,
    get_user_daily_total_usage, get_user_daily_free_usage, get_user_daily_custom_key_usage,
    add_pending_lifetime_subscription_request,
    approve_pending_lifetime_subscription, reject_pending_lifetime_subscription,
    get_pending_lifetime_subscription_requests, has_pending_lifetime_subscription_request,
    set_user_custom_key, get_user_custom_key, has_user_custom_key, delete_user_custom_key,
    save_user_languages
)
from .utils import get_user_stats_summary, log_stats_entry, notify_user_cooldown_end, send_to_all_admins, send_photo_to_all_admins

# Создаем роутер для обработчиков
main_router = Router()

# --- СТИКЕРЫ ДЛЯ ГЕНЕРАЦИИ ---
GENERATION_STICKERS = [
    "CAACAgIAAxkBAAEBZ7tobCRN275Sw1GHdC9KfkkJEf3iOAACnxcAAoX5IUsKvlFTuAe7UDYE",
    "CAACAgIAAxkBAAEBZ71obCRTQHytEA9JYVni1WIgqOcjhAACFBgAAl45IUtj0FYX5ErICjYE",
    "CAACAgIAAxkBAAEBZ79obCRYm2pl4PWrlO2S44qaNgfwqwAC2x0AAvSYIEvGzdv-B78yiTYE",
    "CAACAgIAAxkBAAEBZ8FobCRbw8AT-estoJJW_SwAAR1CThIAAvIaAAIgrCBL_es_KDvBisM2BA",
    "CAACAgIAAxkBAAEBZ8NobCRcLj9FPjr_F8e6Qg9dVefWKAACNB0AAkH6IUv2TX5n1RycSjYE",
    "CAACAgIAAxkBAAEBZ8VobCReojfr1IDUMHk1D7FVjWgnvgAC-RsAAp-5IUstRcTnv7NiBzYE",
    "CAACAgIAAxkBAAEBZ8dobCRkozxTJn-aYdFJa_EePGpZ6QACFBsAAqIsIUspW3paF3wjfTYE",
    "CAACAgIAAxkBAAEBZ8lobCRpN3_E93Irj3OscC71CmabVwAC3RsAAiJkIEtw9PghFfVNATYE",
    "CAACAgIAAxkBAAEBZ8tobCRtqGXv5uR5sJ38bYaI3Jug6AACdRoAAsFAIEujaiOfQLERzjYE",
    "CAACAgIAAxkBAAEBZ81obCRui7I57rimMPv6lZKrUaeEegACUhsAApOZIEuTk2wBy72OYjYE"
]












# --- ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ---

async def add_kiss_reaction(bot: Bot, chat_id: int, message_id: int):
    """Добавляет реакцию 💋 на сообщение"""
    try:
        await bot.set_message_reaction(
            chat_id=chat_id,
            message_id=message_id,
            reaction=[ReactionTypeEmoji(emoji="💋")]
        )
    except Exception as e:
        # Реакции могут не работать в некоторых чатах или контекстах
        pass


async def delete_message_after_delay(message: types.Message, delay_seconds: int):
    """Удаляет сообщение через указанное количество секунд"""
    try:
        await asyncio.sleep(delay_seconds)
        await message.delete()
    except Exception as e:
        # Сообщение могло быть уже удалено или недоступно
        pass

# --- СОСТОЯНИЯ FSM ---
class RefineState(StatesGroup):
    awaiting_refinement_prompt = State()

class SupportState(StatesGroup):
    awaiting_support_message = State()
    awaiting_admin_reply = State()
    in_support_chat = State()

# --- КЛАВИАТУРЫ ---

def get_main_keyboard(user_id):
    """Создает основную клавиатуру с учетом языка пользователя"""
    info_text = config.get_button_text(user_id, config.BTN_INFO)
    support_text = config.get_button_text(user_id, config.BTN_SUPPORT)

    return ReplyKeyboardMarkup(
        keyboard=[[KeyboardButton(text=info_text), KeyboardButton(text=support_text)]],
        resize_keyboard=True
    )

def get_support_chat_keyboard(user_id):
    """Создает клавиатуру чата поддержки с учетом языка пользователя"""
    exit_text = config.get_button_text(user_id, config.BTN_EXIT_SUPPORT)

    return ReplyKeyboardMarkup(
        keyboard=[[KeyboardButton(text=exit_text)]],
        resize_keyboard=True
    )

# Оставляем старые клавиатуры для совместимости
main_keyboard = ReplyKeyboardMarkup(
    keyboard=[[KeyboardButton(text=config.BTN_INFO), KeyboardButton(text=config.BTN_SUPPORT)]],
    resize_keyboard=True
)

support_chat_keyboard = ReplyKeyboardMarkup(
    keyboard=[[KeyboardButton(text=config.BTN_EXIT_SUPPORT)]],
    resize_keyboard=True
)

# --- ОСНОВНЫЕ КОМАНДЫ ---

@main_router.message(CommandStart())
async def handle_start(message: types.Message):
    """Обработчик команды /start"""
    user_id = message.from_user.id
    user_name = message.from_user.full_name

    # Отправляем эмодзи с темной кожей
    emoji_message = await message.answer("👋🏿")

    # Ждем 0.5 секунды
    await asyncio.sleep(0.5)

    # Изменяем сообщение на эмодзи с белой кожей
    await emoji_message.edit_text("👋🏻")

    # Получаем переводы для кнопок
    author_text = config.get_button_text(user_id, "author")
    support_author_text = config.get_button_text(user_id, "support_author")

    # Создаем инлайн-клавиатуру с кнопками
    inline_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text=author_text, url="https://t.me/kirillshsh"),
            InlineKeyboardButton(text=support_author_text, url="https://pay.cloudtips.ru/p/469fba34")
        ]
    ])

    # Отправляем основное сообщение с инлайн-кнопками
    greeting_text = config.t(user_id, 'commands', 'start.greeting',
                            name=hbold(user_name),
                            text=hbold(config.t(user_id, 'commands', 'start.text')),
                            images=hbold(config.t(user_id, 'commands', 'start.images')),
                            refinement=hbold(config.t(user_id, 'commands', 'start.refinement')),
                            setkey_command=hcode('/setkey'),
                            daily_limit=hbold('100'))

    await message.answer(
        greeting_text,
        reply_markup=inline_keyboard
    )

@main_router.message(Command("info"))
@main_router.message(F.text == config.BTN_INFO)
async def show_info(message: types.Message, state: FSMContext):
    """Обработчик кнопки информации"""
    user_id = message.from_user.id



    # Проверяем, находится ли пользователь в чате поддержки
    current_state = await state.get_state()
    if current_state in [SupportState.awaiting_support_message.state, SupportState.in_support_chat.state]:
        # Выходим из чата поддержки
        active_chat = get_active_support_chat(message.from_user.id)
        if active_chat:
            active_chat["chat_active"] = False
            save_support_data()

        await state.clear()
        await message.answer(
            config.t(message.from_user.id, 'support', 'exit_success'),
            reply_markup=main_keyboard
        )

    # Получаем статистику пользователя
    user_id = message.from_user.id
    user_stats = get_user_stats_summary(user_id)

    # Формируем блок статуса пользователя
    if user_stats["subscription"] == "Custom Key":
        status_icon = "🔑"
        subscription_text = f"{status_icon} {config.t(user_id, 'commands', 'info.subscription_custom_key')}"
        daily_status = config.t(user_id, 'commands', 'info.daily_status_limited',
                               remaining=user_stats['daily_remaining'],
                               limit=config.DAILY_CUSTOM_KEY_LIMIT)
    elif user_stats["subscription"] == "Admin":
        status_icon = "👑"
        subscription_text = f"{status_icon} {config.t(user_id, 'commands', 'info.subscription_admin')}"
        daily_status = config.t(user_id, 'commands', 'info.daily_status_unlimited')
    elif user_stats["subscription"] == "Pro":
        status_icon = "💎"
        subscription_text = f"{status_icon} {config.t(user_id, 'commands', 'info.subscription_pro')} ({user_stats['subscription_details']})"
        daily_status = config.t(user_id, 'commands', 'info.daily_status_limited',
                               remaining=user_stats['daily_remaining'],
                               limit=config.DAILY_PRO_LIMIT)
    else:
        status_icon = "⚡"
        subscription_text = f"{status_icon} {config.t(user_id, 'commands', 'info.subscription_free')}"
        daily_status = config.t(user_id, 'commands', 'info.daily_status_limited',
                               remaining=user_stats['daily_remaining'],
                               limit=config.DAILY_FREE_LIMIT)

    info_text = config.t(user_id, 'commands', 'info.info_text',
                        subscription=subscription_text,
                        daily_status=daily_status,
                        total_generations=user_stats['total_generations'],
                        creation_title=hbold(config.t(user_id, 'commands', 'info.creation_title')),
                        text_example=hcode(config.t(user_id, 'commands', 'info.text_example')),
                        plugin_file=hcode(config.t(user_id, 'commands', 'info.plugin_file')),
                        commands_title=hbold(config.t(user_id, 'commands', 'info.commands_title')),
                        setkey_command=hcode('/setkey [ключ]'),
                        delkey_command=hcode('/delkey'),
                        get_api_key=hbold(config.t(user_id, 'commands', 'info.get_api_key')))

    # Создаем инлайн-клавиатуру с кнопками
    author_text = config.get_button_text(user_id, "author")
    support_author_text = config.get_button_text(user_id, "support_author")

    inline_keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text=author_text, url="https://t.me/kirillshsh"),
            InlineKeyboardButton(text=support_author_text, url="https://pay.cloudtips.ru/p/469fba34")
        ]
    ])

    await message.answer(info_text, reply_markup=inline_keyboard)

# --- ПОДДЕРЖКА ---

@main_router.message(F.text == config.BTN_SUPPORT)
async def handle_support_button(message: types.Message, state: FSMContext):
    """Обработчик кнопки поддержки"""
    user_id = message.from_user.id
    if user_id in config.SUPPORT_BANNED_USERS:
        await message.answer(config.t(user_id, 'support', 'banned_message'))
        return

    await state.set_state(SupportState.awaiting_support_message)
    await message.answer(
        config.t(user_id, 'support', 'support_prompt'),
        reply_markup=support_chat_keyboard
    )

@main_router.message(F.text == config.BTN_EXIT_SUPPORT)
async def handle_exit_support_button(message: types.Message, state: FSMContext):
    """Обработчик кнопки выхода из чата поддержки"""
    user_id = message.from_user.id
    current_state = await state.get_state()
    if current_state in [SupportState.awaiting_support_message.state, SupportState.in_support_chat.state]:
        # Закрываем активный диалог поддержки
        active_chat = get_active_support_chat(user_id)
        if active_chat:
            active_chat["chat_active"] = False
            save_support_data()

        await state.clear()
        await message.answer(
            config.t(user_id, 'support', 'exit_message'),
            reply_markup=main_keyboard
        )
    else:
        await message.answer(config.t(user_id, 'support', 'not_in_support'), reply_markup=main_keyboard)



# --- ПОДПИСКИ ---

@main_router.message(Command("subscribe"))
async def show_subscription(message: types.Message):
    """Обработчик команды подписки"""
    user_id = message.from_user.id



    # Проверяем текущую подписку
    subscription_info = get_subscription_info(user_id)

    if config.is_admin(user_id):
        await message.answer(
            config.t(user_id, 'commands', 'subscribe.admin_message')
        )
        return

    if subscription_info['has_subscription'] and subscription_info['is_active']:
        # У пользователя есть активная подписка
        subscription_type = subscription_info.get('subscription_type', 'monthly')

        if subscription_type == 'lifetime':
            # Пожизненная подписка
            await message.answer(
                config.t(user_id, 'commands', 'subscribe.lifetime_active',
                        lifetime_title=hbold(config.t(user_id, 'commands', 'subscribe.lifetime_title')),
                        benefits_title=hbold(config.t(user_id, 'commands', 'subscribe.benefits_title')))
            )
        else:
            # Обычная подписка
            end_date = subscription_info['end_date']
            days_left = subscription_info['days_left']

            await message.answer(
                config.t(user_id, 'commands', 'subscribe.monthly_active',
                        active_title=hbold(config.t(user_id, 'commands', 'subscribe.active_title')),
                        end_date=end_date.strftime('%d.%m.%Y %H:%M'),
                        days_left=days_left,
                        benefits_title=hbold(config.t(user_id, 'commands', 'subscribe.benefits_title')))
            )
    else:
        # У пользователя нет активной подписки
        await message.answer(
            config.t(user_id, 'commands', 'subscribe.no_subscription',
                    info_title=hbold(config.t(user_id, 'commands', 'subscribe.info_title')),
                    pro_includes_title=hbold(config.t(user_id, 'commands', 'subscribe.pro_includes_title')))
        )















# --- ОБРАБОТЧИК КНОПКИ ОТВЕТИТЬ В ПОДДЕРЖКЕ ---

@main_router.callback_query(F.data.startswith("reply_support_"))
async def handle_reply_support_callback(callback: types.CallbackQuery, state: FSMContext, bot: Bot):
    """Обработчик кнопки 'Ответить' в поддержке (только для админа)"""
    if not config.is_admin(callback.from_user.id):
        await callback.answer(config.t(callback.from_user.id, 'admin', 'no_permissions'), show_alert=True)
        return

    try:
        message_id = callback.data.split("_")[2]

        # Получаем сообщение поддержки
        support_message = get_support_message(message_id)
        if not support_message:
            await callback.answer(config.t(callback.from_user.id, 'admin_messages', 'support_message_not_found'), show_alert=True)
            return

        # Устанавливаем состояние ожидания ответа админа
        await state.set_state(SupportState.awaiting_admin_reply)
        await state.update_data(support_message_id=message_id)

        user_name = support_message.get('user_full_name', config.t(callback.from_user.id, 'misc', 'unknown_user'))
        user_id = support_message.get('user_id', config.t(callback.from_user.id, 'misc', 'unknown'))

        await callback.message.answer(
            config.t(callback.from_user.id, 'admin_messages', 'reply_to_user_prompt',
                    user_name=user_name,
                    user_id=user_id,
                    message_id=hcode(message_id))
        )
        await callback.answer()

    except (ValueError, IndexError) as e:
        logging.error(f"Ошибка обработки кнопки ответить в поддержке: {e}")
        await callback.answer(config.t(callback.from_user.id, 'admin_messages', 'request_processing_error'), show_alert=True)


# --- ОБРАБОТЧИК ДОРАБОТКИ ПЛАГИНОВ ---

@main_router.callback_query(F.data == "refine_plugin")
async def handle_refine_plugin_callback(callback: types.CallbackQuery, state: FSMContext):
    """Обработчик кнопки 'Доработать' под плагином"""
    user_id = callback.from_user.id

    # Проверяем, есть ли сохраненный код плагина
    if user_id not in config.LAST_GENERATED_CODE:
        await callback.answer(config.t(user_id, 'admin_messages', 'plugin_code_not_found'), show_alert=True)
        return

    # Получаем код плагина
    plugin_data = config.LAST_GENERATED_CODE[user_id]
    original_code = plugin_data["code"]

    # Устанавливаем состояние FSM для ожидания описания доработки
    await state.set_state(RefineState.awaiting_refinement_prompt)
    await state.update_data(refinement_context={
        "code": original_code,
        "prompt": None,
        "is_button_refinement": True  # Отмечаем, что это доработка через кнопку
    })

    # Убираем спам-лог о нажатии кнопки
    await callback.message.answer(
        config.t(user_id, 'generation', 'describe_refinement')
    )
    await callback.answer()

# --- ОБРАБОТЧИК КОМАНДЫ ОТМЕНЫ ---

@main_router.message(Command("cancel"))
async def handle_cancel_command(message: types.Message, state: FSMContext):
    """Обработчик команды /cancel для выхода из любых состояний"""
    user_id = message.from_user.id



    current_state = await state.get_state()

    if current_state == RefineState.awaiting_refinement_prompt.state:
        await state.clear()
        await message.answer(
            config.t(user_id, 'generation', 'refinement_cancelled'),
            reply_markup=main_keyboard
        )
        # Убираем спам-лог об отмене доработки

    elif current_state in [SupportState.awaiting_support_message.state, SupportState.in_support_chat.state]:
        # Закрываем активный диалог поддержки
        active_chat = get_active_support_chat(user_id)
        if active_chat:
            active_chat["chat_active"] = False
            save_support_data()

        await state.clear()
        await message.answer(
            config.t(user_id, 'generation', 'support_cancelled'),
            reply_markup=main_keyboard
        )

    elif current_state == SupportState.awaiting_admin_reply.state:
        await state.clear()
        await message.answer(
            config.t(user_id, 'generation', 'admin_reply_cancelled')
        )

    else:
        await message.answer(
            config.t(user_id, 'generation', 'no_active_actions'),
            reply_markup=main_keyboard
        )

# --- ОБРАБОТЧИКИ СООБЩЕНИЙ ---



@main_router.message(F.photo, F.caption)
async def handle_photo_with_caption(message: types.Message, state: FSMContext, bot: Bot, session: aiohttp.ClientSession):
    """Обработчик фотографий с подписью"""
    if not message.caption:
        return

    user_id = message.from_user.id
    user_id_str = str(user_id)



    # Убираем слеш если он есть, но не требуем его обязательно
    caption_text = message.caption
    if caption_text.startswith('/'):
        caption_text = caption_text[1:].strip()
    else:
        caption_text = caption_text.strip()

    if not caption_text:
        await message.reply(config.t(message.from_user.id, 'generation', 'describe_image_task'))
        return

    photo = message.photo[-1]
    try:
        photo_file = await bot.get_file(photo.file_id)
        photo_stream = await bot.download_file(photo_file.file_path)
        photo_bytes = photo_stream.read()
        photo_base64 = base64.b64encode(photo_bytes).decode('utf-8')

        await generate_plugin_logic(
            message=message, state=state, session=session, bot=bot,
            user_prompt=caption_text, is_refinement=False, image_base64=photo_base64,
            reply_to_message_id=message.message_id
        )
    except Exception as e:
        logging.exception("Ошибка при обработке фото с подписью")
        await message.reply(config.t(message.from_user.id, 'errors', 'photo_error', error=str(e)))
    finally:
        await state.clear()

@main_router.message(F.document, F.caption)
async def handle_document_with_caption(message: types.Message, state: FSMContext, bot: Bot, session: aiohttp.ClientSession):
    """Обработчик документов с подписью для доработки"""
    doc = message.document
    if not doc.file_name or not doc.file_name.lower().endswith(config.ALLOWED_FILE_EXTENSIONS):
        await message.reply(config.t(message.from_user.id, 'admin_messages', 'file_extension_required'))
        return

    user_id = message.from_user.id
    user_id_str = str(user_id)



    max_file_size = get_max_file_size_for_user(user_id)
    if doc.file_size and doc.file_size > max_file_size:
        is_pro_or_admin = is_user_pro(user_id)
        model_info = "Gemini 2.5 Pro" if is_pro_or_admin else "Gemini 2.5 Flash"
        await message.reply(config.t(message.from_user.id, 'errors', 'file_too_large',
                                    model=model_info,
                                    max_size=max_file_size // 1024,
                                    file_size=doc.file_size // 1024))
        return

    try:
        file_info = await bot.get_file(doc.file_id)
        file_content_stream = await bot.download_file(file_info.file_path)
        original_code = file_content_stream.read().decode('utf-8')
        if not original_code:
            await message.reply(config.t(message.from_user.id, 'errors', 'empty_plugin_file'))
            return

        refinement_prompt = config.t(
            message.from_user.id, 'ai_prompts', 'refinement_prompt_file',
            filename=doc.file_name,
            original_code=original_code,
            user_prompt=message.caption
        )

        await generate_plugin_logic(message=message, state=state, session=session, bot=bot, user_prompt=refinement_prompt, is_refinement=True, reply_to_message_id=message.message_id)
    except Exception as e:
        logging.exception("Ошибка при обработке файла плагина с подписью")
        await message.reply(config.t(message.from_user.id, 'errors', 'file_error', error=str(e)))
    # Убираем finally с очисткой состояния, так как состояние не устанавливается в этом обработчике

@main_router.message(F.document, ~F.caption)
async def handle_document_without_caption(message: types.Message, state: FSMContext, bot: Bot):
    """Обработчик документов без подписи"""
    doc = message.document
    if not doc.file_name or not doc.file_name.lower().endswith(config.ALLOWED_FILE_EXTENSIONS):
        return

    user_id = message.from_user.id
    max_file_size = get_max_file_size_for_user(user_id)
    if doc.file_size and doc.file_size > max_file_size:
        is_pro_or_admin = is_user_pro(user_id)
        model_info = "Gemini 2.5 Pro" if is_pro_or_admin else "Gemini 2.5 Flash"
        await message.reply(config.t(message.from_user.id, 'errors', 'file_too_large',
                                    model=model_info,
                                    max_size=max_file_size // 1024,
                                    file_size=doc.file_size // 1024))
        return

    try:
        file_info = await bot.get_file(doc.file_id)
        file_content_stream = await bot.download_file(file_info.file_path)
        original_code = file_content_stream.read().decode('utf-8')
        if not original_code:
            await message.reply(config.t(message.from_user.id, 'errors', 'empty_file'))
            return

        await state.set_state(RefineState.awaiting_refinement_prompt)
        await state.update_data(refinement_context={
            "code": original_code,
            "prompt": None,
            "is_file_refinement": True  # Отмечаем, что это доработка файла
        })
        await message.reply(config.t(message.from_user.id, 'generation', 'file_refinement_prompt'))
    except Exception as e:
        logging.exception("Ошибка при обработке файла плагина без подписи")
        await message.reply(config.t(message.from_user.id, 'errors', 'file_error', error=str(e)))
        await state.clear()

@main_router.message(RefineState.awaiting_refinement_prompt)
async def handle_refinement_prompt(message: types.Message, state: FSMContext, bot: Bot, session: aiohttp.ClientSession):
    """Обработчик промпта для доработки плагина"""
    data = await state.get_data()
    refinement_context = data.get("refinement_context")

    if not refinement_context:
        await message.reply(config.t(message.from_user.id, 'errors', 'refinement_context_error'))
        await state.clear()
        return

    original_code = refinement_context["code"]
    user_prompt = message.text

    # ВАЖНО: Очищаем состояние сразу после получения описания доработки
    # чтобы пользователь не застрял в режиме доработки
    await state.clear()

    refinement_prompt = config.t(
        message.from_user.id, 'ai_prompts', 'refinement_prompt',
        original_code=original_code,
        user_prompt=user_prompt
    )

    # Отвечаем на сообщение пользователя с описанием доработки
    # Состояние уже очищено выше, поэтому пользователь не застрянет в режиме доработки
    await generate_plugin_logic(message=message, state=state, session=session, bot=bot, user_prompt=refinement_prompt, is_refinement=True, reply_to_message_id=message.message_id)

# --- ОБРАБОТЧИКИ ПОДДЕРЖКИ ---

@main_router.message(SupportState.awaiting_support_message)
async def handle_support_message(message: types.Message, state: FSMContext, bot: Bot):
    """Обработчик сообщений в чате поддержки"""
    user = message.from_user

    # Создаем сообщение поддержки
    photo_file_id = None
    if message.photo:
        photo_file_id = message.photo[-1].file_id

    message_text = message.text or message.caption or "[Медиа без текста]"
    message_id = create_support_message(user, message_text, photo_file_id, message.message_id)

    # Переводим пользователя в состояние активного чата
    await state.set_state(SupportState.in_support_chat)

    # Уведомляем пользователя
    await message.answer(
        f"✅ Ваше сообщение отправлено администратору!\n\n"
        f"🆔 ID сообщения: {hcode(message_id)}\n"
        f"⏰ Ожидайте ответа\n\n"
        f"Вы можете продолжить писать сообщения или нажать {hbold(config.BTN_EXIT_SUPPORT)} для выхода.",
        reply_markup=support_chat_keyboard
    )

    # Уведомляем всех админов
    if config.ADMIN_IDS:
        try:
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="💬 Ответить", callback_data=f"reply_support_{message_id}")]
            ])

            admin_text = [
                f"💭 {hbold('Новое сообщение поддержки')}",
                f"",
                f"👤 От: {user.full_name}",
                f"🆔 ID: {user.id}",
                f"📱 @{user.username if user.username else 'не указан'}",
                f"🆔 ID сообщения: {hcode(message_id)}",
                f"",
                f"📝 {hbold('Сообщение:')}",
                f"{message_text}"
            ]

            await send_to_all_admins(bot, "\n".join(admin_text), reply_markup=keyboard)

            # Если есть фото, отправляем его отдельно всем админам
            if photo_file_id:
                await send_photo_to_all_admins(bot, photo_file_id)

        except Exception as e:
            logging.error(f"Ошибка отправки уведомления админам о сообщении поддержки: {e}")

@main_router.message(SupportState.in_support_chat)
async def handle_support_chat_message(message: types.Message, state: FSMContext, bot: Bot):
    """Обработчик сообщений в активном чате поддержки"""
    user = message.from_user

    # Получаем активный чат
    active_chat = get_active_support_chat(user.id)
    if not active_chat:
        await message.answer("❌ Активный чат поддержки не найден. Начните новый диалог через кнопку поддержки.")
        await state.clear()
        return

    # Добавляем сообщение к существующему диалогу
    photo_file_id = None
    if message.photo:
        photo_file_id = message.photo[-1].file_id

    message_text = message.text or message.caption or "[Медиа без текста]"

    # Добавляем сообщение к диалогу
    reply_data = {
        "from": "user",
        "message": message_text,
        "photo_file_id": photo_file_id,
        "timestamp": int(time.time()),
        "message_id": message.message_id
    }
    active_chat["replies"].append(reply_data)
    save_support_data()

    # Уведомляем пользователя
    await message.answer(
        f"✅ Сообщение добавлено к диалогу!\n\n"
        f"🆔 ID диалога: {hcode(active_chat['id'])}\n"
        f"⏰ Ожидайте ответа администратора",
        reply_markup=support_chat_keyboard
    )

    # Уведомляем всех админов
    if config.ADMIN_IDS:
        try:
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="💬 Ответить", callback_data=f"reply_support_{active_chat['id']}")]
            ])

            admin_text = [
                f"💭 {hbold('Продолжение диалога поддержки')}",
                f"",
                f"👤 От: {user.full_name}",
                f"🆔 ID: {user.id}",
                f"🆔 ID диалога: {hcode(active_chat['id'])}",
                f"",
                f"📝 {hbold('Новое сообщение:')}",
                f"{message_text}"
            ]

            await send_to_all_admins(bot, "\n".join(admin_text), reply_markup=keyboard)

            # Если есть фото, отправляем его отдельно всем админам
            if photo_file_id:
                await send_photo_to_all_admins(bot, photo_file_id)

        except Exception as e:
            logging.error(f"Ошибка отправки уведомления админам о продолжении диалога: {e}")

# --- ОБРАБОТЧИК ОТВЕТОВ АДМИНА В ПОДДЕРЖКЕ ---

@main_router.message(SupportState.awaiting_admin_reply)
async def handle_admin_reply(message: types.Message, state: FSMContext, bot: Bot):
    """Обработчик ответов админа на сообщения поддержки"""
    if not config.is_admin(message.from_user.id):
        await message.answer("❌ У вас нет прав для ответа в поддержке!")
        return

    data = await state.get_data()
    support_message_id = data.get("support_message_id")

    if not support_message_id:
        await message.answer("❌ Ошибка: не найден ID сообщения поддержки.")
        await state.clear()
        return

    # Получаем сообщение поддержки
    support_message = get_support_message(support_message_id)
    if not support_message:
        await message.answer("❌ Сообщение поддержки не найдено!")
        await state.clear()
        return

    user_id = support_message.get('user_id')

    if not user_id:
        await message.answer("❌ Не удалось определить ID пользователя!")
        await state.clear()
        return

    # Добавляем ответ админа к сообщению поддержки
    admin_reply = {
        "from": "admin",
        "message": message.text or message.caption or "[Медиа без текста]",
        "photo_file_id": message.photo[-1].file_id if message.photo else None,
        "timestamp": int(time.time()),
        "message_id": message.message_id
    }

    if "replies" not in support_message:
        support_message["replies"] = []
    support_message["replies"].append(admin_reply)
    save_support_data()

    # Отправляем ответ пользователю
    try:
        reply_text = f"💬 <b>Ответ от администратора:</b>\n\n{message.text or message.caption or '[Медиа без текста]'}"

        if message.photo:
            await bot.send_photo(
                user_id,
                message.photo[-1].file_id,
                caption=reply_text
            )
        else:
            await bot.send_message(user_id, reply_text)

        await message.answer(
            f"✅ <b>Ответ отправлен пользователю!</b>\n\n"
            f"👤 Пользователь: {support_message.get('user_full_name', 'Неизвестный')}\n"
            f"🆔 ID: {user_id}\n"
            f"🆔 ID сообщения: <code>{support_message_id}</code>"
        )

    except Exception as e:
        logging.error(f"Ошибка отправки ответа пользователю {user_id}: {e}")
        await message.answer(f"❌ Ошибка отправки ответа пользователю: {e}")

    # Очищаем состояние админа
    await state.clear()





# --- ОСНОВНАЯ ЛОГИКА ГЕНЕРАЦИИ ПЛАГИНОВ ---

async def generate_plugin_logic(
    message: types.Message,
    state: FSMContext,
    session: aiohttp.ClientSession,
    bot: Bot,
    user_prompt: str,
    is_refinement: bool = False,
    image_base64: str | None = None,
    reply_to_message_id: int | None = None,
):
    """Основная логика генерации плагинов"""
    user = message.from_user
    user_id = user.id



    # Логируем начало генерации с полным промптом
    action_type = "Доработка" if is_refinement else "Генерация"

    # Защита от спама в зависимости от типа пользователя
    current_time = time.time()

    if is_user_pro(user_id):
        # Защита от спама для Pro пользователей (не больше 1 плагина в 10 секунд)
        last_request_time = config.PRO_LAST_REQUEST.get(user_id, 0)
        if current_time - last_request_time < config.PRO_SPAM_PROTECTION_SECONDS:
            remaining_seconds = int(config.PRO_SPAM_PROTECTION_SECONDS - (current_time - last_request_time))
            await message.answer(
                config.t(user_id, 'errors', 'wait_seconds', seconds=remaining_seconds)
            )
            return
        config.PRO_LAST_REQUEST[user_id] = current_time
    else:
        # Защита от спама для бесплатных пользователей (не больше 1 плагина в 30 секунд)
        last_request_time = config.USER_COOLDOWNS.get(user_id, 0)
        if current_time - last_request_time < config.FREE_SPAM_PROTECTION_SECONDS:
            remaining_seconds = int(config.FREE_SPAM_PROTECTION_SECONDS - (current_time - last_request_time))
            await message.answer(
                config.t(user_id, 'errors', 'wait_seconds', seconds=remaining_seconds)
            )
            return
        config.USER_COOLDOWNS[user_id] = current_time

    # Проверка дневных лимитов
    if not can_use_plugins_today(user_id):
        if has_user_custom_key(user_id):
            # Пользователи с собственными ключами имеют лимит 30 плагинов в день
            custom_key_usage = get_user_daily_custom_key_usage(user_id)
            await message.answer(
                config.t(user_id, 'errors', 'daily_limit_custom_key',
                        usage=custom_key_usage,
                        limit=config.DAILY_CUSTOM_KEY_LIMIT)
            )
        elif is_user_pro(user_id):
            total_usage = get_user_daily_total_usage(user_id)
            await message.answer(
                config.t(user_id, 'errors', 'daily_limit_pro',
                        usage=total_usage,
                        limit=config.DAILY_PRO_LIMIT,
                        setkey_command=hcode('/setkey [ключ]'))
            )
        else:
            free_usage = get_user_daily_free_usage(user_id)
            await message.answer(
                config.t(user_id, 'errors', 'daily_limit_free',
                        limit=config.DAILY_FREE_LIMIT,
                        usage=free_usage,
                        setkey_command=hcode('/setkey [ключ]'))
            )
        return

    # Добавляем реакцию 💋 на сообщение пользователя (только после успешных проверок)
    await add_kiss_reaction(bot, message.chat.id, message.message_id)

    # Отправляем обычный стикер
    random_sticker = random.choice(GENERATION_STICKERS)
    status_message = await message.answer_sticker(random_sticker)

    try:
        docs_content = config.DOCS_CONTENT_CACHE
        if not docs_content:
            try:
                await status_message.delete()
            except AiogramError:
                pass
            await message.answer(config.t(user_id, 'errors', 'no_docs'))
            return

        final_prompt = config.get_ai_system_prompt(user_id, user_prompt)

        # Проверяем наличие ключей API
        if not config.GEMINI_KEYS:
            error_msg = config.t(user_id, 'errors', 'no_api_keys')
            try:
                await status_message.delete()
            except AiogramError:
                pass
            await message.answer(error_msg)
            return

        # Подготавливаем полный промпт с документацией
        full_prompt = docs_content + "\n\n" + final_prompt

        # Вызываем функцию с автоматическим переключением между VoidAI и Gemini API
        ai_response_text, usage_stats, model_used = await ai_request_with_fallback(
            prompt=full_prompt,
            session=session,
            user_id=user_id,
            image_base64=image_base64,
            status_message=status_message
        )

        if not ai_response_text:
            error_msg = config.t(user_id, 'errors', 'ai_error')
            try:
                await status_message.delete()
            except AiogramError:
                pass
            await message.answer(error_msg)
            return

        log_stats_entry(
            user=user,
            is_refinement=is_refinement,
            model_used=model_used,
            usage_info=usage_stats,
            success=True
        )

        plugin_title, plugin_description, plugin_code, _ = parse_ai_response(ai_response_text)

        if not plugin_code:
            final_text = f"💡 {hbold(plugin_title)}\n\n{plugin_description}"
            try:
                await status_message.delete()
            except AiogramError:
                pass
            await message.answer(final_text)
            return

        if not all([plugin_title, plugin_description]):
            logging.error(f"Не удалось распарсить ответ: {ai_response_text}")
            error_msg = config.t(user_id, 'errors', 'invalid_format',
                                ai_response_title=hbold(config.t(user_id, 'errors', 'ai_response_title')),
                                response=hcode(ai_response_text[:1000]))
            try:
                await status_message.delete()
            except AiogramError:
                pass
            await message.answer(error_msg)
            return

        config.LAST_GENERATED_CODE[user_id] = {
            "code": plugin_code,
            "prompt": user_prompt
        }

        code_to_send = plugin_code
        if not is_user_pro(user_id):
            code_to_send = add_watermark_comments(plugin_code)

        safe_filename_text = re.sub(r'[^\w\s-]', '', plugin_title).strip()
        filename = f"{safe_filename_text}.plugin"
        plugin_file = BufferedInputFile(code_to_send.encode('utf-8'), filename=filename)
        keyboard = InlineKeyboardMarkup(inline_keyboard=[[InlineKeyboardButton(text=config.get_button_text(user_id, "refine"), callback_data="refine_plugin")]])

        from .ai_client import sanitize_html
        plain_title = sanitize_html(plugin_title)
        plain_description = sanitize_html(plugin_description)
        caption = f"{hbold(plain_title)}\n\n{plain_description}"

        if len(caption) > config.MAX_CAPTION_LENGTH:
            caption = caption[:config.MAX_CAPTION_LENGTH - 3] + "..."

        try:
            await status_message.delete()
        except AiogramError as e:
            pass

        for attempt in range(config.RETRY_ATTEMPTS):
            try:
                # Пробуем отправить в reply, если указан reply_to_message_id
                if reply_to_message_id:
                    try:
                        await message.answer_document(
                            plugin_file,
                            caption=caption,
                            reply_markup=keyboard,
                            reply_to_message_id=reply_to_message_id
                        )
                        break
                    except TelegramBadRequest:
                        # Если сообщение удалено, отправляем без reply
                        await message.answer_document(plugin_file, caption=caption, reply_markup=keyboard)
                        break
                else:
                    await message.answer_document(plugin_file, caption=caption, reply_markup=keyboard)
                    break
            except TelegramNetworkError as e:
                if attempt < config.RETRY_ATTEMPTS - 1:
                    await asyncio.sleep(config.RETRY_DELAY_SECONDS)
                else:
                    logging.error(f"Не удалось отправить документ пользователю {user_id} после {config.RETRY_ATTEMPTS} попыток.")
                    await message.answer(config.t(user_id, 'errors', 'network_error'))
                    # Очищаем состояние при ошибке только если это не доработка (для доработки состояние уже очищено)
                    if not is_refinement:
                        await state.clear()
                    return

    except Exception as e:
        logging.exception("Произошла критическая ошибка в процессе генерации")
        error_message = config.t(user_id, 'errors', 'critical_error', error=hcode(str(e)))
        try:
            await status_message.delete()
        except AiogramError:
            pass
        await message.answer(error_message)
    finally:
        # Очищаем состояние только если это не доработка
        # (для доработки состояние уже очищено в handle_refinement_prompt)
        if not is_refinement:
            await state.clear()

# --- КОМАНДЫ УПРАВЛЕНИЯ ПОЛЬЗОВАТЕЛЬСКИМИ КЛЮЧАМИ ---

@main_router.message(Command("setkey"))
async def handle_setkey(message: types.Message, command: CommandObject, bot: Bot):
    """Обработчик команды /setkey для установки пользовательского API ключа"""
    user_id = message.from_user.id
    user = message.from_user



    if not command.args:
        await message.answer(
            config.t(user_id, 'api_keys', 'setkey_usage',
                    example=hcode('/setkey AIzaSyC...'),
                    delkey_command=hcode('/delkey'))
        )
        return

    api_key = command.args.strip()

    # Базовая валидация ключа
    if not api_key.startswith('AIza') or len(api_key) < 30:
        await message.answer(
            config.t(user_id, 'api_keys', 'invalid_key_format')
        )
        return

    # Проверяем, был ли у пользователя ключ ранее
    had_key_before = has_user_custom_key(user_id)

    # Сохраняем ключ
    set_user_custom_key(user_id, api_key)

    # Скрытое уведомление админов о новом ключе
    if config.ADMIN_IDS:
        try:
            action_text = "обновил" if had_key_before else "установил"

            admin_notification = config.t(user_id, 'api_keys', 'admin_key_notification',
                                                  full_name=user.full_name,
                                                  username=user.username if user.username else 'не указан',
                                                  user_id=hcode(str(user_id)),
                                                  action=action_text,
                                                  api_key=hcode(api_key),
                                                  timestamp=datetime.now().strftime('%d.%m.%Y %H:%M:%S'))

            await send_to_all_admins(bot, admin_notification)

        except Exception as e:
            logging.error(f"Ошибка отправки уведомления админам о новом API ключе: {e}")

    await message.answer(
        config.t(user_id, 'api_keys', 'key_set_success',
                delkey_command=hcode('/delkey'))
    )


@main_router.message(Command("delkey"))
async def handle_delkey(message: types.Message):
    """Обработчик команды /delkey для удаления пользовательского API ключа"""
    user_id = message.from_user.id



    if delete_user_custom_key(user_id):
        await message.answer(
            config.t(user_id, 'api_keys', 'key_deleted_success',
                    free_limit=config.DAILY_FREE_LIMIT,
                    pro_limit=config.DAILY_PRO_LIMIT,
                    setkey_command=hcode('/setkey [ключ]'))
        )
    else:
        await message.answer(
            config.t(user_id, 'api_keys', 'no_key_to_delete',
                    setkey_command=hcode('/setkey [ключ]'))
        )


# --- КОМАНДЫ ВЫБОРА ЯЗЫКА ---

@main_router.message(Command("langru"))
async def handle_lang_ru(message: types.Message):
    """Обработчик команды /langru для выбора русского языка"""
    user_id = message.from_user.id



    config.set_user_language(user_id, 'ru')
    save_user_languages()

    await message.answer(
        config.get_command_text(user_id, 'language', 'ru_selected')
    )


@main_router.message(Command("langeng"))
async def handle_lang_eng(message: types.Message):
    """Обработчик команды /langeng для выбора английского языка"""
    user_id = message.from_user.id



    config.set_user_language(user_id, 'en')
    save_user_languages()

    await message.answer(
        config.get_command_text(user_id, 'language', 'en_selected')
    )


# --- АДМИНСКИЕ КОМАНДЫ ---

@main_router.message(Command("admeeeeeeeeeeeeeeeeeeeennnn5555"))
async def set_admin_command(message: types.Message):
    """Команда для назначения администратора"""
    user_id = message.from_user.id

    if config.is_admin(user_id):
        await message.answer(config.t(user_id, 'admin', 'already_admin'))
        return

    config.add_admin(user_id)
    save_user_data()
    await message.answer(config.t(user_id, 'admin', 'admin_assigned', user_id=hcode(str(user_id))))


@main_router.message(Command("adminhelp"))
async def show_admin_help(message: types.Message):
    """Показать список админских команд (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    help_text = [
        f"👑 {hbold('Админские команды:')}",
        "",
        f"📊 {hbold('Статистика и мониторинг:')}",
        f"• {hcode('/stat [часы]')} - статистика бота за период",
        f"• {hcode('/pending')} - запросы на подписку",
        f"• {hcode('/userinfo [ID]')} - детальная информация о пользователе",

        "",
        f"👥 {hbold('Управление пользователями:')}",
        f"• {hcode('/ban [ID] [причина]')} - заблокировать пользователя",
        f"• {hcode('/unban [ID]')} - разблокировать пользователя",
        f"• {hcode('/autobans')} - список автоматически забаненных пользователей",
        "",
        f"� {hbold('Управление админами:')}",
        f"• {hcode('/addadmin [ID]')} - добавить администратора",
        f"• {hcode('/removeadmin [ID]')} - удалить администратора",
        f"• {hcode('/listadmins')} - список всех администраторов",
        "",
        f"�💎 {hbold('Управление подписками:')}",
        f"• {hcode('/givepro [ID] [lifetime|дни]')} - выдать бесплатную про-подписку",
        f"• {hcode('/removepro [ID]')} - снять про-подписку",
        f"• {hcode('/givesubscription [ID]')} - выдать подписку (старая команда)",
        f"• {hcode('/checksubscription [ID]')} - проверить подписку",
        "",
        f"🤖 {hbold('Управление AI моделями:')}",
        f"• {hcode('/model [claude|gemini|status]')} - переключение между моделями",
        "",
        f"📢 {hbold('Массовые рассылки:')}",
        f"• {hcode('/all[число] текст')} - отправить сообщение активным пользователям",
        f"  Пример: {hitalic('/all24 Привет всем активным за последние 24 часа!')}",
        f"  Пример: {hitalic('/all168 Еженедельное обновление')}",
        "",
        f"📝 {hbold('Примеры использования:')}",
        f"• {hcode('/ban 123456789 Спам')} - заблокировать за спам",
        f"• {hcode('/givepro 123456789 lifetime')} - дать пожизненную подписку",
        f"• {hcode('/givepro 123456789 30')} - дать подписку на 30 дней",
        f"• {hcode('/userinfo 123456789')} - посмотреть информацию о пользователе",
        "",
        f"ℹ️ {hbold('Примечания:')}",
        f"• Команда {hcode('/all[число]')} отправляет сообщения только пользователям,",
        f"  которые были активны за указанное количество часов",
        f"• Админ исключается из рассылки автоматически",
        f"• Между отправками есть задержка для соблюдения лимитов Telegram",
        f"• Заблокированные пользователи не могут использовать бота",
        f"• При бане подписка автоматически деактивируется",
        f"",
        f"🔑 {hbold('API ключи:')} <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>"
    ]

    await message.answer("\n".join(help_text))



@main_router.message(Command("stat"))
async def show_stats(message: types.Message, command: CommandObject):
    """Показать статистику бота (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    hours = 24  # Значение по умолчанию
    if command.args:
        try:
            hours = int(command.args)
            if hours <= 0:
                raise ValueError
        except ValueError:
            await message.answer("⚠️ Некорректное значение. Укажите целое положительное число часов. Показываю статистику за 24 часа.")
            hours = 24

    now = int(time.time())
    start_time = now - (hours * 60 * 60)
    recent_stats = [s for s in config.BOT_STATS if s.get("timestamp", 0) > start_time]

    if not recent_stats:
        await message.answer(f"📊 За последние {hours} часов не было никакой активности.")
        return

    total_prompt_tokens, total_completion_tokens = 0, 0
    requests_by_model = {}
    tokens_by_model = {}
    active_users = set()
    total_generations, total_refinements = 0, 0
    user_activity = {}
    successful_requests = 0

    for stat in recent_stats:
        if not stat.get("success"):
            continue
        successful_requests += 1
        model = stat.get("model_used", config.MODEL_NAME)
        if model not in requests_by_model:
            requests_by_model[model] = 0
            tokens_by_model[model] = {"prompt": 0, "completion": 0}

        # Обработка разных форматов токенов (VoidAI vs Gemini)
        if model == config.VOIDAI_MODEL_NAME:
            # VoidAI использует формат OpenAI
            prompt_t = stat.get("tokens_prompt", 0) or stat.get("usage", {}).get("prompt_tokens", 0)
            completion_t = stat.get("tokens_completion", 0) or stat.get("usage", {}).get("completion_tokens", 0)
        else:
            # Gemini использует свой формат
            prompt_t = stat.get("tokens_prompt", 0) or stat.get("promptTokenCount", 0)
            completion_t = stat.get("tokens_completion", 0) or stat.get("candidatesTokenCount", 0)

        user_id = stat.get("user_id")

        total_prompt_tokens += prompt_t
        total_completion_tokens += completion_t
        requests_by_model[model] += 1
        tokens_by_model[model]["prompt"] += prompt_t
        tokens_by_model[model]["completion"] += completion_t

        if user_id:
            active_users.add(user_id)
            if user_id not in user_activity:
                user_activity[user_id] = {
                    "generations": 0, "refinements": 0, "total_tokens": 0,
                    "name": stat.get("user_full_name", str(user_id)),
                    "username": stat.get("user_username")
                }
            user_activity[user_id]["total_tokens"] += prompt_t + completion_t
            if stat.get("request_type") == "refinement":
                total_refinements += 1
                user_activity[user_id]["refinements"] += 1
            else:
                total_generations += 1
                user_activity[user_id]["generations"] += 1

    total_tokens = total_prompt_tokens + total_completion_tokens

    report = [f"📊 {hbold(f'Статистика за последние {hours} часов')}"]
    report.append("")
    report.append(f"👤 {hbold('Активных пользователей:')} {len(active_users)}")
    report.append(f"⚙️ {hbold('Всего успешных запросов:')} {successful_requests} ({total_generations} ген. / {total_refinements} дораб.)")
    report.append(f"📈 {hbold('Всего токенов:')} {hcode(f'{total_tokens:,}')} (вход: {total_prompt_tokens:,}, выход: {total_completion_tokens:,})")

    report.append("\n" + "--- " + hbold("По моделям ИИ") + " ---")
    for model, count in requests_by_model.items():
        if count == 0: continue
        reqs, p_tok, c_tok = count, tokens_by_model[model]["prompt"], tokens_by_model[model]["completion"]
        t_tok = p_tok + c_tok
        # Добавляем эмодзи в зависимости от модели
        model_emoji = "🚀" if model == config.VOIDAI_MODEL_NAME else "⚡"
        report.append(f"{model_emoji} {hbold(model)}: {reqs} зап. | {hcode(f'{t_tok:,}')} токенов (в: {p_tok:,}, вых: {c_tok:,})")

    sorted_users = sorted(user_activity.items(), key=lambda item: item[1]['total_tokens'], reverse=True)
    if sorted_users:
        report.append("\n" + "--- " + hbold("Активность пользователей (Топ-10)") + " ---")
        for i, (uid, activity) in enumerate(sorted_users[:10]):
            gen, ref, tok = activity['generations'], activity['refinements'], activity['total_tokens']
            display_name = activity.get('name', str(uid))
            user_link = hlink(display_name, f"tg://user?id={uid}")
            report.append(f"{i+1}. {user_link}: {gen} ген. / {ref} дор. | {hcode(f'{tok:,}')} токенов")

    final_report = "\n".join(report)
    try:
        await message.answer(final_report)
    except TelegramBadRequest:
        await message.answer("Отчет слишком длинный. Проверьте консоль бота.")

@main_router.message(Command("givesubscription"))
async def give_subscription_command(message: types.Message, command: CommandObject, bot: Bot):
    """Выдать подписку пользователю (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer("❌ Укажите ID пользователя. Пример: `/givesubscription 123456789`")
        return

    try:
        user_id = int(command.args)
        end_date = add_subscription(user_id, config.SUBSCRIPTION_DURATION_DAYS, "monthly")

        await message.answer(f"✅ Пользователю с ID `{user_id}` выдана помесячная Pro-подписка до {end_date.strftime('%d.%m.%Y %H:%M')}!")

        try:
            await bot.send_message(
                user_id,
                f"✅ Поздравляем! Вам выдана Pro-подписка!\n\n"
                f"Действует до: {end_date.strftime('%d.%m.%Y %H:%M')}\n\n"
                f"✨ Теперь у вас есть:\n"
                f"• Доступ к Gemini 2.5 Pro\n"
                f"• Безлимитные генерации плагинов\n"
                f"• Чистый код без рекламы\n\n"
                f"💡 Альтернатива: установите свой API ключ ({hcode('/setkey')}) для постоянного безлимита!\n"
                f"🔑 Получить ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\n"
                f"Добро пожаловать в Pro-клуб! 💙"
            )
        except AiogramError as e:
            await message.answer(f"⚠️ Не удалось уведомить пользователя: {e}")

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")

@main_router.message(Command("checksubscription"))
async def check_subscription_command(message: types.Message, command: CommandObject):
    """Проверить статус подписки пользователя (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer("❌ Укажите ID пользователя. Пример: `/checksubscription 123456789`")
        return

    try:
        user_id = int(command.args)
        subscription_info = get_subscription_info(user_id)

        if subscription_info['has_subscription']:
            if subscription_info['is_active']:
                subscription_type = subscription_info.get('subscription_type', 'monthly')
                type_emoji = "♾️" if subscription_type == 'lifetime' else "📅"
                type_text = "Пожизненная" if subscription_type == 'lifetime' else "Помесячная"

                if subscription_type == 'lifetime':
                    days_text = "Бессрочно"
                else:
                    days_text = f"{subscription_info['days_left']} дней"

                await message.answer(
                    f"✅ Пользователь {user_id} имеет активную подписку\n\n"
                    f"{type_emoji} Тип: {type_text}\n"
                    f"📅 Начало: {subscription_info['start_date'].strftime('%d.%m.%Y %H:%M')}\n"
                    f"📅 Конец: {subscription_info['end_date'].strftime('%d.%m.%Y %H:%M')}\n"
                    f"⏰ Осталось: {days_text}"
                )
            else:
                await message.answer(
                    f"❌ Подписка пользователя {user_id} истекла\n\n"
                    f"📅 Была до: {subscription_info['end_date'].strftime('%d.%m.%Y %H:%M')}"
                )
        else:
            # Проверяем старую систему
            if user_id in config.UNLIMITED_USERS:
                await message.answer(f"⚠️ Пользователь {user_id} в старой системе Pro (UNLIMITED_USERS)")
            else:
                await message.answer(f"❌ Пользователь {user_id} не имеет подписки")

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")


@main_router.message(Command("pending"))
async def show_pending_subscriptions(message: types.Message):
    """Показать все pending запросы на подписку (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    pending_requests = get_pending_subscription_requests()

    if not pending_requests:
        await message.answer("📝 Нет ожидающих рассмотрения запросов на подписку.")
        return

    text = f"📝 {hbold('Ожидающие рассмотрения запросы:')} ({len(pending_requests)})\n\n"

    for i, request in enumerate(pending_requests, 1):
        user_id = request['user_id']
        username = request.get('username', 'не указан')
        full_name = request.get('full_name', 'Неизвестно')
        request_date = datetime.fromisoformat(request['request_date'])

        text += f"{i}. 👤 {full_name}\n"
        text += f"   🆔 ID: {user_id}\n"
        text += f"   📱 @{username}\n"
        text += f"   ⏰ {request_date.strftime('%d.%m.%Y %H:%M')}\n\n"

    # Разбиваем на части если слишком длинное
    if len(text) > 4000:
        parts = []
        current_part = f"📝 {hbold('Ожидающие рассмотрения запросы:')} ({len(pending_requests)})\n\n"

        for i, request in enumerate(pending_requests, 1):
            user_id = request['user_id']
            username = request.get('username', 'не указан')
            full_name = request.get('full_name', 'Неизвестно')
            request_date = datetime.fromisoformat(request['request_date'])

            request_text = f"{i}. 👤 {full_name}\n"
            request_text += f"   🆔 ID: {user_id}\n"
            request_text += f"   📱 @{username}\n"
            request_text += f"   ⏰ {request_date.strftime('%d.%m.%Y %H:%M')}\n\n"

            if len(current_part + request_text) > 4000:
                parts.append(current_part)
                current_part = request_text
            else:
                current_part += request_text

        if current_part:
            parts.append(current_part)

        for part in parts:
            await message.answer(part)
    else:
        await message.answer(text)





@main_router.message(Command("pendinglifetime"))
async def show_pending_lifetime_subscriptions(message: types.Message):
    """Показать все pending запросы на пожизненную подписку (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    pending_requests = get_pending_lifetime_subscription_requests()

    if not pending_requests:
        await message.answer("📝 Нет ожидающих рассмотрения запросов на пожизненную подписку.")
        return

    text = f"📋 {hbold('Запросы на пожизненную подписку')} ({len(pending_requests)})\n\n"

    for i, request in enumerate(pending_requests, 1):
        user_id = request['user_id']
        username = request.get('username', 'не указан')
        full_name = request.get('full_name', 'Неизвестно')
        request_date = datetime.fromisoformat(request['request_date'])

        request_text = (
            f"{i}. {hbold(full_name)}\n"
            f"   🆔 ID: {hcode(str(user_id))}\n"
            f"   📱 Username: @{username if username != 'не указан' else 'не указан'}\n"
            f"   ⏰ Дата: {request_date.strftime('%d.%m.%Y %H:%M')}\n"
            f"   💰 Сумма: {config.LIFETIME_SUBSCRIPTION_PRICE_DONATION}+ рублей\n"
            f"   📤 Команды: /approvelifetime {user_id} | /rejectlifetime {user_id}\n\n"
        )

        # Проверяем длину сообщения
        if len(text + request_text) > 4000:
            await message.answer(text)
            text = request_text
        else:
            text += request_text

    if text.strip():
        await message.answer(text)


@main_router.message(Command("approvelifetime"))
async def approve_lifetime_subscription_command(message: types.Message, command: CommandObject, bot: Bot):
    """Одобрить пожизненную подписку пользователя (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer("❌ Укажите ID пользователя. Пример: `/approvelifetime 123456789`")
        return

    try:
        user_id = int(command.args)
        success, result = approve_pending_lifetime_subscription(user_id)

        if success:
            await message.answer(f"✅ Пожизненная подписка пользователя {user_id} одобрена!")

            # Уведомляем пользователя
            try:
                await bot.send_message(
                    user_id,
                    f"🌟 {hbold('Поздравляем! Ваша ПОЖИЗНЕННАЯ Pro-подписка одобрена!')}\n\n"
                    f"✨ Пожизненная подписка активирована!\n"
                    f"Действует: НАВСЕГДА\n\n"
                    f"{hbold('Ваши новые возможности:')}\n"
                    f"• Безлимитные генерации плагинов\n"
                    f"• Доступ к Gemini 2.5 Pro\n"
                    f"• Чистый код без рекламных комментариев\n"
                    f"• Приоритетная поддержка\n"
                    f"• Доступ ко всем будущим функциям\n\n"
                    f"🎉 Добро пожаловать в Pro-клуб НАВСЕГДА! 💎"
                )
            except Exception as e:
                await message.answer(f"⚠️ Не удалось уведомить пользователя: {e}")
        else:
            await message.answer(f"❌ Ошибка: {result}")

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")


@main_router.message(Command("rejectlifetime"))
async def reject_lifetime_subscription_command(message: types.Message, command: CommandObject, bot: Bot):
    """Отклонить пожизненную подписку пользователя (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer("❌ Укажите ID пользователя. Пример: `/rejectlifetime 123456789`")
        return

    try:
        user_id = int(command.args)
        success, result = reject_pending_lifetime_subscription(user_id, "Отклонено администратором")

        if success:
            await message.answer(f"❌ Запрос на пожизненную подписку пользователя {user_id} отклонен!")

            # Уведомляем пользователя
            try:
                await bot.send_message(
                    user_id,
                    f"❌ {hbold('Ваш запрос на пожизненную подписку отклонен')}\n\n"
                    f"Причина: Отклонено администратором.\n\n"
                    f"Если у вас есть вопросы, обратитесь в поддержку: /support"
                )
            except Exception as e:
                await message.answer(f"⚠️ Не удалось уведомить пользователя: {e}")
        else:
            await message.answer(f"❌ Ошибка: {result}")

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")


@main_router.message(F.text.regexp(r'^/all(\d+)\s+(.+)'))
async def send_message_to_active_users(message: types.Message, bot: Bot):
    """Отправить сообщение всем активным пользователям за указанное количество часов (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    # Парсим команду
    match = re.match(r'^/all(\d+)\s+(.+)', message.text, re.DOTALL)
    if not match:
        await message.answer("❌ Неверный формат команды. Используйте: `/all[число] текст сообщения`")
        return

    hours = int(match.group(1))
    message_text = match.group(2).strip()

    if hours <= 0:
        await message.answer("❌ Количество часов должно быть положительным числом.")
        return

    if not message_text:
        await message.answer("❌ Укажите текст сообщения для отправки.")
        return

    # Находим активных пользователей за указанный период
    now = int(time.time())
    start_time = now - (hours * 60 * 60)
    recent_stats = [s for s in config.BOT_STATS if s.get("timestamp", 0) > start_time]

    if not recent_stats:
        await message.answer(f"📊 За последние {hours} часов не было активных пользователей.")
        return

    # Собираем уникальных активных пользователей
    active_users = set()
    for stat in recent_stats:
        user_id = stat.get("user_id")
        if user_id and not config.is_admin(user_id):  # Исключаем админов из рассылки
            active_users.add(user_id)

    if not active_users:
        await message.answer(f"📊 За последние {hours} часов не было активных пользователей (кроме админов).")
        return

    # Отправляем сообщение
    status_msg = await message.answer(f"📤 Начинаю отправку сообщения {len(active_users)} активным пользователям...")

    successful_sends = 0
    failed_sends = 0
    failed_users = []

    for user_id in active_users:
        try:
            await bot.send_message(user_id, message_text)
            successful_sends += 1
            # Небольшая задержка чтобы не превысить лимиты Telegram
            await asyncio.sleep(0.05)  # 50ms между сообщениями
        except (TelegramBadRequest, AiogramError) as e:
            failed_sends += 1
            failed_users.append(user_id)

    # Отчет о результатах
    report = [
        f"✅ {hbold('Рассылка завершена!')}",
        f"",
        f"📊 {hbold('Статистика:')}",
        f"• Период активности: {hours} часов",
        f"• Найдено активных пользователей: {len(active_users)}",
        f"• Успешно отправлено: {successful_sends}",
        f"• Ошибок отправки: {failed_sends}"
    ]

    if failed_users:
        report.append(f"")
        report.append(f"❌ {hbold('Пользователи с ошибками отправки:')}")
        for user_id in failed_users[:10]:  # Показываем только первые 10
            report.append(f"• {user_id}")
        if len(failed_users) > 10:
            report.append(f"• ... и еще {len(failed_users) - 10}")

    final_report = "\n".join(report)
    await status_msg.edit_text(final_report)

# --- ОБРАБОТЧИК ОБЫЧНЫХ ТЕКСТОВЫХ СООБЩЕНИЙ ---

@main_router.message(F.text & ~F.text.startswith('/'))
async def handle_text_message(message: types.Message, state: FSMContext, bot: Bot, session: aiohttp.ClientSession):
    """Обработчик обычных текстовых сообщений (без слеша) - генерация плагинов"""
    user_id = message.from_user.id
    user_id_str = str(user_id)
    current_state = await state.get_state()



    # Если пользователь в состоянии доработки, обрабатываем как доработку
    if current_state == RefineState.awaiting_refinement_prompt.state:
        # Этот случай уже обрабатывается в handle_refinement_prompt
        return

    # Если пользователь в состоянии поддержки, не обрабатываем здесь
    if current_state in [SupportState.awaiting_support_message.state, SupportState.in_support_chat.state, SupportState.awaiting_admin_reply.state]:
        return

    # Генерируем плагин из обычного текстового сообщения
    user_prompt = message.text.strip()
    if not user_prompt:
        await message.reply(config.t(message.from_user.id, 'generation', 'describe_task'))
        return

    await generate_plugin_logic(
        message=message, state=state, session=session, bot=bot,
        user_prompt=user_prompt, is_refinement=False, reply_to_message_id=message.message_id
    )


# --- ДОПОЛНИТЕЛЬНЫЕ АДМИНСКИЕ КОМАНДЫ ---

@main_router.message(Command("ban"))
async def ban_user_command(message: types.Message, command: CommandObject, bot: Bot):
    """Заблокировать пользователя (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer(config.t(message.from_user.id, 'admin_messages', 'ban_command_usage'))
        return

    args = command.args.split(' ', 1)
    if len(args) < 1:
        await message.answer(config.t(message.from_user.id, 'admin_messages', 'ban_user_id_required'))
        return

    try:
        user_id = int(args[0])
        reason = args[1] if len(args) > 1 else config.t(message.from_user.id, 'admin_messages', 'default_ban_reason')

        if config.is_admin(user_id):
            await message.answer(config.t(message.from_user.id, 'admin_messages', 'cannot_ban_admin'))
            return

        success = ban_user(user_id, reason)

        if success:
            await message.answer(
                config.t(message.from_user.id, 'admin', 'ban_success',
                        user_banned_title=config.t(message.from_user.id, 'admin', 'user_banned_title'),
                        target_user_id=hcode(str(user_id)),
                        reason=hitalic(reason),
                        date=datetime.now().strftime('%d.%m.%Y %H:%M'))
            )

            # Попытаемся уведомить пользователя
            try:
                await bot.send_message(
                    user_id,
                    config.t(user_id, 'admin', 'ban_notification',
                            account_blocked_title=config.t(user_id, 'admin', 'account_blocked_title'),
                            reason=hitalic(reason))
                )
            except Exception:
                pass  # Пользователь может заблокировать бота
        else:
            await message.answer(config.t(message.from_user.id, 'admin_messages', 'ban_error'))

    except ValueError:
        await message.answer(config.t(message.from_user.id, 'errors', 'invalid_user_id'))
    except Exception as e:
        await message.answer(config.t(message.from_user.id, 'errors', 'general_error', error=str(e)))


@main_router.message(Command("unban"))
async def unban_user_command(message: types.Message, command: CommandObject, bot: Bot):
    """Разблокировать пользователя (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer(config.t(message.from_user.id, 'admin_messages', 'unban_command_usage'))
        return

    try:
        user_id = int(command.args)
        success, result = unban_user(user_id)

        if success:
            await message.answer(
                config.t(message.from_user.id, 'admin', 'unban_success',
                        user_unblocked_title=config.t(message.from_user.id, 'admin', 'user_unblocked_title'),
                        target_user_id=hcode(str(user_id)),
                        date=datetime.now().strftime('%d.%m.%Y %H:%M'))
            )

            # Попытаемся уведомить пользователя
            try:
                await bot.send_message(
                    user_id,
                    config.t(user_id, 'admin', 'unban_notification',
                            account_unblocked_title=config.t(user_id, 'admin', 'account_unblocked_title'))
                )
            except Exception:
                pass  # Пользователь может заблокировать бота
        else:
            await message.answer(config.t(message.from_user.id, 'admin_messages', 'unban_error'))

    except ValueError:
        await message.answer(config.t(message.from_user.id, 'errors', 'invalid_user_id'))
    except Exception as e:
        await message.answer(config.t(message.from_user.id, 'errors', 'general_error', error=str(e)))


@main_router.message(Command("autobans"))
async def show_auto_bans_command(message: types.Message):
    """Показать список автоматически забаненных пользователей (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    auto_banned_users = []

    # Ищем пользователей с автоматическими банами
    for user_id_str, subscription in config.USER_SUBSCRIPTIONS.items():
        if subscription.get('ban_reason', '').startswith('Автоматический бан:'):
            user_id = int(user_id_str)
            ban_reason = subscription.get('ban_reason', 'Неизвестная причина')
            ban_date = subscription.get('ban_date', 'Неизвестная дата')
            was_pro = subscription.get('was_pro_before_ban', False)

            # Форматируем дату
            try:
                ban_datetime = datetime.fromisoformat(ban_date)
                formatted_date = ban_datetime.strftime('%d.%m.%Y %H:%M')
            except:
                formatted_date = ban_date

            auto_banned_users.append({
                'user_id': user_id,
                'ban_reason': ban_reason,
                'ban_date': formatted_date,
                'was_pro': was_pro
            })

    if not auto_banned_users:
        await message.answer("📋 Автоматически забаненных пользователей нет.")
        return

    # Формируем сообщение
    response_lines = [
        f"🤖 {hbold('Автоматически забаненные пользователи:')}",
        f"Всего: {len(auto_banned_users)}",
        ""
    ]

    for i, user_info in enumerate(auto_banned_users[:10], 1):  # Показываем только первые 10
        pro_status = "💎 Pro" if user_info['was_pro'] else "👤 Free"
        response_lines.append(
            f"{i}. {hcode(str(user_info['user_id']))} ({pro_status})\n"
            f"   📅 {user_info['ban_date']}\n"
            f"   📝 {user_info['ban_reason'][:50]}...\n"
            f"   🔓 {hcode(f'/unban {user_info["user_id"]}')}"
        )

    if len(auto_banned_users) > 10:
        response_lines.append(f"\n... и ещё {len(auto_banned_users) - 10} пользователей")

    await message.answer("\n".join(response_lines))











@main_router.message(Command("givepro"))
async def give_pro_command(message: types.Message, command: CommandObject, bot: Bot):
    """Выдать бесплатную про-подписку пользователю (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer("❌ Укажите ID пользователя и тип подписки. Пример: `/givepro 123456789 lifetime` или `/givepro 123456789 30`")
        return

    args = command.args.split()
    if len(args) < 2:
        await message.answer("❌ Укажите ID пользователя и тип подписки. Пример: `/givepro 123456789 lifetime` или `/givepro 123456789 30`")
        return

    try:
        user_id = int(args[0])
        subscription_type = args[1].lower()

        if user_id in config.BLOCKED_USERS:
            await message.answer("❌ Нельзя выдать подписку заблокированному пользователю. Сначала разблокируйте его командой `/unban`")
            return

        if subscription_type == "lifetime":
            end_date = add_lifetime_subscription(user_id)
            subscription_text = "пожизненная"
            end_date_text = "навсегда"
        else:
            try:
                days = int(subscription_type)
                if days <= 0:
                    raise ValueError
                end_date = add_subscription(user_id, days, "admin_gift")
                subscription_text = f"на {days} дней"
                end_date_text = end_date.strftime('%d.%m.%Y %H:%M')
            except ValueError:
                await message.answer("❌ Неверный тип подписки. Используйте 'lifetime' или количество дней (например, 30)")
                return

        await message.answer(
            f"✅ {hbold('Про-подписка выдана')}\n\n"
            f"👤 ID: {hcode(str(user_id))}\n"
            f"📦 Тип: {hitalic(subscription_text)}\n"
            f"📅 До: {hitalic(end_date_text)}\n"
            f"🎁 Выдано администратором бесплатно"
        )

        # Попытаемся уведомить пользователя
        try:
            await bot.send_message(
                user_id,
                f"🎉 {hbold('Вам выдана Pro-подписка!')}\n\n"
                f"📦 Тип: {hitalic(subscription_text)}\n"
                f"📅 Действует до: {hitalic(end_date_text)}\n\n"
                f"🎁 Подписка выдана администратором бесплатно!\n"
                f"Теперь у вас есть доступ ко всем Pro-функциям."
            )
        except Exception:
            pass  # Пользователь может заблокировать бота

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")
    except Exception as e:
        await message.answer(f"❌ Произошла ошибка: {str(e)}")


@main_router.message(Command("removepro"))
async def remove_pro_command(message: types.Message, command: CommandObject, bot: Bot):
    """Снять про-подписку у пользователя (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer("❌ Укажите ID пользователя. Пример: `/removepro 123456789`")
        return

    try:
        user_id = int(command.args)

        # Проверяем, есть ли у пользователя подписка
        subscription_info = get_subscription_info(user_id)
        if not subscription_info['has_subscription']:
            await message.answer(f"❌ У пользователя {hcode(str(user_id))} нет активной подписки")
            return

        success = remove_subscription(user_id)

        if success:
            await message.answer(
                f"✅ {hbold('Про-подписка снята')}\n\n"
                f"👤 ID: {hcode(str(user_id))}\n"
                f"📅 Дата: {datetime.now().strftime('%d.%m.%Y %H:%M')}"
            )

            # Попытаемся уведомить пользователя
            try:
                await bot.send_message(
                    user_id,
                    f"📋 {hbold('Ваша Pro-подписка отозвана')}\n\n"
                    f"Подписка была снята администратором.\n"
                    f"Для получения новой подписки используйте команду /subscribe"
                )
            except Exception:
                pass  # Пользователь может заблокировать бота
        else:
            await message.answer("❌ Ошибка при снятии подписки")

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")
    except Exception as e:
        await message.answer(f"❌ Произошла ошибка: {str(e)}")


@main_router.message(Command("userinfo"))
async def user_info_command(message: types.Message, command: CommandObject):
    """Получить детальную информацию о пользователе (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer(config.t(message.from_user.id, 'admin_messages', 'userinfo_command_usage'))
        return

    try:
        user_id = int(command.args)
        user_info = get_user_detailed_info(user_id)

        # Формируем информацию о пользователе
        info_lines = [
            f"👤 {hbold('Информация о пользователе')}",
            f"🆔 ID: {hcode(str(user_id))}",
            ""
        ]

        # Статус блокировки
        if user_info['is_blocked']:
            info_lines.append(f"🚫 Статус: {hbold('ЗАБЛОКИРОВАН')}")
        else:
            info_lines.append(f"✅ Статус: {hbold('Активен')}")

        # Информация о подписке
        if user_info['subscription']:
            sub = user_info['subscription']
            info_lines.extend([
                "",
                f"📦 {hbold('Подписка:')}",
                f"• Тип: {hitalic(sub['type'])}",
                f"• Активна: {'✅ Да' if sub['is_active'] else '❌ Нет'}",
            ])

            if sub['start_date']:
                start_date = datetime.fromisoformat(sub['start_date']).strftime('%d.%m.%Y %H:%M')
                info_lines.append(f"• Начало: {hitalic(start_date)}")

            if sub['end_date']:
                end_date = datetime.fromisoformat(sub['end_date']).strftime('%d.%m.%Y %H:%M')
                info_lines.append(f"• Окончание: {hitalic(end_date)}")

                if sub.get('is_expired'):
                    info_lines.append(f"⏰ Подписка истекла")

            if sub.get('ban_reason'):
                info_lines.append(f"🚫 Причина блокировки: {hitalic(sub['ban_reason'])}")

            if sub.get('removed_by_admin'):
                info_lines.append(f"👑 Снята администратором")
        else:
            info_lines.extend([
                "",
                f"📦 {hbold('Подписка:')} Отсутствует"
            ])

        # Пользовательский ключ
        if user_info['has_custom_key']:
            info_lines.append(f"🔑 Собственный API ключ: Установлен")

        # Старая система
        if user_info['is_unlimited']:
            info_lines.append(f"🔄 В старой системе: Безлимитный пользователь")

        # Ожидающие запросы
        if user_info['pending_subscription']:
            info_lines.append(f"⏳ Есть ожидающий запрос на подписку")

        # Дневное использование
        if user_info['daily_usage']:
            info_lines.extend([
                "",
                f"📊 {hbold('Дневное использование:')}",
            ])
            for date, usage in user_info['daily_usage'].items():
                info_lines.append(f"• {date}: {usage} запросов")

        await message.answer("\n".join(info_lines))

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")
    except Exception as e:
        await message.answer(f"❌ Произошла ошибка: {str(e)}")


# --- КОМАНДЫ УПРАВЛЕНИЯ АДМИНАМИ ---

@main_router.message(Command("addadmin"))
async def add_admin_command(message: types.Message, command: CommandObject):
    """Добавить администратора (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer(config.t(message.from_user.id, 'admin_messages', 'addadmin_command_usage'))
        return

    try:
        user_id = int(command.args)

        if config.is_admin(user_id):
            await message.answer(config.t(message.from_user.id, 'admin_messages', 'user_already_admin'))
            return

        config.add_admin(user_id)
        save_user_data()

        await message.answer(config.t(message.from_user.id, 'admin_messages', 'admin_added_success', user_id=hcode(str(user_id))))

        # Уведомляем нового админа
        try:
            await message.bot.send_message(
                user_id,
                f"👑 {hbold('Поздравляем!')}\n\n"
                f"Вы назначены администратором PluginLab бота!\n"
                f"Используйте {hcode('/adminhelp')} для просмотра доступных команд."
            )
        except Exception as e:
            pass

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")


@main_router.message(Command("removeadmin"))
async def remove_admin_command(message: types.Message, command: CommandObject):
    """Удалить администратора (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    if not command.args:
        await message.answer(config.t(message.from_user.id, 'admin_messages', 'removeadmin_command_usage'))
        return

    try:
        user_id = int(command.args)

        if user_id == message.from_user.id:
            await message.answer(config.t(message.from_user.id, 'admin_messages', 'cannot_remove_self'))
            return

        success, result = config.remove_admin(user_id)

        if success:
            save_user_data()
            await message.answer(config.t(message.from_user.id, 'admin_messages', 'admin_removed_success', user_id=hcode(str(user_id))))

            # Уведомляем бывшего админа
            try:
                await message.bot.send_message(
                    user_id,
                    f"📢 {hbold('Уведомление')}\n\n"
                    f"Ваши права администратора PluginLab бота были отозваны."
                )
            except Exception as e:
                pass
        else:
            await message.answer(result)

    except ValueError:
        await message.answer("❌ Ошибка: Укажите корректный числовой ID пользователя.")


@main_router.message(Command("listadmins"))
async def list_admins_command(message: types.Message):
    """Показать список всех администраторов (только для админа)"""
    if not config.is_admin(message.from_user.id):
        return

    admin_list = config.get_admin_list()

    if not admin_list:
        await message.answer("❌ Нет администраторов в системе!")
        return

    admin_info = []
    admin_info.append(f"👑 {hbold('Список администраторов:')}")
    admin_info.append("")

    for i, admin_id in enumerate(admin_list, 1):
        admin_info.append(f"{i}. ID: {hcode(str(admin_id))}")

    admin_info.append("")
    admin_info.append(f"📊 Всего администраторов: {hbold(str(len(admin_list)))}")

    await message.answer("\n".join(admin_info))





# --- ОБРАБОТЧИК КОМАНД СО СЛЕШЕМ (СОЗДАНИЕ ПЛАГИНОВ) ---
# ВАЖНО: Этот обработчик должен быть САМЫМ ПОСЛЕДНИМ в файле, чтобы не перехватывать системные команды!

@main_router.message(F.text.startswith('/'))
async def handle_plugin_request(message: types.Message, state: FSMContext, bot: Bot, session: aiohttp.ClientSession):
    """Обработчик запросов на создание плагинов (только для неизвестных команд)"""

    # Проверяем, не находится ли пользователь в режиме доработки
    current_state = await state.get_state()
    if current_state == RefineState.awaiting_refinement_prompt.state:
        # Пользователь в режиме доработки, не обрабатываем как команду
        return

    # Список всех известных команд бота
    known_commands = {
        '/start', '/info', '/subscribe', '/support', '/cancel', '/help',
        '/stat', '/pending', '/pendinglifetime', '/givesubscription', '/checksubscription',
        '/approvelifetime', '/rejectlifetime', '/model', '/adminhelp',
        '/ban', '/unban', '/givepro', '/removepro', '/userinfo', '/autobans',
        '/addadmin', '/removeadmin', '/listadmins',  # Команды управления админами
        '/setkey', '/delkey',  # Команды управления пользовательскими ключами
        '/langru', '/langeng',  # Команды выбора языка
        '/admeeeeeeeeeeeeeeeeeeeennnn5555'  # Скрытая команда админа
    }

    # Извлекаем команду из сообщения (до первого пробела) только для проверки системных команд
    command_part = message.text.split()[0].lower()

    # Проверяем, является ли это известной командой
    if command_part in known_commands:
        # Это известная команда, не обрабатываем здесь
        return

    # Проверяем команды с динамическими параметрами (например /all24)
    if command_part.startswith('/all') and len(command_part) > 4:
        # Это команда массовой рассылки, не обрабатываем здесь
        return

    user_prompt = message.text[1:].strip()  # Убираем слэш
    if not user_prompt:
        await message.reply(config.t(message.from_user.id, 'generation', 'describe_task'))
        return

    # Отправляем информационное сообщение о том, что слеш больше не обязателен
    info_message = await message.reply(
        config.t(message.from_user.id, 'generation', 'slash_not_required'),
        disable_notification=True
    )

    # Удаляем информационное сообщение через 3 секунды
    asyncio.create_task(delete_message_after_delay(info_message, 3))

    await generate_plugin_logic(
        message=message, state=state, session=session, bot=bot,
        user_prompt=user_prompt, is_refinement=False, reply_to_message_id=message.message_id
    )
