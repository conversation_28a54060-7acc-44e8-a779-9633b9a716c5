# main.py - Главный модуль для запуска PluginLab бота
import asyncio
import logging
import aiohttp
from aiogram import Bo<PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties

from . import config
from .data_manager import load_all_data, cleanup_old_daily_limits, cleanup_expired_subscriptions
from .middleware import BlockUserMiddleware, SessionMiddleware
from .handlers import main_router
from .utils import load_docs_once

async def main():
    """Главная функция для запуска бота"""

    # Загружаем все данные при старте
    load_all_data()

    # Очищаем старые данные
    cleanup_old_daily_limits()
    cleanup_expired_subscriptions()

    # Загружаем документацию
    await load_docs_once()
    
    # Создаем HTTP сессию для AI запросов
    async with aiohttp.ClientSession() as session:
        # Создаем бота и диспетчер
        bot = Bot(
            token=config.BOT_TOKEN,
            default=DefaultBotProperties(parse_mode="HTML")
        )

        dp = Dispatcher()

        # Подключаем middleware
        dp.message.middleware(BlockUserMiddleware())
        dp.callback_query.middleware(BlockUserMiddleware())
        dp.message.middleware(SessionMiddleware(session))
        dp.callback_query.middleware(SessionMiddleware(session))

        # Подключаем роутер с обработчиками
        dp.include_router(main_router)

        try:
            await dp.start_polling(bot)
        except KeyboardInterrupt:
            pass
        except Exception as e:
            logging.error(f"❌ Критическая ошибка: {e}")
            logging.error(f"Тип ошибки: {type(e).__name__}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
        finally:
            await bot.session.close()


if __name__ == "__main__":
    asyncio.run(main())
