# middleware.py - Middleware классы для PluginLab
import logging
from typing import Callable, Dict, Any, Awaitable
import aiohttp

from aiogram import types
from aiogram.dispatcher.middlewares.base import BaseMiddleware

from . import config


class BlockUserMiddleware(BaseMiddleware):
    """Middleware для блокировки пользователей"""

    async def __call__(
        self,
        handler: Callable[[types.TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: types.TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        user = data.get('event_from_user')
        if user and user.id in config.BLOCKED_USERS:
            return
        return await handler(event, data)


class SessionMiddleware(BaseMiddleware):
    """Middleware для передачи HTTP сессии в обработчики"""

    def __init__(self, session: aiohttp.ClientSession):
        self.session = session
        super().__init__()

    async def __call__(
        self,
        handler: Callable[[types.TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: types.TelegramObject,
        data: Dict[str, Any]
    ) -> Any:
        data['session'] = self.session
        return await handler(event, data)
