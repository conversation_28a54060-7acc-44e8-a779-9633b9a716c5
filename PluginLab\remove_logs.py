#!/usr/bin/env python3
"""
Скрипт для быстрого удаления всех логов из кода, кроме критических ошибок
"""

import os
import re
import sys

def process_file(file_path):
    """Обрабатывает один файл, удаляя все логи кроме критических ошибок"""
    print(f"Обрабатываем файл: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        lines = content.split('\n')
        new_lines = []
        
        i = 0
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # Пропускаем пустые строки и комментарии
            if not stripped or stripped.startswith('#'):
                new_lines.append(line)
                i += 1
                continue
            
            # Проверяем, является ли строка логированием
            is_logging_line = False
            
            # Паттерны для поиска логирования
            logging_patterns = [
                r'logging\.(info|debug|warning)\(',
                r'print\(',
                r'console\.log\(',
                r'logger\.(info|debug|warning)\(',
            ]
            
            # Паттерны для критических ошибок (НЕ удаляем)
            critical_patterns = [
                r'logging\.error\(',
                r'logging\.critical\(',
                r'logger\.error\(',
                r'logger\.critical\(',
                r'logging\.exception\(',
                r'logger\.exception\(',
            ]
            
            # Проверяем, является ли это критической ошибкой
            is_critical = any(re.search(pattern, line) for pattern in critical_patterns)
            
            if not is_critical:
                # Проверяем, является ли это обычным логированием
                is_logging_line = any(re.search(pattern, line) for pattern in logging_patterns)
            
            if is_logging_line and not is_critical:
                # Пропускаем строку с логированием
                print(f"  Удаляем строку {i+1}: {stripped[:80]}...")
                
                # Проверяем, есть ли многострочный вызов
                if '(' in line and ')' not in line:
                    # Ищем закрывающую скобку
                    j = i + 1
                    while j < len(lines) and ')' not in lines[j]:
                        j += 1
                    if j < len(lines):
                        j += 1  # Включаем строку с закрывающей скобкой
                    i = j  # Пропускаем все строки многострочного вызова
                else:
                    i += 1
            else:
                new_lines.append(line)
                i += 1
        
        # Записываем обновленный контент
        new_content = '\n'.join(new_lines)
        
        if new_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"  ✅ Файл обновлен")
        else:
            print(f"  ℹ️ Изменений не требуется")
            
    except Exception as e:
        print(f"  ❌ Ошибка обработки файла {file_path}: {e}")

def remove_logging_setup(file_path):
    """Удаляет настройку логирования из main.py"""
    print(f"Удаляем настройку логирования из: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        new_lines = []
        
        skip_until_main = False
        i = 0
        
        while i < len(lines):
            line = lines[i]
            stripped = line.strip()
            
            # Начинаем пропускать с класса PluginLabSpamFilter
            if 'class PluginLabSpamFilter' in line:
                skip_until_main = True
                print(f"  Начинаем пропускать с строки {i+1}: {stripped[:50]}...")
            
            # Останавливаем пропуск на функции main()
            if skip_until_main and 'async def main():' in line:
                skip_until_main = False
                new_lines.append(line)
                print(f"  Останавливаем пропуск на строке {i+1}: {stripped[:50]}...")
                i += 1
                continue
            
            if not skip_until_main:
                new_lines.append(line)
            else:
                print(f"  Пропускаем строку {i+1}: {stripped[:50]}...")
            
            i += 1
        
        # Записываем обновленный контент
        new_content = '\n'.join(new_lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"  ✅ Настройка логирования удалена")
            
    except Exception as e:
        print(f"  ❌ Ошибка обработки файла {file_path}: {e}")

def main():
    """Главная функция"""
    print("🧹 Начинаем удаление логов из всех файлов...")
    print("⚠️  Оставляем только критические ошибки (logging.error, logging.critical, logging.exception)")
    
    # Список файлов для обработки
    files_to_process = [
        'main.py',
        'handlers.py', 
        'utils.py',
        'middleware.py',
        'config.py',
        'ai_client.py',
        'data_manager.py',
        'switch_model.py',
        'test_simple_start.py',
        'test_data_loading.py',
        'test_bot_token.py'
    ]
    
    # Обрабатываем каждый файл
    for file_name in files_to_process:
        if os.path.exists(file_name):
            if file_name == 'main.py':
                # Для main.py удаляем всю настройку логирования
                remove_logging_setup(file_name)
            else:
                # Для остальных файлов удаляем только вызовы логирования
                process_file(file_name)
        else:
            print(f"⚠️  Файл {file_name} не найден, пропускаем")
    
    print("\n✅ Обработка завершена!")
    print("🔄 Перезапустите бота для применения изменений")
    print("\n📝 Что было сделано:")
    print("   • Удалены все logging.info(), logging.debug(), logging.warning()")
    print("   • Удалены все print() (кроме критических мест)")
    print("   • Оставлены только logging.error(), logging.critical(), logging.exception()")
    print("   • Удалена вся настройка логирования из main.py")

if __name__ == "__main__":
    main()
