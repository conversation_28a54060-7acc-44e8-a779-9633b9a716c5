#!/usr/bin/env python3
"""
Скрипт для быстрого переключения между официальным Gemini API и VoidAI (Claude Sonnet 4)
"""

import os
import sys

def switch_to_claude():
    """Переключить на Claude Sonnet 4 через VoidAI (отключить официальный Gemini)"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.py')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Заменяем флаг на False
    content = content.replace('USE_OFFICIAL_GEMINI_API = True', 'USE_OFFICIAL_GEMINI_API = False')
    
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)
    

def switch_to_gemini():
    """Переключить на официальный Gemini API"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.py')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Заменяем флаг на True
    content = content.replace('USE_OFFICIAL_GEMINI_API = False', 'USE_OFFICIAL_GEMINI_API = True')
    
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(content)
    

def show_status():
    """Показать текущий статус"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.py')
    
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'USE_OFFICIAL_GEMINI_API = True' in content:
    else:

if __name__ == "__main__":
    if len(sys.argv) < 2:
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "claude":
        switch_to_claude()
    elif command == "gemini":
        switch_to_gemini()
    elif command == "status":
        show_status()
    else:
