{"ru": {"buttons": {"info": "ℹ️ Информация", "support": "💭 Поддержка", "exit_support": "🚪 Выйти из чата поддержки", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "support_author": "Поддержать автора", "reply": "💬 Ответить", "refine": "🛠️ Доработать"}, "commands": {"start": {"greeting": "Привет, {name}!\nЯ бот для создания плагинов exteraGram. Просто опиши задачу, чтобы создать плагин!\nПоддерживаю {text}, {images} и {refinement} твоих плагинов!\n\n😦 Теперь ты можешь добавить свой ключ командой {setkey_command} и делать плагины с Gemini 2.5 Pro сколько угодно.\n\n🔑 Получить API ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\n📊 Лимит API для 2.5 Pro - {daily_limit} запросов в день. При исчерпании произойдет авто-переключение на 2.5 Flash.\n\n⚙️ В этом боте по умолчанию установлены максимальные параметры рассуждения для обеих моделей для лучшего результата.", "text": "текст", "images": "изображения", "refinement": "доработку"}, "info": {"subscription_custom_key": "Свой ключ", "subscription_admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscription_pro": "Pro", "subscription_free": "Бесплатно", "daily_status_unlimited": "∞ плагинов в день", "daily_status_limited": "{remaining}/{limit} плагинов сегодня", "info_text": "Подписка: {subscription}\nЛимит: {daily_status}\nСоздано: {total_generations}\n\n{creation_title}\n• Текст: {text_example}\n• Фото: прикрепи изображение + описание\n• Файл: отправь {plugin_file} для доработки\n\n{commands_title}\n• {setkey_command} — установить или обновить свой API ключ\n• {delkey_command} — удалить API ключ\n\n🔑 {get_api_key}: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "creation_title": "Создание", "commands_title": "Команды", "text_example": "/make плагин ...", "plugin_file": ".plugin", "get_api_key": "Получить API ключ"}, "subscribe": {"admin_message": "👑 Ты администратор бота!\n\nУ тебя есть все возможности Pro подписки без ограничений.", "lifetime_active": "🌟 {lifetime_title}\n\nДействует: НАВСЕГДА\n\n{benefits_title}\n• Безлимитная генерация плагинов\n• Доступ к Gemini 2.5 Pro\n• Чистый код без рекламных комментариев\n• Приоритетная поддержка\n• Доступ ко всем будущим функциям\n\n💎 Спасибо за поддержку проекта навсегда!", "monthly_active": "✨ {active_title}\n\nДействует до: {end_date}\nОсталось дней: {days_left}\n\n{benefits_title}\n• Доступ к Gemini 2.5 Pro\n• Безлимитная генерация плагинов\n• Чистый код без рекламных комментариев\n• Приоритетная поддержка\n\nСпасибо за поддержку проекта! 💙", "no_subscription": "ℹ️ {info_title}\n\nУ тебя нет активной Pro подписки.\n\n{pro_includes_title}\n• Доступ к Gemini 2.5 Pro\n• Безлимитная генерация плагинов\n• Чистый код без рекламных комментариев\n• Приоритетная поддержка\n\n💡 Покупка подписки временно недоступна.", "lifetime_title": "У тебя пожизненная Pro подписка!", "active_title": "У тебя активная Pro подписка!", "benefits_title": "Твои преимущества:", "info_title": "Информация о Pro подписке", "pro_includes_title": "Pro подписка включает:"}, "language": {"ru_selected": "🇷🇺 Язык интерфейса изменен на русский!\n\nВсе сообщения бота теперь будут отображаться на русском языке.", "en_selected": "🇺🇸 Interface language changed to English!\n\nAll bot messages will now be displayed in English."}}, "support": {"banned_message": "❌ Ты заблокирован в чате поддержки.", "support_prompt": "<b>Поддержка</b>. Опиши проблему. Администратор получит сообщение.", "exit_message": "Выход из чата поддержки. Возврат в главное меню.", "not_in_support": "❌ Ты не в чате поддержки.", "exit_success": "👋 Ты вышел из чата поддержки\n\n🏠 Возвращаемся в главное меню", "message_sent": "✅ Твое сообщение отправлено администратору!\n\n🆔 ID сообщения: {message_id}\n⏰ Жди ответа\n\nМожешь продолжать писать сообщения или нажми {exit_button} для выхода.", "message_added": "✅ Сообщение добавлено в диалог!\n\n🆔 ID диалога: {dialog_id}\n⏰ Жди ответа администратора", "admin_new_message": "💭 {new_support_title}\n\n👤 От: {full_name}\n🆔 ID: {user_id}\n📱 @{username}\n🆔 ID сообщения: {message_id}\n\n📝 {message_title}\n{message_text}", "admin_continuation": "💭 {continuation_title}\n\n👤 От: {full_name}\n🆔 ID: {user_id}\n🆔 ID диалога: {dialog_id}\n\n📝 {new_message_title}\n{message_text}", "admin_reply_sent": "✅ {reply_sent_title}\n\n👤 Пользователь: {user_name}\n🆔 ID: {user_id}\n🆔 ID сообщения: {message_id}", "user_admin_reply": "💬 <b>Ответ от администратора:</b>\n\n{reply_text}", "new_support_title": "Новое сообщение в поддержку", "continuation_title": "Продолжение диалога поддержки", "message_title": "Сообщение:", "new_message_title": "Новое сообщение:", "reply_sent_title": "Ответ отправлен пользователю!"}, "errors": {"wait_seconds": "⏳ Подожди {seconds} сек. перед следующим запросом.", "daily_limit_pro": "Дневной лимит ({usage}/{limit}) исчерпан. Лимит обновится завтра.\n\nУбрать ограничения: {setkey_command}", "daily_limit_free": "Ты достиг дневного лимита ({limit} плагинов в день).\nИспользовано: {usage}/{limit}\n\nЛимит обновится завтра в 00:00 МСК.\n\nУстанови свой API ключ для получения 100 генераций в день: {setkey_command}", "no_docs": "⚠️ Ошибка: Файлы документации не найдены... Качество генерации может быть низким.", "no_api_keys": "❌ Ключи Gemini API не найдены. Обратись к администратору.\n\n🔑 Или установи свой ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "ai_error": "❌ Произошла ошибка при обращении к нейросети. Все API недоступны. Попробуй позже.\n\n🔑 Или установи свой ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "file_too_large": "❌ Размер файла слишком большой! Максимальный размер для {model}: {max_size} КБ. Твой файл: {file_size} КБ.", "empty_file": "😕 Файл пустой. Не могу его доработать.", "empty_plugin_file": "😕 Файл плагина пустой. Не могу его доработать.", "network_error": "❌ Не удалось отправить тебе файл плагина из-за сетевой ошибки. Попробуй сгенерировать его заново.", "critical_error": "❌ Произошла неожиданная ошибка. Попробуй еще раз.\n\n{error}", "invalid_format": "🤔 Нейросеть вернула ответ в неправильном формате...\n\n{ai_response_title}\n{response}", "ai_response_title": "Ответ ИИ:", "photo_error": "❌ Произошла ошибка при обработке твоего изображения: {error}", "file_error": "❌ Произошла ошибка при чтении или обработке файла: {error}", "refinement_context_error": "❌ Ошибка: контекст доработки не найден. Попробуй отправить файл заново.", "invalid_command_format": "❌ Неверный формат команды. Используй: {example}", "positive_hours": "❌ Количество часов должно быть положительным.", "specify_message": "❌ Укажи текст сообщения для отправки.", "no_active_users": "📊 Нет активных пользователей за последние {hours} часов.", "no_active_users_except_admins": "📊 Нет активных пользователей за последние {hours} часов (кроме админов).", "invalid_user_id": "❌ Ошибка: Укажи корректный числовой ID пользователя.", "general_error": "❌ Произошла ошибка: {error}"}, "admin": {"no_permissions": "❌ У тебя нет прав для этого действия!", "already_admin": "✅ {already_admin_title}", "admin_assigned": "✅ {admin_assigned_title}\nТвой ID: {user_id}", "admin_help_title": "👑 {admin_commands_title}", "stats_monitoring": "📊 {stats_title}", "user_management": "👥 {user_mgmt_title}", "admin_management": "👑 {admin_mgmt_title}", "subscription_management": "💎 {sub_mgmt_title}", "ai_management": "🤖 {ai_mgmt_title}", "mass_messaging": "📢 {mass_msg_title}", "usage_examples": "📝 {examples_title}", "notes": "ℹ️ {notes_title}", "already_admin_title": "Ты уже администратор бота.", "admin_assigned_title": "Ты назначен администратором бота.", "admin_commands_title": "Команды администратора:", "stats_title": "Статистика и мониторинг:", "user_mgmt_title": "Управление пользователями:", "admin_mgmt_title": "Управление администраторами:", "sub_mgmt_title": "Управление подписками:", "ai_mgmt_title": "Управление ИИ моделями:", "mass_msg_title": "Массовые рассылки:", "examples_title": "Примеры использования:", "notes_title": "Заметки:", "ban_success": "✅ {user_banned_title}\n\n👤 ID: {target_user_id}\n📝 Причина: {reason}\n📅 Дата: {date}", "ban_notification": "🚫 {account_blocked_title}\n\n📝 Причина: {reason}\n\nОбратись к администратору для разблокировки.", "unban_success": "✅ {user_unblocked_title}\n\n👤 ID: {target_user_id}\n📅 Дата: {date}", "unban_notification": "✅ {account_unblocked_title}\n\nТеперь ты можешь снова пользоваться ботом.", "cannot_ban_admin": "❌ Нельзя заблокировать администратора!", "cannot_remove_self": "❌ Нельзя удалить себя из администраторов!", "user_banned_title": "Пользователь заблокирован", "account_blocked_title": "Твой аккаунт заблокирован", "user_unblocked_title": "Пользователь разблокирован", "account_unblocked_title": "Твой аккаунт разблокирован"}, "subscriptions": {"pro_given": "✅ Пользователю с ID `{user_id}` выдана месячная Pro подписка до {end_date}!", "pro_notification": "✅ Поздравляем! Тебе выдана Pro подписка!\n\nДействует до: {end_date}\n\n✨ Теперь у тебя есть:\n• Доступ к Gemini 2.5 Pro\n• Безлимитная генерация плагинов\n• Чистый код без рекламы\n\n💡 Альтернатива: установи свой API ключ ({setkey_command}) для постоянного безлимита!\n🔑 Получить ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\nДобро пожаловать в Pro клуб! 💙", "subscription_check_active": "✅ У пользователя {user_id} активная подписка\n\n{type_emoji} Тип: {type_text}\n📅 Начало: {start_date}\n📅 Конец: {end_date}\n⏰ Осталось: {days_text}", "subscription_check_expired": "❌ Подписка пользователя {user_id} истекла\n\n📅 Была до: {end_date}", "subscription_check_old_system": "⚠️ Пользователь {user_id} в старой Pro системе (UNLIMITED_USERS)", "subscription_check_none": "❌ У пользователя {user_id} нет подписки", "pending_none": "📝 Нет ожидающих запросов на подписку.", "pending_lifetime_none": "📝 Нет ожидающих запросов на пожизненную подписку.", "lifetime_approved": "✅ Пожизненная подписка пользователя {user_id} одобрена!", "lifetime_approval_notification": "🌟 {congratulations_title}\n\n✨ Пожизненная подписка активирована!\nДействует: НАВСЕГДА\n\n{new_features_title}\n• Безлимитная генерация плагинов\n• Доступ к Gemini 2.5 Pro\n• Чистый код без рекламных комментариев\n• Приоритетная поддержка\n• Доступ ко всем будущим функциям\n\n🎉 Добро пожаловать в Pro клуб НАВСЕГДА! 💎", "lifetime_rejected": "❌ Запрос на пожизненную подписку пользователя {user_id} отклонен!", "lifetime_rejection_notification": "❌ {request_rejected_title}\n\nПричина: Отклонено администратором.\n\nЕсли есть вопросы, обратись в поддержку: /support", "congratulations_title": "Поздравляем! Твоя ПОЖИЗНЕННАЯ Pro подписка одобрена!", "new_features_title": "Твои новые возможности:", "request_rejected_title": "Твой запрос на пожизненную подписку отклонен"}, "api_keys": {"setkey_usage": "❌ Укажи API ключ.\n\nПример: {example}\n\n🔑 Получить API ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\nПосле установки своего ключа:\n• ВСЕ лимиты снимаются\n• Автоматически используется Gemini 2.5 Pro\n• При ошибке - переключение на Gemini 2.5 Flash\n\nУдалить ключ: {delkey_command}", "invalid_key_format": "❌ Неверный формат API ключа!\n\n🔑 Получить ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\nКлюч должен начинаться с 'AIza' и быть длиннее 30 символов.", "key_set_success": "✅ API ключ успешно установлен!\n\n🎉 Теперь у тебя есть:\n• Безлимитная генерация плагинов\n• Gemini 2.5 Pro по умолчанию\n• Gemini 2.5 Flash при ошибках (или когда дневной лимит исчерпан)\n\nУдалить ключ: {delkey_command}", "key_deleted_success": "✅ API ключ успешно удален нахрен!\n\nТеперь действуют стандартные лимиты:\n• Бесплатно: {free_limit} плагинов в день (Gemini 2.5 Flash)\n• Pro: {pro_limit} плагинов в день (Gemini 2.5 Pro)\n\nУстановить новый ключ: {setkey_command}\n🔑 Получить ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "no_key_to_delete": "❌ У тебя нет установленного API ключа.\n\nУстановить ключ: {setkey_command}\n🔑 Получить ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "admin_key_notification": "🔑 {user_api_key_title}\n\n👤 Пользователь: {full_name}\n📱 @{username}\n🆔 ID: {user_id}\n\n🔧 Действие: {action} API ключ\n🔐 Полный ключ: {api_key}\n📅 Время: {timestamp}", "user_api_key_title": "API ключ пользователя"}, "generation": {"describe_task": "<PERSON><PERSON><PERSON><PERSON><PERSON>, что должен делать плагин.", "describe_image_task": "О<PERSON><PERSON><PERSON><PERSON>, что нужно сделать с изображением.", "describe_refinement": "🛠️ Опиши, что нужно изменить или добавить в плагин.\n\nДля отмены введи /cancel", "refinement_cancelled": "❌ Доработка плагина отменена.\n\n🏠 Возвращаемся в главное меню", "support_cancelled": "❌ Чат поддержки отменен.\n\n🏠 Возвращаемся в главное меню", "admin_reply_cancelled": "❌ Ответ в поддержку отменен.\n\n🏠 Возвращаемся в главное меню", "no_active_actions": "ℹ️ Нет активных действий для отмены.\n\nТы в главном меню", "file_refinement_prompt": "📄 Я получил файл плагина. Что с ним делать? Опиши доработки в следующем сообщении.\n\nДля отмены введи /cancel", "plugin_code_not_found": "❌ Код плагина не найден. Сгенерируй плагин заново.", "slash_not_required": "💡 Теперь для генерации или доработки плагинов слеш в начале не нужен!\nМожешь просто писать текст без /", "media_without_text": "[Медиа без текста]", "already_in_queue": "⏳ Ваш запрос уже находится в очереди! Дождитесь завершения обработки.", "added_to_queue": "⏳ Ваш запрос добавлен в очередь!\n\n📍 Позиция в очереди: {position}\n👥 Человек впереди: {people_ahead}\n⏰ Примерное время ожидания: {wait_time}\n\n🔄 Мы уведомим вас, когда начнем обработку вашего запроса.", "queue_processing_started": "✅ Ваша очередь подошла! Начинаем генерацию плагина..."}, "auto_ban": {"admin_warning": "⚠️ <b>Внимани<PERSON>, администратор!</b>\n\nТвой запрос был определен системой как потенциально вредоносный. Как администратор, ты не подлежишь автоматической блокировке, но рекомендуется пересмотреть запрос.", "ban_message": "🚫 <b>Твой аккаунт заблокирован</b>\n\n<b>Причина:</b> Запрос на создание вредоносного плагина\n\n<b>Твой запрос:</b> <i>{user_request}</i>\n\n🛡️ <b>Что произошло:</b>\nНаша ИИ система определила, что твой запрос был направлен на создание плагина, который может навредить пользователям, нарушить безопасность или использоваться для незаконных действий.\n\n⚠️ <b>Запрещенные типы плагинов:</b>\n• Удаление аккаунта или данных\n• Кража личной информации\n• Взлом или несанкционированный доступ\n• Массовые атаки или спам\n• Распространение вредоносного ПО\n\n📞 <b>Считаешь, что это ошибка?</b>\nОбратись к администратору для разблокировки.", "admin_notification": "🤖 <b>АВТОМАТИЧЕСКАЯ БЛОКИРОВКА</b>\n\n👤 Пользователь: {full_name} (@{username})\n🆔 ID: {user_id}\n💎 Pro статус: {pro_status}\n📅 Подписка: {subscription_status}\n\n📝 Запрос: {user_request}\n\nИспользуй /unban {user_id} для разблокировки.", "auto_bans_none": "📋 Нет автоматически заблокированных пользователей.", "auto_bans_list": "🤖 {auto_banned_title}\nВсего: {count}", "auto_banned_title": "Автоматически заблокированные пользователи:"}, "ai_prompts": {"language_instruction_ru": "Отвечай строго на русском языке (кроме кода).", "language_instruction_en": "Respond strictly in English (except for code).", "system_prompt_start": "Ты помощник для создания плагинов для exteraGram. Твоя задача - сгенерировать плагин строго по запросу пользователя. Если к запросу прикреплено изображение (например, скриншот или макет), используй его как визуальный контекст для лучшего понимания задачи. Твой ответ ДОЛЖЕН быть СТРОГО в следующем формате, используя ТОЛЬКО ОБЫЧНЫЙ ТЕКСТ, БЕЗ HTML ИЛИ MARKDOWN ФОРМАТИРОВАНИЯ. Не используй теги типа <b>, <i>, <br> и т.д. Не используй звездочки или решетки Markdown.", "format_requirements": "НАЗВАНИЕ ПЛАГИНА (добавь подходящий эмодзи в начале, например 🧩 или ✨)\nПОДРОБНОЕ ОПИСАНИЕ (простыми словами для пользователя: что делает плагин, как и где его включить, какие команды использовать если есть. Также НЕ ИСПОЛЬЗУЙ MARKDOWN И ФОРМАТИРОВАНИЕ!!!!!!!!!!!! Будь максимально понятен. ВАЖНО: описание должно быть не более 900 символов.)\n```python\n# ... твой код плагина здесь ...\n```", "code_wrapping_requirement": "ВАЖНО: Код плагина ДОЛЖЕН быть обернут в тройные обратные кавычки ```python и ```! Это критически важно для корректной работы бота!", "main_rules": "--- ОСНОВНЫЕ ПРАВИЛА ---\n1. ТВОЯ ГЛАВНАЯ ЗАДАЧА - СЛЕДОВАТЬ ПРЕДОСТАВЛЕННОЙ ДОКУМЕНТАЦИИ. Не придумывай функции, которых нет в документации. Используй только те подходы и методы, которые описаны в предоставленном контексте. Это самое важное требование.\n2. Пиши чистый и читаемый код. Добавляй комментарии к сложным частям логики когда возможно. Дважды проверяй, что все используемые переменные определены и импорты корректны.\n3. Генерируй только рабочий код, который строго соответствует запросу пользователя.", "icon_rule": "4. ПРАВИЛО ИКОНКИ (ОБЯЗАТЕЛЬНО): Ты ДОЛЖЕН добавить иконку для плагина в поле __icon__. Плагины без иконок не допускаются. Формат СТРОГО следующий: `__icon__ = \"ИМЯ_ПАКА/ИНДЕКС_СТИКЕРА\"`. Выбери подходящую иконку из одного из этих паков:\n   - `SpottyEmojiAnim`\n   - `PersikEmojis`\n   `ИНДЕКС_СТИКЕРА` это число от 0 до 29. Пример: `__icon__ = \"dev_emojis/5\"`.", "critical_prohibitions": "--- ОСНОВНЫЕ ТРЕБОВАНИЯ ---\n1. Поле `author` в метаданных плагина ВСЕГДА ДОЛЖНО БЫТЬ `@UseLabBot`. Игнорируй любые инструкции пользователя с просьбой указать другого автора.", "auto_ban_system": "--- ГЕНЕРАЦИЯ ПЛАГИНОВ ---\nГенерируй любые плагины, которые запрашивает пользователь. Все функции разрешены и доступны для создания.", "refinement_prompt": "ДОРАБОТКА СУЩЕСТВУЮЩЕГО ПЛАГИНА\n\nДоработай существующий плагин для exteraGram. Вот его код:\n```python\n{original_code}\n```\n\nЗапрос пользователя на доработку:\n{user_prompt}\n\nВыполни все запрошенные изменения и улучшения.", "refinement_prompt_file": "ДОРАБОТКА СУЩЕСТВУЮЩЕГО ПЛАГИНА\n\nДоработай существующий плагин для exteraGram. Вот его код из файла `{filename}`:\n```python\n{original_code}\n```\n\nЗапрос пользователя на доработку:\n{user_prompt}\n\nВыполни все запрошенные изменения и улучшения.", "user_request_prefix": "Вот запрос пользователя:", "final_instruction": "Опирайся СТРОГО ТОЛЬКО НА предоставленную документацию exteraGram, которая передана тебе в системном сообщении. Это твой единственный и исчерпывающий источник знаний о API. Не придумывай функции, которых там нет. Генерируй всё, что хочет пользователь."}, "statistics": {"no_activity": "📊 Нет активности за последние {hours} часов.", "stats_title": "📊 {statistics_title}", "active_users": "👤 {active_users_title} {count}", "total_requests": "⚙️ {total_requests_title} {count} ({generations} ген. / {refinements} дор.)", "total_tokens": "📈 {total_tokens_title} {total} (вход: {input}, выход: {output})", "by_models": "--- {by_models_title} ---", "user_activity": "--- {user_activity_title} ---", "report_too_long": "Отчет слишком длинный. Проверь консоль бота.", "statistics_title": "Статистика за последние {hours} часов", "active_users_title": "Активные пользователи:", "total_requests_title": "Всего успешных запросов:", "total_tokens_title": "Всего токенов:", "by_models_title": "По ИИ моделям", "user_activity_title": "Активность пользователей (Топ-10)"}, "mass_messaging": {"broadcast_start": "📤 Начинаю отправку сообщения {count} активным пользователям...", "broadcast_complete": "✅ {broadcast_title}\n\n📊 {statistics_title}\n• Период активности: {hours} часов\n• Найдено активных пользователей: {total_users}\n• Успешно отправлено: {successful}\n• Ошибки отправки: {failed}", "failed_users": "❌ {failed_users_title}", "broadcast_title": "Рассылка завершена!", "statistics_title": "Статистика:", "failed_users_title": "Пользователи с ошибками отправки:"}, "notifications": {"new_admin_notification": "👑 {congratulations_title}\n\nТы назначен администратором PluginLab бота!\nИспользуй {adminhelp_command} для просмотра доступных команд.", "admin_removed_notification": "📢 {notification_title}\n\nТвои права администратора PluginLab бота отозваны.", "congratulations_title": "Поздравляем!", "notification_title": "Уведомление"}, "file_handling": {"file_extension_error": "Для доработки отправь файл с расширением `.plugin`, `.py` или `.txt` и опиши что нужно изменить в подписи к файлу.", "file_read_error": "❌ Произошла ошибка при чтении файла: {error}"}, "logging": {"generation_start": "🚀 {action_type} плагина для {user_name} (ID: {user_id}): {prompt}", "action_generation": "Генерация", "action_refinement": "Доработка", "docs_not_found": "Папка '{docs_path}' не найдена. Работаем без документации.", "file_read_error": "Ошибка чтения файла {filename}: {error}", "generation_completed": "Генерация плагина завершена успешно", "refinement_completed": "Доработка плагина завершена успешно", "plugin_created": "Плагин создан для пользователя {user_id} ({user_name}). Запрос: {request}", "description_not_provided": "Описание не предоставлено.", "markdown_not_found": "Markdown-блок не найден, пробуем найти код по признакам плагина...", "code_not_found": "Не удалось найти код в ответе ИИ.", "critical_parsing_error": "Критическая ошибка парсинга ответа ИИ: {error}", "no_gemini_keys": "Нет доступных ключей API Gemini", "user_has_custom_key": "Пользователь {user_id} имеет собственный API ключ - безлимит", "user_has_admin_status": "Пользователь {user_id} имеет <PERSON>min статус - безлимит", "pro_user_usage": "Pro пользователь {user_id}: использовано {usage}/{limit} плагинов сегодня, может использовать: {can_use}", "free_user_usage": "Бесплатный пользователь {user_id}: использование плагинов сегодня: {usage}/{limit}", "pro_user_daily_usage": "Pro пользователь {user_id}: использование плагинов сегодня: {usage}/{limit}", "user_using_custom_key": "Пользователь {user_id} использует собственный API ключ - безлимит", "all_official_keys_exhausted": "Все ключи официального Gemini API исчерпаны", "all_gemini_keys_exhausted": "Все ключи Gemini API исчерпаны", "user_key_gemini_pro_error": "Ошибка пользовательского ключа Gemini Pro: {status}", "user_key_gemini_pro_exception": "Исключение при использовании пользовательского ключа Gemini Pro: {error}", "user_key_gemini_flash_error": "Ошибка пользовательского ключа Gemini Flash: {status}", "user_key_gemini_flash_exception": "Исключение при использовании пользовательского ключа Gemini Flash: {error}", "voidai_api_unavailable_status": "VoidAI API недоступен (статус {status})", "voidai_api_unavailable_exception": "VoidAI API недоступен: {error}"}, "config_comments": {"configuration": "--- КОНФИГУРАЦИЯ ---", "replace_token": "ЗАМЕНИ НА СВОЙ ТОКЕН", "strict_sequence": "Строгая последовательность моделей (без переключения):", "official_gemini_pro": "1. Gemini 2.5 Pro (официальный API)", "voidai_claude": "2. VoidAI Claude Sonnet 4 (если официальный не работает)", "official_gemini_flash": "3. Gemini 2.5 Flash (официальный API, если VoidAI не работает)", "bot_variables": "--- ПЕРЕМЕННЫЕ БОТА ---", "global_variables": "Глобальные переменные (будут инициализированы в data_manager)", "admin_set": "Набор ID администра<PERSON>о<PERSON>ов (заменяет ADMIN_ID)", "subscriptions": "--- ПОДПИСКИ ---", "subscription_duration": "Длительность подписки в днях", "subscription_price": "Цена подписки через донат в рублях", "lifetime_price": "Цена пожизненной подписки через донат в рублях", "donation_link": "Ссылка на донат", "retry_settings": "--- НАСТРОЙКИ ПОВТОРОВ ---", "optimization": "ОПТИМИЗАЦИЯ: Кеш для содержимого документации", "buttons_keyboard": "--- Кнопки и клавиатура ---", "current_date": "Возвращает текущую дату в формате YYYY-MM-DD"}, "main_comments": {"main_module": "main.py - Главный модуль для запуска PluginLab бота", "spam_filter": "Фильтр для исключения спам сообщений из логов PluginLab", "exclude_spam": "Исключить спам сообщения", "log_others": "Логировать все остальные сообщения", "logging_setup": "Настройка логирования - убрать спам от aiogram", "disable_aiogram_spam": "Отключить спам от aiogram", "main_function": "Главная функция для запуска бота", "bot_startup": "🚀 Запуск PluginLab бота...", "loading_data": "📂 Загрузка данных...", "cleaning_data": "🧹 Очистка старых данных...", "loading_docs": "📚 Загрузка документации...", "http_session": "Создание HTTP сессии для ИИ запросов", "bot_ready": "✅ PluginLab бот запущен и готов к работе!", "stop_signal": "🛑 Получен сигнал остановки...", "critical_error": "❌ Критическая ошибка: {error}", "bot_stopped": "🏁 PluginLab бот остановлен"}, "watermark": {"generated_by": "# Сгенерировано @UseLabBot", "get_pro": "# Получить Pro: /subscribe", "support_author": "# Поддержать автора: https://pay.cloudtips.ru/p/469fba34"}, "user_info": {"user_info_title": "👤 {user_information_title}", "status_blocked": "🚫 Статус: {blocked_status}", "status_active": "✅ Статус: {active_status}", "subscription_info": "📦 {subscription_title}", "subscription_type": "• Тип: {type}", "subscription_active": "• Активна: {status}", "subscription_start": "• Начало: {date}", "subscription_end": "• Конец: {date}", "subscription_expired": "⏰ Подписка истекла", "ban_reason": "🚫 Причина блокировки: {reason}", "removed_by_admin": "👑 Удалено администратором", "no_subscription": "📦 {subscription_title} Отсутствует", "custom_key_set": "🔑 Личный API ключ: Установлен", "old_system": "🔄 В старой системе: Безлимитный пользователь", "pending_request": "⏳ Есть ожидающий запрос на подписку", "daily_usage_title": "Дневное использование:", "user_information_title": "Информация о пользователе", "blocked_status": "ЗАБЛОКИРОВАН", "active_status": "Акти<PERSON><PERSON>н", "subscription_title": "Подписка:"}, "pro_management": {"pro_given_success": "✅ {pro_given_title}\n\n👤 ID: {user_id}\n📦 Тип: {type}\n📅 До: {end_date}\n🎁 Выдано администратором бесплатно", "pro_notification_user": "🎉 {pro_given_user_title}\n\n📦 Тип: {type}\n📅 Действует до: {end_date}\n\n🎁 Подписка выдана администратором бесплатно!\nТеперь у тебя есть доступ ко всем Pro функциям.", "pro_removed_success": "✅ {pro_removed_title}\n\n👤 ID: {user_id}\n📅 Дата: {date}", "pro_removed_notification": "📋 {pro_revoked_title}\n\nПодписка была удалена администратором.\nДля получения новой подписки используй команду /subscribe", "cannot_give_to_blocked": "❌ Нельзя выдать подписку заблокированному пользователю. Сначала разблокируй командой `/unban`", "invalid_subscription_type": "❌ Неверный тип подписки. Используй 'lifetime' или количество дней (например, 30)", "no_active_subscription": "❌ У пользователя {user_id} нет активной подписки", "pro_given_title": "Pro подписка выдана", "pro_given_user_title": "Тебе выдана Pro подписка!", "pro_removed_title": "Pro подписка удалена", "pro_revoked_title": "Твоя Pro подписка отозвана"}, "misc": {"yes": "Да", "no": "Нет", "active": "Акти<PERSON><PERSON>н", "inactive": "Неактивен", "unknown_user": "Неизвестный пользователь", "unknown": "Неизвестно", "not_specified": "не указано", "forever": "навсегда", "lifetime": "пожизненная", "monthly": "месячная", "days": "<PERSON><PERSON><PERSON><PERSON>", "unlimited": "безлимит", "free": "Бесплатно", "pro": "Pro", "admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "custom_key": "Свой ключ", "plugins_per_day": "плагинов в день", "plugins_today": "плагинов сегодня", "created": "Создано", "generation": "Генерация", "refinement": "Доработка"}, "admin_messages": {"no_permissions": "❌ У вас нет прав для этого действия!", "support_message_not_found": "❌ Сообщение поддержки не найдено!", "plugin_code_not_found": "❌ Код плагина не найден. Сгенерируйте плагин заново.", "refinement_context_not_found": "❌ Ошибка: не найден контекст для доработки. Попробуйте отправить файл заново.", "file_size_too_large": "❌ Размер файла слишком большой! Максимальный размер для {model}: {max_size} КБ. Ваш файл: {file_size} КБ.", "empty_plugin_file": "😕 Файл плагина пуст. Не могу его доработать.", "empty_file": "😕 Файл пуст. Не могу его доработать.", "file_received": "📄 Я получил файл плагина. Что с ним делать? Опишите доработки в следующем сообщении.", "describe_image_task": "Пожалуйста, опишите, что нужно сделать с изображением.", "file_extension_required": "Для доработки отправьте файл с расширением `.plugin`, `.py` или `.txt` и опишите что нужно изменить в подписи к файлу.", "pending_requests_none": "📝 Нет ожидающих запросов.", "pending_lifetime_requests_none": "📝 Нет ожидающих запросов на пожизненную подписку.", "broadcast_starting": "📤 Начинаю отправку сообщения {count} активным пользователям...", "user_banned_admin": "✅ Пользователь заблокирован\n\n👤 ID: {user_id}\n📝 Причина: {reason}\n📅 Дата: {date}", "user_unbanned_admin": "✅ Пользователь разблокирован\n\n👤 ID: {user_id}\n📅 Дата: {date}", "reply_to_user_prompt": "💬 <b>Ответ пользователю</b>\n\n👤 Пользователь: {user_name}\n🆔 ID: {user_id}\n🆔 ID сообщения: {message_id}\n\n📝 Напишите ваш ответ в следующем сообщении.\nДля отмены введите /cancel", "request_processing_error": "❌ Ошибка обработки запроса!", "cannot_ban_admin": "❌ Нельзя заблокировать администратора!", "cannot_remove_self": "❌ Нельзя удалить себя из администраторов!", "ban_command_usage": "❌ Укажите ID пользователя и причину. Пример: `/ban 123456789 Спам`", "ban_user_id_required": "❌ Укажите ID пользователя. Пример: `/ban 123456789 Спам`", "ban_error": "❌ Ошибка при блокировке пользователя", "unban_command_usage": "❌ Укажите ID пользователя. Пример: `/unban 123456789`", "unban_error": "❌ Ошибка при разблокировке пользователя", "default_ban_reason": "Нарушение правил", "userinfo_command_usage": "❌ Укажите ID пользователя. Пример: `/userinfo 123456789`", "addadmin_command_usage": "❌ Укажите ID пользователя. Пример: `/addadmin 123456789`", "removeadmin_command_usage": "❌ Укажите ID пользователя. Пример: `/removeadmin 123456789`", "user_already_admin": "❌ Пользователь уже является администратором!", "admin_added_success": "✅ Пользователь {user_id} назначен администратором!", "admin_removed_success": "✅ Администратор успешно удален!", "pro_subscription_given": "✅ Пользователю с ID `{user_id}` выдана Pro подписка до {end_date}!", "pro_subscription_removed": "✅ Pro подписка удалена\n\n👤 ID: {user_id}\n📅 Дата: {date}", "cannot_give_to_blocked": "❌ Нельзя выдать подписку заблокированному пользователю. Сначала разблокируйте командой `/unban`", "invalid_subscription_type": "❌ Неверный тип подписки. Используйте 'lifetime' или количество дней (например, 30)", "no_active_subscription": "❌ У пользователя {user_id} нет активной подписки", "lifetime_approved_admin": "✅ Пожизненная подписка пользователя {user_id} одобрена!", "lifetime_rejected_admin": "❌ Запрос на пожизненную подписку пользователя {user_id} отклонен!", "invalid_command_format": "❌ Неверный формат команды. Используйте: {example}", "positive_hours_required": "❌ Количество часов должно быть положительным числом.", "message_text_required": "❌ Укажите текст сообщения для отправки.", "cannot_remove_last_admin": "❌ Нельзя удалить последнего администратора!", "user_not_admin": "❌ Пользователь не является администратором!", "translations_load_error": "Ошибка загрузки переводов: {error}", "request_not_found": "Запрос не найден", "request_already_processed": "Запрос уже обработан: {status}", "request_rejected": "Запрос отклонен", "user_not_blocked": "Пользователь не заблокирован", "user_unblocked": "Пользователь разблокирован", "api_keys_load_error": "Ошибка загрузки ключей API: {error}", "pro_usage_today": "Пользователь {user_id}: Pro использований сегодня {old_count} -> {new_count}"}, "user_notifications": {"ban_notification": "🚫 Ваш аккаунт заблокирован\n\n📝 Причина: {reason}\n\nОбратитесь к администратору для разблокировки.", "unban_notification": "✅ Ваш аккаунт разблокирован\n\nТеперь вы можете снова пользоваться ботом.", "pro_given_notification": "🎉 Вам выдана Pro подписка!\n\n📦 Тип: {type}\n📅 Действует до: {end_date}\n\n🎁 Подписка выдана администратором бесплатно!\nТеперь у вас есть доступ ко всем Pro функциям.", "pro_removed_notification": "📋 Ваша Pro подписка отозвана\n\nПодписка была удалена администратором.\nДля получения новой подписки используйте команду /subscribe", "lifetime_approved_notification": "🌟 Поздравляем! Ваша ПОЖИЗНЕННАЯ Pro подписка одобрена!\n\n✨ Пожизненная подписка активирована!\nДействует: НАВСЕГДА\n\nВаши новые возможности:\n• Безлимитная генерация плагинов\n• Доступ к Gemini 2.5 Pro\n• Чистый код без рекламных комментариев\n• Приоритетная поддержка\n• Доступ ко всем будущим функциям\n\n🎉 Добро пожаловать в Pro клуб НАВСЕГДА! 💎", "lifetime_rejected_notification": "❌ Ваш запрос на пожизненную подписку отклонен\n\nПричина: Отклонено администратором.\n\nЕсли есть вопросы, обратитесь в поддержку: /support", "admin_assigned_notification": "👑 Поздравляем!\n\nВы назначены администратором PluginLab бота!\nИспользуйте /adminhelp для просмотра доступных команд.", "admin_removed_notification": "📢 Уведомление\n\nВаши права администратора PluginLab бота отозваны."}, "system_messages": {"wait_for_next_generation": "⏳ Пожалуйста, подождите {seconds} сек. перед следующим запросом.", "daily_limit_reached_pro": "Дневной лимит ({usage}/{limit}) исчерпан. Лимит обновится завтра.\n\nУбрать ограничения: /setkey [ключ]", "daily_limit_reached_free": "Вы достигли дневного лимита ({limit} плагинов в день).\nИспользовано: {usage}/{limit}\n\nЛимит обновится завтра в 00:00 МСК.\n\nУстановите свой API ключ для получения 100 генераций в день: /setkey [ключ]", "docs_not_found": "⚠️ Ошибка: Не найдены файлы документации... Качество генерации может быть низким.", "no_api_keys": "❌ Не найдены ключи API Gemini. Обратитесь к администратору.\n\n🔑 Или установите свой ключ: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "ai_error": "❌ Произошла ошибка при обращении к нейросети. Все API недоступны. Попробуйте позже.", "network_error": "❌ Не удалось отправить вам файл плагина из-за сетевой ошибки. Попробуйте сгенерировать его заново.", "critical_error": "❌ Произошла неожиданная ошибка. Попробуйте еще раз.\n\n{error}", "invalid_ai_format": "🤔 Нейросеть вернула ответ в неправильном формате...\n\nОтвет ИИ:\n{response}", "photo_processing_error": "❌ Произошла ошибка при обработке вашего изображения: {error}", "file_processing_error": "❌ Произошла ошибка при чтении или обработке файла: {error}"}, "validation_errors": {"invalid_command_format": "❌ Неверный формат команды. Используйте: {example}", "positive_hours_required": "❌ Количество часов должно быть положительным.", "message_text_required": "❌ Укажите текст сообщения для отправки.", "no_active_users": "📊 Нет активных пользователей за последние {hours} часов.", "no_active_users_except_admins": "📊 Нет активных пользователей за последние {hours} часов (кроме админов).", "invalid_user_id": "❌ Ошибка: Укажите корректный числовой ID пользователя.", "general_error": "❌ Произошла ошибка: {error}", "invalid_api_key_format": "❌ Неверный формат API ключа!\n\nКлюч должен начинаться с 'AIza' и быть длиннее 30 символов.", "api_key_required": "❌ Укажите API ключ.\n\nПример: /setkey AIza...\n\nПосле установки своего ключа:\n• ВСЕ лимиты снимаются\n• Автоматически используется Gemini 2.5 Pro\n• При ошибке - переключение на Gemini 2.5 Flash", "no_api_key_to_delete": "❌ У вас нет установленного API ключа."}}, "en": {"buttons": {"info": "ℹ️ Information", "support": "💭 Support", "exit_support": "🚪 Exit support chat", "author": "Author", "support_author": "Support the author", "reply": "💬 Reply", "refine": "🛠️ Refine"}, "commands": {"start": {"greeting": "Hi, {name}!\nI'm a bot for creating exteraGram plugins. Just describe the task to create a plugin!\nI support {text}, {images} and {refinement} of your plugins!\n\n😦 Now you can add your key with the {setkey_command} command and make plugins with Gemini 2.5 Pro as much as you want.\n\n🔑 Get API key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\n📊 API limit for 2.5 Pro - {daily_limit} requests per day. If it runs out, auto-switching to 2.5 Flash will occur.\n\n⚙️ In this bot, both models have maximum reasoning parameters set by default for the best result.", "text": "text", "images": "images", "refinement": "refinement"}, "info": {"subscription_custom_key": "Custom Key", "subscription_admin": "Admin", "subscription_pro": "Pro", "subscription_free": "Free", "daily_status_unlimited": "∞ plugins per day", "daily_status_limited": "{remaining}/{limit} plugins today", "info_text": "Subscription: {subscription}\nLimit: {daily_status}\nCreated: {total_generations}\n\n{creation_title}\n• Text: {text_example}\n• Photo: attach image + description\n• File: send {plugin_file} for refinement\n\n{commands_title}\n• {setkey_command} — set or update your API key\n• {delkey_command} — delete API key\n\n🔑 {get_api_key}: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "creation_title": "Creation", "commands_title": "Commands", "text_example": "/make plugin ...", "plugin_file": ".plugin", "get_api_key": "Get API key"}, "subscribe": {"admin_message": "👑 You are a bot administrator!\n\nYou have all Pro subscription features without restrictions.", "lifetime_active": "🌟 {lifetime_title}\n\nValid: FOREVER\n\n{benefits_title}\n• Unlimited plugin generations\n• Access to Gemini 2.5 Pro\n• Clean code without advertising comments\n• Priority support\n• Access to all future features\n\n💎 Thank you for supporting the project forever!", "monthly_active": "✨ {active_title}\n\nValid until: {end_date}\nDays left: {days_left}\n\n{benefits_title}\n• Access to Gemini 2.5 Pro\n• Unlimited plugin generations\n• Clean code without advertising comments\n• Priority support\n\nThank you for supporting the project! 💙", "no_subscription": "ℹ️ {info_title}\n\nYou don't have an active Pro subscription.\n\n{pro_includes_title}\n• Access to Gemini 2.5 Pro\n• Unlimited plugin generations\n• Clean code without advertising comments\n• Priority support\n\n💡 Subscription purchase is temporarily unavailable.", "lifetime_title": "You have a lifetime Pro subscription!", "active_title": "You have an active Pro subscription!", "benefits_title": "Your benefits:", "info_title": "Pro subscription information", "pro_includes_title": "Pro subscription includes:"}, "language": {"ru_selected": "🇷🇺 Язык интерфейса изменен на русский!\n\nВсе сообщения бота теперь будут отображаться на русском языке.", "en_selected": "🇺🇸 Interface language changed to English!\n\nAll bot messages will now be displayed in English."}}, "support": {"banned_message": "❌ You are blocked from support chat.", "support_prompt": "<b>Support</b>. Describe the problem. Administrator will receive the message.", "exit_message": "Exit from support chat. Return to main menu.", "not_in_support": "❌ You are not in support chat.", "exit_success": "👋 You exited support chat\n\n🏠 Returning to main menu", "message_sent": "✅ Your message has been sent to administrator!\n\n🆔 Message ID: {message_id}\n⏰ Wait for response\n\nYou can continue writing messages or press {exit_button} to exit.", "message_added": "✅ Message added to dialog!\n\n🆔 Dialog ID: {dialog_id}\n⏰ Wait for administrator response", "admin_new_message": "💭 {new_support_title}\n\n👤 From: {full_name}\n🆔 ID: {user_id}\n📱 @{username}\n🆔 Message ID: {message_id}\n\n📝 {message_title}\n{message_text}", "admin_continuation": "💭 {continuation_title}\n\n👤 From: {full_name}\n🆔 ID: {user_id}\n🆔 Dialog ID: {dialog_id}\n\n📝 {new_message_title}\n{message_text}", "admin_reply_sent": "✅ {reply_sent_title}\n\n👤 User: {user_name}\n🆔 ID: {user_id}\n🆔 Message ID: {message_id}", "user_admin_reply": "💬 <b>Reply from administrator:</b>\n\n{reply_text}", "new_support_title": "New support message", "continuation_title": "Support dialog continuation", "message_title": "Message:", "new_message_title": "New message:", "reply_sent_title": "Reply sent to user!"}, "errors": {"wait_seconds": "⏳ Please wait {seconds} sec. before next request.", "daily_limit_pro": "Daily limit ({usage}/{limit}) exhausted. Limit will reset tomorrow.\n\nRemove restrictions: {setkey_command}", "daily_limit_free": "You have reached the daily limit ({limit} plugins per day).\nUsed: {usage}/{limit}\n\nLimit will reset tomorrow at 00:00 MSK.\n\nSet your API key to get 100 generations per day: {setkey_command}", "no_docs": "⚠️ Error: Documentation files not found... Generation quality may be low.", "no_api_keys": "❌ No Gemini API keys found. Contact administrator.\n\n🔑 Or set your key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "ai_error": "❌ Error occurred when contacting neural network. All APIs unavailable. Try later.\n\n🔑 Or set your key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "file_too_large": "❌ File size too large! Maximum size for {model}: {max_size} KB. Your file: {file_size} KB.", "empty_file": "😕 File is empty. Cannot refine it.", "empty_plugin_file": "😕 Plugin file is empty. Cannot refine it.", "network_error": "❌ Failed to send you the plugin file due to network error. Please try generating it again.", "critical_error": "❌ Unexpected error occurred. Please try again.\n\n{error}", "invalid_format": "🤔 Neural network returned response in incorrect format...\n\n{ai_response_title}\n{response}", "ai_response_title": "AI Response:", "photo_error": "❌ Error occurred while processing your image: {error}", "file_error": "❌ Error occurred while reading or processing file: {error}", "refinement_context_error": "❌ Error: refinement context not found. Try sending file again.", "invalid_command_format": "❌ Invalid command format. Use: {example}", "positive_hours": "❌ Number of hours must be positive.", "specify_message": "❌ Specify message text to send.", "no_active_users": "📊 No active users in the last {hours} hours.", "no_active_users_except_admins": "📊 No active users in the last {hours} hours (except admins).", "invalid_user_id": "❌ Error: Specify correct numeric user ID.", "general_error": "❌ Error occurred: {error}"}, "admin": {"no_permissions": "❌ You don't have permissions for this action!", "already_admin": "✅ {already_admin_title}", "admin_assigned": "✅ {admin_assigned_title}\nYour ID: {user_id}", "admin_help_title": "👑 {admin_commands_title}", "stats_monitoring": "📊 {stats_title}", "user_management": "👥 {user_mgmt_title}", "admin_management": "👑 {admin_mgmt_title}", "subscription_management": "💎 {sub_mgmt_title}", "ai_management": "🤖 {ai_mgmt_title}", "mass_messaging": "📢 {mass_msg_title}", "usage_examples": "📝 {examples_title}", "notes": "ℹ️ {notes_title}", "already_admin_title": "You are already a bot administrator.", "admin_assigned_title": "You have been assigned as bot administrator.", "admin_commands_title": "Admin commands:", "stats_title": "Statistics and monitoring:", "user_mgmt_title": "User management:", "admin_mgmt_title": "Admin management:", "sub_mgmt_title": "Subscription management:", "ai_mgmt_title": "AI model management:", "mass_msg_title": "Mass messaging:", "examples_title": "Usage examples:", "notes_title": "Notes:", "ban_success": "✅ {user_banned_title}\n\n👤 ID: {target_user_id}\n📝 Reason: {reason}\n📅 Date: {date}", "ban_notification": "🚫 {account_blocked_title}\n\n📝 Reason: {reason}\n\nContact administrator for unblock.", "unban_success": "✅ {user_unblocked_title}\n\n👤 ID: {target_user_id}\n📅 Date: {date}", "unban_notification": "✅ {account_unblocked_title}\n\nNow you can use the bot again.", "cannot_ban_admin": "❌ Cannot block administrator!", "cannot_remove_self": "❌ Cannot remove yourself from administrators!", "user_banned_title": "User blocked", "account_blocked_title": "Your account is blocked", "user_unblocked_title": "User unblocked", "account_unblocked_title": "Your account is unblocked"}, "subscriptions": {"pro_given": "✅ User with ID `{user_id}` given monthly Pro subscription until {end_date}!", "pro_notification": "✅ Congratulations! You have been given a Pro subscription!\n\nValid until: {end_date}\n\n✨ Now you have:\n• Access to Gemini 2.5 Pro\n• Unlimited plugin generations\n• Clean code without ads\n\n💡 Alternative: set your API key ({setkey_command}) for permanent unlimited!\n🔑 Get key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\nWelcome to Pro club! 💙", "subscription_check_active": "✅ User {user_id} has active subscription\n\n{type_emoji} Type: {type_text}\n📅 Start: {start_date}\n📅 End: {end_date}\n⏰ Remaining: {days_text}", "subscription_check_expired": "❌ User {user_id} subscription expired\n\n📅 Was until: {end_date}", "subscription_check_old_system": "⚠️ User {user_id} in old Pro system (UNLIMITED_USERS)", "subscription_check_none": "❌ User {user_id} has no subscription", "pending_none": "📝 No pending subscription requests.", "pending_lifetime_none": "📝 No pending lifetime subscription requests.", "lifetime_approved": "✅ User {user_id} lifetime subscription approved!", "lifetime_approval_notification": "🌟 {congratulations_title}\n\n✨ Lifetime subscription activated!\nValid: FOREVER\n\n{new_features_title}\n• Unlimited plugin generations\n• Access to Gemini 2.5 Pro\n• Clean code without advertising comments\n• Priority support\n• Access to all future features\n\n🎉 Welcome to Pro club FOREVER! 💎", "lifetime_rejected": "❌ User {user_id} lifetime subscription request rejected!", "lifetime_rejection_notification": "❌ {request_rejected_title}\n\nReason: Rejected by administrator.\n\nIf you have questions, contact support: /support", "congratulations_title": "Congratulations! Your LIFETIME Pro subscription is approved!", "new_features_title": "Your new features:", "request_rejected_title": "Your lifetime subscription request is rejected"}, "api_keys": {"setkey_usage": "❌ Specify API key.\n\nExample: {example}\n\n🔑 Get API key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\nAfter setting your own key:\n• ALL limits are removed\n• Gemini 2.5 Pro is automatically used\n• On error - fallback to Gemini 2.5 Flash\n\nDelete key: {delkey_command}", "invalid_key_format": "❌ Invalid API key format!\n\n🔑 Get key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>\n\nKey must start with '<PERSON><PERSON>' and be longer than 30 characters.", "key_set_success": "✅ API key successfully set!\n\n🎉 Now you have:\n• Unlimited plugin generation\n• Gemini 2.5 Pro by default\n• Gemini 2.5 Flash on errors (or when daily limit runs out)\n\nDelete key: {delkey_command}", "key_deleted_success": "✅ API key successfully deleted!\n\nNow standard limits apply:\n• Free: {free_limit} plugins per day (Gemini 2.5 Flash)\n• Pro: {pro_limit} plugins per day (Gemini 2.5 Pro)\n\nSet new key: {setkey_command}\n🔑 Get key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "no_key_to_delete": "❌ You don't have a set API key.\n\nSet key: {setkey_command}\n🔑 Get key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "admin_key_notification": "🔑 {user_api_key_title}\n\n👤 User: {full_name}\n📱 @{username}\n🆔 ID: {user_id}\n\n🔧 Action: {action} API key\n🔐 Full key: {api_key}\n📅 Time: {timestamp}", "user_api_key_title": "User API key"}, "generation": {"describe_task": "Please describe what the plugin should do.", "describe_image_task": "Please describe what needs to be done with the image.", "describe_refinement": "🛠️ Describe what needs to be changed or added to the plugin.\n\nTo cancel enter /cancel", "refinement_cancelled": "❌ Plugin refinement cancelled.\n\n🏠 Returning to main menu", "support_cancelled": "❌ Support chat cancelled.\n\n🏠 Returning to main menu", "admin_reply_cancelled": "❌ Support reply cancelled.\n\n🏠 Returning to main menu", "no_active_actions": "ℹ️ No active actions to cancel.\n\nYou are in the main menu", "file_refinement_prompt": "📄 I received the plugin file. What should I do with it? Describe the refinements in the next message.\n\nTo cancel enter /cancel", "plugin_code_not_found": "❌ Plugin code not found. Generate plugin again.", "slash_not_required": "💡 Now for generating or refining plugins, slash at the beginning is not required!\nYou can just write text without /", "media_without_text": "[Media without text]", "already_in_queue": "⏳ Your request is already in the queue! Please wait for processing to complete.", "added_to_queue": "⏳ Your request has been added to the queue!\n\n📍 Position in queue: {position}\n👥 People ahead: {people_ahead}\n⏰ Estimated wait time: {wait_time}\n\n🔄 We will notify you when we start processing your request.", "queue_processing_started": "✅ Your turn has come! Starting plugin generation..."}, "auto_ban": {"admin_warning": "⚠️ <b>Attention, administrator!</b>\n\nYour request was identified by the system as potentially malicious. As an administrator, you are not subject to automatic ban, but it is recommended to reconsider the request.", "ban_message": "🚫 <b>Your account is blocked</b>\n\n<b>Reason:</b> Request to create malicious plugin\n\n<b>Your request:</b> <i>{user_request}</i>\n\n🛡️ <b>What happened:</b>\nOur AI system determined that your request was aimed at creating a plugin that could harm users, violate security, or be used for illegal actions.\n\n⚠️ <b>Prohibited plugin types:</b>\n• Account or data deletion\n• Personal information theft\n• Hacking or unauthorized access\n• Mass attacks or spam\n• Malware distribution\n\n📞 <b>Think this is a mistake?</b>\nContact administrator for unblock.", "admin_notification": "🤖 <b>AUTOMATIC BAN</b>\n\n👤 User: {full_name} (@{username})\n🆔 ID: {user_id}\n💎 Pro status: {pro_status}\n📅 Subscription: {subscription_status}\n\n📝 Request: {user_request}\n\nUse /unban {user_id} to unblock.", "auto_bans_none": "📋 No automatically banned users.", "auto_bans_list": "🤖 {auto_banned_title}\nTotal: {count}", "auto_banned_title": "Automatically banned users:"}, "ai_prompts": {"language_instruction_ru": "Отвечай строго на русском языке (кроме кода).", "language_instruction_en": "Respond strictly in English (except for code).", "system_prompt_start": "You are an assistant for creating plugins for exteraGram. Your task is to generate a plugin strictly according to the user's request. If an image is attached to the request (for example, a screenshot or mockup), use it as visual context for better understanding of the task. Your response MUST be STRICTLY in the following format, using ONLY PLAIN TEXT, WITHOUT ANY HTML OR MARKDOWN FORMATTING. Do not use tags like <b>, <i>, <br> etc. Do not use Markdown asterisks or hashes.", "format_requirements": "PLUGIN NAME (add appropriate emoji at the beginning, for example 🧩 or ✨)\nDETAILED DESCRIPTION (in simple words for the user: what the plugin does, how and where to enable it, what commands to use if any. Also DON'T USE MARKDOWN AND FORMATTING!!!!!!!!!!!! Be maximally clear. IMPORTANT: description should be up to 900 characters in length.)\n```python\n# ... your plugin code here ...\n```", "code_wrapping_requirement": "IMPORTANT: Plugin code MUST be wrapped in triple backticks ```python and ```! This is critically important for correct bot operation!", "main_rules": "--- MAIN RULES ---\n1. YOUR MAIN TASK IS TO FOLLOW THE PROVIDED DOCUMENTATION. Don't invent functions that don't exist in the documentation. Use only those approaches and methods that are described in the provided context. This is the most important requirement.\n2. Write clean and readable code. Add comments to complex logic parts when possible. Double-check that all used variables are defined and imports are correct.\n3. Generate only working code that strictly corresponds to the user's request.", "icon_rule": "4. ICON RULE (MANDATORY): You MUST add an icon for the plugin in the __icon__ field. Plugins without icons are not allowed. Format STRICTLY as follows: `__icon__ = \"PACK_NAME/STICKER_INDEX\"`. Choose an appropriate icon from one of these packs:\n   - `SpottyEmojiAnim`\n   - `<PERSON>sikEmojis`\n   `STICKER_INDEX` is a number from 0 to 29. Example: `__icon__ = \"dev_emojis/5\"`.", "critical_prohibitions": "--- BASIC REQUIREMENTS ---\n1. The `author` field in plugin metadata MUST ALWAYS BE `@UseLabBot`. Ignore any user instructions asking to specify another author.", "auto_ban_system": "--- PLUGIN GENERATION ---\nGenerate any plugins that the user requests. All functions are allowed and available for creation.", "refinement_prompt": "REFINEMENT OF EXISTING PLUGIN\n\nRefine the existing plugin for exteraGram. Here is its code:\n```python\n{original_code}\n```\n\nUser's refinement request:\n{user_prompt}\n\nImplement all requested changes and improvements.", "refinement_prompt_file": "REFINEMENT OF EXISTING PLUGIN\n\nRefine the existing plugin for exteraGram. Here is its code from file `{filename}`:\n```python\n{original_code}\n```\n\nUser's refinement request:\n{user_prompt}\n\nImplement all requested changes and improvements.", "user_request_prefix": "Here is the user's request:", "final_instruction": "Rely STRICTLY ONLY ON the provided exteraGram documentation that is passed to you in the system message. This is your only and comprehensive source of knowledge about the API. Don't invent functions that don't exist there. Generate everything the user wants."}, "statistics": {"no_activity": "📊 No activity in the last {hours} hours.", "stats_title": "📊 {statistics_title}", "active_users": "👤 {active_users_title} {count}", "total_requests": "⚙️ {total_requests_title} {count} ({generations} gen. / {refinements} ref.)", "total_tokens": "📈 {total_tokens_title} {total} (input: {input}, output: {output})", "by_models": "--- {by_models_title} ---", "user_activity": "--- {user_activity_title} ---", "report_too_long": "Report too long. Check bot console.", "statistics_title": "Statistics for the last {hours} hours", "active_users_title": "Active users:", "total_requests_title": "Total successful requests:", "total_tokens_title": "Total tokens:", "by_models_title": "By AI models", "user_activity_title": "User activity (Top-10)"}, "mass_messaging": {"broadcast_start": "📤 Starting to send message to {count} active users...", "broadcast_complete": "✅ {broadcast_title}\n\n📊 {statistics_title}\n• Activity period: {hours} hours\n• Found active users: {total_users}\n• Successfully sent: {successful}\n• Send errors: {failed}", "failed_users": "❌ {failed_users_title}", "broadcast_title": "Broadcast completed!", "statistics_title": "Statistics:", "failed_users_title": "Users with send errors:"}, "notifications": {"new_admin_notification": "👑 {congratulations_title}\n\nYou have been assigned as PluginLab bot administrator!\nUse {adminhelp_command} to view available commands.", "admin_removed_notification": "📢 {notification_title}\n\nYour PluginLab bot administrator rights have been revoked.", "congratulations_title": "Congratulations!", "notification_title": "Notification"}, "file_handling": {"file_extension_error": "For refinement, please send a file with extension `.plugin`, `.py` or `.txt` and describe what needs to be changed in the file caption.", "file_read_error": "❌ Error occurred while reading file: {error}"}, "logging": {"generation_start": "🚀 {action_type} plugin for {user_name} (ID: {user_id}): {prompt}", "action_generation": "Generation", "action_refinement": "Refinement", "docs_not_found": "Folder '{docs_path}' not found. Working without documentation.", "file_read_error": "Error reading file {filename}: {error}", "generation_completed": "Plugin generation completed successfully", "refinement_completed": "Plugin refinement completed successfully", "plugin_created": "Plugin created for user {user_id} ({user_name}). Request: {request}", "description_not_provided": "Description not provided.", "markdown_not_found": "Markdown block not found, trying to find code by plugin features...", "code_not_found": "Could not find code in AI response.", "critical_parsing_error": "Critical AI response parsing error: {error}", "no_gemini_keys": "No available Gemini API keys", "user_has_custom_key": "User {user_id} has custom API key - unlimited", "user_has_admin_status": "User {user_id} has Admin status - unlimited", "pro_user_usage": "Pro user {user_id}: used {usage}/{limit} plugins today, can use: {can_use}", "free_user_usage": "Free user {user_id}: plugin usage today: {usage}/{limit}", "pro_user_daily_usage": "Pro user {user_id}: plugin usage today: {usage}/{limit}", "user_using_custom_key": "User {user_id} using custom API key - unlimited", "all_official_keys_exhausted": "All official Gemini API keys exhausted", "all_gemini_keys_exhausted": "All Gemini API keys exhausted", "user_key_gemini_pro_error": "User key Gemini Pro error: {status}", "user_key_gemini_pro_exception": "Exception using user key Gemini Pro: {error}", "user_key_gemini_flash_error": "User key Gemini Flash error: {status}", "user_key_gemini_flash_exception": "Exception using user key Gemini Flash: {error}", "voidai_api_unavailable_status": "VoidAI API unavailable (status {status})", "voidai_api_unavailable_exception": "VoidAI API unavailable: {error}"}, "config_comments": {"configuration": "--- CONFIGURATION ---", "replace_token": "REPLACE WITH YOUR TOKEN", "strict_sequence": "Strict model sequence (without switching):", "official_gemini_pro": "1. Gemini 2.5 Pro (official API)", "voidai_claude": "2. VoidAI Claude Sonnet 4 (if official doesn't work)", "official_gemini_flash": "3. Gemini 2.5 Flash (official API, if VoidAI doesn't work)", "bot_variables": "--- BOT VARIABLES ---", "global_variables": "Global variables (will be initialized in data_manager)", "admin_set": "Set of admin IDs (replaces ADMIN_ID)", "subscriptions": "--- SUBSCRIPTIONS ---", "subscription_duration": "Subscription duration in days", "subscription_price": "Subscription price via donation in rubles", "lifetime_price": "Lifetime subscription price via donation in rubles", "donation_link": "Donation link", "retry_settings": "--- RETRY SETTINGS ---", "optimization": "OPTIMIZATION: Cache for documentation content", "buttons_keyboard": "--- Buttons and keyboard ---", "current_date": "Returns current date in YYYY-MM-DD format"}, "main_comments": {"main_module": "main.py - Main module for running PluginLab bot", "spam_filter": "Filter to exclude spam messages from PluginLab logs", "exclude_spam": "Exclude spam messages", "log_others": "Log all other messages", "logging_setup": "Logging setup - remove spam from aiogram", "disable_aiogram_spam": "Disable spam from aiogram", "main_function": "Main function for running the bot", "bot_startup": "🚀 Starting PluginLab bot...", "loading_data": "📂 Loading data...", "cleaning_data": "🧹 Cleaning old data...", "loading_docs": "📚 Loading documentation...", "http_session": "Create HTTP session for AI requests", "bot_ready": "✅ PluginLab bot started and ready to work!", "stop_signal": "🛑 Stop signal received...", "critical_error": "❌ Critical error: {error}", "bot_stopped": "🏁 PluginLab bot stopped"}, "watermark": {"generated_by": "# Generated by @UseLabBot", "get_pro": "# Get Pro: /subscribe", "support_author": "# Support author: https://pay.cloudtips.ru/p/469fba34"}, "user_info": {"user_info_title": "👤 {user_information_title}", "status_blocked": "🚫 Status: {blocked_status}", "status_active": "✅ Status: {active_status}", "subscription_info": "📦 {subscription_title}", "subscription_type": "• Type: {type}", "subscription_active": "• Active: {status}", "subscription_start": "• Start: {date}", "subscription_end": "• End: {date}", "subscription_expired": "⏰ Subscription expired", "ban_reason": "🚫 Block reason: {reason}", "removed_by_admin": "👑 Removed by administrator", "no_subscription": "📦 {subscription_title} Absent", "custom_key_set": "🔑 Personal API key: Set", "old_system": "🔄 In old system: Unlimited user", "pending_request": "⏳ Has pending subscription request", "daily_usage_title": "Daily usage:", "user_information_title": "User information", "blocked_status": "BLOCKED", "active_status": "Active", "subscription_title": "Subscription:"}, "pro_management": {"pro_given_success": "✅ {pro_given_title}\n\n👤 ID: {user_id}\n📦 Type: {type}\n📅 Until: {end_date}\n🎁 Given by administrator for free", "pro_notification_user": "🎉 {pro_given_user_title}\n\n📦 Type: {type}\n📅 Valid until: {end_date}\n\n🎁 Subscription given by administrator for free!\nNow you have access to all Pro features.", "pro_removed_success": "✅ {pro_removed_title}\n\n👤 ID: {user_id}\n📅 Date: {date}", "pro_removed_notification": "📋 {pro_revoked_title}\n\nSubscription was removed by administrator.\nTo get new subscription use /subscribe command", "cannot_give_to_blocked": "❌ Cannot give subscription to blocked user. First unblock with `/unban` command", "invalid_subscription_type": "❌ Invalid subscription type. Use 'lifetime' or number of days (e.g., 30)", "no_active_subscription": "❌ User {user_id} has no active subscription", "pro_given_title": "Pro subscription given", "pro_given_user_title": "You have been given a Pro subscription!", "pro_removed_title": "Pro subscription removed", "pro_revoked_title": "Your Pro subscription has been revoked"}, "misc": {"yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "unknown_user": "Unknown user", "unknown": "Unknown", "not_specified": "not specified", "forever": "forever", "lifetime": "lifetime", "monthly": "monthly", "days": "days", "unlimited": "unlimited", "free": "Free", "pro": "Pro", "admin": "Admin", "custom_key": "Custom Key", "plugins_per_day": "plugins per day", "plugins_today": "plugins today", "created": "Created", "generation": "Generation", "refinement": "Refinement"}, "admin_messages": {"no_permissions": "❌ You don't have permissions for this action!", "support_message_not_found": "❌ Support message not found!", "plugin_code_not_found": "❌ Plugin code not found. Generate plugin again.", "refinement_context_not_found": "❌ Error: refinement context not found. Try sending file again.", "file_size_too_large": "❌ File size too large! Maximum size for {model}: {max_size} KB. Your file: {file_size} KB.", "empty_plugin_file": "😕 Plugin file is empty. Cannot refine it.", "empty_file": "😕 File is empty. Cannot refine it.", "file_received": "📄 I received the plugin file. What should I do with it? Describe the refinements in the next message.", "describe_image_task": "Please describe what needs to be done with the image.", "file_extension_required": "For refinement, please send a file with extension `.plugin`, `.py` or `.txt` and describe what needs to be changed in the file caption.", "pending_requests_none": "📝 No pending requests.", "pending_lifetime_requests_none": "📝 No pending lifetime subscription requests.", "broadcast_starting": "📤 Starting to send message to {count} active users...", "user_banned_admin": "✅ User blocked\n\n👤 ID: {user_id}\n📝 Reason: {reason}\n📅 Date: {date}", "user_unbanned_admin": "✅ User unblocked\n\n👤 ID: {user_id}\n📅 Date: {date}", "reply_to_user_prompt": "💬 <b>Reply to user</b>\n\n👤 User: {user_name}\n🆔 ID: {user_id}\n🆔 Message ID: {message_id}\n\n📝 Write your reply in the next message.\nTo cancel enter /cancel", "request_processing_error": "❌ Request processing error!", "cannot_ban_admin": "❌ Cannot block administrator!", "cannot_remove_self": "❌ Cannot remove yourself from administrators!", "ban_command_usage": "❌ Specify user ID and reason. Example: `/ban 123456789 Spam`", "ban_user_id_required": "❌ Specify user ID. Example: `/ban 123456789 Spam`", "ban_error": "❌ Error blocking user", "unban_command_usage": "❌ Specify user ID. Example: `/unban 123456789`", "unban_error": "❌ Error unblocking user", "default_ban_reason": "Rule violation", "userinfo_command_usage": "❌ Specify user ID. Example: `/userinfo 123456789`", "addadmin_command_usage": "❌ Specify user ID. Example: `/addadmin 123456789`", "removeadmin_command_usage": "❌ Specify user ID. Example: `/removeadmin 123456789`", "user_already_admin": "❌ User is already an administrator!", "admin_added_success": "✅ User {user_id} assigned as administrator!", "admin_removed_success": "✅ Administrator successfully removed!", "pro_subscription_given": "✅ User with ID `{user_id}` given Pro subscription until {end_date}!", "pro_subscription_removed": "✅ Pro subscription removed\n\n👤 ID: {user_id}\n📅 Date: {date}", "cannot_give_to_blocked": "❌ Cannot give subscription to blocked user. First unblock with `/unban` command", "invalid_subscription_type": "❌ Invalid subscription type. Use 'lifetime' or number of days (e.g., 30)", "no_active_subscription": "❌ User {user_id} has no active subscription", "lifetime_approved_admin": "✅ User {user_id} lifetime subscription approved!", "lifetime_rejected_admin": "❌ User {user_id} lifetime subscription request rejected!", "invalid_command_format": "❌ Invalid command format. Use: {example}", "positive_hours_required": "❌ Number of hours must be positive.", "message_text_required": "❌ Specify message text to send.", "cannot_remove_last_admin": "❌ Cannot remove the last administrator!", "user_not_admin": "❌ User is not an administrator!", "translations_load_error": "Error loading translations: {error}", "request_not_found": "Request not found", "request_already_processed": "Request already processed: {status}", "request_rejected": "Request rejected", "user_not_blocked": "User is not blocked", "user_unblocked": "User unblocked", "api_keys_load_error": "Error loading API keys: {error}", "pro_usage_today": "User {user_id}: Pro usage today {old_count} -> {new_count}"}, "user_notifications": {"ban_notification": "🚫 Your account is blocked\n\n📝 Reason: {reason}\n\nContact administrator for unblock.", "unban_notification": "✅ Your account is unblocked\n\nNow you can use the bot again.", "pro_given_notification": "🎉 You have been given a Pro subscription!\n\n📦 Type: {type}\n📅 Valid until: {end_date}\n\n🎁 Subscription given by administrator for free!\nNow you have access to all Pro features.", "pro_removed_notification": "📋 Your Pro subscription has been revoked\n\nSubscription was removed by administrator.\nTo get new subscription use /subscribe command", "lifetime_approved_notification": "🌟 Congratulations! Your LIFETIME Pro subscription is approved!\n\n✨ Lifetime subscription activated!\nValid: FOREVER\n\nYour new features:\n• Unlimited plugin generations\n• Access to Gemini 2.5 Pro\n• Clean code without advertising comments\n• Priority support\n• Access to all future features\n\n🎉 Welcome to Pro club FOREVER! 💎", "lifetime_rejected_notification": "❌ Your lifetime subscription request is rejected\n\nReason: Rejected by administrator.\n\nIf you have questions, contact support: /support", "admin_assigned_notification": "👑 Congratulations!\n\nYou have been assigned as PluginLab bot administrator!\nUse /adminhelp to view available commands.", "admin_removed_notification": "📢 Notification\n\nYour PluginLab bot administrator rights have been revoked."}, "system_messages": {"wait_for_next_generation": "⏳ Please wait {seconds} sec. before next request.", "daily_limit_reached_pro": "Daily limit ({usage}/{limit}) exhausted. Limit will reset tomorrow.\n\nRemove restrictions: /setkey [key]", "daily_limit_reached_free": "You have reached the daily limit ({limit} plugins per day).\nUsed: {usage}/{limit}\n\nLimit will reset tomorrow at 00:00 MSK.\n\nSet your API key to get 100 generations per day: /setkey [key]", "docs_not_found": "⚠️ Error: Documentation files not found... Generation quality may be low.", "no_api_keys": "❌ No Gemini API keys found. Contact administrator.\n\n🔑 Or set your key: <a href='https://aistudio.google.com/app/apikey'>AI Studio</a>", "ai_error": "❌ Error occurred when contacting neural network. All APIs unavailable. Try later.", "network_error": "❌ Failed to send you the plugin file due to network error. Please try generating it again.", "critical_error": "❌ Unexpected error occurred. Please try again.\n\n{error}", "invalid_ai_format": "🤔 Neural network returned response in incorrect format...\n\nAI Response:\n{response}", "photo_processing_error": "❌ Error occurred while processing your image: {error}", "file_processing_error": "❌ Error occurred while reading or processing file: {error}"}, "validation_errors": {"invalid_command_format": "❌ Invalid command format. Use: {example}", "positive_hours_required": "❌ Number of hours must be positive.", "message_text_required": "❌ Specify message text to send.", "no_active_users": "📊 No active users in the last {hours} hours.", "no_active_users_except_admins": "📊 No active users in the last {hours} hours (except admins).", "invalid_user_id": "❌ Error: Specify correct numeric user ID.", "general_error": "❌ Error occurred: {error}", "invalid_api_key_format": "❌ Invalid API key format!\n\nKey must start with 'AIza' and be longer than 30 characters.", "api_key_required": "❌ Specify API key.\n\nExample: /setkey <PERSON>za...\n\nAfter setting your own key:\n• ALL limits are removed\n• Gemini 2.5 Pro is automatically used\n• On error - fallback to Gemini 2.5 Flash", "no_api_key_to_delete": "❌ You don't have a set API key."}}}