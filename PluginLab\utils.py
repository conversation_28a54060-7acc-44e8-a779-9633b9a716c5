# utils.py - Вспомогательные функции для PluginLab
import asyncio
import logging
import os
import time
from aiogram import Bot, types
from aiogram.exceptions import TelegramBadRequest, AiogramError
from aiogram.utils.markdown import hbold, hitalic, hcode, hlink

from . import config
from .data_manager import get_user_daily_pro_usage, get_subscription_info, is_subscription_active, get_user_daily_total_usage, get_user_daily_free_usage, get_user_daily_custom_key_usage, has_user_custom_key


async def load_docs_once():
    """Загружает всю документацию (*.txt) из папки /docs в кэш при старте бота."""
    docs_path = os.path.join(config.BOT_DIR, "docs")
    if not os.path.exists(docs_path) or not os.path.isdir(docs_path):
        config.DOCS_CONTENT_CACHE = ""
        return
    
    full_content = []
    for filename in sorted(os.listdir(docs_path)):
        if filename.endswith(".txt"):
            try:
                with open(os.path.join(docs_path, filename), 'r', encoding='utf-8') as f:
                    content = f.read()
                    full_content.append(f"--- НАЧАЛО ФАЙЛА: {filename} ---\n\n{content}\n\n--- КОНЕЦ ФАЙЛА: {filename} ---")
            except Exception as e:
                logging.error(f"Ошибка чтения файла {filename}: {e}")
    
    config.DOCS_CONTENT_CACHE = "\n\n".join(full_content)


def log_user_action(user: types.User, is_refinement: bool, model_used: str, success: bool):
    """Логирует действия пользователей в читаемом формате"""
    user_display = user.full_name
    if user.username:
        user_display += f" (@{user.username})"

    action_type = "Доработка" if is_refinement else "Генерация"
    status = "✅" if success else "❌"

    # Определяем статус пользователя
    from .ai_client import is_user_pro
    pro_status = "PRO" if is_user_pro(user.id) else "FREE"

    # Определяем эмодзи в зависимости от модели
    if "gemini-2.0-flash" in model_used:
        model_emoji = "⚡"  # Flash
    elif "gemini-pro" in model_used:
        model_emoji = "🌟"  # Официальный Gemini Pro
    elif "voidai" in model_used.lower():
        model_emoji = "🚀"  # VoidAI Pro
    else:
        model_emoji = "⚡"  # Flash по умолчанию

    # Сокращаем название модели для читаемости
    short_model = model_used.replace("gemini-2.0-flash-exp", "flash").replace("gemini-pro", "pro")



def log_stats_entry(user: types.User, is_refinement: bool, model_used: str, usage_info: dict | None, success: bool):
    """Логирует статистику использования бота"""
    if usage_info is None:
        usage_info = {}

    # Логируем действие пользователя в читаемом формате
    log_user_action(user, is_refinement, model_used, success)

    # Определяем токены в зависимости от модели
    if model_used == config.VOIDAI_MODEL_NAME:
        # VoidAI использует формат OpenAI
        prompt_tokens = usage_info.get('prompt_tokens', 0)
        completion_tokens = usage_info.get('completion_tokens', 0)
    else:
        # Официальный Gemini API и Flash используют свой формат
        prompt_tokens = usage_info.get('promptTokenCount', 0)
        completion_tokens = usage_info.get('candidatesTokenCount', 0)

    entry = {
        "timestamp": int(time.time()),
        "user_id": user.id,
        "user_full_name": user.full_name,
        "user_username": user.username,
        "request_type": "refinement" if is_refinement else "generation",
        "model_used": model_used,
        "tokens_prompt": prompt_tokens,
        "tokens_completion": completion_tokens,
        "success": success
    }
    config.BOT_STATS.append(entry)
    
    # Сохраняем статистику
    from .data_manager import save_stats_data
    save_stats_data()


def get_user_stats_summary(user_id: int) -> dict:
    """Возвращает краткую статистику пользователя"""
    # Проверяем подписку
    subscription_info = get_subscription_info(user_id)
    is_admin = config.is_admin(user_id)
    has_custom_key = has_user_custom_key(user_id)
    is_pro = is_admin or (subscription_info['has_subscription'] and subscription_info['is_active']) or user_id in config.UNLIMITED_USERS

    if has_custom_key:
        subscription = "Custom Key"
        subscription_details = "Собственный API ключ"
        # Для пользователей с собственными ключами показываем оставшиеся плагины из лимита 30
        custom_key_usage_today = get_user_daily_custom_key_usage(user_id)
        daily_remaining = max(0, config.DAILY_CUSTOM_KEY_LIMIT - custom_key_usage_today)
    elif is_admin:
        subscription = "Admin"
        subscription_details = "Администратор"
        daily_remaining = "∞"
    elif is_pro:
        subscription_type = subscription_info.get('subscription_type', 'monthly')
        if subscription_type == 'lifetime':
            subscription = "Pro"
            subscription_details = "Пожизненная"
        else:
            days_left = subscription_info['days_left']
            subscription = "Pro"
            subscription_details = f"Активна ({days_left} дн.)"

        # Для Pro пользователей показываем оставшиеся плагины из лимита 50
        total_usage_today = get_user_daily_total_usage(user_id)
        daily_remaining = max(0, config.DAILY_PRO_LIMIT - total_usage_today)
    else:
        # Бесплатный пользователь
        subscription = "Free"
        subscription_details = "Бесплатный доступ"

        # Для бесплатных пользователей показываем оставшиеся плагины из лимита 3
        free_usage_today = get_user_daily_free_usage(user_id)
        daily_remaining = max(0, config.DAILY_FREE_LIMIT - free_usage_today)

    # Общая статистика из BOT_STATS
    user_total_generations = len([entry for entry in config.BOT_STATS if entry.get("user_id") == user_id and entry.get("success", False)])

    return {
        "subscription": subscription,
        "subscription_details": subscription_details,
        "daily_remaining": daily_remaining,
        "total_generations": user_total_generations,
        "subscription_info": subscription_info,
        "has_custom_key": has_custom_key
    }


async def notify_user_cooldown_end(user_id: int, bot: Bot, delay: float):
    """Уведомляет пользователя об окончании кулдауна"""
    await asyncio.sleep(delay)
    try:
        await bot.send_message(user_id, "✅ Ваш лимит на создание плагинов восстановлен! Можете снова отправлять запросы.")
    except (TelegramBadRequest, AiogramError) as e:
        # Игнорируем ошибки отправки уведомления
        pass
    finally:
        if user_id in config.NOTIFICATION_TASKS_PENDING:
            config.NOTIFICATION_TASKS_PENDING.remove(user_id)


# --- ФУНКЦИИ ДЛЯ РАБОТЫ С НЕСКОЛЬКИМИ АДМИНАМИ ---

async def send_to_all_admins(bot: Bot, message_text: str, reply_markup=None):
    """Отправляет сообщение всем админам"""
    if not config.ADMIN_IDS:
        return

    successful_sends = 0
    failed_sends = 0

    for admin_id in config.ADMIN_IDS:
        try:
            await bot.send_message(admin_id, message_text, reply_markup=reply_markup)
            successful_sends += 1
        except Exception as e:
            logging.error(f"Ошибка отправки сообщения админу {admin_id}: {e}")
            failed_sends += 1

    return successful_sends, failed_sends


async def send_photo_to_all_admins(bot: Bot, photo_file_id: str, caption: str = None):
    """Отправляет фото всем админам"""
    if not config.ADMIN_IDS:
        return

    successful_sends = 0
    failed_sends = 0

    for admin_id in config.ADMIN_IDS:
        try:
            await bot.send_photo(admin_id, photo_file_id, caption=caption)
            successful_sends += 1
        except Exception as e:
            logging.error(f"Ошибка отправки фото админу {admin_id}: {e}")
            failed_sends += 1

    return successful_sends, failed_sends
