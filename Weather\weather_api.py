"""
Модуль для работы с WeatherAPI.com API.

Содержит функции для получения данных о погоде:
- fetch_current_weather() - получает текущую погоду
- fetch_forecast_weather() - получает прогноз на 3 дня
- fetch_astronomy_data() - получает данные о восходе/закате
- parse_current_weatherapi() - разбирает текущую погоду
- parse_forecast_weatherapi() - разбирает прогноз на 3 дня
- parse_today_weatherapi() - разбирает почасовые данные для сегодня
- format_sun_times() - форматирует время восхода/заката
"""

import asyncio
import aiohttp
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from zoneinfo import ZoneInfo

from weather_config import (
    WEATHERAPI_KEY,
    WEATHERAPI_CURRENT_URL,
    WEATHERAPI_FORECAST_URL,
    WEATHERAPI_ASTRONOMY_URL,
    LAT,
    LON,
    TZ
)

# Настройка логирования
logger = logging.getLogger(__name__)

# Кэш для хранения последних успешных ответов
_weather_cache: Dict[str, Dict[str, Any]] = {
    'current': {'data': None, 'timestamp': None},
    'forecast': {'data': None, 'timestamp': None},
    'astronomy': {'data': None, 'timestamp': None}
}

# Время жизни кэша (в секундах)
CACHE_TTL = {
    'current': 600,    # 10 минут для текущей погоды
    'forecast': 3600,  # 1 час для прогноза
    'astronomy': 86400 # 24 часа для астрономических данных
}


async def fetch_current_weather(session: aiohttp.ClientSession) -> Optional[Dict[str, Any]]:
    """
    Получает текущую погоду через WeatherAPI.com /current.json

    Args:
        session: Асинхронная HTTP сессия

    Returns:
        Dict с данными о текущей погоде или None при ошибке
    """
    try:
        # Проверяем кэш
        cache_key = 'current'
        if _is_cache_valid(cache_key):
            logger.info("Используем кэшированные данные текущей погоды")
            return _weather_cache[cache_key]['data']

        params = {
            'key': WEATHERAPI_KEY,
            'q': f'{LAT},{LON}',
            'lang': 'ru',
            'aqi': 'no'  # Не запрашиваем данные о качестве воздуха
        }

        logger.info(f"Запрос текущей погоды для координат {LAT}, {LON}")

        async with session.get(WEATHERAPI_CURRENT_URL, params=params, timeout=10) as response:
            if response.status == 200:
                data = await response.json()

                # Сохраняем в кэш
                _weather_cache[cache_key] = {
                    'data': data,
                    'timestamp': datetime.now()
                }

                logger.info("Текущая погода успешно получена")
                return data
            else:
                error_text = await response.text()
                logger.error(f"Ошибка API текущей погоды: {response.status} - {error_text}")

                # Возвращаем кэшированные данные если есть
                if _weather_cache[cache_key]['data']:
                    logger.warning("Используем устаревшие кэшированные данные")
                    return _weather_cache[cache_key]['data']

                return None

    except asyncio.TimeoutError:
        logger.error("Таймаут при запросе текущей погоды")
    except aiohttp.ClientError as e:
        logger.error(f"Ошибка сети при запросе текущей погоды: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка парсинга JSON текущей погоды: {e}")
    except Exception as e:
        logger.error(f"Неожиданная ошибка при запросе текущей погоды: {e}")

    # Возвращаем кэшированные данные при любой ошибке
    if _weather_cache[cache_key]['data']:
        logger.warning("Используем кэшированные данные из-за ошибки")
        return _weather_cache[cache_key]['data']

    return None


async def fetch_forecast_weather(session: aiohttp.ClientSession, days: int = 3) -> Optional[Dict[str, Any]]:
    """
    Получает прогноз погоды на указанное количество дней через WeatherAPI.com /forecast.json

    Args:
        session: Асинхронная HTTP сессия
        days: Количество дней прогноза (максимум 3 для бесплатного плана)

    Returns:
        Dict с данными прогноза или None при ошибке
    """
    try:
        # Проверяем кэш
        cache_key = 'forecast'
        if _is_cache_valid(cache_key):
            logger.info("Используем кэшированные данные прогноза")
            return _weather_cache[cache_key]['data']

        params = {
            'key': WEATHERAPI_KEY,
            'q': f'{LAT},{LON}',
            'days': min(days, 3),  # Ограничиваем 3 днями для бесплатного плана
            'lang': 'ru',
            'aqi': 'no',
            'alerts': 'no'
        }

        logger.info(f"Запрос прогноза погоды на {days} дней для координат {LAT}, {LON}")

        async with session.get(WEATHERAPI_FORECAST_URL, params=params, timeout=15) as response:
            if response.status == 200:
                data = await response.json()

                # Сохраняем в кэш
                _weather_cache[cache_key] = {
                    'data': data,
                    'timestamp': datetime.now()
                }

                logger.info(f"Прогноз погоды на {days} дней успешно получен")
                return data
            else:
                error_text = await response.text()
                logger.error(f"Ошибка API прогноза: {response.status} - {error_text}")

                # Возвращаем кэшированные данные если есть
                if _weather_cache[cache_key]['data']:
                    logger.warning("Используем устаревшие кэшированные данные прогноза")
                    return _weather_cache[cache_key]['data']

                return None

    except asyncio.TimeoutError:
        logger.error("Таймаут при запросе прогноза погоды")
    except aiohttp.ClientError as e:
        logger.error(f"Ошибка сети при запросе прогноза: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка парсинга JSON прогноза: {e}")
    except Exception as e:
        logger.error(f"Неожиданная ошибка при запросе прогноза: {e}")

    # Возвращаем кэшированные данные при любой ошибке
    if _weather_cache[cache_key]['data']:
        logger.warning("Используем кэшированные данные прогноза из-за ошибки")
        return _weather_cache[cache_key]['data']

    return None


async def fetch_astronomy_data(session: aiohttp.ClientSession, date: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Получает астрономические данные (восход/закат) через WeatherAPI.com /astronomy.json

    Args:
        session: Асинхронная HTTP сессия
        date: Дата в формате YYYY-MM-DD (по умолчанию сегодня)

    Returns:
        Dict с астрономическими данными или None при ошибке
    """
    try:
        # Проверяем кэш
        cache_key = 'astronomy'
        if _is_cache_valid(cache_key):
            logger.info("Используем кэшированные астрономические данные")
            return _weather_cache[cache_key]['data']

        params = {
            'key': WEATHERAPI_KEY,
            'q': f'{LAT},{LON}',
        }

        if date:
            params['dt'] = date

        logger.info(f"Запрос астрономических данных для координат {LAT}, {LON}")

        async with session.get(WEATHERAPI_ASTRONOMY_URL, params=params, timeout=10) as response:
            if response.status == 200:
                data = await response.json()

                # Сохраняем в кэш
                _weather_cache[cache_key] = {
                    'data': data,
                    'timestamp': datetime.now()
                }

                logger.info("Астрономические данные успешно получены")
                return data
            else:
                error_text = await response.text()
                logger.error(f"Ошибка API астрономии: {response.status} - {error_text}")

                # Возвращаем кэшированные данные если есть
                if _weather_cache[cache_key]['data']:
                    logger.warning("Используем устаревшие кэшированные астрономические данные")
                    return _weather_cache[cache_key]['data']

                return None

    except asyncio.TimeoutError:
        logger.error("Таймаут при запросе астрономических данных")
    except aiohttp.ClientError as e:
        logger.error(f"Ошибка сети при запросе астрономических данных: {e}")
    except json.JSONDecodeError as e:
        logger.error(f"Ошибка парсинга JSON астрономических данных: {e}")
    except Exception as e:
        logger.error(f"Неожиданная ошибка при запросе астрономических данных: {e}")

    # Возвращаем кэшированные данные при любой ошибке
    if _weather_cache[cache_key]['data']:
        logger.warning("Используем кэшированные астрономические данные из-за ошибки")
        return _weather_cache[cache_key]['data']

    return None


def _is_cache_valid(cache_key: str) -> bool:
    """
    Проверяет, действителен ли кэш для указанного ключа

    Args:
        cache_key: Ключ кэша ('current', 'forecast', 'astronomy')

    Returns:
        bool: True если кэш действителен, False иначе
    """
    cache_entry = _weather_cache.get(cache_key)
    if not cache_entry or not cache_entry['data'] or not cache_entry['timestamp']:
        return False

    ttl = CACHE_TTL.get(cache_key, 600)
    age = (datetime.now() - cache_entry['timestamp']).total_seconds()

    return age < ttl


def parse_current_weatherapi(data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Парсит ответ current API WeatherAPI.com

    Args:
        data: JSON ответ от /current.json

    Returns:
        Dict с обработанными данными текущей погоды
    """
    try:
        if not data or 'current' not in data:
            logger.error("Некорректные данные текущей погоды")
            return None

        current = data['current']

        result = {
            'temp': round(current.get('temp_c', 0)),
            'feels_like': round(current.get('feelslike_c', 0)),
            'description': current.get('condition', {}).get('text', 'Неизвестно'),
            'wind_speed': round(current.get('wind_kph', 0) / 3.6, 1),  # Конвертируем км/ч в м/с
            'wind_dir': current.get('wind_dir', ''),
            'humidity': current.get('humidity', 0),
            'pressure': current.get('pressure_mb', 0),
            'visibility': current.get('vis_km', 0),
            'uv_index': current.get('uv', 0),
            'last_updated': current.get('last_updated', ''),
            'condition_code': current.get('condition', {}).get('code', 0),
            'is_day': current.get('is_day', 1) == 1
        }

        logger.info(f"Текущая погода: {result['temp']}°C, {result['description']}")
        return result

    except Exception as e:
        logger.error(f"Ошибка парсинга текущей погоды: {e}")
        return None


def parse_forecast_weatherapi(data: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
    """
    Парсит прогноз на 3 дня из ответа forecast API WeatherAPI.com

    Args:
        data: JSON ответ от /forecast.json

    Returns:
        List с данными прогноза на каждый день
    """
    try:
        if not data or 'forecast' not in data or 'forecastday' not in data['forecast']:
            logger.error("Некорректные данные прогноза")
            return None

        forecast_days = data['forecast']['forecastday']
        result = []

        for day_data in forecast_days:
            day = day_data.get('day', {})
            date_str = day_data.get('date', '')

            # Парсим дату
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                day_name = _get_day_name(date_obj.weekday())
                formatted_date = f"{day_name}, {date_obj.day} {_get_month_name(date_obj.month)}"
            except ValueError:
                formatted_date = date_str

            day_info = {
                'date': formatted_date,
                'date_raw': date_str,
                'temp_max': round(day.get('maxtemp_c', 0)),
                'temp_min': round(day.get('mintemp_c', 0)),
                'description': day.get('condition', {}).get('text', 'Неизвестно'),
                'wind_speed': round(day.get('maxwind_kph', 0) / 3.6, 1),  # Конвертируем км/ч в м/с
                'humidity': day.get('avghumidity', 0),
                'chance_of_rain': day.get('daily_chance_of_rain', 0),
                'chance_of_snow': day.get('daily_chance_of_snow', 0),
                'condition_code': day.get('condition', {}).get('code', 0),
                'uv_index': day.get('uv', 0)
            }

            result.append(day_info)

        logger.info(f"Прогноз на {len(result)} дней успешно обработан")
        return result

    except Exception as e:
        logger.error(f"Ошибка парсинга прогноза: {e}")
        return None


def parse_today_weatherapi(data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Извлекает почасовые данные для сегодня из прогноза WeatherAPI.com

    Args:
        data: JSON ответ от /forecast.json

    Returns:
        Dict с почасовыми данными на сегодня и астрономическими данными
    """
    try:
        if not data or 'forecast' not in data or 'forecastday' not in data['forecast']:
            logger.error("Некорректные данные для сегодняшнего прогноза")
            return None

        # Берем первый день (сегодня)
        today_data = data['forecast']['forecastday'][0]
        hourly_data = today_data.get('hour', [])
        astro_data = today_data.get('astro', {})

        # Получаем текущее время в нужном часовом поясе
        moscow_tz = ZoneInfo(TZ)
        now = datetime.now(moscow_tz)
        current_hour = now.hour

        # Разбиваем день на периоды
        periods = {
            'morning': {'start': 6, 'end': 12, 'name': 'Утром'},
            'day': {'start': 12, 'end': 18, 'name': 'Днём'},
            'evening': {'start': 18, 'end': 24, 'name': 'Вечером'},
            'night': {'start': 0, 'end': 6, 'name': 'Ночью'}
        }

        result = {
            'periods': {},
            'sunrise': astro_data.get('sunrise', ''),
            'sunset': astro_data.get('sunset', ''),
            'moonrise': astro_data.get('moonrise', ''),
            'moonset': astro_data.get('moonset', ''),
            'moon_phase': astro_data.get('moon_phase', ''),
            'moon_illumination': astro_data.get('moon_illumination', 0)
        }

        # Обрабатываем каждый период
        for period_key, period_info in periods.items():
            period_hours = []

            # Собираем часы для периода
            for hour_data in hourly_data:
                hour_time = hour_data.get('time', '')
                try:
                    hour_dt = datetime.strptime(hour_time, '%Y-%m-%d %H:%M')
                    hour = hour_dt.hour

                    if period_info['start'] <= hour < period_info['end']:
                        period_hours.append(hour_data)
                except ValueError:
                    continue

            if period_hours:
                # Берем средние значения или наиболее подходящие
                if period_key == 'morning':
                    # Для утра берем данные около 9:00
                    target_hour = next((h for h in period_hours if datetime.strptime(h['time'], '%Y-%m-%d %H:%M').hour == 9), period_hours[len(period_hours)//2])
                elif period_key == 'day':
                    # Для дня берем данные около 15:00
                    target_hour = next((h for h in period_hours if datetime.strptime(h['time'], '%Y-%m-%d %H:%M').hour == 15), period_hours[len(period_hours)//2])
                elif period_key == 'evening':
                    # Для вечера берем данные около 21:00
                    target_hour = next((h for h in period_hours if datetime.strptime(h['time'], '%Y-%m-%d %H:%M').hour == 21), period_hours[len(period_hours)//2])
                else:  # night
                    # Для ночи берем данные около 3:00
                    target_hour = next((h for h in period_hours if datetime.strptime(h['time'], '%Y-%m-%d %H:%M').hour == 3), period_hours[len(period_hours)//2])

                result['periods'][period_key] = {
                    'name': period_info['name'],
                    'temp': round(target_hour.get('temp_c', 0)),
                    'feels_like': round(target_hour.get('feelslike_c', 0)),
                    'description': target_hour.get('condition', {}).get('text', 'Неизвестно'),
                    'wind_speed': round(target_hour.get('wind_kph', 0) / 3.6, 1),
                    'humidity': target_hour.get('humidity', 0),
                    'chance_of_rain': target_hour.get('chance_of_rain', 0),
                    'condition_code': target_hour.get('condition', {}).get('code', 0),
                    'is_day': target_hour.get('is_day', 1) == 1
                }

        # Обрабатываем время восхода/заката
        result['sunrise_formatted'] = _format_time_to_msk(result['sunrise'])
        result['sunset_formatted'] = _format_time_to_msk(result['sunset'])

        # Проверяем, прошел ли уже восход сегодня
        if result['sunrise_formatted']:
            try:
                sunrise_time = datetime.strptime(result['sunrise_formatted'], '%H:%M').time()
                current_time = now.time()

                if current_time > sunrise_time:
                    # Восход уже прошел, показываем завтрашний
                    tomorrow = now + timedelta(days=1)
                    result['sunrise_tomorrow'] = True
                    result['sunrise_display'] = f"Восход завтра: {result['sunrise_formatted']}"
                else:
                    result['sunrise_tomorrow'] = False
                    result['sunrise_display'] = f"Восход: {result['sunrise_formatted']}"
            except ValueError:
                result['sunrise_display'] = f"Восход: {result['sunrise_formatted']}"

        logger.info("Данные на сегодня успешно обработаны")
        return result

    except Exception as e:
        logger.error(f"Ошибка парсинга данных на сегодня: {e}")
        return None


def format_sun_times(sunrise_ts: Optional[int], sunset_ts: Optional[int], tz: str = TZ) -> Dict[str, str]:
    """
    Конвертирует UNIX-время восхода/заката в строку по заданному часовому поясу

    Args:
        sunrise_ts: UNIX timestamp восхода солнца
        sunset_ts: UNIX timestamp заката солнца
        tz: Часовой пояс (по умолчанию из конфига)

    Returns:
        Dict с отформатированным временем восхода и заката
    """
    try:
        timezone_obj = ZoneInfo(tz)
        result = {}

        if sunrise_ts:
            sunrise_dt = datetime.fromtimestamp(sunrise_ts, tz=timezone_obj)
            result['sunrise'] = sunrise_dt.strftime('%H:%M')
        else:
            result['sunrise'] = ''

        if sunset_ts:
            sunset_dt = datetime.fromtimestamp(sunset_ts, tz=timezone_obj)
            result['sunset'] = sunset_dt.strftime('%H:%M')
        else:
            result['sunset'] = ''

        return result

    except Exception as e:
        logger.error(f"Ошибка форматирования времени восхода/заката: {e}")
        return {'sunrise': '', 'sunset': ''}


def _format_time_to_msk(time_str: str) -> str:
    """
    Форматирует время в формат HH:MM для московского времени

    Args:
        time_str: Время в формате "HH:MM AM/PM" или "HH:MM"

    Returns:
        str: Время в формате HH:MM
    """
    try:
        if not time_str:
            return ''

        # Убираем лишние пробелы
        time_str = time_str.strip()

        # Если время уже в формате HH:MM, возвращаем как есть
        if ':' in time_str and ('AM' not in time_str.upper() and 'PM' not in time_str.upper()):
            return time_str

        # Парсим время с AM/PM
        if 'AM' in time_str.upper() or 'PM' in time_str.upper():
            time_obj = datetime.strptime(time_str, '%I:%M %p')
            return time_obj.strftime('%H:%M')

        return time_str

    except Exception as e:
        logger.error(f"Ошибка форматирования времени {time_str}: {e}")
        return time_str


def _get_day_name(weekday: int) -> str:
    """
    Возвращает русское название дня недели

    Args:
        weekday: Номер дня недели (0=понедельник, 6=воскресенье)

    Returns:
        str: Сокращенное название дня недели
    """
    days = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']
    return days[weekday] if 0 <= weekday < 7 else 'Неизв'


def _get_month_name(month: int) -> str:
    """
    Возвращает русское название месяца в родительном падеже

    Args:
        month: Номер месяца (1-12)

    Returns:
        str: Название месяца
    """
    months = [
        '', 'января', 'февраля', 'марта', 'апреля', 'мая', 'июня',
        'июля', 'августа', 'сентября', 'октября', 'ноября', 'декабря'
    ]
    return months[month] if 1 <= month <= 12 else 'неизв'


def clear_cache():
    """Очищает весь кэш погодных данных"""
    global _weather_cache
    _weather_cache = {
        'current': {'data': None, 'timestamp': None},
        'forecast': {'data': None, 'timestamp': None},
        'astronomy': {'data': None, 'timestamp': None}
    }
    logger.info("Кэш погодных данных очищен")


def get_cache_status() -> Dict[str, Any]:
    """
    Возвращает статус кэша

    Returns:
        Dict с информацией о состоянии кэша
    """
    status = {}
    for key, cache_entry in _weather_cache.items():
        if cache_entry['data'] and cache_entry['timestamp']:
            age = (datetime.now() - cache_entry['timestamp']).total_seconds()
            ttl = CACHE_TTL.get(key, 600)
            status[key] = {
                'has_data': True,
                'age_seconds': int(age),
                'ttl_seconds': ttl,
                'is_valid': age < ttl
            }
        else:
            status[key] = {
                'has_data': False,
                'age_seconds': 0,
                'ttl_seconds': CACHE_TTL.get(key, 600),
                'is_valid': False
            }

    return status
