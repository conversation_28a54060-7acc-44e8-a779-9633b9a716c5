2025-07-31 23:53:17,381 - __main__ - INFO - 🚀 Запуск Telegram-бота погоды...
2025-07-31 23:53:17,381 - __main__ - INFO - 📋 Проверка конфигурации...
2025-07-31 23:53:17,383 - __main__ - INFO - ✅ Конфигурация валидна
2025-07-31 23:53:17,383 - __main__ - INFO - 💾 Инициализация хранилища...
2025-07-31 23:53:17,383 - weather_storage - INFO - Создана новая структура данных
2025-07-31 23:53:17,383 - __main__ - INFO - ✅ Хранилище инициализировано
2025-07-31 23:53:17,383 - __main__ - INFO - 🤖 Создание Application...
2025-07-31 23:53:17,951 - __main__ - INFO - ✅ Application создан
2025-07-31 23:53:17,951 - __main__ - INFO - 📡 Регистрация обработчиков...
2025-07-31 23:53:17,951 - __main__ - INFO - ✅ Обработчики зарегистрированы
2025-07-31 23:53:17,952 - __main__ - INFO - ⏰ Проверка сохраненных сообщений для планирования обновлений...
2025-07-31 23:53:17,952 - __main__ - INFO - 📋 Проверка сохраненного состояния:
2025-07-31 23:53:17,952 - __main__ - INFO -    - Сохраненный channel_id: None
2025-07-31 23:53:17,952 - __main__ - INFO -    - Сообщение '3days': не найдено
2025-07-31 23:53:17,952 - __main__ - INFO -    - Сообщение 'today': не найдено
2025-07-31 23:53:17,952 - __main__ - INFO -    - Сообщение 'current': не найдено
2025-07-31 23:53:17,952 - __main__ - INFO - 📭 Сохраненных сообщений не найдено. Планировщик будет запущен после первой публикации.
2025-07-31 23:53:17,952 - __main__ - INFO - 🎯 Запуск polling...
2025-07-31 23:53:17,952 - __main__ - INFO - 📢 Бот будет отслеживать канал: 2769056078
2025-07-31 23:53:17,953 - __main__ - INFO - 🔍 Ожидание сообщений с ключевым словом 'weather'...
2025-07-31 23:53:18,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getMe "HTTP/1.1 200 OK"
2025-07-31 23:53:18,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/deleteWebhook "HTTP/1.1 200 OK"
2025-07-31 23:53:18,601 - apscheduler.scheduler - INFO - Scheduler started
2025-07-31 23:53:18,601 - telegram.ext.Application - INFO - Application started
2025-07-31 23:53:28,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 23:53:32,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getUpdates "HTTP/1.1 200 OK"
2025-07-31 23:53:32,174 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-07-31 23:53:32,174 - apscheduler.scheduler - INFO - Scheduler has been shut down
2025-07-31 23:53:32,195 - telegram.ext.Application - INFO - Application.stop() complete
2025-07-31 23:53:32,196 - __main__ - INFO - 🛑 Завершение работы бота
2025-07-31 23:54:02,356 - weather_main - INFO - 🚀 Запуск Telegram-бота погоды (асинхронно)...
2025-07-31 23:54:02,357 - weather_main - INFO - 📋 Проверка конфигурации...
2025-07-31 23:54:02,359 - weather_main - ERROR - ❌ Ошибка конфигурации. Завершение работы.
2025-07-31 23:54:57,883 - weather_main - INFO - 🚀 Запуск Telegram-бота погоды (асинхронно)...
2025-07-31 23:54:57,883 - weather_main - INFO - 📋 Проверка конфигурации...
2025-07-31 23:54:57,885 - weather_main - INFO - ✅ Конфигурация валидна
2025-07-31 23:54:57,885 - weather_main - INFO - 💾 Инициализация хранилища...
2025-07-31 23:54:57,885 - weather_storage - INFO - Создана новая структура данных
2025-07-31 23:54:57,885 - weather_main - INFO - ✅ Хранилище инициализировано
2025-07-31 23:54:57,885 - weather_main - INFO - 🤖 Создание Application...
2025-07-31 23:54:58,471 - weather_main - INFO - ✅ Application создан
2025-07-31 23:54:58,472 - weather_main - INFO - 📡 Регистрация обработчиков...
2025-07-31 23:54:58,472 - weather_main - INFO - ✅ Обработчики зарегистрированы
2025-07-31 23:54:58,472 - weather_main - INFO - ⏰ Проверка сохраненных сообщений для планирования обновлений...
2025-07-31 23:54:58,472 - weather_main - INFO - 📋 Проверка сохраненного состояния:
2025-07-31 23:54:58,472 - weather_main - INFO -    - Сохраненный channel_id: None
2025-07-31 23:54:58,472 - weather_main - INFO -    - Сообщение '3days': не найдено
2025-07-31 23:54:58,472 - weather_main - INFO -    - Сообщение 'today': не найдено
2025-07-31 23:54:58,473 - weather_main - INFO -    - Сообщение 'current': не найдено
2025-07-31 23:54:58,473 - weather_main - INFO - 📭 Сохраненных сообщений не найдено. Планировщик будет запущен после первой публикации.
2025-07-31 23:54:58,473 - weather_main - INFO - 🎯 Запуск polling...
2025-07-31 23:54:58,473 - weather_main - INFO - 📢 Бот будет отслеживать канал: 2769056078
2025-07-31 23:54:58,473 - weather_main - INFO - 🔍 Ожидание сообщений с ключевым словом 'weather'...
2025-07-31 23:54:58,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/getMe "HTTP/1.1 200 OK"
2025-07-31 23:54:58,814 - apscheduler.scheduler - INFO - Scheduler started
2025-07-31 23:54:58,814 - telegram.ext.Application - INFO - Application started
2025-07-31 23:54:58,903 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot8436380362:AAGjPNRdsETvY8X-P_dwyT_Z5mTtph3IFxg/deleteWebhook "HTTP/1.1 200 OK"
2025-07-31 23:54:58,904 - weather_main - ERROR - 💥 Критическая ошибка: This Application is still running!
2025-07-31 23:54:58,904 - weather_main - INFO - 🛑 Завершение работы бота
2025-07-31 23:54:58,904 - weather_main - ERROR - 💥 Фатальная ошибка: This Application is still running!
