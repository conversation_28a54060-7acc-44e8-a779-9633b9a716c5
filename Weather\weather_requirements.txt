# Telegram Bot Framework
python-telegram-bot>=21.0

# HTTP клиенты для асинхронных запросов
aiohttp>=3.8.0
httpx>=0.24.0

# Работа с переменными окружения больше не нужна (этап 3 - все встроено в конфиг)
# python-dotenv>=1.0.0

# Работа с часовыми поясами (для Python < 3.9)
pytz>=2023.3

# Логирование (встроенный модуль, но указываем для ясности)
# logging - встроенный модуль

# JSON обработка (встроенный модуль)
# json - встроенный модуль

# Dataclasses для структур данных (встроенный в Python 3.7+)
# dataclasses - встроенный модуль

# Requests как альтернатива для синхронных запросов
requests>=2.31.0

# Дополнительные утилиты
typing-extensions>=4.0.0
