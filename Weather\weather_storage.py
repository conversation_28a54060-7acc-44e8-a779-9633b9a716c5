"""
Модуль для работы с хранилищем данных бота.
Обеспечивает сохранение и загрузку состояния бота (message_id, временные метки обновлений).
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class Storage:
    """
    Класс для работы с JSON-хранилищем данных бота.

    Хранит:
    - message_id для каждого типа сообщения (3days, today, current)
    - временные метки последних обновлений
    - file_id изображений для повторного использования
    """

    def __init__(self, file_path: str = "weather_state.json"):
        """
        Инициализация хранилища.

        Args:
            file_path: Путь к JSON-файлу для хранения данных
        """
        self.file_path = file_path
        self._data = {}
        self._load_data()

    def _load_data(self) -> None:
        """Загружает данные из JSON-файла."""
        try:
            if os.path.exists(self.file_path):
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    self._data = json.load(f)
                logger.info(f"Данные загружены из {self.file_path}")
            else:
                self._data = self._get_default_structure()
                logger.info(f"Создана новая структура данных")
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"Ошибка загрузки данных: {e}")
            self._data = self._get_default_structure()

    def _get_default_structure(self) -> Dict[str, Any]:
        """Возвращает структуру данных по умолчанию."""
        return {
            "messages": {
                "3days": {
                    "message_id": None,
                    "last_update": None
                },
                "today": {
                    "message_id": None,
                    "last_update": None
                },
                "current": {
                    "message_id": None,
                    "last_update": None
                }
            },
            "file_ids": {},  # Кэш file_id изображений
            "channel_id": None,
            "last_weather_request": None
        }

    def save(self) -> bool:
        """
        Сохраняет данные в JSON-файл.

        Returns:
            bool: True если сохранение прошло успешно, False иначе
        """
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(self._data, f, ensure_ascii=False, indent=2)
            logger.info(f"Данные сохранены в {self.file_path}")
            return True
        except IOError as e:
            logger.error(f"Ошибка сохранения данных: {e}")
            return False

    def load(self) -> Dict[str, Any]:
        """
        Возвращает все данные из хранилища.

        Returns:
            Dict[str, Any]: Словарь с данными
        """
        return self._data.copy()

    def update_message_id(self, message_type: str, message_id: Optional[int]) -> bool:
        """
        Обновляет message_id для указанного типа сообщения.

        Args:
            message_type: Тип сообщения ('3days', 'today', 'current')
            message_id: ID сообщения в Telegram или None для очистки

        Returns:
            bool: True если обновление прошло успешно
        """
        if message_type not in self._data["messages"]:
            logger.error(f"Неизвестный тип сообщения: {message_type}")
            return False

        self._data["messages"][message_type]["message_id"] = message_id
        self._data["messages"][message_type]["last_update"] = datetime.now().isoformat()

        logger.info(f"Обновлен message_id для {message_type}: {message_id}")
        return self.save()

    def get_message_id(self, message_type: str) -> Optional[int]:
        """
        Получает message_id для указанного типа сообщения.

        Args:
            message_type: Тип сообщения ('3days', 'today', 'current')

        Returns:
            Optional[int]: ID сообщения или None если не найден
        """
        if message_type not in self._data["messages"]:
            logger.error(f"Неизвестный тип сообщения: {message_type}")
            return None

        return self._data["messages"][message_type]["message_id"]

    def update_last_update(self, message_type: str, timestamp: Optional[str] = None) -> bool:
        """
        Обновляет временную метку последнего обновления.

        Args:
            message_type: Тип сообщения ('3days', 'today', 'current')
            timestamp: Временная метка в ISO формате (если None - текущее время)

        Returns:
            bool: True если обновление прошло успешно
        """
        if message_type not in self._data["messages"]:
            logger.error(f"Неизвестный тип сообщения: {message_type}")
            return False

        if timestamp is None:
            timestamp = datetime.now().isoformat()

        self._data["messages"][message_type]["last_update"] = timestamp
        logger.info(f"Обновлена временная метка для {message_type}: {timestamp}")
        return self.save()

    def get_last_update(self, message_type: str) -> Optional[str]:
        """
        Получает временную метку последнего обновления.

        Args:
            message_type: Тип сообщения ('3days', 'today', 'current')

        Returns:
            Optional[str]: Временная метка в ISO формате или None
        """
        if message_type not in self._data["messages"]:
            logger.error(f"Неизвестный тип сообщения: {message_type}")
            return None

        return self._data["messages"][message_type]["last_update"]

    def save_file_id(self, image_name: str, file_id: str) -> bool:
        """
        Сохраняет file_id изображения для повторного использования.

        Args:
            image_name: Название файла изображения
            file_id: ID файла в Telegram

        Returns:
            bool: True если сохранение прошло успешно
        """
        self._data["file_ids"][image_name] = file_id
        logger.info(f"Сохранен file_id для {image_name}: {file_id}")
        return self.save()

    def get_file_id(self, image_name: str) -> Optional[str]:
        """
        Получает file_id изображения.

        Args:
            image_name: Название файла изображения

        Returns:
            Optional[str]: ID файла в Telegram или None если не найден
        """
        return self._data["file_ids"].get(image_name)

    def set_channel_id(self, channel_id: int) -> bool:
        """
        Устанавливает ID канала.

        Args:
            channel_id: ID канала в Telegram

        Returns:
            bool: True если сохранение прошло успешно
        """
        self._data["channel_id"] = channel_id
        logger.info(f"Установлен channel_id: {channel_id}")
        return self.save()

    def get_channel_id(self) -> Optional[int]:
        """
        Получает ID канала.

        Returns:
            Optional[int]: ID канала или None если не установлен
        """
        return self._data["channel_id"]

    def update_weather_request_time(self) -> bool:
        """
        Обновляет время последнего запроса погоды.

        Returns:
            bool: True если обновление прошло успешно
        """
        self._data["last_weather_request"] = datetime.now().isoformat()
        return self.save()

    def get_weather_request_time(self) -> Optional[str]:
        """
        Получает время последнего запроса погоды.

        Returns:
            Optional[str]: Временная метка в ISO формате или None
        """
        return self._data["last_weather_request"]

    def has_active_messages(self) -> bool:
        """
        Проверяет, есть ли активные сообщения для обновления.

        Returns:
            bool: True если есть хотя бы один message_id
        """
        for message_type in ["3days", "today", "current"]:
            if self.get_message_id(message_type) is not None:
                return True
        return False

    def clear_all_messages(self) -> bool:
        """
        Очищает все message_id (используется при перезапуске).

        Returns:
            bool: True если очистка прошла успешно
        """
        for message_type in ["3days", "today", "current"]:
            self._data["messages"][message_type]["message_id"] = None
            self._data["messages"][message_type]["last_update"] = None

        logger.info("Очищены все message_id")
        return self.save()

    def get_all_message_ids(self) -> Dict[str, Optional[int]]:
        """
        Получает все message_id.

        Returns:
            Dict[str, Optional[int]]: Словарь с message_id для каждого типа
        """
        return {
            message_type: self.get_message_id(message_type)
            for message_type in ["3days", "today", "current"]
        }
