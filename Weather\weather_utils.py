"""
Вспомогательные функции для Telegram-бота погоды.

Содержит утилиты для:
- Форматирования текста для разных сообщений (HTML-разметка, эмодзи)
- Преобразования дат и времени
- Выбора склонения слов
- Генерации ссылок на изображения
- Обработки исключений
"""

import os
import logging
from datetime import datetime, date
from typing import Dict, List, Optional, Any, Union

# Настройка логирования
logger = logging.getLogger(__name__)

# Эмодзи для разных погодных условий
WEATHER_EMOJIS = {
    # Солнечно/ясно
    'clear': '☀️',
    'sunny': '☀️',

    # Облачно
    'cloudy': '☁️',
    'overcast': '☁️',
    'partly_cloudy': '⛅',
    'few_clouds': '🌤️',

    # Дождь
    'rain': '🌧️',
    'heavy_rain': '🌧️',
    'light_rain': '🌦️',
    'drizzle': '🌦️',
    'shower': '🌧️',

    # Снег
    'snow': '❄️',
    'heavy_snow': '❄️',
    'light_snow': '🌨️',
    'sleet': '🌨️',
    'blizzard': '🌨️',

    # Гроза
    'thunderstorm': '⛈️',
    'thunder': '⛈️',

    # Туман/дымка
    'fog': '🌫️',
    'mist': '🌫️',
    'haze': '🌫️',

    # Ветер
    'windy': '💨',

    # Град
    'hail': '🧊',

    # Заморозки
    'frost': '🧊',
    'freezing': '🧊',

    # По умолчанию
    'default': '🌤️'
}

# Эмодзи для времени суток
TIME_EMOJIS = {
    'morning': '🌅',
    'day': '☀️',
    'evening': '🌇',
    'night': '🌙'
}

# Эмодзи для дополнительных параметров
PARAM_EMOJIS = {
    'temperature': '🌡️',
    'feels_like': '🤔',
    'wind': '💨',
    'humidity': '💧',
    'pressure': '📊',
    'visibility': '👁️',
    'uv': '☀️',
    'sunrise': '🌅',
    'sunset': '🌇'
}


def create_weather_caption_current(data: Dict[str, Any]) -> str:
    """
    Формирует красивый и лаконичный текст для «погоды сейчас»

    Args:
        data: Словарь с данными текущей погоды из parse_current_weatherapi()

    Returns:
        str: Отформатированный HTML-текст для Telegram
    """
    try:
        if not data:
            return "<b>❌ Данные о текущей погоде недоступны</b>"

        # Основные данные
        temp = data.get('temp', 0)
        feels_like = data.get('feels_like', 0)
        description = data.get('description', 'Неизвестно')
        wind_speed = data.get('wind_speed', 0)
        humidity = data.get('humidity', 0)

        # Выбираем эмодзи на основе описания погоды
        emoji = _get_weather_emoji(description, data.get('is_day', True))

        # Формируем заголовок
        caption = f"<b>{emoji} Погода сейчас</b>\n\n"

        # Формируем основную строку с данными
        temp_str = f"{temp}°C"
        if feels_like != temp:
            temp_str += f" (ощущается как {feels_like}°C)"

        wind_desc = describe_wind_strength(wind_speed)
        humidity_desc = describe_humidity_level(humidity)

        caption += f"{temp_str}, ветер {wind_desc}, {humidity_desc}"

        return caption.strip()

    except Exception as e:
        logger.error(f"Ошибка создания подписи текущей погоды: {e}")
        return "<b>❌ Ошибка формирования данных о погоде</b>"


def create_weather_caption_today(data: Dict[str, Any]) -> str:
    """
    Форматирует прогноз на сегодня по периодам дня

    Args:
        data: Словарь с данными на сегодня из parse_today_weatherapi()

    Returns:
        str: Отформатированный HTML-текст для Telegram
    """
    try:
        if not data or 'periods' not in data:
            return "<b>❌ Данные прогноза на сегодня недоступны</b>"

        periods = data['periods']
        caption = f"<b>🌡️ Погода на сегодня</b>\n\n"

        # Порядок периодов и их русские названия
        period_order = ['morning', 'day', 'evening', 'night']
        period_names = {
            'morning': 'Утром',
            'day': 'Днём',
            'evening': 'Вечером',
            'night': 'Ночью'
        }

        for period_key in period_order:
            if period_key not in periods:
                continue

            period_data = periods[period_key]
            temp = period_data.get('temp', 0)
            description = period_data.get('description', 'Неизвестно')
            wind_speed = period_data.get('wind_speed', 0)

            # Эмодзи для погоды в этот период
            weather_emoji = _get_weather_emoji(description, period_key in ['morning', 'day'])

            # Описание ветра словами
            wind_desc = describe_wind_strength(wind_speed)

            # Русское название периода
            period_name = period_names.get(period_key, period_key)

            # Формируем строку для периода в новом формате
            period_line = f"{weather_emoji} {period_name}: {temp}°C, ветер {wind_desc}\n"

            caption += period_line

        # Добавляем информацию о восходе и закате
        sunrise_display = data.get('sunrise_display')
        sunset_formatted = data.get('sunset_formatted')

        if sunrise_display and sunset_formatted:
            # Извлекаем время из строки восхода
            sunrise_time = sunrise_display.split(': ')[1] if ': ' in sunrise_display else sunrise_display
            caption += f"\n🌄 Восход: {sunrise_time}\n"
            caption += f"🌇 Закат: {sunset_formatted}"

        return caption.strip()

    except Exception as e:
        logger.error(f"Ошибка создания подписи прогноза на сегодня: {e}")
        return "<b>❌ Ошибка формирования прогноза на сегодня</b>"


def create_weather_caption_3days(data_list: List[Dict[str, Any]]) -> str:
    """
    Создаёт текст прогноза на 3 дня

    Args:
        data_list: Список словарей с данными прогноза из parse_forecast_weatherapi()

    Returns:
        str: Отформатированный HTML-текст для Telegram
    """
    try:
        if not data_list:
            return "<b>❌ Данные прогноза на 3 дня недоступны</b>"

        caption = f"<b>🌤️ Прогноз на 3 дня</b>\n\n"

        for i, day_data in enumerate(data_list[:3]):  # Ограничиваем 3 днями
            date_str = day_data.get('date', 'Неизвестная дата')
            temp_max = day_data.get('temp_max', 0)
            temp_min = day_data.get('temp_min', 0)
            description = day_data.get('description', 'Неизвестно')
            wind_speed = day_data.get('wind_speed', 0)

            # Выбираем эмодзи для погоды
            weather_emoji = _get_weather_emoji(description, True)  # Для дневного времени

            # Описание ветра словами
            wind_desc = describe_wind_strength(wind_speed)

            # Формируем строку для дня в новом формате
            day_line = f"{weather_emoji} {date_str}: {temp_min}-{temp_max}°C, {wind_desc} ветер\n"

            caption += day_line

        return caption.strip()

    except Exception as e:
        logger.error(f"Ошибка создания подписи прогноза на 3 дня: {e}")
        return "<b>❌ Ошибка формирования прогноза на 3 дня</b>"


def get_day_name(date_obj: Union[datetime, date, int]) -> str:
    """
    Возвращает русское название дня недели

    Args:
        date_obj: Объект datetime, date или номер дня недели (0=понедельник)

    Returns:
        str: Сокращенное название дня недели
    """
    try:
        if isinstance(date_obj, (datetime, date)):
            weekday = date_obj.weekday()
        elif isinstance(date_obj, int):
            weekday = date_obj
        else:
            return 'Неизв'

        days = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']
        return days[weekday] if 0 <= weekday < 7 else 'Неизв'

    except Exception as e:
        logger.error(f"Ошибка получения названия дня: {e}")
        return 'Неизв'


def round_temperature(temp: Union[int, float]) -> int:
    """
    Округляет температуру до целого числа

    Args:
        temp: Температура в градусах

    Returns:
        int: Округленная температура
    """
    try:
        return round(float(temp))
    except (ValueError, TypeError):
        return 0


def format_wind_speed(speed: Union[int, float], unit: str = 'м/с') -> str:
    """
    Форматирует скорость ветра с единицами измерения

    Args:
        speed: Скорость ветра
        unit: Единица измерения (по умолчанию м/с)

    Returns:
        str: Отформатированная строка со скоростью ветра
    """
    try:
        if speed <= 0:
            return "штиль"

        speed_rounded = round(float(speed), 1)
        if speed_rounded == int(speed_rounded):
            return f"{int(speed_rounded)} {unit}"
        else:
            return f"{speed_rounded} {unit}"

    except (ValueError, TypeError):
        return "неизв"


def describe_wind_strength(speed: Union[int, float]) -> str:
    """
    Описывает силу ветра словами

    Args:
        speed: Скорость ветра в м/с

    Returns:
        str: Описание силы ветра
    """
    try:
        speed_float = float(speed)

        if speed_float <= 0.2:
            return "штиль"
        elif speed_float <= 1.5:
            return "тихий"
        elif speed_float <= 3.3:
            return "легкий"
        elif speed_float <= 5.4:
            return "слабый"
        elif speed_float <= 7.9:
            return "умеренный"
        elif speed_float <= 10.7:
            return "свежий"
        elif speed_float <= 13.8:
            return "сильный"
        elif speed_float <= 17.1:
            return "крепкий"
        elif speed_float <= 20.7:
            return "очень крепкий"
        elif speed_float <= 24.4:
            return "шторм"
        elif speed_float <= 28.4:
            return "сильный шторм"
        elif speed_float <= 32.6:
            return "жестокий шторм"
        else:
            return "ураган"

    except (ValueError, TypeError):
        return "неизвестно"


def describe_humidity_level(humidity: Union[int, float]) -> str:
    """
    Описывает уровень влажности словами (упрощенная версия)

    Args:
        humidity: Влажность в процентах

    Returns:
        str: Описание уровня влажности (низкая, средняя, высокая)
    """
    try:
        humidity_int = round(float(humidity))

        if humidity_int <= 40:
            return "низкая влажность"
        elif humidity_int <= 70:
            return "средняя влажность"
        else:
            return "высокая влажность"

    except (ValueError, TypeError):
        return "неизвестная влажность"


def check_image_exists(image_path: str) -> bool:
    """
    Проверяет существование файла изображения

    Args:
        image_path: Путь к файлу изображения

    Returns:
        bool: True если файл существует, False иначе
    """
    try:
        return os.path.isfile(image_path)
    except Exception as e:
        logger.error(f"Ошибка проверки существования файла {image_path}: {e}")
        return False


def get_image_path(image_name: str, images_dir: str = 'images') -> str:
    """
    Генерирует полный путь к изображению

    Args:
        image_name: Название файла изображения
        images_dir: Папка с изображениями

    Returns:
        str: Полный путь к изображению
    """
    try:
        return os.path.join(images_dir, image_name)
    except Exception as e:
        logger.error(f"Ошибка генерации пути к изображению {image_name}: {e}")
        return image_name


def _get_weather_emoji(description: str, is_day: bool = True) -> str:
    """
    Выбирает подходящий эмодзи на основе описания погоды

    Args:
        description: Описание погодных условий
        is_day: True если день, False если ночь

    Returns:
        str: Эмодзи для погодных условий
    """
    try:
        if not description:
            return WEATHER_EMOJIS['default']

        description_lower = description.lower()

        # Проверяем различные погодные условия
        if any(word in description_lower for word in ['ясно', 'солнечно', 'clear', 'sunny']):
            return WEATHER_EMOJIS['clear']
        elif any(word in description_lower for word in ['пасмурно', 'overcast', 'облачно с прояснениями']):
            return WEATHER_EMOJIS['overcast']
        elif any(word in description_lower for word in ['облачно', 'cloudy']):
            return WEATHER_EMOJIS['cloudy']
        elif any(word in description_lower for word in ['переменная облачность', 'partly cloudy', 'малооблачно']):
            return WEATHER_EMOJIS['partly_cloudy']
        elif any(word in description_lower for word in ['небольшая облачность', 'few clouds']):
            return WEATHER_EMOJIS['few_clouds']
        elif any(word in description_lower for word in ['сильный дождь', 'heavy rain', 'ливень']):
            return WEATHER_EMOJIS['heavy_rain']
        elif any(word in description_lower for word in ['дождь', 'rain', 'дождливо']):
            return WEATHER_EMOJIS['rain']
        elif any(word in description_lower for word in ['небольшой дождь', 'light rain', 'морось', 'drizzle']):
            return WEATHER_EMOJIS['light_rain']
        elif any(word in description_lower for word in ['ливень', 'shower']):
            return WEATHER_EMOJIS['shower']
        elif any(word in description_lower for word in ['сильный снег', 'heavy snow', 'метель', 'blizzard']):
            return WEATHER_EMOJIS['heavy_snow']
        elif any(word in description_lower for word in ['снег', 'snow', 'снежно']):
            return WEATHER_EMOJIS['snow']
        elif any(word in description_lower for word in ['небольшой снег', 'light snow', 'снежок']):
            return WEATHER_EMOJIS['light_snow']
        elif any(word in description_lower for word in ['мокрый снег', 'sleet']):
            return WEATHER_EMOJIS['sleet']
        elif any(word in description_lower for word in ['гроза', 'thunderstorm', 'thunder', 'гром']):
            return WEATHER_EMOJIS['thunderstorm']
        elif any(word in description_lower for word in ['туман', 'fog']):
            return WEATHER_EMOJIS['fog']
        elif any(word in description_lower for word in ['дымка', 'mist', 'haze']):
            return WEATHER_EMOJIS['mist']
        elif any(word in description_lower for word in ['ветрено', 'windy']):
            return WEATHER_EMOJIS['windy']
        elif any(word in description_lower for word in ['град', 'hail']):
            return WEATHER_EMOJIS['hail']
        elif any(word in description_lower for word in ['заморозки', 'frost', 'freezing']):
            return WEATHER_EMOJIS['frost']
        else:
            return WEATHER_EMOJIS['default']

    except Exception as e:
        logger.error(f"Ошибка выбора эмодзи для описания '{description}': {e}")
        return WEATHER_EMOJIS['default']


def format_humidity(humidity: Union[int, float]) -> str:
    """
    Форматирует влажность с процентами

    Args:
        humidity: Влажность в процентах

    Returns:
        str: Отформатированная строка с влажностью
    """
    try:
        humidity_int = round(float(humidity))
        return f"{humidity_int}%"
    except (ValueError, TypeError):
        return "неизв"


def format_pressure(pressure: Union[int, float], unit: str = 'мб') -> str:
    """
    Форматирует атмосферное давление

    Args:
        pressure: Давление
        unit: Единица измерения (мб, гПа, мм рт.ст.)

    Returns:
        str: Отформатированная строка с давлением
    """
    try:
        pressure_int = round(float(pressure))
        return f"{pressure_int} {unit}"
    except (ValueError, TypeError):
        return "неизв"


def format_visibility(visibility: Union[int, float], unit: str = 'км') -> str:
    """
    Форматирует видимость

    Args:
        visibility: Видимость
        unit: Единица измерения

    Returns:
        str: Отформатированная строка с видимостью
    """
    try:
        if visibility <= 0:
            return "неизв"

        vis_rounded = round(float(visibility), 1)
        if vis_rounded == int(vis_rounded):
            return f"{int(vis_rounded)} {unit}"
        else:
            return f"{vis_rounded} {unit}"

    except (ValueError, TypeError):
        return "неизв"


def truncate_text(text: str, max_length: int = 1024) -> str:
    """
    Обрезает текст до максимальной длины для Telegram

    Args:
        text: Исходный текст
        max_length: Максимальная длина (по умолчанию 1024 для caption)

    Returns:
        str: Обрезанный текст
    """
    try:
        if len(text) <= max_length:
            return text

        # Обрезаем с учетом HTML-тегов
        truncated = text[:max_length-3]

        # Ищем последний полный HTML-тег
        last_tag_start = truncated.rfind('<')
        last_tag_end = truncated.rfind('>')

        if last_tag_start > last_tag_end:
            # Незакрытый тег, обрезаем до него
            truncated = truncated[:last_tag_start]

        return truncated + "..."

    except Exception as e:
        logger.error(f"Ошибка обрезания текста: {e}")
        return text[:max_length-3] + "..." if len(text) > max_length else text


def escape_html(text: str) -> str:
    """
    Экранирует HTML-символы для безопасного использования в Telegram

    Args:
        text: Исходный текст

    Returns:
        str: Экранированный текст
    """
    try:
        if not text:
            return ""

        # Заменяем специальные символы HTML
        text = str(text)
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('"', '&quot;')
        text = text.replace("'", '&#x27;')

        return text

    except Exception as e:
        logger.error(f"Ошибка экранирования HTML: {e}")
        return str(text) if text else ""


def get_wind_direction_emoji(direction: str) -> str:
    """
    Возвращает эмодзи для направления ветра

    Args:
        direction: Направление ветра (N, NE, E, SE, S, SW, W, NW)

    Returns:
        str: Эмодзи стрелки для направления
    """
    try:
        direction_emojis = {
            'N': '⬆️',    # Север
            'NE': '↗️',   # Северо-восток
            'E': '➡️',    # Восток
            'SE': '↘️',   # Юго-восток
            'S': '⬇️',    # Юг
            'SW': '↙️',   # Юго-запад
            'W': '⬅️',    # Запад
            'NW': '↖️',   # Северо-запад
        }

        return direction_emojis.get(direction.upper(), '💨')

    except Exception as e:
        logger.error(f"Ошибка получения эмодзи направления ветра: {e}")
        return '💨'


def get_wind_description(speed: Union[int, float]) -> str:
    """
    Возвращает описание силы ветра по шкале Бофорта

    Args:
        speed: Скорость ветра в м/с

    Returns:
        str: Описание силы ветра
    """
    try:
        speed = float(speed)

        if speed < 0.3:
            return "штиль"
        elif speed < 1.6:
            return "тихий"
        elif speed < 3.4:
            return "легкий"
        elif speed < 5.5:
            return "слабый"
        elif speed < 8.0:
            return "умеренный"
        elif speed < 10.8:
            return "свежий"
        elif speed < 13.9:
            return "сильный"
        elif speed < 17.2:
            return "крепкий"
        elif speed < 20.8:
            return "очень крепкий"
        elif speed < 24.5:
            return "шторм"
        elif speed < 28.5:
            return "сильный шторм"
        elif speed < 32.7:
            return "жестокий шторм"
        else:
            return "ураган"

    except (ValueError, TypeError):
        return "неизвестно"


def format_chance_of_precipitation(chance: Union[int, float], precip_type: str = "дождя") -> str:
    """
    Форматирует вероятность осадков

    Args:
        chance: Вероятность в процентах
        precip_type: Тип осадков (дождя, снега)

    Returns:
        str: Отформатированная строка с вероятностью
    """
    try:
        chance_int = round(float(chance))

        if chance_int <= 0:
            return ""
        elif chance_int <= 20:
            return f"небольшая вероятность {precip_type} ({chance_int}%)"
        elif chance_int <= 50:
            return f"возможен {precip_type} ({chance_int}%)"
        elif chance_int <= 80:
            return f"вероятен {precip_type} ({chance_int}%)"
        else:
            return f"ожидается {precip_type} ({chance_int}%)"

    except (ValueError, TypeError):
        return ""


def get_uv_description(uv_index: Union[int, float]) -> str:
    """
    Возвращает описание УФ-индекса

    Args:
        uv_index: УФ-индекс

    Returns:
        str: Описание уровня УФ-излучения
    """
    try:
        uv = float(uv_index)

        if uv < 3:
            return "низкий"
        elif uv < 6:
            return "умеренный"
        elif uv < 8:
            return "высокий"
        elif uv < 11:
            return "очень высокий"
        else:
            return "экстремальный"

    except (ValueError, TypeError):
        return "неизвестно"


def validate_weather_data(data: Dict[str, Any], required_fields: List[str]) -> bool:
    """
    Проверяет наличие обязательных полей в данных о погоде

    Args:
        data: Словарь с данными о погоде
        required_fields: Список обязательных полей

    Returns:
        bool: True если все поля присутствуют, False иначе
    """
    try:
        if not data or not isinstance(data, dict):
            return False

        for field in required_fields:
            if field not in data:
                logger.warning(f"Отсутствует обязательное поле: {field}")
                return False

        return True

    except Exception as e:
        logger.error(f"Ошибка валидации данных о погоде: {e}")
        return False


def safe_get(data: Dict[str, Any], key: str, default: Any = None, data_type: type = None) -> Any:
    """
    Безопасно извлекает значение из словаря с проверкой типа

    Args:
        data: Словарь с данными
        key: Ключ для извлечения
        default: Значение по умолчанию
        data_type: Ожидаемый тип данных

    Returns:
        Any: Значение из словаря или значение по умолчанию
    """
    try:
        if not data or not isinstance(data, dict):
            return default

        value = data.get(key, default)

        if data_type and value is not None:
            try:
                if data_type == int:
                    return int(float(value))
                elif data_type == float:
                    return float(value)
                elif data_type == str:
                    return str(value)
                else:
                    return data_type(value)
            except (ValueError, TypeError):
                logger.warning(f"Не удалось преобразовать значение '{value}' к типу {data_type}")
                return default

        return value

    except Exception as e:
        logger.error(f"Ошибка безопасного извлечения значения '{key}': {e}")
        return default
