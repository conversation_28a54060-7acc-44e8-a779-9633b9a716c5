# Алина - Telegram Бот с Gemini 2.5 Pro

Современный Telegram бот с персонажем Алины, использующий Google Gemini 2.5 Pro для генерации естественных ответов.

## Особенности

- 🤖 Интеграция с Gemini 2.5 Pro через новую библиотеку google-genai
- 💬 Естественный стиль общения современной девушки 
- 🔔 Реагирует на упоминания и реплаи
- 📱 Работает в группах и приватных чатах
- ✨ Использует актуальный сленг 2024 года

## Установка

1. Установите зависимости:
```bash
pip install -r requirements.txt
```

2. Убедитесь, что файл `.env` содержит ваши токены:
```
TELEGRAM_BOT_TOKEN=**********************************************
GEMINI_API_KEY=AIzaSyC5zWirvrpVTw_uOK5NusCxhqBypSftNZ0
BOT_USERNAME=alina_bot
```

3. Запустите бота:
```bash
python main.py
```

## Настройка

1. Получите токен бота в @BotFather
2. Получите API ключ Gemini в Google AI Studio
3. Заполните `.env` файл своими данными
4. Добавьте бота в группы

## Использование

- Упомяните "Алина" в сообщении
- Ответьте на сообщение бота
- Напишите боту в приват
- Используйте @username бота

Алина будет отвечать естественно и кратко, как настоящая девушка в мессенджере!

## Структура проекта

```
alina_bot/
├── main.py                    # Основной файл запуска бота
├── bot_handler.py             # Обработчики сообщений
├── gemini_client.py           # Клиент для работы с Gemini API
├── alina_personality.py       # Системный промпт и логика персонажа Алины
├── config.py                  # Конфигурация и настройки
├── utils.py                   # Вспомогательные функции
├── requirements.txt           # Зависимости Python
├── .env                       # Переменные окружения
└── README.md                  # Документация
```

## Ключевые особенности

### 🎭 Персонаж Алины
- Современный сленг 2024: "жиза", "рил", "топчик", "кринж", "вайб"
- Естественная речь: короткие сообщения, эмодзи, сокращения
- Актуальные тренды: использует популярные выражения молодежи

### 🤖 Интеграция с Gemini 2.5 Pro
- Использует новую библиотеку google-genai
- Асинхронная обработка для быстрых ответов
- Настроенная конфигурация для креативных и кратких ответов

### 💬 Умная обработка сообщений
- Реплаи: реагирует на ответы на свои сообщения
- Упоминания: срабатывает на слова "алина", @username бота
- Группы: работает корректно в группах и каналах
- Контекст: учитывает упоминания других пользователей

### 🛠️ Техническая реализация
- Модульная архитектура для легкой поддержки
- Обработка ошибок и fallback ответы
- Логирование для отладки
- Защита от спама с cooldown'ами

## Команды бота

- `/start` - Приветствие и инструкции
- `/help` - Помощь по использованию

## Требования

- Python 3.8+
- Telegram Bot Token
- Google Gemini API Key

Этот бот отвечает очень естественно, используя современный стиль общения девушек в мессенджерах 2024 года!