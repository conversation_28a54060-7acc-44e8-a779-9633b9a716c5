import random
import time
import logging
from typing import Optional, Dict, List
from datetime import datetime, timedelta
from dima_config import GEMINI_API_KEYS, API_KEY_COOLDOWN_HOURS

logger = logging.getLogger(__name__)

class APIKeyManager:
    """Менеджер для ротации и управления API ключами Gemini"""
    
    def __init__(self):
        self.api_keys = GEMINI_API_KEYS.copy()
        # Словарь для отслеживания заблокированных ключей {api_key: timestamp_blocked}
        self.blocked_keys: Dict[str, float] = {}
        # Текущий активный ключ
        self.current_key: Optional[str] = None
        
        logger.info(f"Инициализирован менеджер API ключей с {len(self.api_keys)} ключами")
    
    def _is_key_blocked(self, api_key: str) -> bool:
        """Проверяет, заблокирован ли ключ"""
        if api_key not in self.blocked_keys:
            return False
        
        # Проверяем, прошло ли время блокировки
        blocked_time = self.blocked_keys[api_key]
        current_time = time.time()
        cooldown_seconds = API_KEY_COOLDOWN_HOURS * 3600
        
        if current_time - blocked_time >= cooldown_seconds:
            # Время блокировки прошло, разблокируем ключ
            del self.blocked_keys[api_key]
            logger.info(f"API ключ разблокирован после кулдауна: {api_key[:20]}...")
            return False
        
        return True
    
    def _get_available_keys(self) -> List[str]:
        """Возвращает список доступных (не заблокированных) ключей"""
        available_keys = []
        for key in self.api_keys:
            if not self._is_key_blocked(key):
                available_keys.append(key)
        return available_keys
    
    def get_random_key(self) -> Optional[str]:
        """Возвращает случайный доступный API ключ"""
        available_keys = self._get_available_keys()
        
        if not available_keys:
            logger.error("Нет доступных API ключей! Все ключи заблокированы.")
            return None
        
        # Выбираем случайный ключ из доступных
        selected_key = random.choice(available_keys)
        self.current_key = selected_key
        
        logger.debug(f"Выбран API ключ: {selected_key[:20]}... (доступно {len(available_keys)} из {len(self.api_keys)})")
        return selected_key
    
    def block_key(self, api_key: str, reason: str = "API error"):
        """Блокирует API ключ на заданное время"""
        if api_key in self.api_keys:
            self.blocked_keys[api_key] = time.time()
            
            # Вычисляем время разблокировки для логирования
            unblock_time = datetime.now() + timedelta(hours=API_KEY_COOLDOWN_HOURS)
            
            logger.warning(f"API ключ заблокирован до {unblock_time.strftime('%Y-%m-%d %H:%M:%S')} "
                         f"по причине: {reason}. Ключ: {api_key[:20]}...")
            
            # Если заблокированный ключ был текущим, сбрасываем его
            if self.current_key == api_key:
                self.current_key = None
    
    def get_next_key(self) -> Optional[str]:
        """Получает следующий доступный ключ (исключая текущий если он есть)"""
        available_keys = self._get_available_keys()
        
        # Исключаем текущий ключ из списка доступных
        if self.current_key and self.current_key in available_keys:
            available_keys.remove(self.current_key)
        
        if not available_keys:
            logger.error("Нет других доступных API ключей!")
            return None
        
        # Выбираем случайный ключ из оставшихся
        selected_key = random.choice(available_keys)
        self.current_key = selected_key
        
        logger.info(f"Переключение на следующий API ключ: {selected_key[:20]}...")
        return selected_key
    
    def get_status(self) -> Dict:
        """Возвращает статус всех ключей"""
        available_keys = self._get_available_keys()
        blocked_count = len(self.blocked_keys)
        
        # Информация о времени разблокировки
        blocked_info = []
        for key, blocked_time in self.blocked_keys.items():
            unblock_time = datetime.fromtimestamp(blocked_time + API_KEY_COOLDOWN_HOURS * 3600)
            blocked_info.append({
                'key': key[:20] + '...',
                'unblock_time': unblock_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return {
            'total_keys': len(self.api_keys),
            'available_keys': len(available_keys),
            'blocked_keys': blocked_count,
            'current_key': self.current_key[:20] + '...' if self.current_key else None,
            'blocked_info': blocked_info
        }

# Глобальный экземпляр менеджера
api_manager = APIKeyManager()