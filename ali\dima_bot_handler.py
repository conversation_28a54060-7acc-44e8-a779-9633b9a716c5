import asyncio
import logging
import os
from collections import deque
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ChatAction

from dima_gemini_client import Gemini<PERSON>lient
from dima_personality import get_dima_prompt_with_context, DIMA_STICKERS
from dima_context import ChatContextManager
from dima_utils import (
    should_respond_to_message,
    extract_mentions,
    clean_message_for_processing,
    is_group_chat,
    get_user_display_name,
    typing_delay,
    split_long_message,
    extract_photo_requests,
    extract_gif_requests,
    extract_reaction_requests,
    get_reaction_emoji,
    search_and_download_image,
    search_and_download_gif,
    extract_delay_requests
)
from dima_config import TYPING_DELAY, MAX_MESSAGE_LENGTH, HUMAN_THINKING_DELAY

logger = logging.getLogger(__name__)

class DimaBot:
    def __init__(self):
        self.gemini_client = GeminiClient()
        self.context_manager = ChatContextManager()
        self.last_response_time = {}  # Для избежания спама
        
        # Очереди сообщений для каждого чата (делает димочку более человечным)
        self.message_queues = {}  # chat_id -> deque of message data
        self.processing_queues = {}  # chat_id -> bool (активна ли обработка)
        
        # Запускаем очистку старых сообщений при инициализации
        self.context_manager.clear_old_messages(hours=48)
    
    async def _maintain_typing_status(self, bot, chat_id):
        """Поддерживает статус 'печатает' до завершения ответа"""
        try:
            while True:
                await bot.send_chat_action(chat_id=chat_id, action=ChatAction.TYPING)
                await asyncio.sleep(1)  # Минимальная задержка для поддержания typing
        except asyncio.CancelledError:
            pass
    
    async def _process_message_queue(self, chat_id, context):
        """Обрабатывает очередь сообщений для чата с человеческими задержками"""
        if self.processing_queues.get(chat_id, False):
            return  # Уже обрабатываем этот чат
        
        self.processing_queues[chat_id] = True
        
        try:
            first_message = True
            while chat_id in self.message_queues and self.message_queues[chat_id]:
                # Убрали задержку между сообщениями
                
                # Берем следующее сообщение из очереди
                message_data = self.message_queues[chat_id].popleft()
                
                # Обрабатываем сообщение
                await self._process_single_message(message_data, context)
                
                first_message = False
                
        finally:
            self.processing_queues[chat_id] = False
    
    def _extract_sticker_requests(self, text):
        """Извлекает запросы на стикеры из текста"""
        import re
        
        # Ищем все [STICKER:название] в тексте
        sticker_pattern = r'\[STICKER:([^\]]+)\]'
        matches = re.findall(sticker_pattern, text)
        
        # Убираем теги стикеров из текста
        cleaned_text = re.sub(sticker_pattern, '', text).strip()
        
        # Проверяем что стикеры существуют
        valid_stickers = []
        for sticker_name in matches:
            if sticker_name in DIMA_STICKERS:
                valid_stickers.append(sticker_name)
        
        return cleaned_text, valid_stickers

    async def _download_photos(self, message, context):
        """Скачивает фотографии из сообщения"""
        photos_data = []
        
        try:
            # Обрабатываем фото в сообщении
            if message.photo:
                # Берем фото наилучшего качества
                photo = message.photo[-1]
                file = await context.bot.get_file(photo.file_id)
                photo_bytes = await file.download_as_bytearray()
                photos_data.append(bytes(photo_bytes))
            
            # Обрабатываем медиа группу (несколько фото)
            if hasattr(message, 'media_group_id') and message.media_group_id:
                # Для медиа группы фото уже обработано выше
                pass
                
        except Exception as e:
            logger.error(f"Ошибка при скачивании фото: {e}")
        
        return photos_data

    async def _process_single_message(self, message_data, context):
        """Обрабатывает одно сообщение из очереди"""
        message = message_data['message']
        user_display_name = message_data['user_display_name']
        username = message_data['username']
        chat_id = message.chat_id
        
        try:
            # Создаем задачу для поддержания статуса "печатает"
            typing_task = asyncio.create_task(self._maintain_typing_status(context.bot, chat_id))
            
            try:
                # Анализируем контекст сообщения
                is_reply = message.reply_to_message is not None
                mentions = extract_mentions(message)
                mentioned_user = mentions[0] if mentions else None
                
                # Извлекаем текст оригинального сообщения Алины если это reply на неё
                replied_message_text = None
                if (is_reply and message.reply_to_message.from_user and 
                    message.reply_to_message.from_user.is_bot):
                    replied_message_text = (message.reply_to_message.text or 
                                          message.reply_to_message.caption or "")
                    # Убираем теги из оригинального сообщения для чистоты контекста
                    import re
                    replied_message_text = re.sub(r'\[PHOTO:[^\]]+\]', '', replied_message_text)
                    replied_message_text = re.sub(r'\[STICKER:[^\]]+\]', '', replied_message_text)
                    replied_message_text = re.sub(r'\[REACT:[^\]]+\]', '', replied_message_text)
                    replied_message_text = replied_message_text.strip()
                
                # Получаем текст сообщения (может быть подписью к фото)
                message_text = message.text or message.caption or ""
                cleaned_text = clean_message_for_processing(message_text)
                
                # Скачиваем фотографии если есть
                photos_data = await self._download_photos(message, context)
                
                # Получаем контекст чата
                chat_context = self.context_manager.format_context_for_prompt(
                    chat_id=chat_id, 
                    current_user=user_display_name,
                    last_n_messages=5
                )
                
                # Формируем информацию о пользователе
                user_info = {
                    'username': username,
                    'first_name': message.from_user.first_name,
                    'last_name': message.from_user.last_name
                }
                
                # Формируем промпт для Gemini с учетом фото
                if photos_data:
                    photo_context = f"\n\nПользователь прислал {len(photos_data)} фото"
                    if cleaned_text:
                        photo_context += f" с подписью: {cleaned_text}"
                    else:
                        photo_context += " без подписи"
                    
                    prompt = get_dima_prompt_with_context(
                        user_message=photo_context,
                        is_reply=is_reply,
                        mentioned_user=mentioned_user,
                        user_info=user_info,
                        chat_context=chat_context,
                        replied_message_text=replied_message_text
                    )
                else:
                    prompt = get_dima_prompt_with_context(
                        user_message=cleaned_text,
                        is_reply=is_reply,
                        mentioned_user=mentioned_user,
                        user_info=user_info,
                        chat_context=chat_context,
                        replied_message_text=replied_message_text
                    )
                
                # Генерируем ответ с фото если есть
                response = await self.gemini_client.generate_response(prompt, photos_data if photos_data else None)
            finally:
                # Останавливаем задачу поддержания статуса
                typing_task.cancel()
                try:
                    await typing_task
                except asyncio.CancelledError:
                    pass
            
            if response:
                # Извлекаем DELAY теги из ответа
                text_parts, delays = extract_delay_requests(response)
                
                # Если есть DELAY теги, обрабатываем сообщение по частям
                if delays:
                    await self._process_delayed_message(text_parts, delays, message, context, chat_id)
                else:
                    # Обычная обработка без задержек
                    await self._process_regular_message(response, message, context, chat_id)
            else:
                # Fallback ответ если Gemini не работает
                fallback_response = self._get_fallback_response()
                await message.reply_text(fallback_response)
        
        except Exception as e:
            logger.error(f"Ошибка при обработке сообщения: {e}")
            await message.reply_text("хз что-то сломалось 🤷‍♀️")
        
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Основной обработчик сообщений - добавляет в очередь для человечной обработки"""
        
        if not update.message:
            return
        
        message = update.message
        user_id = message.from_user.id
        chat_id = message.chat_id
        
        # Проверяем есть ли текст или фото
        has_text = bool(message.text or message.caption)
        has_photo = bool(message.photo)
        
        if not has_text and not has_photo:
            return
        
        # Получаем информацию о пользователе
        user_display_name = get_user_display_name(message)
        username = message.from_user.username or ""
        
        # Проверяем, нужно ли отвечать
        should_respond, reason = should_respond_to_message(message)
        
        # Определяем, является ли это обращением к димочке
        is_reply_to_dima = (message.reply_to_message and
                            message.reply_to_message.from_user and
                            message.reply_to_message.from_user.is_bot)
        is_mention_dima = should_respond
        
        # Сохраняем сообщение в контекст, если это обращение к димочке
        if should_respond:
            # Формируем текст для сохранения в контекст
            message_text = message.text or message.caption or ""
            if has_photo:
                photo_info = f"[фото{' с подписью' if message.caption else ''}]"
                message_text = f"{photo_info} {message_text}".strip()

            self.context_manager.add_message(
                chat_id=chat_id,
                user_id=user_id,
                username=username,
                user_display_name=user_display_name,
                message_text=message_text,
                is_reply_to_dima=is_reply_to_dima,
                is_mention_dima=is_mention_dima
            )
        
        if not should_respond:
            return
        
        logger.info(f"Добавляем сообщение в очередь: {reason}")
        
        # Инициализируем очередь для чата если её нет
        if chat_id not in self.message_queues:
            self.message_queues[chat_id] = deque()
        
        # Добавляем сообщение в очередь
        message_data = {
            'message': message,
            'user_display_name': user_display_name,
            'username': username
        }
        self.message_queues[chat_id].append(message_data)
        
        # Запускаем обработку очереди (если она еще не запущена)
        asyncio.create_task(self._process_message_queue(chat_id, context))
    
    async def _process_regular_message(self, response, message, context, chat_id):
        """Обрабатывает обычное сообщение без задержек"""
        # Извлекаем запросы на фото из ответа
        temp_response, photo_requests = extract_photo_requests(response)
        
        # Извлекаем запросы на GIF из ответа
        temp_response1_5, gif_requests = extract_gif_requests(temp_response)
        
        # Извлекаем запросы на стикеры из ответа
        temp_response2, sticker_requests = self._extract_sticker_requests(temp_response1_5)
        
        # Извлекаем запросы на реакции из ответа
        cleaned_response, reaction_requests = extract_reaction_requests(temp_response2)
        
        # Сохраняем ответ димочки в контекст (без тегов)
        self.context_manager.add_message(
            chat_id=chat_id,
            user_id=0,  # ID бота
            username="dima",
            user_display_name="димочка",
            message_text=cleaned_response if cleaned_response else response,
            is_reply_to_dima=False,
            is_mention_dima=False
        )
        
        # Отправляем текстовый ответ (если есть)
        if cleaned_response:
            message_parts = split_long_message(cleaned_response, MAX_MESSAGE_LENGTH)
            
            for part in message_parts:
                # Всегда отправляем в reply, чтобы сохранить ветку разговора
                await message.reply_text(part)
        
        # Обрабатываем запросы на фото
        if photo_requests:
            await asyncio.sleep(0.2)
            await self._send_photos(photo_requests, context, chat_id, message)
        
        # Обрабатываем запросы на GIF
        if gif_requests:
            await asyncio.sleep(0.2)
            await self._send_gifs(gif_requests, context, chat_id, message)
        
        # Обрабатываем запросы на реакции
        if reaction_requests:
            await self._send_reactions(reaction_requests, message)
            if cleaned_response:
                import random
                delay = random.uniform(1.0, 4.0)
                await asyncio.sleep(delay)
        
        # Обрабатываем запросы на стикеры
        if sticker_requests:
            if cleaned_response:
                await asyncio.sleep(0.2)
            await self._send_stickers(sticker_requests, message, context, chat_id, 
                                    not cleaned_response and not photo_requests and not gif_requests)

    async def _process_delayed_message(self, text_parts, delays, message, context, chat_id):
        """Обрабатывает сообщение с задержками"""
        if not text_parts:
            return
        
        # Отправляем первую часть сразу
        first_part = text_parts[0]
        await self._process_message_part(first_part, message, context, chat_id, is_first=True)
        
        # Сохраняем полный ответ в контекст (без тегов DELAY)
        full_response = ' '.join(text_parts)
        self.context_manager.add_message(
            chat_id=chat_id,
            user_id=0,  # ID бота
            username="dima",
            user_display_name="димочка",
            message_text=full_response,
            is_reply_to_dima=False,
            is_mention_dima=False
        )
        
        # Обрабатываем остальные части с задержками
        for i, delay in enumerate(delays):
            if i + 1 < len(text_parts):  # Проверяем что есть следующая часть
                await asyncio.sleep(delay)
                next_part = text_parts[i + 1]
                await self._process_message_part(next_part, message, context, chat_id, is_reply=True)

    async def _process_message_part(self, part_text, message, context, chat_id, is_first=False, is_reply=False):
        """Обрабатывает одну часть сообщения (с инструментами)"""
        # Извлекаем запросы на фото из части
        temp_response, photo_requests = extract_photo_requests(part_text)
        
        # Извлекаем запросы на GIF из части
        temp_response1_5, gif_requests = extract_gif_requests(temp_response)
        
        # Извлекаем запросы на стикеры из части
        temp_response2, sticker_requests = self._extract_sticker_requests(temp_response1_5)
        
        # Извлекаем запросы на реакции из части
        cleaned_response, reaction_requests = extract_reaction_requests(temp_response2)
        
        # Отправляем текстовый ответ (если есть)
        if cleaned_response:
            message_parts = split_long_message(cleaned_response, MAX_MESSAGE_LENGTH)
            
            for part in message_parts:
                # Всегда отправляем в reply, чтобы сохранить ветку разговора
                await message.reply_text(part)
        
        # Обрабатываем запросы на фото
        if photo_requests:
            await asyncio.sleep(0.2)
            await self._send_photos(photo_requests, context, chat_id, message)
        
        # Обрабатываем запросы на GIF
        if gif_requests:
            await asyncio.sleep(0.2)
            await self._send_gifs(gif_requests, context, chat_id, message)
        
        # Обрабатываем запросы на реакции
        if reaction_requests:
            await self._send_reactions(reaction_requests, message)
            if cleaned_response:
                import random
                delay = random.uniform(1.0, 4.0)
                await asyncio.sleep(delay)
        
        # Обрабатываем запросы на стикеры
        if sticker_requests:
            if cleaned_response:
                await asyncio.sleep(0.2)
            await self._send_stickers(sticker_requests, message, context, chat_id, 
                                    not cleaned_response and not photo_requests and not gif_requests)

    async def _send_photos(self, photo_requests, context, chat_id, reply_to_message=None):
        """Отправляет фотографии"""
        for photo_query in photo_requests:
            try:
                await context.bot.send_chat_action(chat_id=chat_id, action=ChatAction.UPLOAD_PHOTO)
                image_path = await search_and_download_image(photo_query.strip())
                
                if image_path and os.path.exists(image_path):
                    with open(image_path, 'rb') as photo:
                        if reply_to_message:
                            await reply_to_message.reply_photo(photo=photo)
                        else:
                            await context.bot.send_photo(chat_id=chat_id, photo=photo)
                    try:
                        os.unlink(image_path)
                    except:
                        pass
                else:
                    error_msg = "не смогла найти картинку 😔"
                    if reply_to_message:
                        await reply_to_message.reply_text(error_msg)
                    else:
                        await context.bot.send_message(chat_id=chat_id, text=error_msg)
            
            except Exception as e:
                logger.error(f"Ошибка при отправке фото: {e}")
                error_msg = "что-то с картинкой не так 🤷‍♀️"
                if reply_to_message:
                    await reply_to_message.reply_text(error_msg)
                else:
                    await context.bot.send_message(chat_id=chat_id, text=error_msg)

    async def _send_gifs(self, gif_requests, context, chat_id, reply_to_message=None):
        """Отправляет GIF анимации"""
        for gif_query in gif_requests:
            try:
                await context.bot.send_chat_action(chat_id=chat_id, action=ChatAction.UPLOAD_PHOTO)
                gif_path = await search_and_download_gif(gif_query.strip())
                
                if gif_path and os.path.exists(gif_path):
                    with open(gif_path, 'rb') as gif_file:
                        # Отправляем как анимацию (GIF)
                        if reply_to_message:
                            await reply_to_message.reply_animation(animation=gif_file)
                        else:
                            await context.bot.send_animation(chat_id=chat_id, animation=gif_file)
                    try:
                        os.unlink(gif_path)
                    except:
                        pass
                else:
                    error_msg = "не смогла найти гифку 😔"
                    if reply_to_message:
                        await reply_to_message.reply_text(error_msg)
                    else:
                        await context.bot.send_message(chat_id=chat_id, text=error_msg)
            
            except Exception as e:
                logger.error(f"Ошибка при отправке GIF: {e}")
                error_msg = "что-то с гифкой не так 🤷‍♀️"
                if reply_to_message:
                    await reply_to_message.reply_text(error_msg)
                else:
                    await context.bot.send_message(chat_id=chat_id, text=error_msg)

    async def _send_reactions(self, reaction_requests, message):
        """Отправляет реакции"""
        for reaction_type in reaction_requests:
            try:
                reaction_emoji = get_reaction_emoji(reaction_type)
                if reaction_emoji:
                    await message.set_reaction(reaction=reaction_emoji)
            except Exception as e:
                logger.error(f"Ошибка при установке реакции {reaction_type}: {e}")

    async def _send_stickers(self, sticker_requests, message, context, chat_id, is_only_sticker):
        """Отправляет стикеры"""
        for sticker_name in sticker_requests:
            try:
                sticker_id = DIMA_STICKERS[sticker_name]
                
                # Всегда отправляем стикер в reply к оригинальному сообщению
                # чтобы он был в правильной ветке разговора
                await message.reply_sticker(sticker=sticker_id)
            
            except Exception as e:
                logger.error(f"Ошибка при отправке стикера {sticker_name}: {e}")

    def _get_fallback_response(self) -> str:
        """Возвращает случайный ответ когда ИИ недоступен"""
        import random
        
        fallback_responses = [
            "хз что сказать 🤷‍♀️",
            "не работает что-то(",
            "дайте подумать...",
            "ой что-то тупит",
            "сейчас не до этого бро",
            "позже отвечу норм"
        ]
        
        return random.choice(fallback_responses)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Обработка команды /start"""
        welcome_message = """привет! я димочка ✨

пиши мне в группах если упомянешь меня или ответишь на мои сообщения 💅

го общаться! 😊"""

        await update.message.reply_text(welcome_message)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Обработка команды /help"""
        help_message = """как со мной общаться:

📝 просто напиши "димочка" в сообщении
💬 ответь на мое сообщение
🔔 упомяни меня @username

я отвечаю как милый фембойчик в чатике! 😉"""

        await update.message.reply_text(help_message)
    
    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Обработчик ошибок"""
        logger.error(f"Ошибка бота: {context.error}")