import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class ChatMessage:
    """Структура для хранения сообщения в контексте"""
    user_id: int
    username: str
    user_display_name: str
    message_text: str
    timestamp: datetime
    is_reply_to_dima: bool = False
    is_mention_dima: bool = False
    
    def to_dict(self):
        """Преобразует в словарь для JSON"""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'user_display_name': self.user_display_name,
            'message_text': self.message_text,
            'timestamp': self.timestamp.isoformat(),
            'is_reply_to_dima': self.is_reply_to_dima,
            'is_mention_dima': self.is_mention_dima
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """Создает объект из словаря"""
        return cls(
            user_id=data['user_id'],
            username=data['username'],
            user_display_name=data['user_display_name'],
            message_text=data['message_text'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            is_reply_to_dima=data.get('is_reply_to_dima', data.get('is_reply_to_alina', False)),
            is_mention_dima=data.get('is_mention_dima', data.get('is_mention_alina', False))
        )

class ChatContextManager:
    """Управляет контекстом чата для димочки"""

    def __init__(self, context_file: str = "dima_context.json", max_messages: int = 50):
        self.context_file = context_file
        self.max_messages = max_messages
        self.chat_contexts: Dict[int, List[ChatMessage]] = {}
        self.load_context()
    
    def add_message(self, chat_id: int, user_id: int, username: str,
                   user_display_name: str, message_text: str,
                   is_reply_to_dima: bool = False, is_mention_dima: bool = False):
        """Добавляет сообщение в контекст чата"""
        
        # Создаем новое сообщение
        message = ChatMessage(
            user_id=user_id,
            username=username,
            user_display_name=user_display_name,
            message_text=message_text,
            timestamp=datetime.now(),
            is_reply_to_dima=is_reply_to_dima,
            is_mention_dima=is_mention_dima
        )
        
        # Добавляем в контекст чата
        if chat_id not in self.chat_contexts:
            self.chat_contexts[chat_id] = []
        
        self.chat_contexts[chat_id].append(message)
        
        # Ограничиваем количество сообщений
        if len(self.chat_contexts[chat_id]) > self.max_messages:
            self.chat_contexts[chat_id] = self.chat_contexts[chat_id][-self.max_messages:]
        
        # Сохраняем контекст
        self.save_context()
        
        logger.info(f"Добавлено сообщение в контекст чата {chat_id} от {user_display_name}")
    
    def get_chat_context(self, chat_id: int, last_n_messages: int = 10) -> List[ChatMessage]:
        """Получает последние сообщения из контекста чата"""
        if chat_id not in self.chat_contexts:
            return []
        
        # Возвращаем только последние N сообщений
        messages = self.chat_contexts[chat_id][-last_n_messages:]
        
        # Фильтруем старые сообщения (старше 24 часов)
        cutoff_time = datetime.now() - timedelta(hours=24)
        recent_messages = [msg for msg in messages if msg.timestamp > cutoff_time]
        
        return recent_messages
    
    def format_context_for_prompt(self, chat_id: int, current_user: str, 
                                 last_n_messages: int = 5) -> str:
        """Форматирует контекст для промпта Gemini"""
        messages = self.get_chat_context(chat_id, last_n_messages)
        
        if not messages:
            return ""
        
        context_lines = ["КОНТЕКСТ ПРЕДЫДУЩИХ СООБЩЕНИЙ:"]
        
        for msg in messages:
            time_str = msg.timestamp.strftime("%H:%M")
            context_lines.append(f"[{time_str}] {msg.user_display_name}: {msg.message_text}")
        
        context_lines.append(f"\nСЕЙЧАС ПИШЕТ: {current_user}")
        context_lines.append("---")
        
        return "\n".join(context_lines)
    
    def save_context(self):
        """Сохраняет контекст в файл"""
        try:
            # Преобразуем в формат для JSON
            data = {}
            for chat_id, messages in self.chat_contexts.items():
                data[str(chat_id)] = [msg.to_dict() for msg in messages]
            
            with open(self.context_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"Ошибка сохранения контекста: {e}")
    
    def load_context(self):
        """Загружает контекст из файла"""
        try:
            if os.path.exists(self.context_file):
                with open(self.context_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Восстанавливаем объекты
                for chat_id_str, messages_data in data.items():
                    chat_id = int(chat_id_str)
                    messages = [ChatMessage.from_dict(msg_data) for msg_data in messages_data]
                    self.chat_contexts[chat_id] = messages
                
                logger.info(f"Загружен контекст для {len(self.chat_contexts)} чатов")
            
        except Exception as e:
            logger.error(f"Ошибка загрузки контекста: {e}")
            self.chat_contexts = {}
    
    def clear_old_messages(self, hours: int = 48):
        """Очищает старые сообщения"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        for chat_id in self.chat_contexts:
            original_count = len(self.chat_contexts[chat_id])
            self.chat_contexts[chat_id] = [
                msg for msg in self.chat_contexts[chat_id] 
                if msg.timestamp > cutoff_time
            ]
            
            removed_count = original_count - len(self.chat_contexts[chat_id])
            if removed_count > 0:
                logger.info(f"Удалено {removed_count} старых сообщений из чата {chat_id}")
        
        self.save_context()