import asyncio
from typing import Optional, List, Union
import logging
from google import genai
from google.genai import types
from dima_config import GEMINI_MODEL
from dima_api_manager import api_manager

logger = logging.getLogger(__name__)

class GeminiClient:
    def __init__(self):
        """Инициализация клиента Gemini"""
        self.client = None
        self.model_name = GEMINI_MODEL
        self.current_api_key = None
        
        # Настройки генерации для более естественных ответов
        self.generation_config = types.GenerateContentConfig(
            temperature=1.9,  # Больше креативности
            top_p=0.8,
            top_k=40,
            max_output_tokens=2000,  # Короткие ответы
            stop_sequences=["\n\n"]  # Останавливаем на двойном переносе
        )
    
    def _initialize_client(self, api_key: str):
        """Инициализирует клиента с новым API ключом"""
        self.client = genai.Client(api_key=api_key)
        self.current_api_key = api_key
    
    def _is_rate_limit_error(self, error: Exception) -> bool:
        """Проверяет, является ли ошибка ошибкой лимита"""
        error_str = str(error).lower()
        rate_limit_indicators = [
            'rate limit', 'quota exceeded', 'too many requests',
            'resource exhausted', 'limit exceeded', '429'
        ]
        return any(indicator in error_str for indicator in rate_limit_indicators)
    
    async def generate_response(self, prompt: str, images: Optional[List[bytes]] = None) -> Optional[str]:
        """
        Генерирует ответ от Gemini с поддержкой изображений и ротацией API ключей
        
        Args:
            prompt (str): Промпт для отправки модели
            images (Optional[List[bytes]]): Список изображений в байтах
            
        Returns:
            Optional[str]: Ответ от модели или None при ошибке
        """
        max_retries = 3  # Максимум попыток с разными ключами
        
        for attempt in range(max_retries):
            try:
                # Получаем API ключ (первый раз случайный, потом следующий)
                if attempt == 0:
                    api_key = api_manager.get_random_key()
                else:
                    api_key = api_manager.get_next_key()
                
                if not api_key:
                    logger.error("Нет доступных API ключей для запроса")
                    return None
                
                # Инициализируем клиента с новым ключом если нужно
                if self.current_api_key != api_key:
                    self._initialize_client(api_key)
                
                # Используем asyncio для неблокирующего вызова
                loop = asyncio.get_event_loop()
                
                # Формируем контент для отправки
                contents = []
                
                # Добавляем текст
                contents.append(prompt)
                
                # Добавляем изображения если есть
                if images:
                    for image_data in images:
                        # Используем правильный формат для нового SDK
                        contents.append(
                            types.Part.from_bytes(
                                data=image_data, 
                                mime_type="image/jpeg"
                            )
                        )
                
                response = await loop.run_in_executor(
                    None,
                    lambda: self.client.models.generate_content(
                        model=self.model_name,
                        contents=contents,
                        config=self.generation_config
                    )
                )
                
                if response and response.text:
                    # Обрезаем ответ если он слишком длинный
                    text = response.text.strip()
                    if len(text) > 300:  # Максимум для краткости
                        # Обрезаем по предложениям
                        sentences = text.split('. ')
                        result = sentences[0]
                        if len(result) < 150 and len(sentences) > 1:
                            result += '. ' + sentences[1]
                        return result.rstrip('.') + '.'
                    return text
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Ошибка при генерации ответа (попытка {attempt + 1}): {error_msg}")
                
                # Проверяем, является ли это ошибкой лимита
                if self._is_rate_limit_error(e):
                    # Блокируем текущий ключ
                    if self.current_api_key:
                        api_manager.block_key(self.current_api_key, f"Rate limit error: {error_msg}")
                    
                    # Если это не последняя попытка, пробуем следующий ключ
                    if attempt < max_retries - 1:
                        logger.info(f"Переключаемся на следующий API ключ (попытка {attempt + 2})")
                        continue
                else:
                    # Для других ошибок тоже блокируем ключ, но с другой причиной
                    if self.current_api_key:
                        api_manager.block_key(self.current_api_key, f"API error: {error_msg}")
                    
                    # Пробуем следующий ключ
                    if attempt < max_retries - 1:
                        logger.info(f"Переключаемся на следующий API ключ после ошибки (попытка {attempt + 2})")
                        continue
                
                # Если дошли сюда, значит все попытки исчерпаны
                break
        
        logger.error("Все попытки генерации ответа исчерпаны")
        return None
    
    async def health_check(self) -> bool:
        """Проверка работоспособности API"""
        try:
            test_response = await self.generate_response("тест")
            return test_response is not None
        except:
            return False
    
    def get_api_status(self) -> dict:
        """Возвращает статус API ключей"""
        return api_manager.get_status()