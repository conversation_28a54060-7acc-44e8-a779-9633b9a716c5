import logging
import asyncio
import signal
import sys
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters
from telegram.request import HTTPXRequest
from telegram.error import TimedOut, NetworkError

from dima_config import TELEGRAM_BOT_TOKEN
from dima_bot_handler import DimaBot

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def main():
    """Основная асинхронная функция запуска бота с retry логикой"""
    
    max_retries = 5
    retry_delay = 10  # секунд
    
    for attempt in range(max_retries):
        try:
            logger.info(f"🚀 димочка запускается... (попытка {attempt + 1}/{max_retries})")

            # Создаем экземпляр бота
            dima = DimaBot()
            
            # Создаем кастомный HTTP request с увеличенными таймаутами
            request = HTTPXRequest(
                connection_pool_size=8,
                connect_timeout=30.0,
                read_timeout=30.0,
                write_timeout=30.0,
                pool_timeout=30.0
            )
            
            # Создаем приложение с кастомным request
            application = Application.builder().token(TELEGRAM_BOT_TOKEN).request(request).build()
            
            # Добавляем обработчики команд
            application.add_handler(CommandHandler("start", dima.start_command))
            application.add_handler(CommandHandler("help", dima.help_command))

            # Добавляем обработчик сообщений
            message_handler = MessageHandler(
                (filters.TEXT | filters.PHOTO) & ~filters.COMMAND,
                dima.handle_message
            )
            application.add_handler(message_handler)

            # Добавляем обработчик ошибок
            application.add_error_handler(dima.error_handler)
            
            # Инициализируем приложение с retry
            logger.info("🔄 Инициализация приложения...")
            await application.initialize()
            
            logger.info("🔄 Запуск приложения...")
            await application.start()
            
            logger.info("🔄 Запуск polling...")
            await application.updater.start_polling(
                allowed_updates=Update.ALL_TYPES,
                drop_pending_updates=True
            )
            
            logger.info("✅ Алина успешно запущена!")
            
            # Создаем событие для graceful shutdown
            stop_event = asyncio.Event()
            
            def signal_handler():
                logger.info("👋 Получен сигнал остановки...")
                stop_event.set()
            
            # Регистрируем обработчики сигналов
            if sys.platform == "win32":
                signal.signal(signal.SIGINT, lambda s, f: signal_handler())
                signal.signal(signal.SIGTERM, lambda s, f: signal_handler())
            else:
                loop = asyncio.get_event_loop()
                for sig in (signal.SIGTERM, signal.SIGINT):
                    loop.add_signal_handler(sig, signal_handler)
            
            try:
                # Ждем сигнала остановки
                await stop_event.wait()
                logger.info("🛑 Получен сигнал остановки")
                break  # Выходим из цикла retry при нормальной остановке
                
            except KeyboardInterrupt:
                logger.info("👋 Получен KeyboardInterrupt")
                break  # Выходим из цикла retry при KeyboardInterrupt
                
            finally:
                logger.info("🛑 Останавливаем бота...")
                try:
                    await application.updater.stop()
                    await application.stop()
                    await application.shutdown()
                    logger.info("✅ Алина остановлена")
                except Exception as e:
                    logger.error(f"❌ Ошибка при остановке: {e}")
            
            # Если дошли сюда без исключений, выходим из цикла
            break
            
        except (TimedOut, NetworkError) as e:
            logger.error(f"❌ Сетевая ошибка (попытка {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"⏳ Повтор через {retry_delay} секунд...")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # Экспоненциальная задержка
            else:
                logger.error("❌ Превышено максимальное количество попыток подключения")
                raise
                
        except Exception as e:
            logger.error(f"❌ Неожиданная ошибка (попытка {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"⏳ Повтор через {retry_delay} секунд...")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2
            else:
                logger.error("❌ Превышено максимальное количество попыток запуска")
                raise

if __name__ == '__main__':
    try:
        # Запускаем асинхронную main функцию
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 Алина остановлена пользователем")
    except Exception as e:
        logger.error(f"❌ Ошибка запуска: {e}")