import re
import asyncio
import os
import tempfile
import aiohttp
import aiofiles
import random
import json
from datetime import datetime, date
from typing import List, Optional, Tuple
from telegram import Message, MessageEntity
from dima_config import (
    TRIGGER_WORDS_VARIATIONS, BOT_USERNAME,
    PIXABAY_API_KEY, PIXABAY_API_URL, PIXABAY_MAX_RESULTS,
    GOOGLE_SEARCH_API_KEY, GOOGLE_SEARCH_ENGINE_ID, GOOGLE_SEARCH_URL, GOOGLE_DAILY_LIMIT,
    GIPHY_API_KEY, GIPHY_API_URL, GIPHY_MAX_RESULTS
)

# Поддерживаемые реакции в Telegram
SUPPORTED_REACTIONS = {
    'heart': '❤️',
    'laugh': '😁', 
    'like': '👍',
    'dislike': '👎',
    'kiss': '😘'
}

# Файл для отслеживания лимитов Google API
GOOGLE_LIMITS_FILE = 'google_limits.json'

def _load_google_limits() -> dict:
    """Загружает данные о лимитах Google API"""
    try:
        if os.path.exists(GOOGLE_LIMITS_FILE):
            with open(GOOGLE_LIMITS_FILE, 'r') as f:
                return json.load(f)
    except:
        pass
    return {'date': str(date.today()), 'count': 0}

def _save_google_limits(limits_data: dict):
    """Сохраняет данные о лимитах Google API"""
    try:
        with open(GOOGLE_LIMITS_FILE, 'w') as f:
            json.dump(limits_data, f)
    except:
        pass

def _can_use_google_search() -> bool:
    """Проверяет, можно ли использовать Google Search API"""
    limits = _load_google_limits()
    today = str(date.today())
    
    # Если новый день - сбрасываем счетчик
    if limits.get('date') != today:
        limits = {'date': today, 'count': 0}
        _save_google_limits(limits)
        return True
    
    # Проверяем лимит
    return limits.get('count', 0) < GOOGLE_DAILY_LIMIT

def _increment_google_usage():
    """Увеличивает счетчик использования Google API"""
    limits = _load_google_limits()
    today = str(date.today())
    
    if limits.get('date') != today:
        limits = {'date': today, 'count': 1}
    else:
        limits['count'] = limits.get('count', 0) + 1
    
    _save_google_limits(limits)

def should_respond_to_message(message: Message) -> Tuple[bool, str]:
    """
    Определяет, должен ли димочка ответить на сообщение

    Args:
        message: Telegram сообщение

    Returns:
        Tuple[bool, str]: (должен ли отвечать, причина)
    """
    if not message:
        return False, "no_message"
    
    # Получаем текст из сообщения или подписи к фото
    text = (message.text or message.caption or "").lower()
    has_photo = bool(message.photo)
    
    # Проверяем реплай на сообщение бота
    if message.reply_to_message and message.reply_to_message.from_user:
        if message.reply_to_message.from_user.username == BOT_USERNAME:
            return True, "reply_to_bot"
    
    # Убрано: В личных чатах отвечаем на все фото
    # if has_photo and message.chat.type == 'private':
    #     return True, "photo_in_private"
    
    # Если есть текст, проверяем триггеры
    if text:
        # Проверяем упоминания димочки (только целые слова)
        for trigger in TRIGGER_WORDS_VARIATIONS:
            # Используем регулярное выражение для поиска целых слов
            pattern = r'\b' + re.escape(trigger.lower()) + r'\b'
            if re.search(pattern, text):
                return True, f"mentioned_{trigger}"
        
        # Проверяем @username бота
        if f"@{BOT_USERNAME}" in text:
            return True, "direct_mention"
    
    # В группах отвечаем на фото если есть триггерные слова в подписи
    if has_photo and text:
        for trigger in TRIGGER_WORDS_VARIATIONS:
            # Используем регулярное выражение для поиска целых слов
            pattern = r'\b' + re.escape(trigger.lower()) + r'\b'
            if re.search(pattern, text):
                return True, f"photo_with_mention_{trigger}"
    
    return False, "no_trigger"

def extract_mentions(message: Message) -> List[str]:
    """Извлекает все упоминания пользователей из сообщения"""
    mentions = []
    
    if message.entities:
        for entity in message.entities:
            if entity.type == MessageEntity.MENTION:
                # Извлекаем @username
                mention_text = message.text[entity.offset:entity.offset + entity.length]
                mentions.append(mention_text)
            elif entity.type == MessageEntity.TEXT_MENTION and entity.user:
                # Упоминание пользователя без username
                mentions.append(f"@{entity.user.first_name}")
    
    return mentions

def clean_message_for_processing(message_text: str) -> str:
    """Очищает сообщение для обработки ИИ"""
    # Убираем @username бота из текста
    cleaned = re.sub(f'@{BOT_USERNAME}', '', message_text, flags=re.IGNORECASE)
    
    # Убираем триггерные слова для димочки в начале
    for trigger in TRIGGER_WORDS_VARIATIONS:
        pattern = f'^{re.escape(trigger)}[,\\s]*'
        cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
    
    return cleaned.strip()

def is_group_chat(message: Message) -> bool:
    """Проверяет, отправлено ли сообщение в группе"""
    return message.chat.type in ['group', 'supergroup']

def get_user_display_name(message: Message) -> str:
    """Получает отображаемое имя пользователя"""
    user = message.from_user
    if user.username:
        return f"@{user.username}"
    elif user.first_name:
        return user.first_name
    else:
        return "аноним"

async def typing_delay(duration: float = 2.0):
    """Имитирует задержку печатания"""
    await asyncio.sleep(duration)

def split_long_message(text: str, max_length: int = 4096) -> List[str]:
    """Разбивает длинное сообщение на части"""
    if len(text) <= max_length:
        return [text]
    
    parts = []
    while text:
        if len(text) <= max_length:
            parts.append(text)
            break
        
        # Ищем последний перенос строки или пробел перед лимитом
        split_pos = max_length
        for i in range(max_length - 1, 0, -1):
            if text[i] in ['\n', ' ', '.', '!', '?']:
                split_pos = i + 1
                break
        
        parts.append(text[:split_pos])
        text = text[split_pos:].lstrip()
    
    return parts

def extract_photo_requests(text: str) -> Tuple[str, List[str]]:
    """
    Извлекает запросы на фото из текста
    
    Args:
        text: Текст сообщения
        
    Returns:
        Tuple[str, List[str]]: (очищенный текст, список запросов на фото)
    """
    photo_pattern = r'\[PHOTO:([^\]]+)\]'
    photo_requests = re.findall(photo_pattern, text, re.IGNORECASE)
    cleaned_text = re.sub(photo_pattern, '', text, flags=re.IGNORECASE).strip()
    
    return cleaned_text, photo_requests

def extract_gif_requests(text: str) -> Tuple[str, List[str]]:
    """
    Извлекает запросы на GIF из текста
    
    Args:
        text: Текст сообщения
        
    Returns:
        Tuple[str, List[str]]: (очищенный текст, список запросов на GIF)
    """
    gif_pattern = r'\[GIF:([^\]]+)\]'
    gif_requests = re.findall(gif_pattern, text, re.IGNORECASE)
    cleaned_text = re.sub(gif_pattern, '', text, flags=re.IGNORECASE).strip()
    
    return cleaned_text, gif_requests



def extract_reaction_requests(text: str) -> Tuple[str, List[str]]:
    """
    Извлекает запросы на реакции из текста
    
    Args:
        text: Текст сообщения
        
    Returns:
        Tuple[str, List[str]]: (очищенный текст, список типов реакций)
    """
    reaction_pattern = r'\[REACT:([^\]]+)\]'
    reaction_requests = re.findall(reaction_pattern, text, re.IGNORECASE)
    cleaned_text = re.sub(reaction_pattern, '', text, flags=re.IGNORECASE).strip()
    
    # Фильтруем только поддерживаемые реакции
    valid_reactions = []
    for reaction in reaction_requests:
        reaction_lower = reaction.lower().strip()
        if reaction_lower in SUPPORTED_REACTIONS:
            valid_reactions.append(reaction_lower)
    
    return cleaned_text, valid_reactions

def get_reaction_emoji(reaction_type: str) -> Optional[str]:
    """
    Получает эмодзи для типа реакции
    
    Args:
        reaction_type: Тип реакции (heart, laugh, like, dislike, kiss)
        
    Returns:
        Optional[str]: Эмодзи реакции или None если не поддерживается
    """
    return SUPPORTED_REACTIONS.get(reaction_type.lower().strip())

def extract_delay_requests(text: str) -> Tuple[List[str], List[float]]:
    """
    Извлекает запросы на задержки из текста и разбивает текст на части
    
    Args:
        text: Текст сообщения
        
    Returns:
        Tuple[List[str], List[float]]: (список частей текста, список задержек в секундах)
    """
    # Паттерн для поиска [DELAY:XS] где X - число
    delay_pattern = r'\[DELAY:(\d+(?:\.\d+)?)S\]'
    
    # Находим все позиции DELAY тегов
    matches = list(re.finditer(delay_pattern, text, re.IGNORECASE))
    
    if not matches:
        # Если нет DELAY тегов, возвращаем весь текст как одну часть
        return [text], []
    
    text_parts = []
    delays = []
    last_end = 0
    
    for match in matches:
        # Добавляем текст до текущего DELAY тега
        part_text = text[last_end:match.start()].strip()
        if part_text:  # Добавляем только непустые части
            text_parts.append(part_text)
        
        # Извлекаем значение задержки
        delay_value = float(match.group(1))
        delays.append(delay_value)
        
        last_end = match.end()
    
    # Добавляем оставшийся текст после последнего DELAY тега
    remaining_text = text[last_end:].strip()
    if remaining_text:
        text_parts.append(remaining_text)
    
    # Если первая часть пустая (сообщение начинается с DELAY), удаляем её
    if text_parts and not text_parts[0]:
        text_parts.pop(0)
        if delays:
            delays.pop(0)
    
    return text_parts, delays

async def search_google_images(query: str) -> Optional[str]:
    """
    Ищет и скачивает изображение через Google Custom Search API
    
    Args:
        query: Поисковый запрос
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    if not GOOGLE_SEARCH_API_KEY or not GOOGLE_SEARCH_ENGINE_ID:
        print("❌ Google Search API не настроен")
        return None
    
    if not _can_use_google_search():
        print("❌ Лимит Google Search API исчерпан на сегодня")
        return None
    
    try:
        # Параметры для Google Custom Search API
        params = {
            'key': GOOGLE_SEARCH_API_KEY,
            'cx': GOOGLE_SEARCH_ENGINE_ID,
            'q': query,
            'searchType': 'image',
            'num': 10,  # Максимум 10 результатов
            'safe': 'off',  # Отключаем безопасный поиск
            'imgSize': 'medium',
            'imgType': 'photo'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(GOOGLE_SEARCH_URL, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    items = data.get('items', [])
                    
                    if not items:
                        print(f"🔍 Google: Не найдено изображений для запроса: {query}")
                        return None
                    
                    # Увеличиваем счетчик использования
                    _increment_google_usage()
                    
                    # Выбираем случайное изображение
                    selected_image = random.choice(items)
                    image_url = selected_image.get('link')
                    
                    if image_url:
                        downloaded_path = await download_image(image_url)
                        if downloaded_path:
                            print(f"✅ Google: Скачано изображение для запроса: {query}")
                            return downloaded_path
                    
                    print(f"❌ Google: Не удалось скачать изображение для запроса: {query}")
                    return None
                else:
                    print(f"❌ Ошибка Google Search API: {response.status}")
                    return None
        
    except Exception as e:
        print(f"❌ Ошибка при поиске изображения через Google: {e}")
        return None

async def search_pixabay_images(query: str) -> Optional[str]:
    """
    Ищет и скачивает изображение через Pixabay API
    
    Args:
        query: Поисковый запрос
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    if not PIXABAY_API_KEY:
        print("❌ Pixabay API ключ не настроен")
        return None
    
    try:
        # Параметры для Pixabay API
        params = {
            'key': PIXABAY_API_KEY,
            'q': query,
            'image_type': 'photo',
            'orientation': 'all',
            'category': 'all',
            'safesearch': 'false',  # Разрешаем разный контент для "треша"
            'per_page': min(PIXABAY_MAX_RESULTS, 200),  # Максимум 200 на запрос
            'order': 'popular',
            'lang': 'ru'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(PIXABAY_API_URL, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    hits = data.get('hits', [])
                    
                    if not hits:
                        print(f"🔍 Pixabay: Не найдено изображений для запроса: {query}")
                        return None
                    
                    # Выбираем случайное изображение
                    selected_image = random.choice(hits)
                    
                    # Пробуем разные размеры изображения (от большего к меньшему)
                    image_urls = [
                        selected_image.get('largeImageURL'),
                        selected_image.get('webformatURL'),
                        selected_image.get('previewURL')
                    ]
                    
                    for image_url in image_urls:
                        if image_url:
                            downloaded_path = await download_image(image_url)
                            if downloaded_path:
                                print(f"✅ Pixabay: Скачано изображение для запроса: {query}")
                                return downloaded_path
                    
                    print(f"❌ Pixabay: Не удалось скачать изображение для запроса: {query}")
                    return None
                else:
                    print(f"❌ Ошибка Pixabay API: {response.status}")
                    return None
        
    except Exception as e:
        print(f"❌ Ошибка при поиске изображения через Pixabay: {e}")
        return None

async def search_giphy_gif(query: str) -> Optional[str]:
    """
    Ищет и скачивает GIF через Giphy API
    
    Args:
        query: Поисковый запрос
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    if not GIPHY_API_KEY:
        print("❌ Giphy API ключ не настроен")
        return None
    
    try:
        # Параметры для Giphy API
        params = {
            'api_key': GIPHY_API_KEY,
            'q': query,
            'limit': GIPHY_MAX_RESULTS,
            'offset': 0,
            'rating': 'r',  # Разрешаем разный контент
            'lang': 'ru'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(GIPHY_API_URL, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    gifs = data.get('data', [])
                    
                    if not gifs:
                        print(f"🔍 Giphy: Не найдено GIF для запроса: {query}")
                        return None
                    
                    # Выбираем случайный GIF
                    selected_gif = random.choice(gifs)
                    
                    # Пробуем разные размеры GIF (от большего к меньшему)
                    gif_urls = []
                    images = selected_gif.get('images', {})
                    
                    # Добавляем URL в порядке предпочтения
                    if 'original' in images:
                        gif_urls.append(images['original'].get('url'))
                    if 'fixed_height' in images:
                        gif_urls.append(images['fixed_height'].get('url'))
                    if 'fixed_width' in images:
                        gif_urls.append(images['fixed_width'].get('url'))
                    if 'downsized' in images:
                        gif_urls.append(images['downsized'].get('url'))
                    
                    for gif_url in gif_urls:
                        if gif_url:
                            downloaded_path = await download_image(gif_url)
                            if downloaded_path:
                                print(f"✅ Giphy: Скачан GIF для запроса: {query}")
                                return downloaded_path
                    
                    print(f"❌ Giphy: Не удалось скачать GIF для запроса: {query}")
                    return None
                else:
                    print(f"❌ Ошибка Giphy API: {response.status}")
                    return None
        
    except Exception as e:
        print(f"❌ Ошибка при поиске GIF через Giphy: {e}")
        return None

async def search_pixabay_gif(query: str) -> Optional[str]:
    """
    Ищет и скачивает GIF через Pixabay API
    
    Args:
        query: Поисковый запрос
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    if not PIXABAY_API_KEY:
        print("❌ Pixabay API ключ не настроен")
        return None
    
    try:
        # Параметры для Pixabay API (ищем анимации)
        params = {
            'key': PIXABAY_API_KEY,
            'q': query,
            'image_type': 'animation',  # Ищем анимации/GIF
            'orientation': 'all',
            'category': 'all',
            'safesearch': 'false',
            'per_page': min(PIXABAY_MAX_RESULTS, 200),
            'order': 'popular',
            'lang': 'ru'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(PIXABAY_API_URL, params=params, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    hits = data.get('hits', [])
                    
                    if not hits:
                        print(f"🔍 Pixabay: Не найдено GIF для запроса: {query}")
                        return None
                    
                    # Выбираем случайный GIF
                    selected_gif = random.choice(hits)
                    
                    # Пробуем разные размеры GIF
                    gif_urls = [
                        selected_gif.get('largeImageURL'),
                        selected_gif.get('webformatURL'),
                        selected_gif.get('previewURL')
                    ]
                    
                    for gif_url in gif_urls:
                        if gif_url:
                            downloaded_path = await download_image(gif_url)
                            if downloaded_path:
                                print(f"✅ Pixabay: Скачан GIF для запроса: {query}")
                                return downloaded_path
                    
                    print(f"❌ Pixabay: Не удалось скачать GIF для запроса: {query}")
                    return None
                else:
                    print(f"❌ Ошибка Pixabay API: {response.status}")
                    return None
        
    except Exception as e:
        print(f"❌ Ошибка при поиске GIF через Pixabay: {e}")
        return None

async def search_and_download_gif(query: str) -> Optional[str]:
    """
    Ищет и скачивает GIF, сначала через Giphy, потом через Pixabay
    
    Args:
        query: Поисковый запрос
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    # Сначала пробуем Giphy API (лучше для GIF)
    result = await search_giphy_gif(query)
    if result:
        return result
    
    print("🔄 Giphy не дал результат, пробуем Pixabay...")
    
    # Если Giphy не сработал, используем Pixabay
    return await search_pixabay_gif(query)

async def search_and_download_image(query: str) -> Optional[str]:
    """
    Ищет и скачивает изображение, сначала через Google, потом через Pixabay
    
    Args:
        query: Поисковый запрос
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    # Сначала пробуем Google Custom Search API
    if _can_use_google_search():
        result = await search_google_images(query)
        if result:
            return result
        print("🔄 Google не дал результат, пробуем Pixabay...")
    else:
        print("⚠️ Лимит Google исчерпан, используем Pixabay")
    
    # Если Google не сработал или лимит исчерпан, используем Pixabay
    return await search_pixabay_images(query)

async def download_image(url: str) -> Optional[str]:
    """
    Скачивает изображение по URL
    
    Args:
        url: URL изображения
        
    Returns:
        Optional[str]: Путь к скачанному файлу или None
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    # Определяем расширение файла
                    content_type = response.headers.get('content-type', '')
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        ext = '.jpg'
                    elif 'png' in content_type:
                        ext = '.png'
                    elif 'gif' in content_type:
                        ext = '.gif'
                    elif 'webp' in content_type:
                        ext = '.webp'
                    else:
                        ext = '.jpg'  # По умолчанию
                    
                    # Создаем временный файл
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=ext)
                    temp_path = temp_file.name
                    temp_file.close()
                    
                    # Сохраняем изображение
                    async with aiofiles.open(temp_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            await f.write(chunk)
                    
                    return temp_path
                    
    except Exception as e:
        print(f"Ошибка при скачивании изображения: {e}")
        return None