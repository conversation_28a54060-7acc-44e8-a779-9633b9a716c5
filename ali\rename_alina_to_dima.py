#!/usr/bin/env python3
"""
Скрипт для переименования файлов с 'alina' на 'dima'
"""

import os
import glob

def rename_files():
    """Переименовывает все файлы с префиксом alina_ на dima_"""
    
    # Находим все файлы с префиксом alina_
    alina_files = glob.glob("alina_*.py")
    
    if not alina_files:
        print("Файлы с префиксом 'alina_' не найдены")
        return
    
    print(f"Найдено {len(alina_files)} файлов для переименования:")
    
    renamed_count = 0
    
    for old_filename in alina_files:
        # Заменяем alina_ на dima_
        new_filename = old_filename.replace("alina_", "dima_")
        
        try:
            # Проверяем, что новый файл не существует
            if os.path.exists(new_filename):
                print(f"⚠️  Файл {new_filename} уже существует, пропускаем {old_filename}")
                continue
            
            # Переименовываем файл
            os.rename(old_filename, new_filename)
            print(f"✅ {old_filename} → {new_filename}")
            renamed_count += 1
            
        except Exception as e:
            print(f"❌ Ошибка при переименовании {old_filename}: {e}")
    
    print(f"\nПереименовано файлов: {renamed_count}")

if __name__ == "__main__":
    print("🔄 Начинаем переименование файлов...")
    rename_files()
    print("✨ Готово!")