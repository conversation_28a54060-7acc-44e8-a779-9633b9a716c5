#!/usr/bin/env python3
"""
Скрипт для переименования всех файлов с добавлением приставки 'alina_' 
и обновления импортов в коде
"""

import os
import re
import shutil

def rename_files():
    """Переименовывает все Python файлы с добавлением приставки alina_"""
    
    # Список файлов для переименования (исключаем этот скрипт и служебные файлы)
    files_to_rename = [
        'main.py',
        'bot_handler.py', 
        'gemini_client.py',
        'alina_personality.py',
        'config.py',
        'utils.py'
    ]
    
    # Словарь старое_имя -> новое_имя
    rename_map = {}
    
    print("🔄 Переименовываем файлы...")
    
    for old_name in files_to_rename:
        if os.path.exists(old_name):
            # Добавляем приставку alina_ если её ещё нет
            if not old_name.startswith('alina_'):
                new_name = f'alina_{old_name}'
            else:
                new_name = old_name
                
            rename_map[old_name] = new_name
            
            if old_name != new_name:
                os.rename(old_name, new_name)
                print(f"✅ {old_name} -> {new_name}")
            else:
                print(f"⏭️  {old_name} уже имеет правильное имя")
    
    return rename_map

def update_imports_in_file(filepath, rename_map):
    """Обновляет импорты в конкретном файле"""
    
    if not os.path.exists(filepath):
        return
        
    print(f"🔧 Обновляем импорты в {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Обновляем импорты для каждого переименованного файла
    for old_name, new_name in rename_map.items():
        if old_name != new_name:
            # Убираем расширение .py для импортов
            old_module = old_name.replace('.py', '')
            new_module = new_name.replace('.py', '')
            
            # Паттерны для поиска импортов
            patterns = [
                # from module import ...
                (rf'\bfrom\s+{re.escape(old_module)}\s+import\b', f'from {new_module} import'),
                # import module
                (rf'\bimport\s+{re.escape(old_module)}\b', f'import {new_module}'),
                # import module as alias
                (rf'\bimport\s+{re.escape(old_module)}\s+as\s+', f'import {new_module} as '),
            ]
            
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content)
    
    # Записываем обновленный контент только если что-то изменилось
    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Импорты обновлены в {filepath}")
    else:
        print(f"⏭️  Импорты в {filepath} не требуют обновления")

def main():
    """Основная функция скрипта"""
    
    print("🚀 Запуск скрипта переименования файлов Алины...")
    print("=" * 50)
    
    # Переименовываем файлы
    rename_map = rename_files()
    
    print("\n📝 Обновляем импорты...")
    
    # Обновляем импорты во всех переименованных файлах
    for old_name, new_name in rename_map.items():
        update_imports_in_file(new_name, rename_map)
    
    print("\n🎉 Переименование завершено!")
    print("=" * 50)
    
    # Показываем итоговую структуру
    print("\n📁 Итоговая структура файлов:")
    python_files = [f for f in os.listdir('.') if f.endswith('.py')]
    for file in sorted(python_files):
        print(f"   📄 {file}")
    
    print(f"\n✨ Теперь все файлы имеют приставку 'alina_'")
    print("🔧 Импорты обновлены автоматически")
    print("\n🚀 Для запуска бота используйте: python alina_main.py")

if __name__ == "__main__":
    main()