#!/usr/bin/env python3
"""
Тест API ключей Gemini
Проверяет каждый ключ из списка на работоспособность
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any
import time

# Список API ключей для тестирования
API_KEYS = [
    "AIzaSyBGzoS-mUQgImGlcEaX1QdfyznNwTZSKBo",
    "AIzaSyDwt7D5zJ1jlAxgqh7_cQQl4UwjK2I6m2g",
    "AIzaSyACs7h8c_0m2-WUm6iOvuQIcynKObPwyWc",
    "AIzaSyAsspWKTaD-vBuL4w03GgYfgFMzdSb4qVU",
    "AIzaSyBtWZhKwaA5pKjSkD2IjsxOOCH-SQwpsQU",
    "AIzaSyAgo7YgVRofruAm4Q7-C6LPTR5z9g2sFME",
    "AIzaSyCWQn23gkC42JGL5W5O4vEcKxSsgqm3Lhw",
    "AIzaSyDLbOB5tze88GzRdt2ZI4Us0Mm5gjS_9rI",
    "AIzaSyBMGlOVwfmci8C4Rvm4Gb2kVMaKQr1Z2wc",
    "AIzaSyDGiB3DOmmosBqTqKQu2sKB4xeFf0GXYvY",
    "AIzaSyDGvR1YrmU8Asj9TR0PThjBpJmAetRytqw",
    "AIzaSyBAtHWM-FthYoYxm1lgKX1uh0fmJO5yesw",
    "AIzaSyAhwlKB__mvi_Gp8ciIRQBqs2DqeA8_R4o",
    "AIzaSyD9UV-_dMGV2sqol5rCEoZqPTXi0dbf7tM",
    "AIzaSyB2JihqbiDE4Z8T3otu14Eqt34vA9_mn5w",
    "AIzaSyCTyLtQVsFsdCFl3Kh4BMCgJGoFibKFBlo",
    "AIzaSyBf_zHl2pe-jMc9UVRdvEkWg5A3BmpVn6k",
    "AIzaSyC5zWirvrpVTw_uOK5NusCxhqBypSftNZ0",
    "AIzaSyDOXlNnwkr61RLOdElX3y_KMtbSqpgdLe0",
    "AIzaSyAai8RmvV0srj0b_uZLQwYhP7yuwmDxD-I",
    "AIzaSyA6ROogrGMD8sENVdYTSqiCp4wb_aih_O4",
    "AIzaSyB1ZEgMjh68w6aX6z3rW8TwTd2SLx9lkJc",
    "AIzaSyD2ZOXPpQYPxmGVOWrzJhjQU65KWXC9l7s",
    "AIzaSyAW8nto6mQTCtuNTzRcnod1xWtFe2eHhyg",
    "AIzaSyAYvtNyPQXwDbd8-NqExoi0-X_4Oas2emA",
    "AIzaSyAORhrhY8ECNkPxxMPN3eJlxm1ddPcxzrQ",
    "AIzaSyCkbfaRF2fL-9h6Fc8Bj10UF-Xnn7pKBQ0",
    "AIzaSyBMKQoKx3_2kDuqPRwDfkzWL5lV5hsK4Nw",
    "AIzaSyBO0p7hrpQm2iEFHzNwI5HALwH0_GYz4ds",
    "AIzaSyDU-f7TIxA5WYlFMnSyW-FvwwYSaZ2cR9g",
    "AIzaSyBZRAbQOnrgE_B4L4zmHSmvVsu65J9kqts",
    "AIzaSyAkBD6Yw6x2YoDmDYoKaNQYIlxc9L0gEKw",
    "AIzaSyCFCXjS9EeOXWeTD4AL7CjlZN0XlWUY3J0",
    "AIzaSyAE2cNIRAujoBn9RX5uOQYRC6n-NfaUbx4",
    "AIzaSyDNYky37vWdIeM85mn8MrQPHDtvwT7Olqs",
    "AIzaSyC4CP6wXMdj0Zpz2O9CCRLq_0H-S0HnZuo",
    "AIzaSyCNoz4qAAjuzSrcW-hB4YG0zheex9ozeeY",
    "AIzaSyDijHv8qgwBJOjKF9PwmOK4blrIUTPA4LY",
    "AIzaSyBG1O6CWxxobgavZpY2P0Cr86iPtqm3PYU",
    "AIzaSyBQ2mflBgtNUDPw_lyiOsGjVCldqAjshFA",
    "AIzaSyCxcoPKqtgm4Yj_HHnoVyo7hGLqKrxE7cM",
    "AIzaSyBQFYkwwHeEQnG_g5u0Po0Jddsl9ewnQOU",
    "AIzaSyBu7MrtQ6WgoozapCXVWVcMUyQZWtnvgUk",
    "AIzaSyCydyho2fDEJrYnn2GYGE3j1AD9HA8IW_s",
    "AIzaSyAGSNMSqiVonw8N7F5I0eTs3M2pJQ9iDW8",
    "AIzaSyD4QF9kchSnd43IRPFmw5TI7mz87f-3gro",
    "AIzaSyCrE2SJwmZFZQ2dfFUWiUns3NvkwLWPa5Q",
    "AIzaSyDTo6ysMUViZSdb4QbG28KXRWv1hTS2jlQ",
    "AIzaSyDAHT4RnsEeYeOWREuSBNbuUU6vrOENE1U",
    "AIzaSyD7qtsY-k13MnOLOZB5CxDxlGCwzVQ4vWE",
    "AIzaSyAJV_sZNAs4W5jYvawwoaIsFSg6I0lvr_s",
    "AIzaSyDq58KYIzKglneBERG5-pzHcK_4GTCk-WM",
    "AIzaSyB8m7UEeGFkLTxh0rzmvOTZot0RTJE8Vi4",
    "AIzaSyAjdNQiTzQzxqQclP_qTMnBmbBZyGv3qZ0",
    "AIzaSyAL6_uzOrDm1CLulV2rdgmE1o-RQA5SFiA",
    "AIzaSyA2K_W7-rmnxlgmpzxjvw8vHHq7HO_lD2s",
    "AIzaSyCbvdy6BtpnpBj0W_GVAFpb3BJJcoIuPss",
    "AIzaSyA7RdfWMx6PSlJIrXm1zirr7nTtR4cMm3U",
    "AIzaSyA85o_3WDKyESZJs643qWD65zeWZ0G_NYU",
    "AIzaSyCqXpj-CLVNr8OPlTbdMS-9LJLp0C3Rgmo",
    "AIzaSyAOOALFIuCO4V2xTJ3bVMPEWqvawQ536tA",
    "AIzaSyBdu5p4aHp87XNRm2bjQ4w6j6HpRJObBLw",
    "AIzaSyBrwkH1LyqB1x4LQJXJv_XA9ZoV-8N2o4E",
    "AIzaSyAu4cYRLHBlQUihDJ5BjwJYctV8568uamM"
]

class GeminiAPITester:
    def __init__(self):
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent"
        self.test_message = "Привет! Как дела?"
        self.results = []
        
    async def test_single_key(self, session: aiohttp.ClientSession, api_key: str, index: int) -> Dict[str, Any]:
        """Тестирует один API ключ"""
        url = f"{self.base_url}?key={api_key}"
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": self.test_message
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 100
            }
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        start_time = time.time()
        
        try:
            async with session.post(url, json=payload, headers=headers, timeout=30) as response:
                response_time = time.time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    if 'candidates' in data and len(data['candidates']) > 0:
                        response_text = data['candidates'][0]['content']['parts'][0]['text']
                        return {
                            "index": index + 1,
                            "key": api_key,
                            "status": "SUCCESS",
                            "response_time": round(response_time, 2),
                            "response": response_text[:100] + "..." if len(response_text) > 100 else response_text,
                            "error": None
                        }
                    else:
                        return {
                            "index": index + 1,
                            "key": api_key,
                            "status": "ERROR",
                            "response_time": round(response_time, 2),
                            "response": None,
                            "error": "No candidates in response"
                        }
                else:
                    error_text = await response.text()
                    return {
                        "index": index + 1,
                        "key": api_key,
                        "status": "ERROR",
                        "response_time": round(response_time, 2),
                        "response": None,
                        "error": f"HTTP {response.status}: {error_text[:200]}"
                    }
                    
        except asyncio.TimeoutError:
            return {
                "index": index + 1,
                "key": api_key,
                "status": "TIMEOUT",
                "response_time": round(time.time() - start_time, 2),
                "response": None,
                "error": "Request timeout (30s)"
            }
        except Exception as e:
            return {
                "index": index + 1,
                "key": api_key,
                "status": "ERROR",
                "response_time": round(time.time() - start_time, 2),
                "response": None,
                "error": str(e)
            }
    
    async def test_all_keys(self, batch_size: int = 10, delay: float = 1.0):
        """Тестирует все ключи батчами"""
        print(f"🚀 Начинаем тестирование {len(API_KEYS)} API ключей...")
        print(f"📦 Размер батча: {batch_size}, задержка: {delay}с\n")
        
        connector = aiohttp.TCPConnector(limit=50, limit_per_host=10)
        timeout = aiohttp.ClientTimeout(total=60)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # Обрабатываем ключи батчами
            for i in range(0, len(API_KEYS), batch_size):
                batch = API_KEYS[i:i + batch_size]
                batch_start = i
                
                print(f"🔄 Обрабатываем батч {i//batch_size + 1}/{(len(API_KEYS) + batch_size - 1)//batch_size} (ключи {batch_start + 1}-{min(batch_start + batch_size, len(API_KEYS))})")
                
                # Создаем задачи для текущего батча
                tasks = []
                for j, key in enumerate(batch):
                    task = self.test_single_key(session, key, batch_start + j)
                    tasks.append(task)
                
                # Выполняем задачи батча
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Обрабатываем результаты
                for result in batch_results:
                    if isinstance(result, Exception):
                        print(f"❌ Ошибка выполнения: {result}")
                    else:
                        self.results.append(result)
                        self.print_result(result)
                
                # Задержка между батчами (кроме последнего)
                if i + batch_size < len(API_KEYS):
                    await asyncio.sleep(delay)
                    print()
    
    def print_result(self, result: Dict[str, Any]):
        """Выводит результат тестирования одного ключа"""
        status_emoji = {
            "SUCCESS": "✅",
            "ERROR": "❌", 
            "TIMEOUT": "⏰"
        }
        
        emoji = status_emoji.get(result["status"], "❓")
        key_short = result["key"][:20] + "..."
        
        print(f"{emoji} #{result['index']:2d} | {key_short} | {result['response_time']:4.1f}s | {result['status']}")
        
        if result["status"] == "SUCCESS" and result["response"]:
            print(f"    💬 {result['response']}")
        elif result["error"]:
            print(f"    🚫 {result['error']}")
    
    def print_summary(self):
        """Выводит итоговую статистику"""
        if not self.results:
            print("❌ Нет результатов для анализа")
            return
            
        total = len(self.results)
        success = len([r for r in self.results if r["status"] == "SUCCESS"])
        errors = len([r for r in self.results if r["status"] == "ERROR"])
        timeouts = len([r for r in self.results if r["status"] == "TIMEOUT"])
        
        print(f"\n{'='*60}")
        print(f"📊 ИТОГОВАЯ СТАТИСТИКА")
        print(f"{'='*60}")
        print(f"🔢 Всего ключей: {total}")
        print(f"✅ Рабочих: {success} ({success/total*100:.1f}%)")
        print(f"❌ С ошибками: {errors} ({errors/total*100:.1f}%)")
        print(f"⏰ Таймауты: {timeouts} ({timeouts/total*100:.1f}%)")
        
        if success > 0:
            working_keys = [r["key"] for r in self.results if r["status"] == "SUCCESS"]
            avg_time = sum(r["response_time"] for r in self.results if r["status"] == "SUCCESS") / success
            
            print(f"\n🚀 Среднее время ответа рабочих ключей: {avg_time:.2f}с")
            print(f"\n✅ РАБОЧИЕ КЛЮЧИ ({len(working_keys)}):")
            for i, key in enumerate(working_keys, 1):
                print(f"  {i:2d}. {key}")
    
    def save_results(self, filename: str = "api_keys_test_results.json"):
        """Сохраняет результаты в JSON файл"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "total_keys": len(API_KEYS),
                    "results": self.results,
                    "summary": {
                        "total": len(self.results),
                        "success": len([r for r in self.results if r["status"] == "SUCCESS"]),
                        "errors": len([r for r in self.results if r["status"] == "ERROR"]),
                        "timeouts": len([r for r in self.results if r["status"] == "TIMEOUT"])
                    }
                }, f, ensure_ascii=False, indent=2)
            print(f"\n💾 Результаты сохранены в {filename}")
        except Exception as e:
            print(f"\n❌ Ошибка сохранения: {e}")

async def main():
    """Главная функция"""
    tester = GeminiAPITester()
    
    try:
        await tester.test_all_keys(batch_size=5, delay=2.0)  # Небольшие батчи с задержкой
        tester.print_summary()
        tester.save_results()
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Тестирование прервано пользователем")
        if tester.results:
            tester.print_summary()
            tester.save_results()
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")

if __name__ == "__main__":
    asyncio.run(main())