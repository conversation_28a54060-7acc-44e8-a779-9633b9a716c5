#!/usr/bin/env python3
"""
Тест Google Custom Search API для поиска изображений
"""

import asyncio
import os
import shutil
from dima_utils import search_google_images, search_and_download_image

async def test_google_search():
    """Тестирует поиск изображений через Google API"""
    
    # Создаем папку для сохранения изображений
    test_folder = "test_images"
    if not os.path.exists(test_folder):
        os.makedirs(test_folder)
    
    print("🔍 Тестируем Google Custom Search API...")
    print("=" * 50)
    
    # Тестовые запросы
    test_queries = [
        "кот",
        "собака", 
        "природа",
        "машина",
        "еда"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Ищем: '{query}'")
        print("-" * 30)
        
        try:
            # Пробуем найти изображение
            image_path = await search_google_images(query)
            
            if image_path and os.path.exists(image_path):
                # Определяем расширение файла
                _, ext = os.path.splitext(image_path)
                if not ext:
                    ext = '.jpg'
                
                # Копируем в папку test_images с понятным именем
                new_path = os.path.join(test_folder, f"{query}_{i}{ext}")
                shutil.copy2(image_path, new_path)
                
                # Удаляем временный файл
                try:
                    os.unlink(image_path)
                except:
                    pass
                
                print(f"✅ Успешно! Сохранено: {new_path}")
                
                # Показываем размер файла
                file_size = os.path.getsize(new_path)
                print(f"📁 Размер файла: {file_size} байт ({file_size/1024:.1f} KB)")
                
            else:
                print(f"❌ Не удалось найти изображение для '{query}'")
                
        except Exception as e:
            print(f"❌ Ошибка при поиске '{query}': {e}")
        
        # Небольшая пауза между запросами
        await asyncio.sleep(1)
    
    print("\n" + "=" * 50)
    print("🏁 Тест завершен!")
    
    # Показываем что получилось
    if os.path.exists(test_folder):
        files = os.listdir(test_folder)
        if files:
            print(f"📂 Сохранено {len(files)} изображений в папке '{test_folder}':")
            for file in files:
                file_path = os.path.join(test_folder, file)
                file_size = os.path.getsize(file_path)
                print(f"   • {file} ({file_size/1024:.1f} KB)")
        else:
            print(f"📂 Папка '{test_folder}' пуста - ничего не найдено")

async def test_combined_search():
    """Тестирует комбинированный поиск (Google + Pixabay)"""
    
    print("\n🔄 Тестируем комбинированный поиск...")
    print("=" * 50)
    
    test_folder = "test_images"
    if not os.path.exists(test_folder):
        os.makedirs(test_folder)
    
    # Тест с запросом который может не найтись в Google
    query = "редкий тест запрос 12345"
    print(f"Ищем: '{query}' (должен переключиться на Pixabay)")
    
    try:
        image_path = await search_and_download_image(query)
        
        if image_path and os.path.exists(image_path):
            # Сохраняем
            _, ext = os.path.splitext(image_path)
            if not ext:
                ext = '.jpg'
            
            new_path = os.path.join(test_folder, f"combined_test{ext}")
            shutil.copy2(image_path, new_path)
            
            try:
                os.unlink(image_path)
            except:
                pass
            
            print(f"✅ Комбинированный поиск работает! Сохранено: {new_path}")
        else:
            print("❌ Комбинированный поиск не дал результата")
            
    except Exception as e:
        print(f"❌ Ошибка комбинированного поиска: {e}")

if __name__ == "__main__":
    print("🚀 Запуск тестов Google Custom Search API")
    print("=" * 60)
    
    # Запускаем тесты
    asyncio.run(test_google_search())
    asyncio.run(test_combined_search())
    
    print("\n✨ Все тесты завершены!")