# DJST - Daily Journal Summary Telegram Bot
"""
DJST - модуль для создания ежедневных дайджестов сообщений из Telegram чатов.

Основные компоненты:
- config.py - конфигурация бота
- database.py - работа с базой данных SQLite
- ai_handler.py - обработка AI запросов через Gemini
- telegraph_handler.py - создание статей в Telegraph
- memory_handler.py - управление памятью чатов
- main.py - основной модуль бота
"""

__version__ = "1.0.0"
__author__ = "DJST Team"

# Импортируем основные компоненты для удобства использования
from .main import *

__all__ = ["main"]
