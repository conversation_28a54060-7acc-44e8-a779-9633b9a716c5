import logging
from typing import Dict, Optional
from datetime import datetime
import pytz
from google import genai
from google.genai import types
from .config import (
    GEMINI_API_KEY,
    GEMINI_PRO_MODEL,
    GEMINI_FLASH_MODEL,
    DIGEST_SYSTEM_PROMPT
)
from .memory_handler import format_memory_for_ai

# Не переопределяем basicConfig, используем уже настроенное логирование
logger = logging.getLogger(__name__)

class AIHandler:
    def __init__(self):
        """Инициализация AI клиента"""
        try:
            self.client = genai.Client(api_key=GEMINI_API_KEY)
            logger.info("AI клиент инициализирован успешно")
        except Exception as e:
            logger.error(f"Ошибка инициализации AI клиента: {e}")
            self.client = None

    def generate_digest(self, messages_text: str, chat_title: str = "чат", chat_id: int = None) -> Dict[str, str]:
        """
        Генерация дайджеста сообщений

        Args:
            messages_text: Отформатированный текст сообщений
            chat_title: Название чата
            chat_id: ID чата для получения памяти (опционально)

        Returns:
            Dict с ключом 'digest'
        """
        if not self.client:
            logger.error("AI клиент не инициализирован")
            return {
                'digest': "Ошибка: AI клиент недоступен"
            }
        
        try:
            # Получаем текущее время по МСК
            msk_tz = pytz.timezone('Europe/Moscow')
            current_time_msk = datetime.now(msk_tz).strftime('%Y-%m-%d %H:%M:%S МСК')

            # Формируем полный промпт для дайджеста с текущим временем
            system_prompt_with_time = DIGEST_SYSTEM_PROMPT.format(current_time_msk=current_time_msk)

            # Добавляем информацию из памяти чата, если она есть
            memory_info = ""
            if chat_id:
                chat_memory = format_memory_for_ai(chat_id)
                if chat_memory:
                    memory_info = f"\n\n{chat_memory}\n"

            full_prompt = f"{system_prompt_with_time}{memory_info}\n\nСами сообщения:\n{messages_text}"
            
            # Генерируем дайджест через gemini-2.5-pro с максимальными параметрами
            logger.info("Генерация дайджеста через Gemini Pro с максимальными параметрами...")
            digest_response = self.client.models.generate_content(
                model=GEMINI_PRO_MODEL,
                contents=full_prompt,
                config=types.GenerateContentConfig(
                    thinking_config=types.ThinkingConfig(
                        thinking_budget=32768  # Максимальный бюджет размышлений для Gemini 2.5 Pro
                    ),
                    max_output_tokens=65536,  # Максимальное количество выходных токенов
                    temperature=0.7  # Оптимальная температура для креативности
                )
            )
            
            digest_text = digest_response.text if digest_response.text else "Не удалось сгенерировать дайджест"

            logger.info("Дайджест сгенерирован успешно")

            return {
                'digest': digest_text
            }
            
        except Exception as e:
            logger.error(f"Ошибка генерации дайджеста: {e}")
            return {
                'digest': f"Ошибка генерации дайджеста: {str(e)}"
            }

    def test_connection(self) -> bool:
        """Тестирование соединения с AI API"""
        if not self.client:
            return False
        
        try:
            response = self.client.models.generate_content(
                model=GEMINI_FLASH_MODEL,
                contents="Тест соединения. Ответь 'OK'",
                config=types.GenerateContentConfig(
                    max_output_tokens=100,  # Минимальное количество токенов для теста
                    temperature=0.1  # Низкая температура для предсказуемого ответа
                )
            )
            return bool(response.text)
        except Exception as e:
            logger.error(f"Ошибка тестирования соединения: {e}")
            return False

# Глобальный экземпляр AI обработчика
ai_handler = AIHandler()

def generate_digest(messages_text: str, chat_title: str = "чат", chat_id: int = None) -> Dict[str, str]:
    """Функция-обертка для генерации дайджеста"""
    return ai_handler.generate_digest(messages_text, chat_title, chat_id)

def test_ai_connection() -> bool:
    """Функция-обертка для тестирования соединения"""
    return ai_handler.test_connection()


