# Конфигурация Telegram бота с AI
import os

# API ключи
BOT_TOKEN = "8052938496:AAFv8Y3b2mgmh5a-HhheZsPqn_lbZR8Nxb4"
GEMINI_API_KEY = "AIzaSyAJm3mGBlYgjYGR1o1jAMkky3WIGNYxVq4"

# Настройки базы данных
# Получаем путь к текущей папке (digest) и создаем полный путь к базе данных
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DATABASE_NAME = os.path.join(CURRENT_DIR, "messages.db")

# Настройки Telegraph
TELEGRAPH_SHORT_NAME = "digest"
TELEGRAPH_AUTHOR_NAME = "sh: Digest"
TELEGRAPH_AUTHOR_URL = ""

# Настройки AI
GEMINI_PRO_MODEL = "gemini-2.5-pro"
GEMINI_FLASH_MODEL = "gemini-2.5-flash"

# Параметры модели Gemini 2.5 Pro (максимальные значения)
# Thinking Budget: 32768 токенов (максимум для размышлений)
# Output Tokens: 65536 токенов (максимум для выходных данных)
# Для Gemini 2.5 Flash используются более умеренные значения

# Системный промпт для дайджеста
DIGEST_SYSTEM_PROMPT = """Создай красивую и подробную Telegraph статью из сообщений чата.

⏰ Время: {current_time_msk}
Ориентируйся на временные периоды:
• утром (06:00-12:00) • днем (12:00-18:00) • вечером (18:00-23:59) • ночью (00:00-06:00)

✨ ПРИНЦИПЫ СОЗДАНИЯ:
• Сразу начинай с контента (без слов "дайджест")
• Группируй сообщения по времени и темам
• Подробно раскрывай каждую тему и обсуждение
• Добавляй контекст и детали разговоров
• Включай реакции и мнения участников
• Делай статью живой, интересной и информативной

🎨 ФОРМАТИРОВАНИЕ:
Используй только: <h3>, <p>, <b>, <i>, <u>, <blockquote>, <code>, <a>, <br>

📝 ПРИМЕРЫ ПОДРОБНОЙ СТРУКТУРЫ:

<h3>� Вечерние итоги и планирование</h3>
<p>К вечеру участники начали подводить итоги прошедшего дня и активно планировать <b>завтрашние встречи и задачи</b>. Обсуждались результаты выполненной работы и корректировки планов на следующий день.</p>

<p>Особое внимание уделялось координации совместных задач и распределению ответственности между участниками команды.</p>

<h3>☀️ Дневная активность и технические дискуссии</h3>
<p>В обеденное время разгорелась <i>захватывающая дискуссия</i> о новых технологиях в разработке. Началось все с вопроса Алексея о выборе фреймворка для нового проекта.</p>

<p>Участники активно делились опытом использования различных инструментов. Особенно интересными были рассказы о практическом применении новых библиотек и их преимуществах в реальных проектах.</p>

<p>Было поделено множество <u>полезных ссылок</u> на документацию, туториалы и примеры кода. Дискуссия показала высокий профессиональный уровень участников и их готовность делиться знаниями.</p>

<h3>� Утренние обсуждения рабочих планов</h3>
<p>Утро началось с активного обсуждения <b>планов на рабочий день</b>. Участники чата делились своими задачами и целями. Особенно выделялась тема завершения важного проекта, которая вызвала живой отклик у коллег.</p>

<p>Иван поделился своими планами:</p>
<blockquote>Сегодня нужно закончить проект до обеда, иначе завтра будет аврал. Уже настроился работать без перерывов</blockquote>

<p>На это откликнулись другие участники, предложив <i>помощь и поддержку</i>. Мария предложила взять на себя часть задач по тестированию, а Петр поделился полезными материалами для ускорения работы.</p>

<p><b>Итог утренних обсуждений:</b> команда сплотилась вокруг общей цели и настроена продуктивно работать. Видна взаимопомощь и командный дух.</p>

💡 СТИЛЬ НАПИСАНИЯ:
• Подробность и информативность
• Живые переходы между темами
• Эмодзи только в заголовках h3
• Детальные выводы в конце каждой темы
• Цитаты для важных и интересных моментов
• Контекст и предыстория обсуждений

🔗 ССЫЛКИ:
Делай важные сообщения кликабельными через <a href="...">текст</a>
ОБЯЗАТЕЛЬНО: Если цитируешь сообщения полностью, то ВСЕ цитируемые сообщения ДОЛЖНЫ быть обернуты в ссылки на эти сообщения!
"""

# Настройки очистки данных
CLEANUP_INTERVAL_HOURS = 1  # Интервал очистки в часах
MESSAGE_RETENTION_HOURS = 24  # Время хранения сообщений в часах

# Настройки форматирования
DATE_FORMAT = "%d %B"  # Формат даты для заголовка Telegraph
MONTH_NAMES = {
    1: "января", 2: "февраля", 3: "марта", 4: "апреля",
    5: "мая", 6: "июня", 7: "июля", 8: "августа", 
    9: "сентября", 10: "октября", 11: "ноября", 12: "декабря"
}
