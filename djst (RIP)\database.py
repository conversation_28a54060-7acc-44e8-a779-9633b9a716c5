import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pytz
import threading
import time

from .config import DATABASE_NAME, MESSAGE_RETENTION_HOURS

# Не переопределяем basicConfig, используем уже настроенное логирование
logger = logging.getLogger(__name__)

MOSCOW_TZ = pytz.timezone("Europe/Moscow")

# Блокировка для синхронизации доступа к базе данных
db_lock = threading.Lock()

def get_db_connection(timeout=30):
    """Получает соединение с базой данных с таймаутом и повторными попытками"""
    max_retries = 3
    retry_delay = 0.1

    for attempt in range(max_retries):
        try:
            conn = sqlite3.connect(DATABASE_NAME, timeout=timeout)
            # Включаем WAL режим для лучшей производительности при конкурентном доступе
            conn.execute('PRAGMA journal_mode=WAL')
            # Устанавливаем таймаут для блокировок
            conn.execute('PRAGMA busy_timeout=30000')  # 30 секунд
            return conn
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e) and attempt < max_retries - 1:
                logger.warning(f"База данных заблокирована, попытка {attempt + 1}/{max_retries}")
                time.sleep(retry_delay * (2 ** attempt))  # Экспоненциальная задержка
                continue
            else:
                logger.error(f"Ошибка подключения к базе данных: {e}")
                raise

    raise sqlite3.OperationalError("Не удалось подключиться к базе данных после нескольких попыток")

def init_db():
    with db_lock:  # Используем блокировку для синхронизации
        try:
            conn = get_db_connection()
            try:
                cursor = conn.cursor()

                # Создаем таблицу с новой схемой
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        chat_id INTEGER NOT NULL,
                        user_id INTEGER,
                        username TEXT,
                        full_name TEXT NOT NULL,
                        message_text TEXT NOT NULL,
                        message_link TEXT,
                        timestamp DATETIME NOT NULL,
                        reply_to_message_id INTEGER,
                        reply_to_user_id INTEGER,
                        reply_to_username TEXT,
                        reply_to_full_name TEXT,
                        reply_to_message_text TEXT
                    )
                ''')

                # Проверяем, нужно ли добавить новые колонки (для существующих баз данных)
                cursor.execute("PRAGMA table_info(messages)")
                columns = [column[1] for column in cursor.fetchall()]

                # Добавляем новые колонки, если их нет
                new_columns = [
                    'reply_to_message_id INTEGER',
                    'reply_to_user_id INTEGER',
                    'reply_to_username TEXT',
                    'reply_to_full_name TEXT',
                    'reply_to_message_text TEXT'
                ]

                for column_def in new_columns:
                    column_name = column_def.split()[0]
                    if column_name not in columns:
                        try:
                            cursor.execute(f'ALTER TABLE messages ADD COLUMN {column_def}')
                            logger.info(f"Добавлена колонка {column_name} в таблицу messages")
                        except Exception as e:
                            logger.warning(f"Не удалось добавить колонку {column_name}: {e}")

                cursor.execute('CREATE INDEX IF NOT EXISTS idx_chat_timestamp ON messages(chat_id, timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON messages(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_reply_to_message_id ON messages(reply_to_message_id)')



                # Создаем таблицу для хранения информации о чатах пользователя
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_chats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        chat_id INTEGER NOT NULL,
                        chat_title TEXT NOT NULL,
                        chat_username TEXT,
                        last_seen DATETIME NOT NULL,
                        UNIQUE(user_id, chat_id)
                    )
                ''')

                # Создаем таблицу для заблокированных пользователей
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS blocked_users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        chat_id INTEGER NOT NULL,
                        user_id INTEGER NOT NULL,
                        blocked_at DATETIME NOT NULL,
                        UNIQUE(chat_id, user_id)
                    )
                ''')

                # Создаем таблицу для ежедневных дайджестов
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_digests (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        chat_id INTEGER NOT NULL,
                        time_msk TEXT NOT NULL,
                        created_at DATETIME NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        UNIQUE(chat_id)
                    )
                ''')

                # Миграция: удаляем таблицу auto_search_settings если она существует
                try:
                    cursor.execute('DROP TABLE IF EXISTS auto_search_settings')
                    logger.info("Таблица auto_search_settings удалена (миграция)")
                except Exception as e:
                    logger.warning(f"Ошибка при удалении таблицы auto_search_settings: {e}")

                # Создаем индексы для новых таблиц
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_chats_user_id ON user_chats(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_blocked_users_chat_user ON blocked_users(chat_id, user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_digests_chat_id ON daily_digests(chat_id)')

                conn.commit()
                logger.info("База данных инициализирована успешно")

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Ошибка инициализации базы данных: {e}")

def save_message(message):
    with db_lock:  # Используем блокировку для синхронизации
        try:
            # Проверяем, начинается ли сообщение с "..ю" - если да, не сохраняем
            if message.text and message.text.startswith("..ю"):
                return

            # Проверяем, заблокирован ли пользователь
            if message.from_user and is_user_blocked(message.chat.id, message.from_user.id):
                return

            conn = get_db_connection()
            try:
                cursor = conn.cursor()

                full_name = ""
                if message.from_user.first_name:
                    full_name += message.from_user.first_name
                if message.from_user.last_name:
                    full_name += f" {message.from_user.last_name}"

                message_link = ""
                if message.chat.username:
                    message_link = f"https://t.me/{message.chat.username}/{message.message_id}"
                else:
                    message_link = f"https://t.me/c/{str(message.chat.id)[4:]}/{message.message_id}"

                moscow_time = datetime.now(MOSCOW_TZ)

                # Обработка реплая (ответа на сообщение)
                reply_to_message_id = None
                reply_to_user_id = None
                reply_to_username = None
                reply_to_full_name = None
                reply_to_message_text = None

                if message.reply_to_message:
                    reply_to_message_id = message.reply_to_message.message_id
                    if message.reply_to_message.from_user:
                        reply_to_user_id = message.reply_to_message.from_user.id
                        reply_to_username = message.reply_to_message.from_user.username or ""

                        # Формируем полное имя для реплая
                        reply_full_name = ""
                        if message.reply_to_message.from_user.first_name:
                            reply_full_name += message.reply_to_message.from_user.first_name
                        if message.reply_to_message.from_user.last_name:
                            reply_full_name += f" {message.reply_to_message.from_user.last_name}"
                        reply_to_full_name = reply_full_name.strip()

                        reply_to_message_text = message.reply_to_message.text or ""

                cursor.execute('''
                    INSERT INTO messages (chat_id, user_id, username, full_name, message_text, message_link, timestamp,
                                        reply_to_message_id, reply_to_user_id, reply_to_username, reply_to_full_name, reply_to_message_text)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    message.chat.id,
                    message.from_user.id,
                    message.from_user.username or "",
                    full_name.strip(),
                    message.text or "",
                    message_link,
                    moscow_time,
                    reply_to_message_id,
                    reply_to_user_id,
                    reply_to_username,
                    reply_to_full_name,
                    reply_to_message_text
                ))

                conn.commit()

                # Логируем информацию о реплае, если он есть (только в файл, не в консоль)
                if reply_to_message_id:
                    logger.debug(f"Сохранено сообщение с реплаем: {full_name.strip()} ответил на сообщение от {reply_to_full_name}")

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"[database] ERROR: Ошибка сохранения сообщения: {e}")

def get_messages_last_hours(chat_id: int, hours: int) -> List[Dict]:
    """Получает сообщения за последние N часов"""
    with db_lock:  # Используем блокировку для синхронизации
        try:
            conn = get_db_connection()
            try:
                cursor = conn.cursor()

                now_moscow = datetime.now(MOSCOW_TZ)
                time_ago = now_moscow - timedelta(hours=hours)

                cursor.execute('''
                    SELECT full_name, username, timestamp, message_link, message_text,
                           reply_to_message_id, reply_to_user_id, reply_to_username, reply_to_full_name, reply_to_message_text
                    FROM messages
                    WHERE chat_id = ? AND timestamp >= ?
                    ORDER BY timestamp DESC
                ''', (chat_id, time_ago))

                messages = []
                for row in cursor.fetchall():
                    full_name, username, timestamp_str, message_link, message_text, reply_to_message_id, reply_to_user_id, reply_to_username, reply_to_full_name, reply_to_message_text = row

                    msg_datetime = datetime.fromisoformat(timestamp_str)
                    msg_time_str = msg_datetime.strftime("%H:%M")

                    message_data = {
                        'full_name': full_name,
                        'username': username or "",
                        'time': msg_time_str,
                        'link': message_link,
                        'text': message_text
                    }

                    # Добавляем информацию о реплае, если есть
                    if reply_to_message_id:
                        message_data['reply_to'] = {
                            'message_id': reply_to_message_id,
                            'user_id': reply_to_user_id,
                            'username': reply_to_username or "",
                            'full_name': reply_to_full_name or "",
                            'text': reply_to_message_text or ""
                        }

                    messages.append(message_data)

                if len(messages) > 0:
                    logger.info(f"Получено {len(messages)} сообщений для дайджеста за {hours} часов")
                return messages

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Ошибка получения сообщений: {e}")
            return []

def get_messages_last_24h(chat_id: int) -> List[Dict]:
    """Получает сообщения за последние 24 часа (для обратной совместимости)"""
    return get_messages_last_hours(chat_id, 24)

def cleanup_old_messages():
    with db_lock:  # Используем блокировку для синхронизации
        try:
            conn = get_db_connection()
            try:
                cursor = conn.cursor()

                now_moscow = datetime.now(MOSCOW_TZ)
                cutoff_time = now_moscow - timedelta(hours=MESSAGE_RETENTION_HOURS)

                cursor.execute('DELETE FROM messages WHERE timestamp < ?', (cutoff_time,))
                deleted_count = cursor.rowcount

                conn.commit()

                if deleted_count > 0:
                    logger.info(f"Удалено {deleted_count} старых сообщений")

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Ошибка очистки старых сообщений: {e}")

def format_messages_for_ai(messages: List[Dict]) -> str:
    if not messages:
        return "Нет сообщений за последние 24 часа."

    formatted_messages = []
    for msg in messages:
        username_part = f", ЮЗЕРНЕЙМ: @{msg['username']}" if msg['username'] else ""

        # Основная информация о сообщении в новом формате
        formatted_msg = f"[НИКНЕЙМ: {msg['full_name']}{username_part}], [ВРЕМЯ: {msg['time']}], [ССЫЛКА: {msg['link']}]"

        # Добавляем информацию о реплае, если есть
        if 'reply_to' in msg and msg['reply_to']:
            reply_info = msg['reply_to']
            reply_username_part = f", @{reply_info['username']}" if reply_info['username'] else ""
            formatted_msg += f"\n↳ Ответ на сообщение от [{reply_info['full_name']}{reply_username_part}]: \"{reply_info['text'][:100]}{'...' if len(reply_info['text']) > 100 else ''}\""

        formatted_msg += f"\nСообщение:\n{msg['text']}"
        formatted_messages.append(formatted_msg)

    return "\n\n".join(formatted_messages)

def get_chat_title(chat_id: int) -> Optional[str]:
    return None

# Функции для работы с автопоиском

def save_user_chat(user_id: int, chat_id: int, chat_title: str, chat_username: str = None):
    """Сохраняет информацию о чате пользователя"""
    with db_lock:  # Используем блокировку для синхронизации
        try:
            conn = get_db_connection()
            try:
                cursor = conn.cursor()

                moscow_time = datetime.now(MOSCOW_TZ)

                cursor.execute('''
                    INSERT OR REPLACE INTO user_chats (user_id, chat_id, chat_title, chat_username, last_seen)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, chat_id, chat_title, chat_username or "", moscow_time))

                conn.commit()
                return True

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"[database] ERROR: Ошибка сохранения чата пользователя: {e}")
            return False

def get_user_chats(user_id: int) -> List[Dict]:
    """Получает список чатов пользователя"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT chat_id, chat_title, chat_username
            FROM user_chats
            WHERE user_id = ?
            ORDER BY last_seen DESC
        ''', (user_id,))

        chats = []
        for row in cursor.fetchall():
            chat_id, chat_title, chat_username = row
            chats.append({
                'chat_id': chat_id,
                'chat_title': chat_title,
                'chat_username': chat_username or ""
            })

        conn.close()
        return chats

    except Exception as e:
        logger.error(f"Ошибка получения чатов пользователя: {e}")
        return []

def get_chat_statistics(chat_id: int) -> Dict:
    """Получает статистику для чата"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        # Общее количество сообщений
        cursor.execute('SELECT COUNT(*) FROM messages WHERE chat_id = ?', (chat_id,))
        total_messages = cursor.fetchone()[0]

        # Количество сообщений за последние 24 часа
        now_moscow = datetime.now(MOSCOW_TZ)
        time_24h_ago = now_moscow - timedelta(hours=24)
        cursor.execute('SELECT COUNT(*) FROM messages WHERE chat_id = ? AND timestamp >= ?',
                      (chat_id, time_24h_ago))
        messages_24h = cursor.fetchone()[0]

        # Количество уникальных пользователей
        cursor.execute('SELECT COUNT(DISTINCT user_id) FROM messages WHERE chat_id = ?', (chat_id,))
        unique_users = cursor.fetchone()[0]

        # Количество сообщений за последние 7 дней
        time_7d_ago = now_moscow - timedelta(days=7)
        cursor.execute('SELECT COUNT(*) FROM messages WHERE chat_id = ? AND timestamp >= ?',
                      (chat_id, time_7d_ago))
        messages_7d = cursor.fetchone()[0]

        conn.close()

        return {
            'total_messages': total_messages,
            'messages_24h': messages_24h,
            'messages_7d': messages_7d,
            'unique_users': unique_users
        }

    except Exception as e:
        logger.error(f"Ошибка получения статистики чата: {e}")
        return {
            'total_messages': 0,
            'messages_24h': 0,
            'messages_7d': 0,
            'unique_users': 0
        }

# Функции для работы с ежедневными дайджестами

def add_daily_digest(chat_id: int, time_msk: str) -> bool:
    """Добавляет или обновляет настройку ежедневного дайджеста для чата"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        moscow_time = datetime.now(MOSCOW_TZ)

        cursor.execute('''
            INSERT OR REPLACE INTO daily_digests (chat_id, time_msk, created_at, is_active)
            VALUES (?, ?, ?, 1)
        ''', (chat_id, time_msk, moscow_time))

        conn.commit()
        conn.close()
        logger.info(f"Добавлен ежедневный дайджест для чата {chat_id} на время {time_msk}")
        return True

    except Exception as e:
        logger.error(f"Ошибка добавления ежедневного дайджеста: {e}")
        return False

def get_daily_digests() -> List[Dict]:
    """Получает все активные настройки ежедневных дайджестов"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT chat_id, time_msk
            FROM daily_digests
            WHERE is_active = 1
        ''')

        results = cursor.fetchall()
        conn.close()

        return [{'chat_id': row[0], 'time_msk': row[1]} for row in results]

    except Exception as e:
        logger.error(f"Ошибка получения ежедневных дайджестов: {e}")
        return []

def remove_daily_digest(chat_id: int) -> bool:
    """Удаляет настройку ежедневного дайджеста для чата"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        cursor.execute('DELETE FROM daily_digests WHERE chat_id = ?', (chat_id,))

        deleted_count = cursor.rowcount
        conn.commit()
        conn.close()

        if deleted_count > 0:
            logger.info(f"Удален ежедневный дайджест для чата {chat_id}")
            return True
        else:
            logger.info(f"Ежедневный дайджест для чата {chat_id} не найден")
            return False

    except Exception as e:
        logger.error(f"Ошибка удаления ежедневного дайджеста: {e}")
        return False

def get_chat_daily_digest(chat_id: int) -> Optional[str]:
    """Получает время ежедневного дайджеста для конкретного чата"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT time_msk
            FROM daily_digests
            WHERE chat_id = ? AND is_active = 1
        ''', (chat_id,))

        result = cursor.fetchone()
        conn.close()

        return result[0] if result else None

    except Exception as e:
        logger.error(f"Ошибка получения ежедневного дайджеста для чата {chat_id}: {e}")
        return None









# Функции для работы с блокировкой пользователей

def block_user(chat_id: int, user_id: int) -> bool:
    """Блокирует пользователя в чате"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        moscow_time = datetime.now(MOSCOW_TZ)

        # Добавляем пользователя в заблокированные
        cursor.execute('''
            INSERT OR REPLACE INTO blocked_users (chat_id, user_id, blocked_at)
            VALUES (?, ?, ?)
        ''', (chat_id, user_id, moscow_time))

        # Удаляем все сообщения этого пользователя из базы данных
        cursor.execute('''
            DELETE FROM messages WHERE chat_id = ? AND user_id = ?
        ''', (chat_id, user_id))

        deleted_count = cursor.rowcount

        conn.commit()
        conn.close()

        logger.info(f"Пользователь {user_id} заблокирован в чате {chat_id}, удалено {deleted_count} сообщений")
        return True

    except Exception as e:
        logger.error(f"Ошибка блокировки пользователя: {e}")
        return False

def unblock_user(chat_id: int, user_id: int) -> bool:
    """Разблокирует пользователя в чате"""
    try:
        conn = sqlite3.connect(DATABASE_NAME)
        cursor = conn.cursor()

        cursor.execute('''
            DELETE FROM blocked_users WHERE chat_id = ? AND user_id = ?
        ''', (chat_id, user_id))

        was_blocked = cursor.rowcount > 0

        conn.commit()
        conn.close()

        if was_blocked:
            logger.info(f"Пользователь {user_id} разблокирован в чате {chat_id}")

        return was_blocked

    except Exception as e:
        logger.error(f"Ошибка разблокировки пользователя: {e}")
        return False

def is_user_blocked(chat_id: int, user_id: int) -> bool:
    """Проверяет, заблокирован ли пользователь в чате"""
    with db_lock:  # Используем блокировку для синхронизации
        try:
            conn = get_db_connection()
            try:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT 1 FROM blocked_users WHERE chat_id = ? AND user_id = ?
                ''', (chat_id, user_id))

                result = cursor.fetchone()
                return result is not None

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Ошибка проверки блокировки пользователя: {e}")
            return False