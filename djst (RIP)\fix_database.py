#!/usr/bin/env python3
"""
Скрипт для исправления проблем с базой данных SQLite
Исправляет блокировки и проверяет целостность базы данных
"""

import sqlite3
import os
import logging
import shutil
from datetime import datetime

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_database_integrity(db_path):
    """Проверяет целостность базы данных"""
    try:
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        # Проверяем целостность
        cursor.execute('PRAGMA integrity_check')
        result = cursor.fetchone()
        
        conn.close()
        
        if result[0] == 'ok':
            logger.info("✅ Целостность базы данных в порядке")
            return True
        else:
            logger.error(f"❌ Проблемы с целостностью базы данных: {result[0]}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Ошибка проверки целостности: {e}")
        return False

def fix_database_locks(db_path):
    """Исправляет проблемы с блокировками базы данных"""
    try:
        # Создаем резервную копию
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(db_path, backup_path)
        logger.info(f"📁 Создана резервная копия: {backup_path}")
        
        # Подключаемся к базе данных
        conn = sqlite3.connect(db_path, timeout=30)
        cursor = conn.cursor()
        
        # Включаем WAL режим для лучшей производительности
        cursor.execute('PRAGMA journal_mode=WAL')
        logger.info("✅ Включен WAL режим")
        
        # Устанавливаем таймаут для блокировок
        cursor.execute('PRAGMA busy_timeout=30000')  # 30 секунд
        logger.info("✅ Установлен таймаут блокировок: 30 секунд")
        
        # Оптимизируем базу данных
        cursor.execute('VACUUM')
        logger.info("✅ Выполнена оптимизация базы данных (VACUUM)")
        
        # Анализируем статистику
        cursor.execute('ANALYZE')
        logger.info("✅ Обновлена статистика базы данных (ANALYZE)")
        
        conn.commit()
        conn.close()
        
        logger.info("✅ База данных успешно исправлена!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Ошибка исправления базы данных: {e}")
        return False

def check_database_size(db_path):
    """Проверяет размер базы данных"""
    try:
        size = os.path.getsize(db_path)
        size_mb = size / (1024 * 1024)
        logger.info(f"📊 Размер базы данных: {size_mb:.2f} MB")
        
        if size_mb > 100:
            logger.warning("⚠️ База данных довольно большая, рекомендуется очистка старых сообщений")
            
    except Exception as e:
        logger.error(f"❌ Ошибка проверки размера: {e}")

def main():
    """Основная функция"""
    # Путь к базе данных
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "messages.db")
    
    if not os.path.exists(db_path):
        logger.error(f"❌ База данных не найдена: {db_path}")
        return
    
    logger.info(f"🔍 Проверяем базу данных: {db_path}")
    
    # Проверяем размер
    check_database_size(db_path)
    
    # Проверяем целостность
    if not check_database_integrity(db_path):
        logger.error("❌ База данных повреждена!")
        return
    
    # Исправляем блокировки
    if fix_database_locks(db_path):
        logger.info("🎉 Все исправления применены успешно!")
    else:
        logger.error("❌ Не удалось исправить все проблемы")

if __name__ == "__main__":
    main()
