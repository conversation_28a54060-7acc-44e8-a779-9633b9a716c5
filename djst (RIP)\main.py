#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import telebot
from telebot import types
import schedule
import threading
import time
import logging
from datetime import datetime
import re
from difflib import SequenceMatcher

# Импорт наших модулей
try:
    # Пробуем относительные импорты (когда запускается как пакет)
    from .config import BOT_TOKEN, CLEANUP_INTERVAL_HOURS
    from .database import (
        init_db,
        save_message,
        cleanup_old_messages,
        get_messages_last_24h,
        get_messages_last_hours,
        format_messages_for_ai,
        save_user_chat,
        get_user_chats,

        block_user,
        unblock_user,
        is_user_blocked,
        get_chat_statistics,
        add_daily_digest,
        get_daily_digests,
        remove_daily_digest,
        get_chat_daily_digest
    )
    from .ai_handler import generate_digest, test_ai_connection
    from .telegraph_handler import create_telegraph_page, test_telegraph_connection
    from .memory_handler import add_memory_entry, get_chat_memory, format_memory_for_ai, clear_chat_memory
except ImportError:
    # Fallback к абсолютным импортам (когда запускается напрямую)
    from config import BOT_TOKEN, CLEANUP_INTERVAL_HOURS
    from database import (
        init_db,
        save_message,
        cleanup_old_messages,
        get_messages_last_24h,
        get_messages_last_hours,
        format_messages_for_ai,
        save_user_chat,
        get_user_chats,

        block_user,
        unblock_user,
        is_user_blocked,
        get_chat_statistics,
        add_daily_digest,
        get_daily_digests,
        remove_daily_digest,
        get_chat_daily_digest
    )
    from ai_handler import generate_digest, test_ai_connection
    from telegraph_handler import create_telegraph_page, test_telegraph_connection
    from memory_handler import add_memory_entry, get_chat_memory, format_memory_for_ai, clear_chat_memory

# Настройка логирования
# Создаем кастомный фильтр для исключения определенных сообщений из консоли
class ConsoleLogFilter(logging.Filter):
    def filter(self, record):
        # Исключаем из консоли сообщения о сохранении реплаев и другие частые логи
        message = record.getMessage()
        excluded_patterns = [
            "Сохранено сообщение с реплаем:",
            "All 8 API keys failed",
            "Stopped typing status after video processing",
            "send_long_message",
            "remove_reaction",
            "set_reaction",
            "Replaced",
            "Video processed successfully",

            # Новые паттерны для подавления подробных логов
            "Utils remove_reaction",
            "Utils set_reaction",
            "Successfully sent request to remove reactions",
            "Successfully set reaction",
            "Removed '⚡' reaction from user message",
            "Added '🎉' reaction to USER message",
            "final_reply_markup is None",
            "AFC is enabled with max remote calls:",
            "HTTP Request: POST https://generativelanguage.googleapis.com",
            "AFC remote call",
            "is done",

        ]

        for pattern in excluded_patterns:
            if pattern in message:
                return False
        return True

# Настройка обработчиков
file_handler = logging.FileHandler('bot.log', encoding='utf-8')
console_handler = logging.StreamHandler()

# Применяем фильтр только к консольному выводу
console_handler.addFilter(ConsoleLogFilter())

# Настройка базового логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    handlers=[file_handler, console_handler]
)

# Настройка уровней логирования для внешних библиотек
# Подавляем логи от определенных логгеров в консоли
external_loggers = ['bot_logger', 'Video_analysis', 'Utils', 'httpx', 'google_genai.models']
for logger_name in external_loggers:
    ext_logger = logging.getLogger(logger_name)
    # Удаляем все обработчики консоли для этих логгеров
    for handler in ext_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
            ext_logger.removeHandler(handler)
    # Добавляем только файловый обработчик
    if not any(isinstance(h, logging.FileHandler) for h in ext_logger.handlers):
        ext_logger.addHandler(file_handler)
    ext_logger.propagate = False  # Не передаем логи родительским логгерам

logger = logging.getLogger(__name__)

# Инициализация бота с HTML форматированием
bot = telebot.TeleBot(BOT_TOKEN, parse_mode='HTML')

# --- НОВЫЕ ПЕРЕМЕННЫЕ ДЛЯ ОГРАНИЧЕНИЯ ---
# Кулдаун для команды /digest в секундах (15 минут)
DIGEST_COOLDOWN_SECONDS = 15 * 60
# Словарь для отслеживания времени последнего вызова /digest для каждого чата
# {chat_id: timestamp}
digest_cooldowns = {}
# --- КОНЕЦ НОВЫХ ПЕРЕМЕННЫХ ---

def create_auto_digest(chat_id: int):
    """Создает автоматический дайджест для чата"""
    try:
        logger.info(f"Создание автоматического дайджеста для чата {chat_id}")

        # Получаем сообщения за последние 24 часа
        messages = get_messages_last_24h(chat_id)

        if not messages:
            logger.info(f"Нет сообщений за последние 24 часа для чата {chat_id}")
            return

        # Форматируем сообщения для AI
        messages_text = format_messages_for_ai(messages)

        # Получаем информацию о чате
        try:
            chat_info = bot.get_chat(chat_id)
            chat_title = chat_info.title or "Неизвестный чат"
        except Exception as e:
            logger.warning(f"Не удалось получить информацию о чате {chat_id}: {e}")
            chat_title = "Неизвестный чат"

        # Генерируем дайджест
        ai_result = generate_digest(messages_text, chat_title, chat_id)

        if "Ошибка" in ai_result['digest']:
            logger.error(f"Ошибка генерации автоматического дайджеста для чата {chat_id}: {ai_result['digest']}")
            return

        # Создаем страницу в Telegraph
        telegraph_url = create_telegraph_page(ai_result['digest'], chat_title)

        if not telegraph_url:
            logger.error(f"Ошибка публикации автоматического дайджеста в Telegraph для чата {chat_id}")
            return

        # Формируем финальное сообщение
        final_message = f"""🤖 <b>Автоматический дайджест за последние 24 часа</b>

📖 <a href="{telegraph_url}">Читать дайджест</a>"""

        # Отправляем сообщение в чат
        bot.send_message(
            chat_id,
            final_message,
            disable_web_page_preview=True
        )

        logger.info(f"Автоматический дайджест успешно отправлен в чат {chat_id}")

    except Exception as e:
        logger.error(f"Ошибка создания автоматического дайджеста для чата {chat_id}: {e}")


def check_daily_digests():
    """Проверяет и выполняет ежедневные дайджесты"""
    try:
        from datetime import datetime
        import pytz

        # Получаем текущее время по МСК
        msk_tz = pytz.timezone('Europe/Moscow')
        current_time_msk = datetime.now(msk_tz)
        current_time_str = current_time_msk.strftime('%H:%M')

        # Получаем все активные настройки ежедневных дайджестов
        daily_digests = get_daily_digests()

        for digest_setting in daily_digests:
            if digest_setting['time_msk'] == current_time_str:
                logger.info(f"Время для автоматического дайджеста в чате {digest_setting['chat_id']}")
                create_auto_digest(digest_setting['chat_id'])

    except Exception as e:
        logger.error(f"Ошибка проверки ежедневных дайджестов: {e}")


def startup_checks():
    """Проверки при запуске бота"""
    logger.info("Запуск проверок системы...")

    # Проверка базы данных
    try:
        init_db()
        logger.info("✅ База данных инициализирована")
    except Exception as e:
        logger.error(f"❌ Ошибка инициализации БД: {e}")
        return False

    # Проверка AI соединения
    if test_ai_connection():
        logger.info("✅ AI соединение работает")
    else:
        logger.warning("⚠️ AI соединение недоступно")

    # Проверка Telegraph соединения
    if test_telegraph_connection():
        logger.info("✅ Telegraph соединение работает")
    else:
        logger.warning("⚠️ Telegraph соединение недоступно")

    logger.info("Проверки завершены")
    return True

@bot.message_handler(commands=['digest'])
def digest_command(message):
    """Обработчик команды /digest для создания дайджеста"""
    logger.info(f"🎯 Получена команда /digest от {message.from_user.first_name} в чате {message.chat.id}")

    # Команда работает только в группах и супергруппах
    if message.chat.type not in ['group', 'supergroup']:
        bot.reply_to(message, "❌ Эта команда работает только в группах!")
        return

    # Парсим количество часов из команды
    command_parts = message.text.split()
    hours = 24  # По умолчанию 24 часа

    if len(command_parts) > 1:
        try:
            hours = int(command_parts[1])
            if hours < 1 or hours > 48:
                bot.reply_to(message, "❌ Количество часов должно быть от 1 до 48!")
                return
        except ValueError:
            bot.reply_to(message, "❌ Неверный формат команды!\n\nИспользуйте: <code>/digest</code> или <code>/digest 8</code>\nЧисло должно быть от 1 до 48 часов.")
            return

    # --- НАЧАЛО ЛОГИКИ ОГРАНИЧЕНИЯ ---
    chat_id = message.chat.id
    current_time = time.time()

    last_request_time = digest_cooldowns.get(chat_id)
    if last_request_time and (current_time - last_request_time) < DIGEST_COOLDOWN_SECONDS:
        logger.warning(f"Cooldown для /digest в чате {chat_id}. Попытка вызова отклонена.")
        bot.reply_to(message, "😉 Дайджест уже недавно делался — нет потребности делать новый")
        return

    # Устанавливаем кулдаун сразу, чтобы избежать одновременных вызовов
    # и учесть время генерации дайджеста.
    digest_cooldowns[chat_id] = current_time
    # --- КОНЕЦ ЛОГИКИ ОГРАНИЧЕНИЯ ---

    # Отправляем сообщение о начале обработки
    hours_text = f"{hours} час" if hours == 1 else f"{hours} часа" if 2 <= hours <= 4 else f"{hours} часов"
    processing_msg = bot.reply_to(message, f"<b>📝 Делаю дайджест за последние {hours_text}...</b>")

    try:
        # Получаем сообщения за указанное количество часов
        messages = get_messages_last_hours(message.chat.id, hours)

        if not messages:
            # Сбрасываем кулдаун, если нет сообщений, чтобы можно было попробовать снова
            digest_cooldowns.pop(chat_id, None)
            bot.reply_to(message, f"📭 Нет сообщений за последние {hours_text} для создания дайджеста.")
            return

        # Форматируем сообщения для AI
        messages_text = format_messages_for_ai(messages)
        chat_title = message.chat.title or "Неизвестный чат"

        # Генерируем дайджест
        ai_result = generate_digest(messages_text, chat_title, message.chat.id)

        if "Ошибка" in ai_result['digest']:
            # Сбрасываем кулдаун при ошибке
            digest_cooldowns.pop(chat_id, None)
            bot.reply_to(message, f"❌ {ai_result['digest']}")
            return

        # Создаем страницу в Telegraph
        telegraph_url = create_telegraph_page(ai_result['digest'], chat_title)

        if not telegraph_url:
            # Сбрасываем кулдаун при ошибке
            digest_cooldowns.pop(chat_id, None)
            bot.reply_to(message, "❌ Ошибка публикации в Telegraph")
            return

        # Формируем финальное сообщение
        final_message = f"""😇 <b>Сводка сообщений за последние {hours_text}</b>

📖 <a href="{telegraph_url}">Читать дайджест</a>"""

        # Отправляем финальное сообщение как ответ на команду
        bot.reply_to(
            message,
            final_message,
            disable_web_page_preview=True
        )

        logger.info(f"Дайджест успешно создан для чата {message.chat.id} за {hours} часов")

    except Exception as e:
        # Сбрасываем кулдаун при любой другой ошибке
        digest_cooldowns.pop(chat_id, None)
        logger.error(f"Ошибка создания дайджеста: {e}")
        bot.reply_to(message, f"❌ Ошибка создания дайджеста: {str(e)}")
    finally:
        # В любом случае удаляем сообщение "Делаю дайджест..."
        try:
            bot.delete_message(
                chat_id=processing_msg.chat.id,
                message_id=processing_msg.message_id
            )
        except Exception as delete_error:
            logger.warning(f"Не удалось удалить временное сообщение: {delete_error}")


@bot.message_handler(commands=['digestevery'])
def digestevery_command(message):
    """Обработчик команды /digestevery для настройки ежедневных дайджестов"""
    logger.info(f"🕐 Получена команда /digestevery от {message.from_user.first_name} в чате {message.chat.id}")

    # Команда работает только в группах и супергруппах
    if message.chat.type not in ['group', 'supergroup']:
        bot.reply_to(message, "❌ Эта команда работает только в группах!")
        return

    # Проверяем права администратора
    try:
        chat_member = bot.get_chat_member(message.chat.id, message.from_user.id)
        if chat_member.status not in ['administrator', 'creator']:
            bot.reply_to(message, "❌ Эта команда доступна только администраторам группы!")
            return
    except Exception as e:
        logger.error(f"Ошибка проверки прав администратора: {e}")
        bot.reply_to(message, "❌ Ошибка проверки прав доступа!")
        return

    # Парсим время из команды
    command_parts = message.text.split()
    if len(command_parts) != 2:
        bot.reply_to(message,
                    "❌ Неверный формат команды!\n\n"
                    "Используйте: <code>/digestevery 20:00</code>\n"
                    "Время указывается по МСК")
        return

    time_str = command_parts[1]

    # Проверяем формат времени
    import re
    if not re.match(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', time_str):
        bot.reply_to(message,
                    "❌ Неверный формат времени!\n\n"
                    "Используйте формат ЧЧ:ММ (например: 20:00)\n"
                    "Время указывается по МСК")
        return

    # Сохраняем настройку в базу данных
    if add_daily_digest(message.chat.id, time_str):
        bot.reply_to(message,
                    f"✅ <b>Ежедневный дайджест настроен!</b>\n\n"
                    f"🕐 Время: <code>{time_str}</code> МСК\n"
                    f"📝 Каждый день в это время я буду автоматически создавать дайджест за последние 24 часа\n\n"
                    f"Для отмены используйте команду <code>/everydel</code>")
        logger.info(f"Настроен ежедневный дайджест для чата {message.chat.id} на время {time_str}")
    else:
        bot.reply_to(message, "❌ Ошибка сохранения настроек ежедневного дайджеста!")


@bot.message_handler(commands=['everydel'])
def everydel_command(message):
    """Обработчик команды /everydel для удаления ежедневных дайджестов"""
    logger.info(f"🗑️ Получена команда /everydel от {message.from_user.first_name} в чате {message.chat.id}")

    # Команда работает только в группах и супергруппах
    if message.chat.type not in ['group', 'supergroup']:
        bot.reply_to(message, "❌ Эта команда работает только в группах!")
        return

    # Проверяем права администратора
    try:
        chat_member = bot.get_chat_member(message.chat.id, message.from_user.id)
        if chat_member.status not in ['administrator', 'creator']:
            bot.reply_to(message, "❌ Эта команда доступна только администраторам группы!")
            return
    except Exception as e:
        logger.error(f"Ошибка проверки прав администратора: {e}")
        bot.reply_to(message, "❌ Ошибка проверки прав доступа!")
        return

    # Проверяем, есть ли настроенный дайджест
    current_time = get_chat_daily_digest(message.chat.id)
    if not current_time:
        bot.reply_to(message, "❌ В этом чате нет настроенных ежедневных дайджестов!")
        return

    # Удаляем настройку
    if remove_daily_digest(message.chat.id):
        bot.reply_to(message,
                    f"✅ <b>Ежедневный дайджест отменен!</b>\n\n"
                    f"🕐 Отменено время: <code>{current_time}</code> МСК\n"
                    f"📝 Автоматические дайджесты больше не будут создаваться")
        logger.info(f"Удален ежедневный дайджест для чата {message.chat.id}")
    else:
        bot.reply_to(message, "❌ Ошибка удаления настроек ежедневного дайджеста!")


@bot.message_handler(commands=['start', 'help'])
def help_command(message):
    """Обработчик команд помощи"""
    # Команды /start и /help работают только в личных чатах
    if message.chat.type == 'private':
        help_text = """Привет! 👋
Я <b>sh: Digest</b> — делаю удобные дайджесты чатов коротко и по делу.
Больше не нужно читать сотни сообщений, я соберу для тебя всё самое важное за секунды!

Добавь меня в группу, чтобы начать пользоваться мной!

⚠️ <b>Важно:</b> Для корректной работы сделайте меня <b>администратором</b> в группе с правами на чтение сообщений."""

        # Создаем inline клавиатуру с кнопкой добавления в группу
        inline_keyboard = types.InlineKeyboardMarkup()
        add_to_group_btn = types.InlineKeyboardButton(
            text="Добавить в группу",
            url=f"https://t.me/{bot.get_me().username}?startgroup=true"
        )
        inline_keyboard.add(add_to_group_btn)

        bot.reply_to(message, help_text, reply_markup=inline_keyboard)

@bot.message_handler(content_types=['new_chat_members'])
def welcome_new_members(message):
    """Обработчик добавления новых участников в группу"""
    try:
        # Проверяем, добавили ли именно нашего бота
        bot_info = bot.get_me()
        for new_member in message.new_chat_members:
            if new_member.id == bot_info.id:
                # Бот был добавлен в группу
                welcome_text = """Привет! 👋

Я создаю дайджесты сообщений по команде /digest.

⚠️ <b>Важно:</b> Для корректной работы сделайте меня <b>администратором</b> группы с правами на чтение сообщений.

Только что начал сбор сообщений в вашей группе, поэтому дайджест пока недоступен. Попробуйте через несколько часов! ⏰"""
                bot.send_message(message.chat.id, welcome_text)
                logger.info(f"Бот добавлен в группу {message.chat.id} ({message.chat.title})")

                # Сохраняем информацию о чате для пользователя, который добавил бота
                save_user_chat(message.from_user.id, message.chat.id,
                              message.chat.title or "Неизвестный чат",
                              message.chat.username)
                break
    except Exception as e:
        logger.error(f"Ошибка при обработке добавления в группу: {e}")

@bot.message_handler(commands=['addmemory'])
def add_memory_command(message):
    """Обработчик команды /addmemory для добавления информации в память чата"""
    try:
        # Команда работает только в группах и супергруппах
        if message.chat.type not in ['group', 'supergroup']:
            bot.reply_to(message, "❌ Эта команда работает только в группах!")
            return

        # Проверяем права доступа
        user_has_access = False

        # Проверяем, отправлено ли сообщение анонимным администратором
        if is_anonymous_admin_message(message):
            user_has_access = True
            logger.info(f"Команда /addmemory выполнена анонимным администратором в чате {message.chat.id}")
        else:
            # Проверяем, является ли пользователь администратором или модератором
            if message.from_user and is_user_admin_or_moderator(message.chat.id, message.from_user.id):
                user_has_access = True
                logger.info(f"Команда /addmemory выполнена администратором {message.from_user.id} в чате {message.chat.id}")

        if not user_has_access:
            bot.reply_to(message, "❌ Эта команда доступна только администраторам и модераторам чата!")
            return

        # Получаем текст после команды
        command_text = message.text.strip()
        if len(command_text.split(' ', 1)) < 2:
            bot.reply_to(message, "❌ Укажите информацию для добавления в память.\n\n<b>Пример:</b> <code>/addmemory vo 1 в чате это Вася</code>")
            return

        memory_text = command_text.split(' ', 1)[1].strip()

        if not memory_text:
            bot.reply_to(message, "❌ Информация для добавления не может быть пустой!")
            return

        # Добавляем запись в память
        if add_memory_entry(message.chat.id, memory_text):
            bot.reply_to(message, f"✅ Информация добавлена в память чата")
        else:
            bot.reply_to(message, "❌ Ошибка добавления информации в память")

    except Exception as e:
        logger.error(f"Ошибка команды /addmemory: {e}")
        bot.reply_to(message, f"❌ Ошибка: {str(e)}")

@bot.message_handler(commands=['memory'])
def show_memory_command(message):
    """Обработчик команды /memory для просмотра памяти чата"""
    try:
        # Команда работает только в группах и супергруппах
        if message.chat.type not in ['group', 'supergroup']:
            bot.reply_to(message, "❌ Эта команда работает только в группах!")
            return

        chat_memory = get_chat_memory(message.chat.id)

        if not chat_memory:
            bot.reply_to(message, "📭 Память чата пуста")
            return

        memory_text = "<b>🧠 Память чата:</b>\n\n"
        for i, entry in enumerate(chat_memory, 1):
            memory_text += f"{i}. <code>{entry}</code>\n"

        bot.reply_to(message, memory_text)

    except Exception as e:
        logger.error(f"Ошибка команды /memory: {e}")
        bot.reply_to(message, f"❌ Ошибка: {str(e)}")

@bot.message_handler(commands=['delmemory'])
def delete_memory_command(message):
    """Обработчик команды /delmemory для удаления всей памяти чата"""
    try:
        # Команда работает только в группах и супергруппах
        if message.chat.type not in ['group', 'supergroup']:
            bot.reply_to(message, "❌ Эта команда работает только в группах!")
            return

        # Проверяем права доступа
        user_has_access = False

        # Проверяем, отправлено ли сообщение анонимным администратором
        if is_anonymous_admin_message(message):
            user_has_access = True
            logger.info(f"Команда /delmemory выполнена анонимным администратором в чате {message.chat.id}")
        else:
            # Проверяем, является ли пользователь администратором или модератором
            if message.from_user and is_user_admin_or_moderator(message.chat.id, message.from_user.id):
                user_has_access = True
                logger.info(f"Команда /delmemory выполнена администратором {message.from_user.id} в чате {message.chat.id}")

        if not user_has_access:
            bot.reply_to(message, "❌ Эта команда доступна только администраторам и модераторам чата!")
            return

        # Проверяем, есть ли память для удаления
        chat_memory = get_chat_memory(message.chat.id)
        if not chat_memory:
            bot.reply_to(message, "📭 Память чата уже пуста - нечего удалять!")
            return

        # Создаем inline клавиатуру с подтверждением
        keyboard = types.InlineKeyboardMarkup()
        confirm_btn = types.InlineKeyboardButton(
            text="🗑️ Да, удалить всю память",
            callback_data=f"confirm_delete_memory_{message.chat.id}"
        )
        cancel_btn = types.InlineKeyboardButton(
            text="❌ Отмена",
            callback_data="cancel_delete_memory"
        )
        keyboard.add(confirm_btn)
        keyboard.add(cancel_btn)

        # Показываем предупреждение с количеством записей
        warning_text = f"""⚠️ <b>Внимание!</b>

Вы собираетесь удалить <b>всю память чата</b>.

📊 Количество записей: <b>{len(chat_memory)}</b>

<i>Это действие нельзя отменить!</i>

Вы уверены?"""

        bot.reply_to(message, warning_text, reply_markup=keyboard)

    except Exception as e:
        logger.error(f"Ошибка команды /delmemory: {e}")
        bot.reply_to(message, f"❌ Ошибка: {str(e)}")

@bot.message_handler(commands=['status'])
def status_command(message):
    """Команда для проверки статуса бота"""
    try:
        # Проверяем соединения
        ai_status = "✅" if test_ai_connection() else "❌"
        telegraph_status = "✅" if test_telegraph_connection() else "❌"

        status_text = f"""📊 <b>Статус бота</b>

🤖 AI (Gemini): {ai_status}
📝 Telegraph: {telegraph_status}
💾 База данных: ✅
⏰ Время работы: {datetime.now().strftime('%H:%M:%S')}

<i>Бот работает нормально</i>"""

        bot.reply_to(message, status_text)

    except Exception as e:
        bot.reply_to(message, f"❌ Ошибка проверки статуса: {str(e)}")

@bot.message_handler(commands=['info'])
def info_command(message):
    """Обработчик команды /info для показа информации о боте и статистики"""
    try:
        # Проверяем, если команда адресована другому боту (например /info@другойбот)
        command_text = message.text.strip()
        if '@' in command_text:
            # Получаем username нашего бота
            try:
                bot_info = bot.get_me()
                bot_username = bot_info.username

                # Проверяем, адресована ли команда нашему боту
                if not command_text.endswith(f'@{bot_username}'):
                    return  # Команда адресована другому боту, игнорируем
            except Exception as e:
                logger.error(f"Ошибка получения информации о боте: {e}")
                return

        # Базовая информация о боте
        info_text = """📝 <b>sh: Digest</b>

📋 <b>Основные команды:</b>
• <code>/digest</code> — создать дайджест за 24 часа
• <code>/digest [часы]</code> — дайджест за указанное время (1-48 ч)
• <code>/addmemory</code> — добавить информацию в память чата
• <code>/memory</code> — показать память чата
• <code>/delmemory</code> — очистить память чата

👥 <b>Для админов:</b>
• <code>/digestevery [время]</code> — настроить ежедневный дайджест (например: /digestevery 20:00)
• <code>/everydel</code> — отменить ежедневный дайджест
• <code>/block [user_id]</code> — заблокировать пользователя
• <code>/unblock [user_id]</code> — разблокировать пользователя"""

        # Добавляем статистику для групп
        if message.chat.type in ['group', 'supergroup']:
            stats = get_chat_statistics(message.chat.id)

            # Проверяем, есть ли настроенный ежедневный дайджест
            daily_digest_time = get_chat_daily_digest(message.chat.id)

            info_text += f"""

📊 <b>Статистика чата:</b>
• Всего сообщений: <b>{stats['total_messages']}</b>
• За 24 часа: <b>{stats['messages_24h']}</b>
• За 7 дней: <b>{stats['messages_7d']}</b>
• Участников: <b>{stats['unique_users']}</b>"""

            if daily_digest_time:
                info_text += f"""

🕐 <b>Ежедневный дайджест:</b>
• Время: <b>{daily_digest_time} МСК</b>"""

        # Добавляем информацию о личных сообщениях
        if message.chat.type == 'private':
            info_text += """

💬 <b>В личных сообщениях:</b>
• <code>/start</code> или <code>/help</code> — помощь

<i>Добавьте меня в группу для создания дайджестов!</i>"""

        bot.reply_to(message, info_text)

    except Exception as e:
        logger.error(f"Ошибка команды /info: {e}")
        bot.reply_to(message, f"❌ Ошибка: {str(e)}")

@bot.message_handler(commands=['block'])
def block_command(message):
    """Команда для блокировки пользователя (только для админов групп)"""
    try:
        # Команда работает только в группах и супергруппах
        if message.chat.type not in ['group', 'supergroup']:
            bot.reply_to(message, "❌ Эта команда работает только в группах!")
            return

        # Проверяем права доступа
        user_has_access = False

        # Проверяем, отправлено ли сообщение анонимным администратором
        if is_anonymous_admin_message(message):
            user_has_access = True
        else:
            # Проверяем, является ли пользователь администратором
            if message.from_user and is_user_admin_or_moderator(message.chat.id, message.from_user.id):
                user_has_access = True

        if not user_has_access:
            bot.reply_to(message, "❌ Эта команда доступна только администраторам!")
            return

        # Извлекаем ID пользователя из команды
        command_parts = message.text.split()
        if len(command_parts) < 2:
            bot.reply_to(message, "❌ Укажите ID пользователя: /block [user_id]")
            return

        try:
            target_user_id = int(command_parts[1])
        except ValueError:
            bot.reply_to(message, "❌ Неверный формат ID пользователя")
            return

        # Блокируем пользователя
        if block_user(message.chat.id, target_user_id):
            bot.reply_to(message, f"✅ Пользователь {target_user_id} заблокирован")
        else:
            bot.reply_to(message, "❌ Ошибка блокировки пользователя")

    except Exception as e:
        logger.error(f"Ошибка команды /block: {e}")
        bot.reply_to(message, "❌ Произошла ошибка")

@bot.message_handler(commands=['unblock'])
def unblock_command(message):
    """Команда для разблокировки пользователя (только для админов групп)"""
    try:
        # Команда работает только в группах и супергруппах
        if message.chat.type not in ['group', 'supergroup']:
            bot.reply_to(message, "❌ Эта команда работает только в группах!")
            return

        # Проверяем права доступа
        user_has_access = False

        # Проверяем, отправлено ли сообщение анонимным администратором
        if is_anonymous_admin_message(message):
            user_has_access = True
        else:
            # Проверяем, является ли пользователь администратором
            if message.from_user and is_user_admin_or_moderator(message.chat.id, message.from_user.id):
                user_has_access = True

        if not user_has_access:
            bot.reply_to(message, "❌ Эта команда доступна только администраторам!")
            return

        # Извлекаем ID пользователя из команды
        command_parts = message.text.split()
        if len(command_parts) < 2:
            bot.reply_to(message, "❌ Укажите ID пользователя: /unblock [user_id]")
            return

        try:
            target_user_id = int(command_parts[1])
        except ValueError:
            bot.reply_to(message, "❌ Неверный формат ID пользователя")
            return

        # Разблокируем пользователя
        if unblock_user(message.chat.id, target_user_id):
            bot.reply_to(message, f"✅ Пользователь {target_user_id} разблокирован")
        else:
            bot.reply_to(message, "❌ Пользователь не был заблокирован")

    except Exception as e:
        logger.error(f"Ошибка команды /unblock: {e}")
        bot.reply_to(message, "❌ Произошла ошибка")






def extract_chat_id(text):
    """Извлекает ID чата из различных форматов ввода"""
    text = text.strip()

    # Если это уже числовой ID
    if text.lstrip('-').isdigit():
        return int(text)

    # Если это ссылка на группу
    if 'https://t.me/' in text:
        # Извлекаем username из ссылки
        username = text.split('/')[-1]
        if 'joinchat' in text or username.startswith('+'):
            return None  # Приватные ссылки не поддерживаются
        return f"@{username}" if not username.startswith('@') else username

    # Если это username
    if text.startswith('@'):
        return text

    # Если это username без @
    if text.isalnum() or '_' in text:
        return f"@{text}"

    return None

def get_chat_info_safe(chat_identifier):
    """Безопасно получает информацию о чате"""
    try:
        chat = bot.get_chat(chat_identifier)
        return chat
    except Exception as e:
        logger.error(f"Ошибка получения информации о чате {chat_identifier}: {e}")
        return None

def is_user_admin_or_moderator(chat_id: int, user_id: int) -> bool:
    """
    Проверяет, является ли пользователь администратором или модератором чата

    Args:
        chat_id: ID чата
        user_id: ID пользователя

    Returns:
        True если пользователь админ/модератор, False в противном случае
    """
    try:
        # Получаем информацию об администраторах чата
        chat_admins = bot.get_chat_administrators(chat_id)

        # Проверяем, есть ли пользователь среди администраторов
        for admin in chat_admins:
            if admin.user.id == user_id:
                # Проверяем статус: creator (создатель) или administrator (администратор)
                if admin.status in ['creator', 'administrator']:
                    return True

        return False

    except Exception as e:
        logger.error(f"Ошибка проверки прав администратора для пользователя {user_id} в чате {chat_id}: {e}")
        return False

def is_anonymous_admin_message(message) -> bool:
    """
    Проверяет, отправлено ли сообщение анонимным администратором
    (от имени канала или группы)

    Args:
        message: Объект сообщения Telegram

    Returns:
        True если сообщение от анонимного админа, False в противном случае
    """
    try:
        # Проверяем, есть ли поле sender_chat (сообщение от имени канала/группы)
        if hasattr(message, 'sender_chat') and message.sender_chat:
            # Если sender_chat.id равен chat.id, то это сообщение от имени группы (анонимный админ)
            if message.sender_chat.id == message.chat.id:
                return True
            # Если sender_chat - это связанный канал группы
            if message.sender_chat.type == 'channel':
                return True

        return False

    except Exception as e:
        logger.error(f"Ошибка проверки анонимного сообщения: {e}")
        return False

























# Обработчики callback-запросов для удаления памяти

@bot.callback_query_handler(func=lambda call: call.data.startswith("confirm_delete_memory_"))
def confirm_delete_memory_callback(call):
    """Обработчик подтверждения удаления памяти чата"""
    try:
        # Извлекаем chat_id из callback_data
        chat_id = int(call.data.split("_")[-1])

        # Проверяем права доступа еще раз для безопасности
        user_has_access = False

        # Проверяем, отправлено ли сообщение анонимным администратором
        if hasattr(call.message, 'sender_chat') and call.message.sender_chat:
            if call.message.sender_chat.id == chat_id:
                user_has_access = True
        else:
            # Проверяем, является ли пользователь администратором или модератором
            if call.from_user and is_user_admin_or_moderator(chat_id, call.from_user.id):
                user_has_access = True

        if not user_has_access:
            bot.answer_callback_query(call.id, "❌ У вас нет прав для выполнения этого действия!", show_alert=True)
            return

        # Удаляем память чата
        if clear_chat_memory(chat_id):
            success_text = "✅ <b>Память чата успешно удалена!</b>\n\nВсе записи были стерты."
            logger.info(f"Память чата {chat_id} удалена пользователем {call.from_user.id}")
        else:
            success_text = "❌ <b>Ошибка при удалении памяти чата</b>\n\nПопробуйте еще раз позже."

        # Редактируем сообщение с результатом
        bot.edit_message_text(
            chat_id=call.message.chat.id,
            message_id=call.message.message_id,
            text=success_text
        )

        bot.answer_callback_query(call.id, "Готово!")

    except Exception as e:
        logger.error(f"Ошибка в confirm_delete_memory_callback: {e}")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка", show_alert=True)

@bot.callback_query_handler(func=lambda call: call.data == "cancel_delete_memory")
def cancel_delete_memory_callback(call):
    """Обработчик отмены удаления памяти чата"""
    try:
        cancel_text = "❌ <b>Удаление памяти отменено</b>\n\nПамять чата осталась без изменений."

        # Редактируем сообщение
        bot.edit_message_text(
            chat_id=call.message.chat.id,
            message_id=call.message.message_id,
            text=cancel_text
        )

        bot.answer_callback_query(call.id, "Отменено")

    except Exception as e:
        logger.error(f"Ошибка в cancel_delete_memory_callback: {e}")
        bot.answer_callback_query(call.id, "❌ Произошла ошибка", show_alert=True)



@bot.message_handler(content_types=['text'])
def handle_text_messages(message):
    """Обработчик всех текстовых сообщений (должен быть последним)"""
    try:


        # Сохраняем сообщения только из групп и супергрупп
        if message.chat.type in ['group', 'supergroup']:
            save_message(message)
            # Сохраняем информацию о чате для всех участников
            save_user_chat(message.from_user.id, message.chat.id,
                          message.chat.title or "Неизвестный чат",
                          message.chat.username)



    except Exception as e:
        logger.error(f"Ошибка обработки сообщения: {e}")









def run_scheduler():
    """Запуск планировщика задач в отдельном потоке"""
    logger.info("Запуск планировщика задач...")

    # Планируем очистку старых сообщений каждый час
    schedule.every(CLEANUP_INTERVAL_HOURS).hours.do(cleanup_old_messages)

    # Планируем проверку ежедневных дайджестов каждую минуту
    schedule.every().minute.do(check_daily_digests)

    while True:
        try:
            schedule.run_pending()
            time.sleep(60)  # Проверяем каждую минуту
        except Exception as e:
            logger.error(f"Ошибка в планировщике: {e}")
            time.sleep(60)

def main():
    """Основная функция запуска бота"""
    logger.info("Запуск sh: Digest...")

    # Проверки при запуске
    if not startup_checks():
        logger.error("❌ Критические ошибки при запуске. Остановка.")
        return

    # Запускаем планировщик в отдельном потоке
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()

    # Запускаем бота
    logger.info("✅ sh: Digest запущен и готов к работе!")
    try:
        bot.infinity_polling(none_stop=True, interval=1)
    except KeyboardInterrupt:
        logger.info("🛑 Получен сигнал остановки")
    except Exception as e:
        logger.error(f"❌ Критическая ошибка бота: {e}")
    finally:
        logger.info("👋 Бот остановлен")

if __name__ == "__main__":
    main()