#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import logging
import os
from typing import Dict, List, Optional

# Настройка логирования
logger = logging.getLogger(__name__)

# Путь к файлу памяти
MEMORY_FILE = "memory.json"

class MemoryHandler:
    def __init__(self):
        """Инициализация обработчика памяти"""
        self.memory_file = MEMORY_FILE
        self._ensure_memory_file_exists()
    
    def _ensure_memory_file_exists(self):
        """Создает файл памяти если он не существует"""
        if not os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)
                logger.info(f"Создан файл памяти: {self.memory_file}")
            except Exception as e:
                logger.error(f"Ошибка создания файла памяти: {e}")
    
    def load_memory(self) -> Dict[str, List[str]]:
        """
        Загружает всю память из файла
        
        Returns:
            Dict где ключ - chat_id (строка), значение - список записей памяти
        """
        try:
            with open(self.memory_file, 'r', encoding='utf-8') as f:
                memory_data = json.load(f)
                return memory_data
        except FileNotFoundError:
            logger.warning(f"Файл памяти {self.memory_file} не найден, создаем новый")
            self._ensure_memory_file_exists()
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Ошибка декодирования JSON в файле памяти: {e}")
            return {}
        except Exception as e:
            logger.error(f"Ошибка загрузки памяти: {e}")
            return {}
    
    def save_memory(self, memory_data: Dict[str, List[str]]):
        """
        Сохраняет всю память в файл
        
        Args:
            memory_data: Словарь с памятью всех чатов
        """
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(memory_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Ошибка сохранения памяти: {e}")
    
    def get_chat_memory(self, chat_id: int) -> List[str]:
        """
        Получает память для конкретного чата
        
        Args:
            chat_id: ID чата
            
        Returns:
            Список записей памяти для чата
        """
        memory_data = self.load_memory()
        chat_id_str = str(chat_id)
        return memory_data.get(chat_id_str, [])
    
    def add_memory_entry(self, chat_id: int, memory_entry: str) -> bool:
        """
        Добавляет запись в память чата
        
        Args:
            chat_id: ID чата
            memory_entry: Текст записи памяти
            
        Returns:
            True если успешно добавлено, False в случае ошибки
        """
        try:
            memory_data = self.load_memory()
            chat_id_str = str(chat_id)
            
            if chat_id_str not in memory_data:
                memory_data[chat_id_str] = []
            
            # Добавляем новую запись
            memory_data[chat_id_str].append(memory_entry.strip())
            
            # Сохраняем обновленную память
            self.save_memory(memory_data)

            logger.info(f"Добавлена запись в память чата {chat_id}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка добавления записи в память: {e}")
            return False
    
    def remove_memory_entry(self, chat_id: int, entry_index: int) -> bool:
        """
        Удаляет запись из памяти чата по индексу
        
        Args:
            chat_id: ID чата
            entry_index: Индекс записи для удаления (начиная с 0)
            
        Returns:
            True если успешно удалено, False в случае ошибки
        """
        try:
            memory_data = self.load_memory()
            chat_id_str = str(chat_id)
            
            if chat_id_str not in memory_data:
                logger.warning(f"Память для чата {chat_id} не найдена")
                return False
            
            chat_memory = memory_data[chat_id_str]
            
            if 0 <= entry_index < len(chat_memory):
                removed_entry = chat_memory.pop(entry_index)
                self.save_memory(memory_data)
                logger.info(f"Удалена запись из памяти чата {chat_id}")
                return True
            else:
                logger.warning(f"Неверный индекс {entry_index} для памяти чата {chat_id}")
                return False
                
        except Exception as e:
            logger.error(f"Ошибка удаления записи из памяти: {e}")
            return False
    
    def clear_chat_memory(self, chat_id: int) -> bool:
        """
        Очищает всю память чата
        
        Args:
            chat_id: ID чата
            
        Returns:
            True если успешно очищено, False в случае ошибки
        """
        try:
            memory_data = self.load_memory()
            chat_id_str = str(chat_id)
            
            if chat_id_str in memory_data:
                del memory_data[chat_id_str]
                self.save_memory(memory_data)
                logger.info(f"Память чата {chat_id} очищена")
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка очистки памяти чата: {e}")
            return False
    
    def format_memory_for_ai(self, chat_id: int) -> Optional[str]:
        """
        Форматирует память чата для включения в AI запрос
        
        Args:
            chat_id: ID чата
            
        Returns:
            Отформатированная строка с памятью или None если памяти нет
        """
        chat_memory = self.get_chat_memory(chat_id)
        
        if not chat_memory:
            return None
        
        formatted_memory = "Дополнительная информация по чату (или его участникам):\n"
        for i, entry in enumerate(chat_memory, 1):
            formatted_memory += f"{i}. {entry}\n"
        
        return formatted_memory.strip()

# Глобальный экземпляр обработчика памяти
memory_handler = MemoryHandler()

def get_chat_memory(chat_id: int) -> List[str]:
    """Функция-обертка для получения памяти чата"""
    return memory_handler.get_chat_memory(chat_id)

def add_memory_entry(chat_id: int, memory_entry: str) -> bool:
    """Функция-обертка для добавления записи в память"""
    return memory_handler.add_memory_entry(chat_id, memory_entry)

def format_memory_for_ai(chat_id: int) -> Optional[str]:
    """Функция-обертка для форматирования памяти для AI"""
    return memory_handler.format_memory_for_ai(chat_id)

def clear_chat_memory(chat_id: int) -> bool:
    """Функция-обертка для очистки памяти чата"""
    return memory_handler.clear_chat_memory(chat_id)
