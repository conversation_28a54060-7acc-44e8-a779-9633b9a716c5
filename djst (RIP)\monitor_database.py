#!/usr/bin/env python3
"""
Скрипт для мониторинга состояния базы данных SQLite
Показывает статистику и проверяет наличие блокировок
"""

import sqlite3
import os
import logging
import time
from datetime import datetime, timedelta

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_database_stats(db_path):
    """Получает статистику базы данных"""
    try:
        conn = sqlite3.connect(db_path, timeout=10)
        cursor = conn.cursor()
        
        stats = {}
        
        # Общее количество сообщений
        cursor.execute('SELECT COUNT(*) FROM messages')
        stats['total_messages'] = cursor.fetchone()[0]
        
        # Количество уникальных чатов
        cursor.execute('SELECT COUNT(DISTINCT chat_id) FROM messages')
        stats['unique_chats'] = cursor.fetchone()[0]
        
        # Количество уникальных пользователей
        cursor.execute('SELECT COUNT(DISTINCT user_id) FROM messages')
        stats['unique_users'] = cursor.fetchone()[0]
        
        # Сообщения за последние 24 часа
        yesterday = datetime.now() - timedelta(hours=24)
        cursor.execute('SELECT COUNT(*) FROM messages WHERE timestamp >= ?', (yesterday,))
        stats['messages_24h'] = cursor.fetchone()[0]
        
        # Размер базы данных
        cursor.execute('PRAGMA page_count')
        page_count = cursor.fetchone()[0]
        cursor.execute('PRAGMA page_size')
        page_size = cursor.fetchone()[0]
        stats['db_size_mb'] = (page_count * page_size) / (1024 * 1024)
        
        # Режим журнала
        cursor.execute('PRAGMA journal_mode')
        stats['journal_mode'] = cursor.fetchone()[0]
        
        # Таймаут блокировок
        cursor.execute('PRAGMA busy_timeout')
        stats['busy_timeout'] = cursor.fetchone()[0]
        
        conn.close()
        return stats
        
    except Exception as e:
        logger.error(f"Ошибка получения статистики: {e}")
        return None

def check_database_locks(db_path):
    """Проверяет наличие блокировок базы данных"""
    try:
        # Пытаемся подключиться с коротким таймаутом
        conn = sqlite3.connect(db_path, timeout=1)
        cursor = conn.cursor()
        
        # Пытаемся выполнить простой запрос
        cursor.execute('SELECT 1')
        cursor.fetchone()
        
        conn.close()
        return False  # Блокировок нет
        
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e):
            return True  # База заблокирована
        else:
            logger.error(f"Другая ошибка базы данных: {e}")
            return None
    except Exception as e:
        logger.error(f"Неожиданная ошибка: {e}")
        return None

def monitor_database(db_path, duration_minutes=5):
    """Мониторит базу данных в течение указанного времени"""
    logger.info(f"🔍 Начинаем мониторинг базы данных на {duration_minutes} минут...")
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    lock_count = 0
    check_count = 0
    
    while time.time() < end_time:
        check_count += 1
        
        # Проверяем блокировки
        is_locked = check_database_locks(db_path)
        
        if is_locked is True:
            lock_count += 1
            logger.warning(f"⚠️ База данных заблокирована! (проверка {check_count})")
        elif is_locked is False:
            logger.info(f"✅ База данных доступна (проверка {check_count})")
        else:
            logger.error(f"❌ Ошибка проверки базы данных (проверка {check_count})")
        
        # Ждем 10 секунд перед следующей проверкой
        time.sleep(10)
    
    # Итоговая статистика
    logger.info(f"📊 Мониторинг завершен:")
    logger.info(f"   Всего проверок: {check_count}")
    logger.info(f"   Блокировок обнаружено: {lock_count}")
    logger.info(f"   Процент блокировок: {(lock_count/check_count)*100:.1f}%")

def main():
    """Основная функция"""
    # Путь к базе данных
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "messages.db")
    
    if not os.path.exists(db_path):
        logger.error(f"❌ База данных не найдена: {db_path}")
        return
    
    logger.info(f"📊 Анализируем базу данных: {db_path}")
    
    # Получаем статистику
    stats = get_database_stats(db_path)
    if stats:
        logger.info("📈 Статистика базы данных:")
        logger.info(f"   Всего сообщений: {stats['total_messages']:,}")
        logger.info(f"   Уникальных чатов: {stats['unique_chats']:,}")
        logger.info(f"   Уникальных пользователей: {stats['unique_users']:,}")
        logger.info(f"   Сообщений за 24ч: {stats['messages_24h']:,}")
        logger.info(f"   Размер БД: {stats['db_size_mb']:.2f} MB")
        logger.info(f"   Режим журнала: {stats['journal_mode']}")
        logger.info(f"   Таймаут блокировок: {stats['busy_timeout']} мс")
    
    # Проверяем текущее состояние
    is_locked = check_database_locks(db_path)
    if is_locked is True:
        logger.warning("⚠️ База данных в данный момент заблокирована!")
    elif is_locked is False:
        logger.info("✅ База данных доступна")
    else:
        logger.error("❌ Ошибка проверки состояния базы данных")
    
    # Запускаем мониторинг
    monitor_database(db_path, duration_minutes=2)

if __name__ == "__main__":
    main()
