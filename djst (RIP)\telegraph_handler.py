import requests
import json
import logging
from datetime import datetime
from typing import Optional, Dict
from .config import (
    TELEGRAPH_SHORT_NAME,
    TELEGRAPH_AUTHOR_NAME,
    TELEGRAPH_AUTHOR_URL,
    MONTH_NAMES
)

# Не переопределяем basicConfig, используем уже настроенное логирование
logger = logging.getLogger(__name__)

class TelegraphHandler:
    def __init__(self):
        """Инициализация Telegraph обработчика"""
        self.access_token = None
        self.base_url = "https://api.telegra.ph"
        self._create_account()

    def _create_account(self):
        """Создание аккаунта Telegraph"""
        try:
            url = f"{self.base_url}/createAccount"
            params = {
                'short_name': TELEGRAPH_SHORT_NAME,
                'author_name': TELEGRAPH_AUTHOR_NAME,
                'author_url': TELEGRAPH_AUTHOR_URL
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            if data.get('ok'):
                self.access_token = data['result']['access_token']
                logger.info("Telegraph аккаунт создан успешно")
            else:
                logger.error(f"Ошибка создания аккаунта Telegraph: {data.get('error')}")
                
        except Exception as e:
            logger.error(f"Ошибка при создании аккаунта Telegraph: {e}")

    def _prepare_content(self, digest_text: str) -> list:
        """Подготовка контента для Telegraph в формате Node"""
        try:
            import re

            # Разбиваем текст на блоки по HTML тегам
            content = []

            # Разделяем текст на части по основным блочным элементам
            blocks = re.split(r'(<h3[^>]*>.*?</h3>|<p[^>]*>.*?</p>|<blockquote[^>]*>.*?</blockquote>)', digest_text, flags=re.DOTALL)

            for block in blocks:
                block = block.strip()
                if not block:
                    continue

                # Обрабатываем заголовки
                h3_match = re.match(r'<h3[^>]*>(.*?)</h3>', block, re.DOTALL)
                if h3_match:
                    content.append({
                        "tag": "h3",
                        "children": self._parse_inline_html(h3_match.group(1))
                    })
                    continue

                # Обрабатываем цитаты
                blockquote_match = re.match(r'<blockquote[^>]*>(.*?)</blockquote>', block, re.DOTALL)
                if blockquote_match:
                    content.append({
                        "tag": "blockquote",
                        "children": self._parse_inline_html(blockquote_match.group(1))
                    })
                    continue

                # Обрабатываем абзацы
                p_match = re.match(r'<p[^>]*>(.*?)</p>', block, re.DOTALL)
                if p_match:
                    content.append({
                        "tag": "p",
                        "children": self._parse_inline_html(p_match.group(1))
                    })
                    continue

                # Если это обычный текст без тегов, оборачиваем в <p>
                if block and not re.match(r'<[^>]+>', block):
                    content.append({
                        "tag": "p",
                        "children": self._parse_inline_html(block)
                    })

            return content if content else [{"tag": "p", "children": [digest_text]}]

        except Exception as e:
            logger.error(f"Ошибка подготовки контента: {e}")
            # Возвращаем простой текстовый контент в случае ошибки
            return [{"tag": "p", "children": [digest_text]}]

    def _parse_inline_html(self, text: str) -> list:
        """Парсер инлайн HTML тегов для Telegraph"""
        import re

        try:
            # Список для хранения узлов
            nodes = []

            # Паттерн для поиска HTML тегов (включая ссылки)
            pattern = r'<(a)\s+href="([^"]*)"[^>]*>(.*?)</\1>|<(b|strong|i|em|u|code)(?:[^>]*)>(.*?)</\4>|<br\s*/?>'

            last_end = 0

            for match in re.finditer(pattern, text, re.DOTALL):
                # Добавляем текст перед тегом
                if match.start() > last_end:
                    plain_text = text[last_end:match.start()]
                    if plain_text:
                        nodes.append(plain_text)

                # Обрабатываем найденный тег
                if match.group(0).startswith('<br'):
                    # Перенос строки
                    nodes.append({"tag": "br"})
                elif match.group(1) == 'a':
                    # Ссылка
                    href = match.group(2)
                    content = match.group(3)

                    # Рекурсивно обрабатываем содержимое ссылки
                    children = self._parse_inline_html(content) if content else []

                    nodes.append({
                        "tag": "a",
                        "attrs": {"href": href},
                        "children": children
                    })
                else:
                    # Обычные теги форматирования
                    tag = match.group(4)
                    content = match.group(5)

                    # Рекурсивно обрабатываем содержимое тега
                    children = self._parse_inline_html(content) if content else []

                    nodes.append({
                        "tag": tag,
                        "children": children
                    })

                last_end = match.end()

            # Добавляем оставшийся текст
            if last_end < len(text):
                remaining_text = text[last_end:]
                if remaining_text:
                    nodes.append(remaining_text)

            return nodes if nodes else [text]

        except Exception as e:
            logger.error(f"Ошибка парсинга HTML: {e}")
            return [text]

    def create_page(self, digest_text: str, chat_title: str = "чат") -> Optional[str]:
        """
        Создание страницы в Telegraph
        
        Args:
            digest_text: Текст дайджеста с HTML форматированием
            chat_title: Название чата
            
        Returns:
            URL созданной страницы или None в случае ошибки
        """
        if not self.access_token:
            logger.error("Telegraph аккаунт не инициализирован")
            return None
        
        try:
            # Формируем заголовок страницы
            today = datetime.now()
            day = today.day
            month_name = MONTH_NAMES.get(today.month, str(today.month))
            title = f"🫦 Подкаст в чате \"{chat_title}\" за {day} {month_name}"
            
            # Подготавливаем контент
            content = self._prepare_content(digest_text)
            
            # Создаем страницу
            url = f"{self.base_url}/createPage"
            data = {
                'access_token': self.access_token,
                'title': title,
                'author_name': TELEGRAPH_AUTHOR_NAME,
                'author_url': TELEGRAPH_AUTHOR_URL,
                'content': json.dumps(content),
                'return_content': False
            }
            
            response = requests.post(url, data=data)
            response.raise_for_status()
            
            result = response.json()
            if result.get('ok'):
                page_url = result['result']['url']
                logger.info(f"Telegraph страница создана: {page_url}")
                return page_url
            else:
                logger.error(f"Ошибка создания страницы: {result.get('error')}")
                return None
                
        except Exception as e:
            logger.error(f"Ошибка создания Telegraph страницы: {e}")
            return None

    def test_connection(self) -> bool:
        """Тестирование соединения с Telegraph API"""
        try:
            if not self.access_token:
                return False
            
            url = f"{self.base_url}/getAccountInfo"
            params = {
                'access_token': self.access_token,
                'fields': '["short_name"]'
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return data.get('ok', False)
            
        except Exception as e:
            logger.error(f"Ошибка тестирования Telegraph соединения: {e}")
            return False

# Глобальный экземпляр Telegraph обработчика
telegraph_handler = TelegraphHandler()

def create_telegraph_page(digest_text: str, chat_title: str = "чат") -> Optional[str]:
    """Функция-обертка для создания Telegraph страницы"""
    return telegraph_handler.create_page(digest_text, chat_title)

def test_telegraph_connection() -> bool:
    """Функция-обертка для тестирования соединения"""
    return telegraph_handler.test_connection()
