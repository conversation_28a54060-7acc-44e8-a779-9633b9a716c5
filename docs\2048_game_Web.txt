
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from hook_utils import find_class
from android.content import Context, Intent
from android.net import U<PERSON>
from android.view.inputmethod import InputMethod<PERSON>anager
from java import dynamic_proxy, jboolean
import uuid

__id__ = "game_2048"
__name__ = "2048 Game"
__description__ = "Play 2048 in an overlay. Use: Side menu or Chat menu"
__author__ = "@JasonVurhyz"
__version__ = "1.0.3"
__min_version__ = "11.12.0"
__icon__ = "Plugins_Test/3"

url = "https://2048-tau-three.vercel.app"


class Game2048Plugin(BasePlugin):
    WindowManager, LayoutParams, PixelFormat, WebView, Button, LinearLayout = (None,) * 6
    ApplicationLoader, View, OnClickListener, InputMethodManager = (None,) * 4
    window_manager = None
    browser_view = None
    is_game_open = False

    def __init__(self):
        super().__init__()
        self.instance_id = str(uuid.uuid4())

    def load_android_classes(self):
        if self.WindowManager:
            return
        self.__class__.WindowManager = find_class("android.view.WindowManager")
        self.__class__.LayoutParams = find_class("android.view.WindowManager$LayoutParams")
        self.__class__.PixelFormat = find_class("android.graphics.PixelFormat")
        self.__class__.WebView = find_class("android.webkit.WebView")
        self.__class__.Button = find_class("android.widget.Button")
        self.__class__.LinearLayout = find_class("android.widget.LinearLayout")
        self.__class__.ApplicationLoader = find_class("org.telegram.messenger.ApplicationLoader")
        self.__class__.View = find_class("android.view.View")
        self.__class__.OnClickListener = find_class("android.view.View$OnClickListener")
        self.__class__.InputMethodManager = find_class("android.view.inputmethod.InputMethodManager")

    def on_plugin_load(self):
        self.load_android_classes()
        self.add_menu_item(
            MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text="2048 Game",
                icon="msg_game",
                on_click=self.toggle_game,
            )
        )

    def on_plugin_unload(self):
        run_on_ui_thread(self.close_game)

    def toggle_game(self, context: dict):
        drawer_layout = context.get("drawer_layout")
        if drawer_layout:
            drawer_layout.closeDrawer(False)

        if self.is_game_open:
            run_on_ui_thread(self.close_game)
        else:
            run_on_ui_thread(self.open_game)

    def open_game(self):
        if self.is_game_open:
            return
        self.is_game_open = True
        try:
            app_context = self.ApplicationLoader.applicationContext
            if not app_context:
                self.is_game_open = False
                return

            self.window_manager = app_context.getSystemService(Context.WINDOW_SERVICE)
            display = self.window_manager.getDefaultDisplay()
            size = find_class("android.graphics.Point")()
            display.getSize(size)
            screen_width, screen_height = size.x, size.y

            layout = self.LinearLayout(app_context)
            layout.setOrientation(self.LinearLayout.VERTICAL)

            web_view = self.WebView(app_context)
            web_settings = web_view.getSettings()
            web_settings.setJavaScriptEnabled(True)
            web_settings.setDomStorageEnabled(True)
            web_view.loadUrl(url)
            layout_params_web = self.LinearLayout.LayoutParams(
                self.LinearLayout.LayoutParams.MATCH_PARENT, 0, 1.0
            )
            layout.addView(web_view, layout_params_web)

            close_button = self.Button(app_context)
            close_button.setText("Закрыть игру")

            class CloseButtonListener(dynamic_proxy(self.OnClickListener)):
                def __init__(self, plugin):
                    super().__init__()
                    self.plugin = plugin

                def onClick(self, view):
                    run_on_ui_thread(self.plugin.close_game)

            close_button.setOnClickListener(CloseButtonListener(self))
            layout_params_button = self.LinearLayout.LayoutParams(
                self.LinearLayout.LayoutParams.MATCH_PARENT,
                self.LinearLayout.LayoutParams.WRAP_CONTENT,
            )
            layout.addView(close_button, layout_params_button)

            self.browser_view = layout

            params = self.LayoutParams()
            Build_VERSION = find_class("android.os.Build$VERSION")
            params.type = (
                self.LayoutParams.TYPE_APPLICATION_OVERLAY
                if Build_VERSION.SDK_INT >= 26
                else self.LayoutParams.TYPE_PHONE
            )
            params.flags = self.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            params.format = self.PixelFormat.TRANSLUCENT
            params.gravity = (
                find_class("android.view.Gravity").TOP | find_class("android.view.Gravity").LEFT
            )
            params.width = int(screen_width * 0.9)
            params.height = int(screen_height * 0.8)
            params.x = int(screen_width * 0.05)
            params.y = int(screen_height * 0.1)

            Settings = find_class("android.provider.Settings")
            if (
                Build_VERSION.SDK_INT >= 23
                and not Settings.canDrawOverlays(app_context)
            ):
                fragment = get_last_fragment()
                activity = fragment.getParentActivity() if fragment else None
                if activity:
                    intent = Intent(
                        Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                        Uri.parse("package:" + app_context.getPackageName()),
                    )
                    activity.startActivity(intent)
                    self.is_game_open = False
                    return

                self.is_game_open = False
                return

            self.window_manager.addView(self.browser_view, params)

        except Exception as e:
            self.is_game_open = False
            self.close_game()

    def close_game(self):
        if not self.is_game_open:
            return
        try:
            if self.browser_view and self.window_manager:
                if self.browser_view.isAttachedToWindow():
                    self.window_manager.removeView(self.browser_view)
                self.browser_view = None
            self.is_game_open = False
        except Exception as e:
            self.is_game_open = False
            self.browser_view = None
