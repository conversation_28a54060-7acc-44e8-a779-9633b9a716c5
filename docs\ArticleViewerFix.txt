from android_utils import log

from android.view import View, MotionEvent
from java import jclass

__name__ = "ArticleViewerFix"
__description__ = "Disables goofy-ahh browser's close on swipe"
__icon__ = "cats_for_conversation/17"
__version__ = "1.0.0"
__id__ = "articleViewerFix"
__author__ = "@bleizix"
__min_version__ = "11.9.1"


class ArticleViewerFix:
    def before_hooked_method(self, param):
        param.setResult(False)


class HashTagsHookPlugin(BasePlugin):
    def on_plugin_load(self):
        try:
            self._on_plugin_load()
        except Exception as e:
            log(str(e))

    def _on_plugin_load(self):
        clazz = jclass("org.telegram.ui.ArticleViewer$WindowView")
        self.hook_method(clazz.getClass().getDeclaredMethod("handleTouchEvent", MotionEvent), ArticleViewerFix())
