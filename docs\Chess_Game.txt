
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.bulletin import BulletinHelper
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from hook_utils import find_class
from android.content import Context, Intent
from android.net import <PERSON><PERSON>
from java import dynamic_proxy
from android.graphics.drawable import GradientDrawable
from android.webkit import WebSettings, WebViewClient, WebChromeClient

__id__ = "chess_game"
__name__ = "Chess Game (Overlay)"
__description__ = "Игра в шахматы в оверлее с возможностью перетаскивания свернутого окна."
__author__ = "@SaturnFake"
__version__ = "0.0.3"
__min_version__ = "11.12.0"
__icon__ = "Plugins_Test/4"

GAME_URL = "https://chess2tbs.vercel.app/"

class ChessOverlayPlugin(BasePlugin):
    window_manager = None
    browser_view = None
    restore_view = None
    is_game_open = False
    is_minimized = False
    last_params = None
    android_classes_loaded = False
    dp_cache = {}
    WindowManager = None
    LayoutParams = None
    PixelFormat = None
    WebView = None
    Button = None
    LinearLayout = None
    FrameLayout = None
    ApplicationLoader = None
    View = None
    OnClickListener = None
    OnTouchListener = None
    Gravity = None
    Theme = None
    AndroidUtilities = None
    MotionEvent = None
    GradientDrawable = None
    GradientOrientation = None

    def __init__(self):
        super().__init__()

    def load_android_classes(self):
        if self.android_classes_loaded: return
        try:
            self.WindowManager = find_class("android.view.WindowManager")
            self.LayoutParams = find_class("android.view.WindowManager$LayoutParams")
            self.PixelFormat = find_class("android.graphics.PixelFormat")
            self.WebView = find_class("android.webkit.WebView")
            self.Button = find_class("android.widget.Button")
            self.LinearLayout = find_class("android.widget.LinearLayout")
            self.FrameLayout = find_class("android.widget.FrameLayout")
            self.ApplicationLoader = find_class("org.telegram.messenger.ApplicationLoader")
            self.View = find_class("android.view.View")
            self.OnClickListener = find_class("android.view.View$OnClickListener")
            self.OnTouchListener = find_class("android.view.View$OnTouchListener")
            self.Gravity = find_class("android.view.Gravity")
            self.Theme = find_class("org.telegram.ui.ActionBar.Theme")
            self.AndroidUtilities = find_class("org.telegram.messenger.AndroidUtilities")
            self.MotionEvent = find_class("android.view.MotionEvent")
            self.GradientDrawable = find_class("android.graphics.drawable.GradientDrawable")
            self.GradientOrientation = find_class("android.graphics.drawable.GradientDrawable$Orientation")
            self.android_classes_loaded = True
        except: pass

    def get_dp(self, value):
        if value not in self.dp_cache:
            self.dp_cache[value] = self.AndroidUtilities.dp(value)
        return self.dp_cache[value]

    def on_plugin_load(self):
        self.load_android_classes()
        self.add_menu_item(MenuItemData(
            menu_type=MenuItemType.CHAT_ACTION_MENU,
            text="Chess Game 💎",
            icon="msg_mini_game",
            on_click=self.toggle_game
        ))

    def on_plugin_unload(self):
        run_on_ui_thread(self.close_game)

    def toggle_game(self, context: dict):
        activity = context.get("context")
        try:
            if drawer_layout := context.get("drawer_layout"):
                drawer_layout.closeDrawer(False)
            if self.is_game_open:
                run_on_ui_thread(self.restore_game if self.is_minimized else self.close_game)
            else:
                run_on_ui_thread(lambda: self.open_game(activity))
        except Exception as e:
            BulletinHelper.show_error(f"Ошибка: {e}")

    def create_control_button(self, text, action):
        app_context = self.ApplicationLoader.applicationContext
        button = self.Button(app_context)
        button.setText(text)
        button.setTextColor(self.Theme.getColor(self.Theme.key_featuredStickers_buttonText))
        button.setBackgroundDrawable(self.Theme.createSimpleSelectorRoundRectDrawable(
            self.get_dp(8), 
            self.Theme.getColor(self.Theme.key_featuredStickers_addButton), 
            self.Theme.getColor(self.Theme.key_featuredStickers_addButtonPressed)
        ))
        pad = self.get_dp(16)
        button.setPadding(pad, self.get_dp(8), pad, self.get_dp(8))
        class ClickListener(dynamic_proxy(self.OnClickListener)):
            def __init__(self, action): 
                super().__init__()
                self.action = action
            def onClick(self, _): 
                run_on_ui_thread(self.action)
        button.setOnClickListener(ClickListener(action))
        return button

    def open_game(self, activity):
        if self.is_game_open: return
        self.is_game_open = True
        try:
            app_context = self.ApplicationLoader.applicationContext
            if not app_context: 
                self.is_game_open = False
                return
           
            self.window_manager = app_context.getSystemService(Context.WINDOW_SERVICE)
            main_layout = self.LinearLayout(app_context)
            main_layout.setOrientation(self.LinearLayout.VERTICAL)
            web_view = self.WebView(app_context)
            web_settings = web_view.getSettings()
            web_settings.setJavaScriptEnabled(True)
            web_settings.setDomStorageEnabled(True)
            web_settings.setDatabaseEnabled(True)
            web_settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW)
            web_settings.setDefaultTextEncodingName("utf-8")
            web_settings.setAllowContentAccess(True)
            web_settings.setAllowFileAccess(True)
            web_settings.setAllowUniversalAccessFromFileURLs(True)
            web_settings.setMediaPlaybackRequiresUserGesture(False)
            web_settings.setSupportMultipleWindows(True)
            web_settings.setJavaScriptCanOpenWindowsAutomatically(True)
            web_settings.setLoadsImagesAutomatically(True)
            web_settings.setCacheMode(WebSettings.LOAD_DEFAULT)
            web_view.setWebViewClient(WebViewClient())
            web_view.setWebChromeClient(WebChromeClient())
            web_view.loadUrl(GAME_URL)
            main_layout.addView(web_view, self.LinearLayout.LayoutParams(
                self.LinearLayout.LayoutParams.MATCH_PARENT, 0, 1.0
            ))
            controls_layout = self.LinearLayout(app_context)
            controls_layout.setOrientation(self.LinearLayout.HORIZONTAL)
            controls_layout.setGravity(self.Gravity.CENTER)
            pad = self.get_dp(10)
            controls_layout.setPadding(pad, pad, pad, pad)
            controls_layout.setBackgroundDrawable(self.GradientDrawable(
                self.GradientOrientation.TOP_BOTTOM, [
                    self.Theme.getColor(self.Theme.key_windowBackgroundGray),
                    self.Theme.getColor(self.Theme.key_windowBackgroundWhite)
                ]
            ))
            
            button_params = self.LinearLayout.LayoutParams(0, self.LinearLayout.LayoutParams.WRAP_CONTENT, 1.0)
            controls_layout.addView(self.create_control_button("Закрыть", self.close_game), button_params)
            controls_layout.addView(self.View(app_context), self.LinearLayout.LayoutParams(pad, 1))
            controls_layout.addView(self.create_control_button("Свернуть", self.minimize_game), button_params)
            main_layout.addView(controls_layout)
            self.browser_view = main_layout
            params = self.LayoutParams()
            Build_VERSION = find_class("android.os.Build$VERSION")
            params.type = self.LayoutParams.TYPE_APPLICATION_OVERLAY if Build_VERSION.SDK_INT >= 26 else self.LayoutParams.TYPE_PHONE
            params.flags = self.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            params.format = self.PixelFormat.TRANSLUCENT
            params.gravity = self.Gravity.TOP | self.Gravity.LEFT
            params.width = self.LayoutParams.MATCH_PARENT
            params.height = self.LayoutParams.MATCH_PARENT
            params.x = params.y = 0
            self.last_params = params
            Settings = find_class("android.provider.Settings")
            if Build_VERSION.SDK_INT >= 23 and not Settings.canDrawOverlays(app_context):
                self.is_game_open = False
                activity.startActivity(Intent(
                    Settings.ACTION_MANAGE_OVERLAY_PERMISSION, 
                    Uri.parse("package:" + app_context.getPackageName())
                ))
                BulletinHelper.show_error("Пожалуйста, предоставьте разрешение на оверлей.")
                return
            
            self.window_manager.addView(self.browser_view, params)
        except Exception as e:
            self.is_game_open = False
            BulletinHelper.show_error(f"Ошибка при открытии: {e}")
            self.close_game()

    def minimize_game(self):
        if not self.is_game_open or self.is_minimized or not self.window_manager: return
        class DragListener(dynamic_proxy(self.OnTouchListener)):
            def __init__(self, plugin, view):
                super().__init__()
                self.plugin = plugin
                self.view = view
            def onTouch(self, view, event):
                params = self.view.getLayoutParams()
                action = event.getAction()
                if action == self.plugin.MotionEvent.ACTION_DOWN:
                    self.ix, self.iy = params.x, params.y
                    self.itx, self.ity = event.getRawX(), event.getRawY()
                    self.moving = False
                    return True
                if action == self.plugin.MotionEvent.ACTION_MOVE:
                    dx, dy = event.getRawX() - self.itx, event.getRawY() - self.ity
                    if not self.moving and (abs(dx) > 10 or abs(dy) > 10): 
                        self.moving = True
                    if self.moving:
                        params.x, params.y = int(self.ix + dx), int(self.iy + dy)
                        self.plugin.window_manager.updateViewLayout(self.view, params)
                    return True
                if action == self.plugin.MotionEvent.ACTION_UP:
                    if not self.moving: 
                        view.performClick()
                    return True
                return False
        
        try:
            if self.browser_view and self.browser_view.isAttachedToWindow(): 
                self.window_manager.removeView(self.browser_view)
            self.restore_view = self.create_control_button("Развернуть", self.restore_game)
            self.restore_view.setOnTouchListener(DragListener(self, self.restore_view))
            params = self.LayoutParams()
            Build_VERSION = find_class("android.os.Build$VERSION")
            params.type = self.LayoutParams.TYPE_APPLICATION_OVERLAY if Build_VERSION.SDK_INT >= 26 else self.LayoutParams.TYPE_PHONE
            params.flags = self.LayoutParams.FLAG_NOT_FOCUSABLE | self.LayoutParams.FLAG_NOT_TOUCH_MODAL
            params.format = self.PixelFormat.TRANSLUCENT
            params.gravity = self.Gravity.TOP | self.Gravity.LEFT
            params.width = self.LayoutParams.WRAP_CONTENT
            params.height = self.LayoutParams.WRAP_CONTENT
            params.x, params.y = self.get_dp(10), self.get_dp(50)
            self.window_manager.addView(self.restore_view, params)
            self.is_minimized = True
        except Exception as e: 
            BulletinHelper.show_error(f"Ошибка при сворачивании: {e}")

    def restore_game(self):
        if not self.is_game_open or not self.is_minimized or not self.window_manager: return
        try:
            if self.restore_view and self.restore_view.isAttachedToWindow(): 
                self.window_manager.removeView(self.restore_view)
            self.restore_view = None
            if self.browser_view: 
                self.window_manager.addView(self.browser_view, self.last_params)
            self.is_minimized = False
        except Exception as e: 
            BulletinHelper.show_error(f"Ошибка при разворачивании: {e}")

    def close_game(self):
        if not self.is_game_open: return
        try:
            if self.window_manager:
                if self.browser_view and self.browser_view.isAttachedToWindow(): 
                    self.window_manager.removeView(self.browser_view)
                if self.restore_view and self.restore_view.isAttachedToWindow(): 
                    self.window_manager.removeView(self.restore_view)
            self.browser_view = self.restore_view = self.window_manager = self.last_params = None
            self.is_game_open = self.is_minimized = False
        except: pass
