# -*- coding: utf-8 -*-
import traceback
from base_plugin import BasePlugin, MenuItemData, MenuItemType, MethodHook, HookResult
from android_utils import run_on_ui_thread, log
from client_utils import get_last_fragment
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from org.telegram.messenger import AndroidUtilities, R, MessageObject
from org.telegram.ui.ActionBar import Theme
from org.telegram.ui import ChatActivity
from org.telegram.ui.Cells import ChatMessageCell
from java.lang import Boolean
from hook_utils import find_class

__id__ = "LocalEdictor"
__name__ = "Local Editor"
__description__ = "Позволяет локально редактировать сообщения в чатах и каналах. Нажмите на сообщение -> Плагины -> Изменить локально. Очистить изменения: Три точки -> Плагины -> Очистить редактор. Сколько фейков теперь будет..."
__author__ = "@Nikita218000"
__version__ = "1.0.1"
__min_version__ = "11.12.0"
__icon__ = "IconForPlugins_by_TgEmodziBot/2"


class LocalHook(MethodHook):
    """Хук для перехвата setMessageContent."""
    def __init__(self, plugin):
        self.plugin = plugin

    def before_hooked_method(self, param):
        try:
            message_object = param.args[0]
            if message_object is None:
                log(f"[{__name__}] message_object is None in setMessageContent hook.")
                return HookResult(HookStrategy.DEFAULT)
            
            message_id = message_object.getId()
            dialog_id = message_object.getDialogId()
            key = (message_id, dialog_id)
            if key in self.plugin.edited_messages:
                new_text = self.plugin.edited_messages[key]
                if new_text and isinstance(new_text, str):
                    message_object.applyNewText(new_text)
                    message_object.forceUpdate = True
                    log(f"[{__name__}] Applied edited text for message {message_id} in dialog {dialog_id}.")
                else:
                    log(f"[{__name__}] Invalid new_text for message {message_id} in dialog {dialog_id}: {new_text}")
            else:
                log(f"[{__name__}] No edited text found for message {message_id} in dialog {dialog_id}")
        except Exception as e:
            log(f"[{__name__}] Error in setMessageContent hook: {str(e)}")
            traceback.print_exc()
        return HookResult(HookStrategy.DEFAULT)

class LocalEditPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.edited_messages = {}  # Хранилище: (message_id, dialog_id) -> text

    def on_plugin_load(self):
        """Called when the plugin is loaded."""
        try:
            # Пункт меню для редактирования сообщения (если я правильно понял ::////)
            self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.MESSAGE_CONTEXT_MENU,
                    text="Изменить локально",
                    on_click=self.handle_edit_click,
                    icon="msg_edit"
                )
            )
            # Пункт меню для сброса всех изменений (наверное)
            self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.CHAT_ACTION_MENU,
                    text="Сбросить лок. изменения",
                    on_click=self.handle_reset_click,
                    icon="msg_delete"
                )
            )
            # Хук на setMessageContent (точно)
            ChatMessageCellClass = find_class("org.telegram.ui.Cells.ChatMessageCell")
            method = ChatMessageCellClass.getDeclaredMethod(
                "setMessageContent",
                MessageObject.getClass(),
                MessageObject.GroupedMessages.getClass(),
                Boolean.TYPE,
                Boolean.TYPE,
                Boolean.TYPE
            )
            self.hook = self.hook_method(method, LocalHook(self), priority=10)
            log(f"[{__name__}] Plugin loaded successfully.")
        except Exception as e:
            log(f"[{__name__}] Error loading plugin: {str(e)}")

    def on_plugin_unload(self):
        """Called when the plugin is unloaded."""
        try:
            self.edited_messages.clear()
            log(f"[{__name__}] Plugin unloaded successfully.")
        except Exception as e:
            log(f"[{__name__}] Error unloading plugin: {str(e)}")

    def handle_edit_click(self, context):
        """Handles the click on the 'Изменить локально' menu item."""
        try:
            message_object = context.get("message")
            if not message_object:
                log(f"[{__name__}] No message object found.")
                BulletinHelper.show_error("Не удалось получить объект сообщения. Плаки плаки!")
                return

            last_fragment = get_last_fragment()
            if not last_fragment or not last_fragment.getParentActivity():
                log(f"[{__name__}] No parent activity found.")
                BulletinHelper.show_error("Не удалось получить контекст для диалога.")
                return
            
            activity = last_fragment.getParentActivity()
            LinearLayout = find_class("android.widget.LinearLayout")
            EditText = find_class("android.widget.EditText")

            layout = LinearLayout(activity)
            layout.setOrientation(LinearLayout.VERTICAL)
            padding = AndroidUtilities.dp(20)
            layout.setPadding(padding, AndroidUtilities.dp(10), padding, AndroidUtilities.dp(10))

            edit_text = EditText(activity)
            current_text = str(message_object.messageText) if message_object.messageText else ""
            edit_text.setText(current_text)
            edit_text.setHint("Введите новый текст")
            edit_text.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
            edit_text.setHintTextColor(Theme.getColor(Theme.key_dialogTextHint))
            
            layout.addView(edit_text)

            builder = AlertDialogBuilder(activity)
            builder.set_title("Локальное редактирование")
            builder.set_view(layout)

            def on_save_click(dialog, which):
                new_text = edit_text.getText().toString()
                run_on_ui_thread(lambda: self._apply_local_edit(message_object, new_text))
                dialog.dismiss()

            builder.set_positive_button("Сохранить", on_save_click)
            builder.set_negative_button("Отмена", lambda d, w: d.dismiss())
            
            run_on_ui_thread(builder.show)
            log(f"[{__name__}] Edit dialog shown for message {message_object.getId()} in dialog {message_object.getDialogId()}.")
        except Exception as e:
            log(f"[{__name__}] Error handling edit click: {str(e)}")
            traceback.print_exc()
            BulletinHelper.show_error("Ошибка при открытии окна редактирования.")

    def handle_reset_click(self, context):
        """Handles the click on the 'Сбросить локальные изменения' menu item. Я крут, пишу коменты на английском"""
        try:
            self._reset_messages()
            log(f"[{__name__}] Reset all edited messages requested.")
        except Exception as e:
            log(f"[{__name__}] Error handling reset click: {str(e)}")
            traceback.print_exc()

    def _apply_local_edit(self, message_object, new_text):
        """Applies the new text to the message object and updates the UI."""
        try:
            message_id = message_object.getId()
            dialog_id = message_object.getDialogId()
            key = (message_id, dialog_id)
            if not new_text or not isinstance(new_text, str) or not new_text.strip():
                log(f"[{__name__}] Invalid new_text for message {message_id} in dialog {dialog_id}: {new_text}")
                BulletinHelper.show_error("Текст пустой или некорректный. 😿")
                return

            self.edited_messages[key] = new_text
            message_object.applyNewText(new_text)
            message_object.forceUpdate = True
            log(f"[{__name__}] Saved and applied edited text for message {message_id} in dialog {dialog_id}.")

            fragment = get_last_fragment()
            if isinstance(fragment, ChatActivity):
                cell = fragment.findMessageCell(message_id, False)
                if cell:
                    cell.getMessageObject().applyNewText(new_text)
                    cell.getMessageObject().forceUpdate = True
                    cell.invalidate()
                    log(f"[{__name__}] Updated message {message_id} in dialog {dialog_id} via cell redraw.")
                    BulletinHelper.show_success("Сообщение отредактировано! 😸")
                    return
                else:
                    log(f"[{__name__}] Failed to find message cell for message {message_id} in dialog {dialog_id}.")
                    BulletinHelper.show_error("Не удалось обновить сообщение: ячейка не найдена. 😿")
            else:
                log(f"[{__name__}] Current fragment is not ChatActivity for message {message_id} in dialog {dialog_id}.")
                BulletinHelper.show_error("Не удалось открыть чат: чат не активен. 😿")
        except Exception as e:
            log(f"[{__name__}] Error applying local edit for message {message_id} in dialog {dialog_id}: {str(e)}")
            traceback.print_exc()
            BulletinHelper.show_error("Не удалось отредактировать сообщение. 😿")

    def _reset_messages(self):
        """Resets all local edits for all chats."""
        try:
            log(f"[{__name__}] Clearing all edited messages: {self.edited_messages}")
            self.edited_messages = {}
            log(f"[{__name__}] All edited messages cleared.")
            run_on_ui_thread(lambda: BulletinHelper.show_success("Все локальные изменения сброшены! 😸"))
        except Exception as e:
            log(f"[{__name__}] Error resetting local edits: {str(e)}")
            traceback.print_exc()
            run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка при сбросе изменений. 😿"))