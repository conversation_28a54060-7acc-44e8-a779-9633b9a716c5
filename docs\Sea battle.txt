# Автор плагина - @IRimuru_TempestI
# Пока что нельзя самостоятельно ставить корабли, в будущем сделаю обновление что-бы была такая возможность
# Буду рад подаркам или простого сообщения @IRimuru_TempestI (сообщение платные)
# Так же могу сделать для вас плагин, писать сюда t.me/anonaskbot?start=0fgy8, сразу пишите что за плагин хотите, сколько платите, и ваш тег.
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
import random

from hook_utils import find_class
from java import jboolean, dynamic_proxy

TextView = find_class("android.widget.TextView")
EditText = find_class("android.widget.EditText")
GridLayout = find_class("android.widget.GridLayout")
LinearLayout = find_class("android.widget.LinearLayout")
FrameLayout = find_class("android.widget.FrameLayout")
Button = find_class("android.widget.Button")
R = find_class("org.telegram.messenger.R")
AndroidUtilities = find_class("org.telegram.messenger.AndroidUtilities")
Theme = find_class("org.telegram.ui.ActionBar.Theme")
Gravity = find_class("android.view.Gravity")
InputType = find_class("android.text.InputType")
OnClickListenerInterface = find_class("android.view.View$OnClickListener")
OnLongClickListenerInterface = find_class("android.view.View$OnLongClickListener")

class Ship:
    def __init__(self, size):
        self.size = size
        self.hits = 0
        self.coordinates = []
    
    def is_sunk(self):
        return self.hits >= self.size
    
    def add_coordinate(self, row, col):
        self.coordinates.append((row, col))

class NavalBattleGame:
    def __init__(self, size=10):
        self.size = size
        self.player_board = [[0 for _ in range(size)] for _ in range(size)]
        self.ai_board = [[0 for _ in range(size)] for _ in range(size)]
        self.player_ships = []
        self.ai_ships = []
        self.game_over = False
        self.player_turn = True
        self.dialog = None
        self.title_view = None
        self.last_ai_hit = None
        self.ai_hit_direction = None
        self.ai_hits = []
        self.setup_boards()
    
    def setup_boards(self):
        self.place_ships(self.player_board, self.player_ships)
        self.place_ships(self.ai_board, self.ai_ships)
    
    def place_ships(self, board, ships_list):
        ship_sizes = [4, 3, 3, 2, 2, 2, 1, 1, 1, 1]
        
        for size in ship_sizes:
            placed = False
            attempts = 0
            
            while not placed and attempts < 100:
                attempts += 1
                horizontal = random.choice([True, False])
                if horizontal:
                    max_col = self.size - size
                    max_row = self.size - 1
                else:
                    max_col = self.size - 1
                    max_row = self.size - size
                
                row = random.randint(0, max_row)
                col = random.randint(0, max_col)
                
                if self.can_place_ship(board, row, col, size, horizontal):
                    ship = Ship(size)
                    for i in range(size):
                        if horizontal:
                            board[row][col + i] = 1
                            ship.add_coordinate(row, col + i)
                        else:
                            board[row + i][col] = 1
                            ship.add_coordinate(row + i, col)
                    ships_list.append(ship)
                    placed = True
    
    def can_place_ship(self, board, row, col, size, horizontal):
        for i in range(-1, size + 1):
            for j in range(-1, 2):
                if horizontal:
                    check_row = row + j
                    check_col = col + i
                else:
                    check_row = row + i
                    check_col = col + j
                
                if 0 <= check_row < self.size and 0 <= check_col < self.size:
                    if board[check_row][check_col] != 0:
                        return False
        return True
    
    def player_shoot(self, row, col):
        if self.game_over or not self.player_turn:
            return False
        
        if self.ai_board[row][col] in (2, 3, 4):
            return False
        
        if self.ai_board[row][col] == 1:
            self.ai_board[row][col] = 2
            for ship in self.ai_ships:
                if (row, col) in ship.coordinates:
                    ship.hits += 1
                    if ship.is_sunk():
                        self.mark_surrounding(ship, self.ai_board)
                    break
            return True
        else:
            self.ai_board[row][col] = 3
            self.player_turn = False
            return True
    
    def ai_shoot(self):
        if self.game_over or self.player_turn:
            return False

        if self.last_ai_hit and not self.check_if_ship_sunk(self.last_ai_hit):
            if self.ai_hit_direction:
                # Пробуем продолжить в текущем направлении
                result = self.ai_continue_direction_shot()
                if not result:
                    
                    self.ai_hit_direction = (-self.ai_hit_direction[0], -self.ai_hit_direction[1])
                    result = self.ai_continue_direction_shot()
                    if not result:
                        
                        self.ai_hit_direction = None
                        return self.ai_find_ship_direction()
                return result
            else:
                return self.ai_find_ship_direction()
        
        
        return self.ai_random_shot()

    def ai_continue_direction_shot(self):
        row, col = self.last_ai_hit
        dr, dc = self.ai_hit_direction
        
        
        r, c = row + dr, col + dc
        if 0 <= r < self.size and 0 <= c < self.size:
            if self.player_board[r][c] not in (2, 3, 4):
                return self.process_ai_shot(r, c)
        
        return False

    def ai_find_ship_direction(self):
        row, col = self.last_ai_hit
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]
        random.shuffle(directions) 
        
        for dr, dc in directions:
            r, c = row + dr, col + dc
            if 0 <= r < self.size and 0 <= c < self.size:
                if self.player_board[r][c] not in (2, 3, 4):
                    self.ai_hit_direction = (dr, dc)
                    return self.process_ai_shot(r, c)
        
        
        self.ai_hit_direction = None
        self.last_ai_hit = None
        return self.ai_random_shot()

    def ai_random_shot(self):
        available_cells = []
        for r in range(self.size):
            for c in range(self.size):
                if self.player_board[r][c] not in (2, 3, 4):
                    available_cells.append((r, c))
        
        if not available_cells:
            return False
        
        r, c = random.choice(available_cells)
        return self.process_ai_shot(r, c)

    def process_ai_shot(self, row, col):
        if self.player_board[row][col] == 1:
            self.player_board[row][col] = 2
            self.last_ai_hit = (row, col)
            self.ai_hits.append((row, col))
            
            for ship in self.player_ships:
                if (row, col) in ship.coordinates:
                    ship.hits += 1
                    if ship.is_sunk():
                        self.mark_surrounding(ship, self.player_board)
                        self.last_ai_hit = None
                        self.ai_hit_direction = None
                    break
            
            return True
        else:
            self.player_board[row][col] = 3
            return False
    
    def check_if_ship_sunk(self, coord):
        for ship in self.player_ships:
            if coord in ship.coordinates:
                return ship.is_sunk()
        return True
    
    def mark_surrounding(self, ship, board):
        for (row, col) in ship.coordinates:
            board[row][col] = 4
            for i in range(-1, 2):
                for j in range(-1, 2):
                    r, c = row + i, col + j
                    if 0 <= r < self.size and 0 <= c < self.size and board[r][c] == 0:
                        board[r][c] = 3
    
    def check_game_over(self):
        player_lost = all(ship.is_sunk() for ship in self.player_ships)
        ai_lost = all(ship.is_sunk() for ship in self.ai_ships)
        
        if player_lost or ai_lost:
            self.game_over = True
class CellClickListener(dynamic_proxy(OnClickListenerInterface)):
    def __init__(self, plugin, chat_id, r, c, is_player_board):
        super().__init__()
        self.plugin = plugin
        self.chat_id = chat_id
        self.r = r
        self.c = c
        self.is_player_board = is_player_board
    
    def onClick(self, view):
        if not self.is_player_board:
            self.plugin.on_cell_click(self.chat_id, self.r, self.c)

class NavalBattlePlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.active_games = {}
        self.player_cell_views = {}
        self.ai_cell_views = {}
    
    def on_plugin_load(self):
        self.add_menu_item(MenuItemData(
            menu_type=MenuItemType.CHAT_ACTION_MENU,
            text="Морской Бой",
            icon="msg_mini_ship",
            on_click=self.show_legend_dialog
        ))
    
    def show_legend_dialog(self, context: dict):
        chat_id = context.get("dialog_id")
        fragment = get_last_fragment()
        activity = fragment and fragment.getParentActivity()
        if not activity:
            return
        
        builder = AlertDialogBuilder(activity)
        builder.set_title("Морской Бой - Обозначения")
        
        container = LinearLayout(activity)
        container.setOrientation(LinearLayout.VERTICAL)
        padding = AndroidUtilities.dp(24)
        container.setPadding(padding, AndroidUtilities.dp(8), padding, AndroidUtilities.dp(8))
        
        legend_text = (
            "Обозначения в игре:\n\n"
            "⛵ - 1-палубный корабль\n"
            "⛴️ - 2-палубный корабль\n"
            "🚢 - 3-палубный корабль\n"
            "🛳️ - 4-палубный корабль\n"
            "💥 - Попадание по кораблю\n"
            "📛 - Убитый корабль\n"
            "🌊 - Промах\n\n"
            "На поле противника:\n"
            "Пустая клетка - еще не стреляли\n"
            "💥 - Ваше попадание\n"
            "📛 - Убитый корабль противника\n"
            "🌊 - Ваш промах"
        )
        legend_view = TextView(activity)
        legend_view.setText(legend_text)
        legend_view.setTextSize(16)
        legend_view.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        legend_view.setPadding(0, 0, 0, AndroidUtilities.dp(16))
        container.addView(legend_view)
        
        builder.set_view(container)
        
        def on_next(bld, which):
            bld.dismiss()
            self.show_rules_dialog(context)
        
        builder.set_positive_button("Далее", on_next)
        builder.set_negative_button("Закрыть", lambda b, w: b.dismiss())
        builder.show()
    
    def show_rules_dialog(self, context: dict):
        chat_id = context.get("dialog_id")
        fragment = get_last_fragment()
        activity = fragment and fragment.getParentActivity()
        if not activity:
            return
        
        builder = AlertDialogBuilder(activity)
        builder.set_title("Морской Бой - Правила")
        
        container = LinearLayout(activity)
        container.setOrientation(LinearLayout.VERTICAL)
        padding = AndroidUtilities.dp(24)
        container.setPadding(padding, AndroidUtilities.dp(8), padding, AndroidUtilities.dp(8))
        
        rules_text = (
            "Классическая игра 'Морской Бой'.\n\n"
            "Правила:\n"
            "1. У каждого игрока есть флот кораблей\n"
            "2. Корабли не могут соприкасаться\n"
            "3. Игроки по очереди стреляют по клеткам\n"
            "4. При попадании ход повторяется\n"
            "5. Цель - первым потопить все корабли противника"
        )
        rules_view = TextView(activity)
        rules_view.setText(rules_text)
        rules_view.setTextSize(16)
        rules_view.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        rules_view.setPadding(0, 0, 0, AndroidUtilities.dp(16))
        container.addView(rules_view)
        
        builder.set_view(container)
        
        def on_start(bld, which):
            bld.dismiss()
            run_on_ui_thread(lambda: self.start_new_game(chat_id), 50)
        
        builder.set_positive_button("Начать игру", on_start)
        builder.set_negative_button("Закрыть", lambda b, w: b.dismiss())
        builder.show()
    
    def start_new_game(self, chat_id: int):
        self.cleanup_game(chat_id)
        game = NavalBattleGame()
        self.active_games[chat_id] = game
        self.player_cell_views[chat_id] = {}
        self.ai_cell_views[chat_id] = {}
        self.show_board_dialog(chat_id, game)
    
    def show_board_dialog(self, chat_id: int, game: NavalBattleGame):
        fragment = get_last_fragment()
        activity = fragment and fragment.getParentActivity()
        if not activity:
            return
        
        builder = AlertDialogBuilder(activity)
        
        main_container = LinearLayout(activity)
        main_container.setOrientation(LinearLayout.VERTICAL)
        
        # Заголовок
        title_container = LinearLayout(activity)
        title_container.setOrientation(LinearLayout.HORIZONTAL)
        padding_h = AndroidUtilities.dp(24)
        padding_v = AndroidUtilities.dp(12)
        title_container.setPadding(padding_h, padding_v, padding_h, padding_v)
        
        title_main = TextView(activity)
        title_main.setText("Морской Бой")
        title_main.setTextSize(20)
        title_main.setTypeface(AndroidUtilities.getTypeface("fonts/rmedium.ttf"))
        title_main.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        
        title_turn = TextView(activity)
        title_turn.setTextSize(16)
        title_turn.setTextColor(Theme.getColor(Theme.key_dialogTextGray))
        game.title_view = title_turn
        self.update_dialog_title(game)
        
        params_main = LinearLayout.LayoutParams(-2, -2)
        params_main.weight = 1.0
        title_container.addView(title_main, params_main)
        title_container.addView(title_turn)
        main_container.addView(title_container)
        
        # Доска игрока
        player_label = TextView(activity)
        player_label.setText("Ваши корабли:")
        player_label.setTextSize(16)
        player_label.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        player_label.setPadding(
            AndroidUtilities.dp(24),
            AndroidUtilities.dp(8),
            AndroidUtilities.dp(24),
            AndroidUtilities.dp(8)
        )
        main_container.addView(player_label)
        
        player_grid_wrapper = FrameLayout(activity)
        player_grid = self.create_grid(activity, game, True, chat_id)
        wrapper_params = FrameLayout.LayoutParams(-2, -2)
        wrapper_params.gravity = Gravity.CENTER_HORIZONTAL
        player_grid.setLayoutParams(wrapper_params)
        player_grid_wrapper.addView(player_grid)
        main_container.addView(player_grid_wrapper)
        
        # Доска противника
        ai_label = TextView(activity)
        ai_label.setText("Стреляйте сюда:")
        ai_label.setTextSize(16)
        ai_label.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        ai_label.setPadding(
            AndroidUtilities.dp(24),
            AndroidUtilities.dp(16),
            AndroidUtilities.dp(24),
            AndroidUtilities.dp(8)
        )
        main_container.addView(ai_label)
        
        ai_grid_wrapper = FrameLayout(activity)
        ai_grid = self.create_grid(activity, game, False, chat_id)
        ai_grid.setLayoutParams(wrapper_params)
        ai_grid_wrapper.addView(ai_grid)
        main_container.addView(ai_grid_wrapper)
        
        builder.set_view(main_container)
        
        def on_close(bld, w):
            self.cleanup_game(chat_id)
        
        builder.set_positive_button("Закрыть", on_close)
        game.dialog = builder.show()
    
    def create_grid(self, activity, game, is_player_board, chat_id):
        grid = GridLayout(activity)
        grid.setColumnCount(game.size)
        grid.setRowCount(game.size)
        grid.setAlignmentMode(GridLayout.ALIGN_BOUNDS)
        
        size = AndroidUtilities.dp(30)
        margin = AndroidUtilities.dp(1)
        
        board = game.player_board if is_player_board else game.ai_board
        cell_views = self.player_cell_views[chat_id] if is_player_board else self.ai_cell_views[chat_id]
        
        for r in range(game.size):
            for c in range(game.size):
                cell_view = TextView(activity)
                params = GridLayout.LayoutParams(GridLayout.spec(r), GridLayout.spec(c))
                params.width = params.height = size
                params.setMargins(margin, margin, margin, margin)
                cell_view.setLayoutParams(params)
                cell_view.setTextSize(14)
                cell_view.setGravity(Gravity.CENTER)
                
                text, color_key, background_key = self.get_cell_style(board[r][c], is_player_board, r, c, chat_id)
                cell_view.setText(text)
                cell_view.setTextColor(Theme.getColor(color_key))
                cell_view.setBackgroundColor(Theme.getColor(background_key))
                
                if not is_player_board:
                    cell_view.setOnClickListener(CellClickListener(self, chat_id, r, c, is_player_board))
                
                grid.addView(cell_view)
                cell_views[(r, c)] = cell_view
        
        return grid
    
    def on_cell_click(self, chat_id: int, r: int, c: int):
        game = self.active_games.get(chat_id)
        if not game or game.game_over or not game.player_turn:
            return
        
        if game.player_shoot(r, c):
            self.redraw_boards(chat_id)
            self.update_dialog_title(game)
            game.check_game_over()
            
            if game.game_over:
                self.show_game_over_dialog(chat_id, all(ship.is_sunk() for ship in game.ai_ships))
            elif game.player_turn:
                pass
            else:
                run_on_ui_thread(lambda: self.ai_turn(chat_id), 1000)
    
    def ai_turn(self, chat_id: int):
        game = self.active_games.get(chat_id)
        if not game or game.player_turn or game.game_over:
            return
        
        
        hit_made = game.ai_shoot()
        self.redraw_boards(chat_id)
        self.update_dialog_title(game)
        game.check_game_over()
        
        if game.game_over:
            self.show_game_over_dialog(chat_id, all(ship.is_sunk() for ship in game.ai_ships))
        elif hit_made:
            
            run_on_ui_thread(lambda: self.ai_turn(chat_id), 500)
        else:
            game.player_turn = True
            self.update_dialog_title(game)
    
    def redraw_boards(self, chat_id: int):
        game = self.active_games.get(chat_id)
        if not game:
            return
        
        player_views = self.player_cell_views.get(chat_id)
        if player_views:
            for (r, c), view in player_views.items():
                text, color_key, background_key = self.get_cell_style(game.player_board[r][c], True, r, c, chat_id)
                view.setText(text)
                view.setTextColor(Theme.getColor(color_key))
                view.setBackgroundColor(Theme.getColor(background_key))
        
        ai_views = self.ai_cell_views.get(chat_id)
        if ai_views:
            for (r, c), view in ai_views.items():
                text, color_key, background_key = self.get_cell_style(game.ai_board[r][c], False, r, c, chat_id)
                view.setText(text)
                view.setTextColor(Theme.getColor(color_key))
                view.setBackgroundColor(Theme.getColor(background_key))
    
    def update_dialog_title(self, game: NavalBattleGame):
        if game and game.title_view:
            status = "Ваш ход" if game.player_turn else "Ход противника"
            game.title_view.setText(status)
    
    def get_cell_style(self, cell_state: int, is_player_board: bool, r: int, c: int, chat_id: str) -> (str, str, str):
        if cell_state == 0:  # Пустая клетка
            return "", Theme.key_dialogTextBlack, Theme.key_chat_attachPhotoBackground
        elif cell_state == 1:  # Корабль
            if is_player_board:
                game = self.active_games.get(chat_id)
                if game:
                    for ship in game.player_ships:
                        if (r, c) in ship.coordinates:
                            if ship.size == 1: return "⛵", Theme.key_chat_attachAudioText, Theme.key_chat_attachPhotoBackground
                            elif ship.size == 2: return "⛴️", Theme.key_chat_attachAudioText, Theme.key_chat_attachPhotoBackground
                            elif ship.size == 3: return "🚢", Theme.key_chat_attachAudioText, Theme.key_chat_attachPhotoBackground
                            else: return "🛳️", Theme.key_chat_attachAudioText, Theme.key_chat_attachPhotoBackground
            else:
                return "", Theme.key_dialogTextBlack, Theme.key_chat_attachPhotoBackground
        elif cell_state == 2:  # Попадание
            return "💥", Theme.key_text_RedBold, Theme.key_windowBackgroundGray
        elif cell_state == 3:  # Промах
            return "🌊", Theme.key_dialogTextGray, Theme.key_windowBackgroundGray
        elif cell_state == 4:  # Убитый корабль
            return "📛", Theme.key_text_RedBold, Theme.key_windowBackgroundGray
        else:
            return "", Theme.key_dialogTextBlack, Theme.key_chat_attachPhotoBackground
    
    def show_game_over_dialog(self, chat_id: int, won: bool):
        fragment = get_last_fragment()
        activity = fragment and fragment.getParentActivity()
        if not activity:
            return
        
        builder = AlertDialogBuilder(activity)
        builder.set_title("Победа!" if won else "Поражение!")
        builder.set_message("Вы уничтожили все корабли противника!" if won else "Все ваши корабли потоплены...")
        
        def start_new_action(bld, w):
            bld.dismiss()
            self.cleanup_game(chat_id)
            self.show_rules_dialog({"dialog_id": chat_id})
        
        def close_all_action(bld, w):
            bld.dismiss()
            self.cleanup_game(chat_id)
        
        builder.set_positive_button("Новая игра", start_new_action)
        builder.set_negative_button("Закрыть", close_all_action)
        builder.set_cancelable(False)
        builder.show()
    
    def cleanup_game(self, chat_id: int):
        game = self.active_games.pop(chat_id, None)
        if game and game.dialog:
            try:
                game.dialog.dismiss()
            except:
                pass
        self.player_cell_views.pop(chat_id, None)
        self.ai_cell_views.pop(chat_id, None)

__id__ = "Sea Battle 🚢"
__name__ = "Морской Бой"
__description__ = "Классическая игра Морской Бой"
__author__ = "Creator @IRimuru_TempestI"
__version__ = "2.4"
__min_version__ = "11.12.0"
__icon__ = "нету"