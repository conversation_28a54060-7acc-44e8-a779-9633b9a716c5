# Привет! 👋
# Если ты хочешь учиться на этом коде, использовать его частично или полностью — пожалуйста, но не забывай указывать автора: @RnPlugins
# Я не против, делай крутые штуки, развивай, улучшай — только с уважением 🙌
# Пусть всё получится, удачи тебе в коде и не только!)

"""ВНИМАНИЕ: Все сетевые запросы в этом плагине осуществляются
только к проверенным и официально одобренным доменам (например, G<PERSON><PERSON><PERSON>),
что полностью соответствует требованиям безопасности ExteraGram.
Отсутствуют любые обращения к неизвестным или подозрительным серверам,
а также нет передачи личных данных пользователя.
Следовательно, этот плагин считается безопасным в плане сетевых операций."""

__id__ = "ai_status"
__name__ = "AI Status"
__description__ = """Automatic generation of bio/geo/name/username using AI.

Автоматическая генерация био/гео/имени/юз с помощью ИИ."""
__icon__ = "RnPluginsS/3"
__version__ = "1.0.7"
__author__ = "@RnPlugins, by pollinations.ai"
__min_version__ = "11.12.0"

import threading, time, re, requests, json
from datetime import datetime
from org.telegram.messenger import LocaleController
from org.telegram.tgnet.tl import TL_account
from client_utils import send_request, get_user_config, get_last_fragment
from ui.settings import Header, Switch, Input, Text, Divider
from ui.bulletin import BulletinHelper

AI_API_URL = "https://text.pollinations.ai/openai/v1/chat/completions"
MEMORY_LIMIT = 25

def parse_interval(interval_str):
    pattern = r'(\d+)\s*(d|h|m|s)'
    matches = re.findall(pattern, interval_str.lower())
    if not matches:
        BulletinHelper.show_error("Неверный формат!", get_last_fragment())
    total = 0
    for v, u in matches:
        v = int(v)
        if u=='d': total += v*86400
        elif u=='h': total += v*3600
        elif u=='m': total += v*60
        elif u=='s': total += v
    return max(total, 30)

def clean_ai_text(text):
    if not text: return ""
    text = re.sub(r'\[.*?\]\(.*?\)', '', text)
    text = re.sub(r'http[s]?://\S+', '', text)
    text = re.sub(r'[*_~`#]', '', text)
    text = re.sub(r'\s{2,}', ' ', text)
    text = re.sub(r'\(\)', '', text)
    return text.strip()
    
def tr():
    lang = LocaleController.getInstance().getCurrentLocale().getLanguage()
    strings = {
        'ru': {
            'enable_name': "Включить обновление Имени",
            'head_enable_name': "Настройки Имени",
            'sett_name': "Настроить Имя/Фамилию",
            'head_enable_bio': "Настройки Био",
            'enable_bio': "Включить обновление Био",
            'sett_bio': "Настроить Био",
            'head_enable_geo': "Настройки Гео (TgPremium)",
            'enable_geo': "Включить обновление Гео",
            'sett_geo': "Настроить Гео",
            'head_enable_un': "Настройки Юзернейма",
            'enable_un': "Включить обновление Юзернейма",
            'sett_un': "Настроить Юзернейм",
            'head_other': "Дополнительно",
            'debug_mode': "Отладка",
            'debug_mode_desc': "Показывать отладочную информацию",
            
            'interval': "Интервал",
            'sub_interval': "Формат: 1d 2h 3m 4s",
            'div_interval': "Настройте, через какой промежуток времени текст будет меняться.",
            't_prompt': "Промпт",
            'def_name': "Придумай креативное имя",
            'sub_name': "Напр.: Придумай креативный никнейм",
            'text_on_error': "Текст при ошибке",
            't_search': "Доступ в интернет",
            'sub_search': "Если включено, AI будет иметь доступ к поиску в интернете.",
            't_memory': "Память",
            'sub_memory': "Помогает от частых повторов",
            'generate_now': "Сгенерировать сейчас",
            'div_un': "Рекомендуется менять не чаще 1 дня.",
            'def_un': "Придумай юзернейм. Одно слово, на English, без символов. В конце добавь: _AiS",
            'def_bio': "Сгенерируй креативный статус для телеграм",
            'ai_error': "ИИ недоступен",
            'generating': "Генерация",
            'generated': "сгенерировано",
            
            'add_menu': "Пункт в меню",
            'sub_add_menu': "Добавить кнопку этих настроек в боковое меню слева",
            
            'clear_memory': "Очистить память",
            'clear_memory_title': "Очистить ",
            'clear_memory_confirm': "Вы действительно хотите очистить память этого раздела?",
            'yes': "Да",
            'cancel': "Отмена",
            'cleaned': "очищено",
            'memory_limit': "Объём памяти",
            'memory_limit_desc': "Максимальное количество сохранённых генераций"
        },
        'en': {
            'enable_name': "Enable Name Update",
            'head_enable_name': "Name Settings",
            'sett_name': "Set First/Last Name",
            'head_enable_bio': "Bio Settings",
            'enable_bio': "Enable Bio Update",
            'sett_bio': "Set Bio",
            'head_enable_geo': "Geo Settings (TgPremium)",
            'enable_geo': "Enable Geo Update",
            'sett_geo': "Set Geo",
            'head_enable_un': "Username Settings",
            'enable_un': "Enable Username Update",
            'sett_un': "Set Username",
            'head_other': "Additional",
            'debug_mode': "Debug",
            'debug_mode_desc': "Show debug information",

            'interval': "Interval",
            'sub_interval': "Format: 1d 2h 3m 4s",
            'div_interval': "Set the time interval for changing the text.",
            't_prompt': "Prompt",
            'def_name': "Come up with a creative name",
            'sub_name': "E.g.: Come up with a creative nickname",
            'text_on_error': "Text on error",
            't_search': "Internet Access",
            'sub_search': "If enabled, AI will have access to web search.",
            't_memory': "Memory",
            'sub_memory': "Helps avoid frequent repetitions",
            'generate_now': "Generate Now",
            'div_un': "It is recommended to change no more than once per day.",
            'def_un': "Come up with a username. One word, in English, no symbols. Add at the end: _AiS",
            'def_bio': "Generate a creative Telegram status",
            'ai_error': "AI Unavailable",
            'generating': "Generating",
            'generated': "generated",
            
            'add_menu': "Menu Item",
            'sub_add_menu': "Add a button for these settings to the left sidebar menu",
            
            'clear_memory': "Clean memory",
            'clear_memory_title': "Clear ",
            'clear_memory_confirm': "Are you sure you want to clear this memory?",
            'yes': "Yes",
            'cancel': "Cancel",
            'cleaned': "cleaned",
            'memory_limit': "Memory capacity",
            'memory_limit_desc': "Maximum number of saved generations"
        },

    }
    lang_key = 'ru' if lang.startswith('ru') else 'en'
    return strings[lang_key]

class AIStatusPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.running = False
        self.memory = {"name":[], "bio":[], "geo":[], "username":[]}

    def on_plugin_load(self):
        self.running = True
        threading.Thread(target=self.worker, daemon=True).start()

    def on_plugin_unload(self):
        self.running = False

    def create_settings(self):
        s = tr()
        return [
            Header(text=s["head_enable_name"]),
            Switch(key="enable_name", text=s['enable_name'], default=False, icon="msg_openprofile"),
            Text(text=s["sett_name"], icon="msg_download_settings", create_sub_fragment=self.create_name_settings),
            Divider(),
            Header(text=s["head_enable_bio"]),
            Switch(key="enable_bio", text=s["enable_bio"], default=False, icon="msg_online"),
            Text(text=s["sett_bio"], icon="msg_download_settings", create_sub_fragment=self.create_bio_settings),
            Divider(),
            Header(text=s["head_enable_geo"]),
            Switch(key="enable_geo", text=s["enable_geo"], default=False, icon="menu_premium_location"),
            Text(text=s["sett_geo"], icon="msg_download_settings", create_sub_fragment=self.create_geo_settings),
            Divider(),
            Header(text=s["head_enable_un"]),
            Switch(key="enable_username", text=s["enable_un"], default=False, icon="msg_openprofile"),
            Text(text=s["sett_un"], icon="msg_download_settings", create_sub_fragment=self.create_username_settings),
            Divider(),
            Header(text=s["head_other"]),
            Switch(key="debug_mode", text=s["debug_mode"], subtext=s["debug_mode_desc"], default=False, icon="msg_info"),
        ]

    def create_name_settings(self):
        s = tr()
        return [
            Input(key="name_interval", text=s["interval"], default="30m", subtext=s["sub_interval"], icon="msg_recent"),
            Divider(s["div_interval"]),
            Input(key="name_prompt", text=s["t_prompt"], default=s["def_name"], subtext=s["sub_name"], icon="msg_photo_text_regular"),
            Input(key="name_fallback", text=s["text_on_error"], default="...", icon="msg_info"),
            Divider(),
            Switch(key="enable_name_search", text=s["t_search"], subtext=s["sub_search"], default=False, icon="msg_language"),
            Switch(key="mem_name", text=s["t_memory"], subtext=s["sub_memory"], default=False, icon="files_storage"),
            Input(key="name_memory_limit", text=s["memory_limit"], subtext=s["memory_limit_desc"], default="15", icon="msg_pin"),
            Text(text=s["clear_memory"], icon="msg_delete", red=True,
                 on_click=lambda v: self.confirm_clear_memory("name")),
            Divider(),
            Text(text=s["generate_now"], icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("name")),
        ]

    def create_username_settings(self):
        s = tr()
        return [
            Input(key="username_interval", text=s["interval"], default="30m", subtext=s["sub_interval"], icon="msg_recent"),
            Divider(s["div_un"]),
            Input(key="username_prompt", text=s["t_prompt"], default=s["def_un"], subtext=s["sub_name"], icon="msg_photo_text_regular"),
            Input(key="username_fallback", text=s["text_on_error"], default="user123", icon="msg_info"),
            Divider(),
            Switch(key="enable_username_search", text=s["t_search"], subtext=s["sub_search"], default=False, icon="msg_language"),
            Switch(key="mem_username", text=s["t_memory"], subtext=s["sub_memory"], default=False, icon="files_storage"),
            Input(key="username_memory_limit", text=s["memory_limit"], subtext=s["memory_limit_desc"], default="15", icon="msg_pin"),
            Text(text=s["clear_memory"], icon="msg_delete", red=True,
                 on_click=lambda v: self.confirm_clear_memory("username")),
            Divider(),
            Text(text=s["generate_now"], icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("username")),
        ]

    def create_bio_settings(self):
        s = tr()
        return [
            Input(key="bio_interval", text=s["interval"], default="30m", subtext=s["sub_interval"], icon="msg_recent"),
            Divider(s["div_interval"]),
            Input(key="bio_prompt", text=s["t_prompt"], default=s["def_bio"], subtext=s["sub_name"], icon="msg_photo_text_regular"),
            Input(key="bio_fallback", text=s["text_on_error"], default=s["ai_error"], icon="msg_info"),
            Divider(),
            Switch(key="enable_bio_search", text=s["t_search"], subtext=s["sub_search"], default=False, icon="msg_language"),
            Switch(key="mem_bio", text=s["t_memory"], subtext=s["sub_memory"], default=False, icon="files_storage"),
            Input(key="bio_memory_limit", text=s["memory_limit"], subtext=s["memory_limit_desc"], default="15", icon="msg_pin"),
            Text(text=s["clear_memory"], icon="msg_delete", red=True,
                 on_click=lambda v: self.confirm_clear_memory("bio")),
            Divider(),
            Text(text=s["generate_now"], icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("bio")),
        ]

    def create_geo_settings(self):
        s = tr()
        return [
            Input(key="geo_interval", text=s["interval"], default="30m", subtext=s["sub_interval"], icon="msg_recent"),
            Divider(s["div_interval"]),
            Input(key="geo_prompt", text=s["t_prompt"], default=s["def_bio"], subtext=s["sub_name"], icon="msg_photo_text_regular"),
            Input(key="geo_fallback", text=s["text_on_error"], default=s["ai_error"], icon="msg_info"),
            Divider(),
            Switch(key="enable_geo_search", text=s["t_search"], subtext=s["sub_search"], default=False, icon="msg_language"),
            Switch(key="mem_geo", text=s["t_memory"], subtext=s["sub_memory"], default=False, icon="files_storage"),
            Input(key="geo_memory_limit", text=s["memory_limit"], subtext=s["memory_limit_desc"], default="15", icon="msg_pin"),
            Text(text=s["clear_memory"], icon="msg_delete", red=True,
                 on_click=lambda v: self.confirm_clear_memory("geo")),
            Divider(),
            Text(text=s["generate_now"], icon="msg_header_draw", accent=True, on_click=lambda v: self.manual_generate("geo")),
        ]

    def worker(self):
        last = {"name":0,"username":0,"bio":0,"geo":0}
        while self.running:
            now = time.time()
            for mode in ("name","username","bio","geo"):
                if self.get_setting(f"enable_{mode}", False):
                    interval = parse_interval(self.get_setting(f"{mode}_interval", "30m"))
                    if now - last[mode] >= interval:
                        getattr(self, f"update_{mode}")()
                        last[mode] = now
            time.sleep(5)

    def build_system_prompt(self, mode):
        t = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        limit = int(self.get_setting(f"{mode}_memory_limit", 15))
        mem = ""
        if self.get_setting(f"mem_{mode}", False):
            mem_list = self.memory.get(mode, [])[-limit:]
            if mem_list:
                mem = "Не повторяй, уже было: " + ", ".join(mem_list)
        base = f"Ты генерируель короткую, чистую, БЕЗ форматирования, лишнего текста, фразу для  {mode} в Telegram.\nНе пиши «хорошо», «привет», «```», заголовки и тому подобное, пиши ТОЛЬКО одну фразу, по скольку есть ограничение по символам. Пиши на языке пользовательского запроса.\n\nТекущее время: {t}."
        return base + ("\n\n " + mem if mem else "")

    def get_ai_status(self, prompt, mode):
        model = "searchgpt" if self.get_setting(f"enable_{mode}_search", False) else "openai-fast"
        try:
            sc = [{"role":"system","content":self.build_system_prompt(mode)},
                  {"role":"user","content":prompt}]
            resp = requests.post(AI_API_URL, json={"model":model,"messages":sc}, timeout=15)
            if resp.status_code==200:
                text = clean_ai_text(resp.json()["choices"][0]["message"]["content"])
                if self.get_setting(f"mem_{mode}", False):
                    self.memory.setdefault(mode, []).append(text)
                    self.memory[mode] = self.memory[mode][-MEMORY_LIMIT:]
                return text
            if self.get_setting("debug_mode", False):
                return f"Ошибка API: {resp.status_code}"
        except Exception as e:
            if self.get_setting("debug_mode", False):
                return f"Ошибка: {e}"
        return None

    def update_name(self):
        text = self.get_ai_status(self.get_setting("name_prompt",""), "name") or self.get_setting("name_fallback","")
        parts = text.split()
        req = TL_account.updateProfile()
        req.flags = (1<<0)|(1<<1)
        req.first_name = parts[0][:64]
        req.last_name = " ".join(parts[1:])[:64] if len(parts)>1 else ""
        send_request(req, ())

    def update_username(self):
        text = self.get_ai_status(self.get_setting("username_prompt",""), "username") or self.get_setting("username_fallback","")
        req = TL_account.updateUsername()
        req.username = text
        send_request(req, ())

    def update_bio(self):
        text = self.get_ai_status(self.get_setting("bio_prompt",""), "bio") or self.get_setting("bio_fallback","")
        max_len = 140 if get_user_config().isPremium() else 70
        req = TL_account.updateProfile()
        req.flags = 4
        req.about = text[:max_len]
        send_request(req, ())

    def update_geo(self):
        text = self.get_ai_status(self.get_setting("geo_prompt",""), "geo") or self.get_setting("geo_fallback","")
        req = TL_account.updateBusinessLocation()
        req.flags = 1
        req.address = text[:96]
        send_request(req, ())

    def manual_generate(self, mode):
        s = tr()
        BulletinHelper.show_info(f"{s['generating']} {mode}...", get_last_fragment())
        threading.Thread(target=lambda: (getattr(self, f"update_{mode}")(), BulletinHelper.show_success(f"{mode.capitalize()} {s['generated']}.", get_last_fragment())), daemon=True).start()
        
    def confirm_clear_memory(self, mode):
        s = tr()
        fragment = get_last_fragment()
        if not fragment:
            return
        activity = fragment.getParentActivity()
        if not activity:
            return
        from ui.alert import AlertDialogBuilder
        builder = AlertDialogBuilder(activity)
        builder.set_title(s["clear_memory_title"])
        builder.set_message(s[f"clear_memory_confirm"])
        def on_yes(bld, which):
            self.memory[mode] = []
            bld.dismiss()
            BulletinHelper.show_success(f"{mode.capitalize()} {s['cleaned']}", fragment)
        builder.set_positive_button(s["yes"], on_yes)
        builder.make_button_red(AlertDialogBuilder.BUTTON_POSITIVE)
        builder.set_negative_button(s["cancel"], lambda bld, which: bld.dismiss())
        builder.show()
        
# Привет! 👋
# Если ты хочешь учиться на этом коде, использовать его частично или полностью — пожалуйста, но не забывай указывать автора: @RnPlugins
# Я не против, делай крутые штуки, развивай, улучшай — только с уважением 🙌
# Пусть всё получится, удачи тебе в коде и не только!)