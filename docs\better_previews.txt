import traceback

from android.net import Uri
from org.telegram.messenger import AndroidUtilities

from android_utils import log
from hook_utils import find_class

import java
from java.lang import CharSequence, Iterable, Thread, String as JString
from java.util import ArrayList
from android.text import TextUtils

__name__ = "Better Previews"
__description__ = "Modifies specific URLs (Twitter, TikTok, Reddit, Instagram, Pixiv) for better previews"
__version__ = "1.0.0"
__id__ = "better_previews"
__author__ = "@immat0x1"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.9.0"

def _apply_url_fix(url: str) -> str:
    try:
        processed_url = url
        if not url.startswith("http://") and not url.startswith("https://"):
            processed_url = "https://" + url

        uri = Uri.parse(processed_url)
        if uri is None:
            return url

        host = AndroidUtilities.getHostAuthority(str(uri).lower())
        if host is None:
            return url

        target_authority = None
        if host == "twitter.com" or host == "x.com":
            target_authority = "vxtwitter.com"
        elif host == "tiktok.com" or host.endswith(".tiktok.com"):
            target_authority = host.replace("tiktok.com", "vxtiktok.com")
        elif host == "reddit.com" or host == "www.reddit.com":
            target_authority = "vxreddit.com"
        elif host == "instagram.com" or host == "www.instagram.com":
            target_authority = "ddinstagram.com"
        elif host == "pixiv.net" or host == "www.pixiv.net":
            target_authority = "phixiv.net"
        else:
            return url

        modified_uri = uri.buildUpon().authority(target_authority).build()
        return modified_uri

    except Exception as e:
        return url

class ModifyUrlsHook:
    def before_hooked_method(self, param):
        try:
            delimiter = param.args[0]
            tokens = param.args[1]

            if delimiter != " ":
                return

            current_thread = Thread.currentThread()
            stack_trace = current_thread.getStackTrace()

            called_from_intended_context = False
            for element in stack_trace[2:]:
                if element.getClassName().startswith("org.telegram.messenger.DispatchQueue") and element.getMethodName() == "run":
                    called_from_intended_context = True
                    break

            if not called_from_intended_context:
                return

            modified_urls = ArrayList()
            modified_count = 0
            original_tokens = tokens.toArray()

            for url_sequence in original_tokens:
                if not isinstance(url_sequence, CharSequence):
                    modified_urls.add(url_sequence)
                    continue

                original_url = str(url_sequence)

                modified_url = _apply_url_fix(original_url)

                if modified_url != original_url:
                    modified_count += 1

                modified_urls.add(modified_url)

            if modified_count > 0:
                param.args[1] = modified_urls

        except Exception as e:
            log(f"{e}\n{traceback.format_exc()}")


class UrlModifierPlugin(BasePlugin):
    def on_plugin_load(self):
        try:
            text_utils_class = find_class("android.text.TextUtils")
            if not text_utils_class:
                log(f"[{__name__}] CRITICAL: Could not find TextUtils class!")
                return

            charsequence_class = find_class("java.lang.CharSequence")
            iterable_class = find_class("java.lang.Iterable")

            if not charsequence_class or not iterable_class:
                 log(f"[{__name__}] CRITICAL: Could not find CharSequence or Iterable class!")
                 return

            textutils_join_method = text_utils_class.getClass().getDeclaredMethod(
                "join", charsequence_class.getClass(), iterable_class.getClass()
            )

            if not textutils_join_method:
                 log(f"[{__name__}] CRITICAL: Could not find TextUtils.join(CharSequence, Iterable) method!")
                 return

            self.hook_method(
                textutils_join_method,
                ModifyUrlsHook()
            )

            log(f"[{__name__}] Plugin loaded and TextUtils.join hook activated successfully.")
        except Exception as e:
            log(f"[{__name__}] Failed to activate hook during plugin load: {e}\n{traceback.format_exc()}")
