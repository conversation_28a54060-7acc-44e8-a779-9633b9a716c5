Полное руководство по разработке плагинов для exteraGram

Это руководство предназначено для разработчиков, желающих расширить функциональность клиента exteraGram с помощью плагинов на языке Python. Вы узнаете об архитектуре, настройке окружения, ключевых API и лучших практиках для создания мощных и стабильных плагинов.

Оглавление

Глава 1: Основы

1.1. Архитектура плагинов

1.2. Принцип работы

Глава 2: Настройка окружения

2.1. Установка exteraGram

2.2. Активация движка плагинов

2.3. Подготовка проекта

2.4. Подключение к устройству и отладка

Глава 3: Создание первого плагина: Weather

3.1. Базовая структура плагина

3.2. Начальная реализация (Блокирующая)

3.3. Улучшение производительности (Асинхронная версия)

Глава 4: Справочник по классу BasePlugin

4.1. Метаданные

4.2. Жизненный цикл плагина (on_plugin_load, on_plugin_unload)

4.3. События приложения (on_app_event)

4.4. Настройки плагина (create_settings)

4.4.1. Доступные элементы управления

4.4.2. Работа с настройками в коде

Глава 5: Пользовательский интерфейс

5.1. Элементы меню (add_menu_item)

5.2. Диалоговые окна (AlertDialogBuilder)

5.3. Всплывающие уведомления (BulletinHelper)

Глава 6: Хуки (Hooks)

6.1. Общая концепция

6.2. Хуки API-запросов (pre_request_hook, post_request_hook)

6.3. Хуки обновлений (on_update_hook, on_updates_hook)

6.4. Хук отправки сообщения (on_send_message_hook)

6.5. HookResult и стратегии

Глава 7: Xposed-хуки (Продвинутое взаимодействие)

7.1. Введение в Xposed

7.2. Ключевые концепции

7.3. Процесс хукинга: шаг за шагом

7.4. Практические примеры Xposed

Глава 8: Утилиты

8.1. Android-утилиты (android_utils)

8.2. Клиентские утилиты (client_utils)

8.3. Парсер Markdown (markdown_utils)

Приложение

A: Часто используемые классы Telegram

B: Исходный код (Справочно)

Глава 1: Основы
1.1. Архитектура плагинов

Система плагинов exteraGram — это мощная комбинация из нескольких ключевых технологий, позволяющая разработчикам изменять поведение приложения Telegram, используя привычный язык Python.

exteraGram: Модифицированный клиент Telegram для Android, который включает в себя движок для загрузки и выполнения плагинов.

Chaquopy (v15): Это "мост" между мирами Java (в котором написан Telegram) и Python. Chaquopy позволяет вам вызывать Java-методы из Python и наоборот, передавая данные между двумя языками. Вы можете писать логику плагина на Python, но при этом иметь доступ ко всему внутреннему API Telegram.

Aliucord Hook (Xposed): Aliucord — это фреймворк для моддинга Discord, но его система хуков была адаптирована для exteraGram. Она предоставляет функциональность, аналогичную знаменитому Xposed Framework. Это позволяет "перехватывать" вызовы методов Java прямо во время выполнения, изменять их аргументы, подменять результат или даже полностью заменять их реализацию вашим собственным Python-кодом.

1.2. Принцип работы

Загрузка: exteraGram при запуске сканирует папку с плагинами и загружает каждый .py файл.

Инициализация: Для каждого плагина создается экземпляр его основного класса (наследника BasePlugin). Вызывается метод on_plugin_load().

Регистрация хуков: В on_plugin_load() ваш плагин регистрирует хуки (например, на отправку сообщения или на конкретный API-запрос Telegram).

Событие: Когда в приложении происходит событие, на которое вы подписались (например, пользователь нажимает кнопку "Отправить"), движок плагинов exteraGram активирует ваш хук.

Выполнение: Выполняется ваш Python-код (например, метод on_send_message_hook). Вы можете проанализировать данные, изменить их или отменить действие.

Возврат управления: Ваш хук возвращает результат (HookResult), который сообщает приложению, как действовать дальше (отправить измененное сообщение, отменить отправку и т.д.).

Глава 2: Настройка окружения
2.1. Установка exteraGram

Убедитесь, что вы используете последнюю бета-версию exteraGram или его производного клиента. Это гарантирует совместимость с последними версиями API плагинов. Скачать ее можно из официального бета-канала в Telegram.

2.2. Активация движка плагинов

Войдите в свой аккаунт Telegram.

Перейдите в Настройки exteraGram > Плагины.

Активируйте переключатель "Движок плагинов".

Для доступа к функциям разработчика сделайте долгий тап по заголовку "Плагины" на этом же экране, пока не появится уведомление о включении режима разработчика.

2.3. Подготовка проекта

Создайте папку для вашего проекта на компьютере.

Создайте Python-файл, например, my_first_plugin.py.

Настройте виртуальное окружение. Это изолирует зависимости вашего проекта и является хорошей практикой.

Generated bash
python -m venv venv
source venv/bin/activate  # Для Windows: venv\Scripts\activate


Установите exteragram-utils. Этот пакет предоставляет необходимые тайпинги для автодополнения в вашем редакторе кода и утилиту extera для горячей перезагрузки плагина на телефоне.

Generated bash
pip install exteragram-utils
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
2.4. Подключение к устройству и отладка

Включите отладку по USB на вашем Android-устройстве.

Подключите телефон к компьютеру с помощью USB-кабеля.

Убедитесь, что adb (Android Debug Bridge) установлен и добавлен в PATH вашей системы. Вы можете проверить это, выполнив команду adb devices в терминале.

Горячая перезагрузка

Утилита extera позволяет мгновенно отправлять изменения из вашего кода на телефон без перезапуска приложения.

Generated bash
# Замените my_first_plugin.py на имя вашего файла
extera my_first_plugin.py
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END
Удаленная отладка в VS Code

Для полноценной отладки с точками останова, используйте флаг --debug и настройте ваш VS Code.

Запустите горячую перезагрузку с отладкой:

Generated bash
extera my_first_plugin.py --debug
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Bash
IGNORE_WHEN_COPYING_END

В VS Code создайте файл .vscode/launch.json в папке вашего проекта со следующим содержимым:

Generated json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Remote Attach exteraGram",
            "type": "debugpy",
            "request": "attach",
            "connect": {
                "host": "localhost",
                "port": 5678 // Стандартный порт для debugpy
            },
            "pathMappings": [
                {
                    // Путь к вашему файлу плагина на компьютере
                    "localRoot": "/Users/<USER>/Projects/extera-plugins/my_first_plugin.py",
                    // Путь к файлу плагина на устройстве. 
                    // com.exteragram.messenger - может отличаться для производных клиентов
                    // my_first_plugin - это __id__ вашего плагина
                    "remoteRoot": "/data/user/0/com.exteragram.messenger/files/plugins/my_first_plugin.py"
                }
            ]
        }
    ]
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Json
IGNORE_WHEN_COPYING_END

Важно:

Замените localRoot на абсолютный путь к вашему файлу плагина.

Замените remoteRoot на соответствующий путь на устройстве. Обратите внимание, что remoteRoot должен заканчиваться на PLUGIN_ID.py, где PLUGIN_ID — это значение переменной __id__ из вашего плагина.

Запустите отладку в VS Code, выбрав конфигурацию "Python Debugger: Remote Attach exteraGram". Теперь вы можете ставить точки останова в вашем Python-коде.

Глава 3: Создание первого плагина: Weather
3.1. Базовая структура плагина

Каждый файл плагина должен содержать:

Мета-переменные: Строковые константы, описывающие плагин.

Основной класс: Один класс, унаследованный от BasePlugin.

Вот минимальный шаблон:

Generated python
# --- Метаданные ---
__id__ = "weather"
__name__ = "Weather"
__description__ = "Показывает погоду по команде .wt [город]"
__author__ = "Your Name"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/1"  # Ссылка на стикерпак и индекс стикера
__min_version__ = "11.12.0" # Минимальная версия exteraGram

# --- Основной класс ---
# BasePlugin импортируется неявно, вам не нужно писать from base_plugin import BasePlugin
class WeatherPlugin(BasePlugin):
    # Этот метод вызывается при загрузке плагина
    def on_plugin_load(self):
        # Здесь мы будем регистрировать наши хуки
        pass

    # Этот метод вызывается при выгрузке плагина
    def on_plugin_unload(self):
        pass
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
3.2. Начальная реализация (Блокирующая)

Создадим плагин, который по команде .wt Москва заменяет текст сообщения на текущую погоду в Москве, используя API wttr.in. Эта версия будет простой, но с проблемами производительности.

Полный код (first_plugin.py):

Generated python
import requests
from android_utils import log
from base_plugin import BasePlugin, HookResult, HookStrategy
from typing import Any

# --- Метаданные ---
__id__ = "weather"
__name__ = "Weather"
__description__ = "Показывает погоду по команде .wt [город]"
__author__ = "exteraDev"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

# --- Константы API ---
API_BASE_URL = "https://wttr.in"
API_HEADERS = {"User-Agent": "Mozilla/5.0", "Accept": "application/json"}

# --- Вспомогательные функции ---
def fetch_weather_data(city: str):
    try:
        url = f"{API_BASE_URL}/{city}?format=j1"
        response = requests.get(url, headers=API_HEADERS, timeout=10)
        if response.status_code != 200:
            log(f"Ошибка получения погоды для '{city}' (статус: {response.status_code})")
            return None
        return response.json()
    except Exception as e:
        log(f"Ошибка API погоды: {str(e)}")
        return None

def format_weather_data(data: dict, query_city: str):
    try:
        area_info = data.get("nearest_area", [{}])[0]
        city = area_info.get("areaName", [{}])[0].get("value", query_city)
        region = area_info.get("region", [{}])[0].get("value", "")
        country = area_info.get("country", [{}])[0].get("value", "")

        location_parts = filter(None, [city, region, country])
        location_str = ", ".join(location_parts)

        current = data.get("current_condition", [{}])[0]
        temp = current.get("temp_C", "N/A")
        feels_like = current.get("FeelsLikeC", "N/A")
        condition = current.get("weatherDesc", [{}])[0].get("value", "Неизвестно")
        humidity = current.get("humidity", "N/A")
        wind_speed = current.get("windspeedKmph", "N/A")
        wind_dir = current.get("winddir16Point", "N/A")
        local_time = current.get("localObsDateTime", "N/A")

        return (
            f"<b>Погода в {location_str}:</b>\n\n"
            f"• Температура: {temp}°С (ощущается как: {feels_like}°С)\n"
            f"• Состояние: {condition}\n"
            f"• Влажность: {humidity}%\n"
            f"• Ветер: {wind_speed} км/ч ({wind_dir})\n\n"
            f"<i>Обновлено: {local_time} (местное время)</i>"
        )
    except Exception as e:
        log(f"Ошибка форматирования данных о погоде: {str(e)}")
        return f"Ошибка обработки данных: {str(e)}"

# --- Основной класс плагина ---
class WeatherPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        # Проверяем, что сообщение - строка и начинается с .wt
        if not isinstance(params.message, str) or not params.message.startswith(".wt"):
            return HookResult() # Ничего не делаем

        try:
            parts = params.message.strip().split(" ", 1)
            city = parts[1].strip() if len(parts) > 1 else "Moscow"

            if not city:
                params.message = "Использование: .wt [город]"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Блокирующий вызов в главном потоке!
            data = fetch_weather_data(city)

            if not data:
                params.message = f"Не удалось получить погоду для '{city}'"
            else:
                params.message = format_weather_data(data, city)
            
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except Exception as e:
            log(f"Ошибка в плагине Weather: {str(e)}")
            params.message = f"Ошибка: {str(e)}"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Проблема: Вызов requests.get() является блокирующей операцией ввода-вывода. Он выполняется в главном (UI) потоке приложения. Пока запрос не завершится (что может занять несколько секунд), интерфейс Telegram полностью зависнет.

3.3. Улучшение производительности (Асинхронная версия)

Чтобы исправить зависание, мы вынесем сетевой запрос в фоновый поток.

Решение:

run_on_queue: Запускает тяжелую операцию (сетевой запрос) в фоновом потоке, не блокируя UI.

AlertDialogBuilder: Показывает пользователю индикатор загрузки, пока плагин работает.

HookStrategy.CANCEL: Отменяет отправку исходного сообщения (.wt ...).

client_utils.send_message: Отправляет новое сообщение с результатами погоды.

run_on_ui_thread: Отправляет результат обратно в главный поток для взаимодействия с UI (скрытие диалога, отправка сообщения).

Полный улучшенный код:

Generated python
import requests
from typing import Any, Optional

# Импортируем необходимые утилиты
from android_utils import log, run_on_ui_thread
from base_plugin import BasePlugin, HookResult, HookStrategy
from client_utils import run_on_queue, get_last_fragment, send_message
from ui.alert import AlertDialogBuilder

# --- Метаданные (изменены для уникальности) ---
__id__ = "weather_v2"
__name__ = "Weather (Async)"
__description__ = "Асинхронно показывает погоду по команде .wt"
__author__ = "exteraDev"
__version__ = "1.1.0"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

# --- Константы API (без изменений) ---
API_BASE_URL = "https://wttr.in"
API_HEADERS = {"User-Agent": "Mozilla/5.0", "Accept": "application/json"}

# --- Вспомогательные функции (без изменений) ---
def fetch_weather_data(city: str):
    # ... (код тот же)
def format_weather_data(data: dict, query_city: str):
    # ... (код тот же)

# --- Улучшенный класс плагина ---
class WeatherPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        # Храним ссылку на диалог загрузки
        self.progress_dialog: Optional[AlertDialogBuilder] = None

    def on_plugin_load(self):
        self.add_on_send_message_hook()

    # Метод для выполнения в фоновом потоке
    def _process_weather_request(self, city: str, peer_id: Any):
        data = fetch_weather_data(city)
        
        if not data:
            message_content = f"Не удалось получить погоду для '{city}'."
        else:
            message_content = format_weather_data(data, city)
 
        # Параметры для отправки нового сообщения
        message_params = {
            "message": message_content,
            "peer": peer_id
        }
 
        # Функция для выполнения в UI потоке
        def _send_and_dismiss():
            if self.progress_dialog:
                self.progress_dialog.dismiss()
                self.progress_dialog = None
            send_message(message_params) # Отправляем сообщение
 
        run_on_ui_thread(_send_and_dismiss)
 
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        if not isinstance(params.message, str) or not params.message.startswith(".wt"):
            return HookResult()

        try:
            parts = params.message.strip().split(" ", 1)
            city = parts[1].strip() if len(parts) > 1 else "Moscow"
            
            if not city:
                params.message = "Использование: .wt [город]"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Получаем текущий активный фрагмент и его Activity (контекст) для диалога
            current_fragment = get_last_fragment()
            if not current_fragment:
                 log("WeatherPlugin: Не удалось получить фрагмент для диалога.")
                 return HookResult(strategy=HookStrategy.CANCEL)
            
            current_activity = current_fragment.getParentActivity()
            if not current_activity:
                log("WeatherPlugin: Не удалось получить Activity для диалога.")
                return HookResult(strategy=HookStrategy.CANCEL)

            # Создаем и показываем диалог загрузки
            self.progress_dialog = AlertDialogBuilder(
                current_activity,
                AlertDialogBuilder.ALERT_TYPE_SPINNER
            )
            self.progress_dialog.set_title("Получение погоды...")
            self.progress_dialog.set_cancelable(False)
            self.progress_dialog.show()

            # Запускаем сетевой запрос в фоновом потоке
            run_on_queue(lambda: self._process_weather_request(city, params.peer))

            # Отменяем отправку исходного сообщения ".wt ..."
            return HookResult(strategy=HookStrategy.CANCEL)
 
        except Exception as e:
            log(f"Ошибка в плагине Weather: {str(e)}")
            if self.progress_dialog:
                run_on_ui_thread(lambda: self.progress_dialog.dismiss())
                self.progress_dialog = None
            params.message = f"Ошибка обработки команды: {str(e)}"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Теперь плагин не блокирует интерфейс, показывая индикатор загрузки и отправляя результат как новое сообщение.

Глава 4: Справочник по классу BasePlugin
4.1. Метаданные

Эти переменные определяются на верхнем уровне вашего файла плагина и должны быть простыми строками (без форматирования или конкатенации), так как они парсятся с помощью AST.

__id__ (str): Обязательно. Уникальный идентификатор плагина (латиница, без пробелов). Используется для именования файлов и папок плагина.

__name__ (str): Обязательно. Отображаемое имя плагина.

__description__ (str): Обязательно. Краткое описание функциональности. Поддерживает базовый Markdown.

__author__ (str): Обязательно. Имя автора. Можно указать username (@username) или ссылку на канал (@channel), которые будут кликабельными.

__min_version__ (str): Обязательно. Минимальная версия exteraGram, необходимая для работы плагина (например, "11.12.0").

__version__ (str, опционально): Версия вашего плагина (по умолчанию "1.0").

__icon__ (str, опционально): Иконка плагина. Формат: НАЗВАНИЕ_СТИКЕРПАКА/ИНДЕКС_СТИКЕРА. Например, MyStickerPack/0 для первого стикера из пака https://t.me/addstickers/MyStickerPack.

4.2. Жизненный цикл плагина

on_plugin_load(self): Вызывается, когда плагин активируется (при запуске приложения или включении в настройках). Идеальное место для регистрации хуков, инициализации ресурсов, загрузки настроек.

on_plugin_unload(self): Вызывается, когда плагин деактивируется. Используйте для очистки ресурсов, отмены задач. Хуки и элементы меню удаляются автоматически.

4.3. События приложения

on_app_event(self, event_type: AppEvent): Позволяет реагировать на события жизненного цикла приложения.

event_type может быть одним из значений AppEvent:

AppEvent.START: Приложение запускается.

AppEvent.STOP: Приложение останавливается.

AppEvent.PAUSE: Приложение сворачивается (уходит в фон).

AppEvent.RESUME: Приложение возвращается на передний план.

4.4. Настройки плагина

Вы можете создать страницу настроек для вашего плагина, реализовав метод create_settings.

Generated python
from ui.settings import Header, Input, Divider, Switch, Selector, Text
from org.telegram.messenger import LocaleController # Для локализации

class SettingsExamplePlugin(BasePlugin):
    def _create_subpage(self):
        return [
            Header(text="Дополнительные настройки"),
            Switch(key="sub_toggle", text="Включить что-то еще", default=False)
        ]

    def create_settings(self):
        # Пример локализации
        lang = LocaleController.getInstance().getCurrentLocale().getLanguage()
        is_ru = lang.startswith('ru')
        
        return [
            Header(text="Основные настройки"),
            Input(
                key="api_key",
                text="API Ключ",
                default="",
                icon="msg_pin_code",
                subtext="Ваш приватный ключ для доступа к API"
            ),
            Divider(),
            Switch(
                key="enable_feature",
                text="Включить основную функцию",
                default=True,
                icon="msg_info"
            ),
            Selector(
                key="download_quality",
                text="Качество",
                default=1, # Индекс элемента "720p"
                items=["480p", "720p", "1080p"],
                icon="msg_video"
            ),
            Text(
                text="Перейти к дополнительным настройкам",
                on_click=lambda view: self.log("Клик!"),
                create_sub_fragment=self._create_subpage # Создает вложенную страницу
            )
        ]
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
4.4.1. Доступные элементы управления

Header(text: str): Заголовок для группировки настроек.

Divider(text: Optional[str] = None): Разделительная линия, опционально с текстом.

Switch(key, text, default, subtext=None, icon=None, on_change=None): Переключатель (вкл/выкл). on_change получает bool.

Input(key, text, default, subtext=None, icon=None, on_change=None): Поле для ввода текста. on_change получает str.

Selector(key, text, default, items, icon=None, on_change=None): Выпадающий список. default - индекс элемента. on_change получает int (новый индекс).

Text(text, icon=None, accent=False, red=False, on_click=None, create_sub_fragment=None): Кликабельный текстовый элемент.

on_click: Функция, вызываемая при клике. Получает android.view.View.

create_sub_fragment: Функция, возвращающая список элементов для новой страницы настроек. Имеет приоритет над on_click.

4.4.2. Работа с настройками в коде

self.get_setting(key: str, default_value: Any) -> Any: Получает значение настройки.

Generated python
api_key = self.get_setting("api_key", "")
is_enabled = self.get_setting("enable_feature", True)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

self.set_setting(key: str, new_value: Any): Программно изменяет и сохраняет значение настройки.

Generated python
self.set_setting("api_key", "new-validated-key")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Глава 5: Пользовательский интерфейс
5.1. Элементы меню

Добавляйте свои пункты в различные меню приложения с помощью self.add_menu_item().

Generated python
from base_plugin import BasePlugin, MenuItemData, MenuItemType
from typing import Dict, Any

class MenuPlugin(BasePlugin):
    def on_plugin_load(self):
        # Добавляем пункт в контекстное меню сообщения
        self.add_menu_item(
            MenuItemData(
                menu_type=MenuItemType.MESSAGE_CONTEXT_MENU,
                text="Лог сообщения",
                on_click=self.log_message_info,
                icon="msg_info",
                # Показывать только для исходящих сообщений
                condition="message.isOut()" 
            )
        )
    
    def log_message_info(self, context: Dict[str, Any]):
        message = context.get("message")
        if message:
            self.log(f"ID сообщения: {message.getId()}, текст: {message.messageText}")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

MenuItemData:

menu_type: MenuItemType: Обязательно. Куда добавить пункт. Варианты:

MESSAGE_CONTEXT_MENU: Меню по долгому тапу на сообщении.

DRAWER_MENU: Главное боковое меню (гамбургер-меню).

CHAT_ACTION_MENU: Меню "три точки" внутри чата.

PROFILE_ACTION_MENU: Меню "три точки" на экране профиля.

text: str: Обязательно. Текст пункта.

on_click: Callable: Обязательно. Функция-обработчик клика. Получает словарь с контекстом (message, user, fragment и т.д.).

icon: str (опционально): Имя ресурса иконки.

condition: str (опционально): MVEL-выражение для условного показа.

5.2. Диалоговые окна (AlertDialogBuilder)

Упрощенная обертка для стандартных диалогов Telegram.

Generated python
from ui.alert import AlertDialogBuilder
from client_utils import get_last_fragment

# Получаем Activity из текущего фрагмента
current_fragment = get_last_fragment()
if current_fragment:
    activity = current_fragment.getParentActivity()
    
    # Создаем диалог
    builder = AlertDialogBuilder(activity)
    builder.set_title("Заголовок диалога")
    builder.set_message("Это важное сообщение от вашего плагина.")
    
    # Добавляем кнопки с обработчиками
    def on_ok(bld, which):
        self.log("Нажата кнопка OK")
        bld.dismiss() # Закрываем диалог

    builder.set_positive_button("OK", on_ok)
    builder.set_negative_button("Отмена", lambda b, w: b.dismiss())
    
    # Показываем диалог
    builder.show()
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Типы диалогов:

ALERT_TYPE_MESSAGE (по умолчанию): Простое сообщение.

ALERT_TYPE_SPINNER: С бесконечным индикатором загрузки.

ALERT_TYPE_LOADING: С горизонтальным прогресс-баром.

Важно: Все манипуляции с UI, включая показ и скрытие диалогов, должны выполняться в главном потоке. Используйте run_on_ui_thread при вызове из фоновых задач.

5.3. Всплывающие уведомления (BulletinHelper)

Для показа небольших неинтрузивных уведомлений внизу экрана.

Generated python
from ui.bulletin import BulletinHelper

# Простое информационное сообщение
BulletinHelper.show_info("Текст скопирован в буфер обмена.")

# Сообщение об ошибке
BulletinHelper.show_error("Не удалось подключиться к серверу.")

# Сообщение об успехе
BulletinHelper.show_success("Настройки сохранены.")

# Уведомление с кнопкой
def on_action_click():
    self.log("Кнопка в уведомлении нажата!")

BulletinHelper.show_with_button(
    text="Архив перемещен в корзину.",
    icon_res_id=R.raw.ic_delete, # Используем R из org.telegram.messenger
    button_text="ОТМЕНИТЬ",
    on_click=on_action_click
)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Глава 6: Хуки (Hooks)
6.1. Общая концепция хуков

Хуки — это основной механизм для перехвата событий в приложении.

Регистрация: В методе on_plugin_load() вы "подписываетесь" на событие с помощью self.add_hook("ИМЯ_СОБЫТИЯ").

Реализация: Вы создаете в своем классе соответствующий метод-обработчик (например, pre_request_hook).

Обработка: Когда событие происходит, ваш метод вызывается.

6.2. Хуки API-запросов

Позволяют перехватывать до отправки и после получения ответа на запросы к API Telegram. Имена запросов можно найти в схеме Telegram или в исходном коде.

Generated python
from base_plugin import HookResult, HookStrategy
from typing import Any

# ... в вашем классе плагина
def on_plugin_load(self):
    # Подписываемся на событие отправки реакции на историю
    self.add_hook("TL_stories_sendReaction")

def pre_request_hook(self, request_name: str, account: int, request: Any) -> HookResult:
    if request_name == "TL_stories_sendReaction":
        self.log(f"Перехвачена реакция: {request.reaction.emoticon}")
        # Пример: Заменить реакцию на другую
        # from org.telegram.tgnet import TLRPC
        # request.reaction = TLRPC.TL_reactionEmoji(emoticon="👍")
        # return HookResult(strategy=HookStrategy.MODIFY, request=request)
    return HookResult() # По умолчанию ничего не меняем

def post_request_hook(self, request_name: str, account: int, response: Any, error: Any) -> HookResult:
    if request_name == "TL_stories_sendReaction":
        if error:
            self.log(f"Ошибка отправки реакции: {error.error_message}")
        else:
            self.log(f"Реакция успешно отправлена. Ответ: {type(response)}")
    return HookResult()
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
6.3. Хуки обновлений

Вызываются, когда приложение получает обновления от серверов Telegram.

on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult: Для одиночных обновлений (новое сообщение, статус пользователя).

on_updates_hook(self, container_name: str, account: int, updates: Any) -> HookResult: Для контейнеров, содержащих несколько обновлений.

6.4. Хук отправки сообщения

Специализированный хук для перехвата сообщений, отправляемых пользователем.

on_send_message_hook(self, account: int, params: Any) -> HookResult: params — это Java-объект SendMessagesHelper.SendMessageParams, содержащий все данные о сообщении (текст, получатель, вложения и т.д.).

6.5. HookResult и стратегии

Каждый метод-хук должен возвращать экземпляр HookResult. Поле strategy определяет дальнейшее действие:

HookStrategy.DEFAULT: (по умолчанию, если вернуть пустой HookResult()) Продолжить выполнение как обычно.

HookStrategy.CANCEL: Отменить действие. Для pre_request_hook это отменит отправку запроса. Для on_update_hook — прекратит дальнейшую обработку обновления.

HookStrategy.MODIFY: Изменить данные. Необходимо присвоить измененный объект соответствующему полю в HookResult:

result.request = modified_request (в pre_request_hook)

result.response = modified_response (в post_request_hook)

result.update = modified_update (в on_update_hook)

result.params = modified_params (в on_send_message_hook)

HookStrategy.MODIFY_FINAL: То же, что и MODIFY, но после этого хука другие плагины не будут вызваны для этого же события.

Глава 7: Xposed-хуки (Продвинутое взаимодействие)
7.1. Введение в Xposed

Xposed-хуки — это самый мощный инструмент в арсенале разработчика плагинов. Он позволяет перехватывать вызовы любых методов или конструкторов Java в приложении и изменять их поведение "на лету".

7.2. Ключевые концепции Xposed

Классы-обработчики:

MethodHook: Для выполнения кода до (before_hooked_method) и/или после (after_hooked_method) вызова оригинального метода.

MethodReplacement: Для полной замены логики метода (replace_hooked_method). Оригинальный метод не вызывается.

Объект param:

param.thisObject: Экземпляр объекта, у которого вызывается метод.

param.args: Список аргументов, переданных в метод. Их можно читать и изменять в before_hooked_method.

param.result: Результат, возвращенный методом. Доступен и изменяем в after_hooked_method.

param.returnEarly = True: Если установить в before_hooked_method, оригинальный метод и after_hooked_method не будут вызваны. Необходимо также установить param.result.

7.3. Процесс хукинга: шаг за шагом

Найти целевой метод/конструктор: Используйте Java Reflection через hook_utils.find_class и методы .getDeclaredMethod() / .getDeclaredConstructor().

Реализовать обработчик: Создайте Python-класс, наследуемый от MethodHook или MethodReplacement.

Применить хук: В on_plugin_load вызовите self.hook_method(method, handler_instance).

7.4. Практические примеры Xposed
Пример 1: Изменение аргументов (before_hooked_method)
Generated python
# Добавим префикс ко всем Toast-сообщениям
from base_plugin import MethodHook
from hook_utils import find_class
from java import jint

class ToastPrefixHook(MethodHook):
    def before_hooked_method(self, param):
        # Сигнатура: makeText(Context context, CharSequence text, int duration)
        original_text = param.args[1]
        param.args[1] = f"[Плагин] {original_text}"

# В on_plugin_load:
ToastClass = find_class("android.widget.Toast")
ContextClass = find_class("android.content.Context")
CharSequenceClass = find_class("java.lang.CharSequence")

make_text_method = ToastClass.getDeclaredMethod("makeText", ContextClass, CharSequenceClass, jint)
self.hook_method(make_text_method, ToastPrefixHook())
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Пример 2: Изменение результата (after_hooked_method)
Generated python
# Заставим BuildVars.isMainApp() всегда возвращать False
from base_plugin import MethodHook
from hook_utils import find_class

class BuildVarsHook(MethodHook):
    def after_hooked_method(self, param):
        # param.result содержит оригинальный результат
        param.result = False

# В on_plugin_load:
BuildVarsClass = find_class("org.telegram.messenger.BuildVars")
is_main_app_method = BuildVarsClass.getDeclaredMethod("isMainApp")
self.hook_method(is_main_app_method, BuildVarsHook())
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Пример 3: Полная замена метода (MethodReplacement)
Generated python
# Отключим внутреннее логирование для уменьшения спама в logcat
from base_plugin import MethodReplacement
from hook_utils import find_class
from java.lang import String as JString

class NoOpLogger(MethodReplacement):
    def replace_hooked_method(self, param):
        # Ничего не делаем. Оригинальный метод не будет вызван.
        # Метод void, поэтому возвращаем None.
        return None

# В on_plugin_load:
FileLogClass = find_class("org.telegram.messenger.FileLog")
log_method = FileLogClass.getDeclaredMethod("d", JString)
self.hook_method(log_method, NoOpLogger())
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Глава 8: Утилиты
8.1. Android-утилиты (android_utils)

run_on_ui_thread(callable, delay=0): Крайне важно. Выполняет Python-функцию в главном потоке UI. Любые изменения в интерфейсе должны происходить внутри этой функции.

log(data): Логирует данные в logcat. Умеет красиво выводить как простые типы, так и сложные объекты.

OnClickListener(callable): Обертка для android.view.View.OnClickListener. button.setOnClickListener(OnClickListener(my_func)).

OnLongClickListener(callable): Обертка для android.view.View.OnLongClickListener. callable должен возвращать True, если событие обработано.

8.2. Клиентские утилиты (client_utils)

run_on_queue(callable, queue_name=PLUGINS_QUEUE, delay=0): Выполняет функцию в фоновой очереди. Идеально для сетевых запросов.

send_request(request: TLObject, callback: RequestCallback): Отправляет сырой API-запрос Telegram.

send_message(params: dict): Отправляет сообщение. params - словарь с параметрами, аналогичными полям SendMessagesHelper.SendMessageParams.

get_*(): Множество функций для получения доступа к основным контроллерам Telegram:

get_account_instance()

get_messages_controller()

get_user_config()

get_send_messages_helper()

И многие другие.

8.3. Парсер Markdown (markdown_utils)

parse_markdown(text: str) -> ParsedMessage: Парсит строку с Markdown V2 в объект ParsedMessage.

ParsedMessage.text: Чистый текст без Markdown-разметки.

ParsedMessage.entities: Кортеж объектов RawEntity.

RawEntity.to_tlrpc_object() -> TLRPC.MessageEntity: Преобразует RawEntity в объект, понятный API Telegram.

Generated python
from markdown_utils import parse_markdown

markdown_text = "*Жирный* и _курсив_"
parsed = parse_markdown(markdown_text)

# plain_text = "Жирный и курсив"
plain_text = parsed.text 
# tlrpc_entities = [TLRPC.TL_messageEntityBold, TLRPC.TL_messageEntityItalic]
tlrpc_entities = [entity.to_tlrpc_object() for entity in parsed.entities]
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Приложение
A: Часто используемые классы Telegram

org.telegram.ui.LaunchActivity: Главная Activity, точка входа.

org.telegram.ui.ChatActivity: Фрагмент чата.

org.telegram.ui.ProfileActivity: Фрагмент профиля.

org.telegram.messenger.MessagesController: Управление состоянием приложения и API-запросами. Один из важнейших классов.

org.telegram.messenger.SendMessagesHelper: Отправка сообщений, файлов, стикеров.

org.telegram.messenger.MessageObject: Обертка над TLRPC.Message с множеством удобных методов.

org.telegram.ui.Cells.ChatMessageCell: Класс ячейки сообщения в списке.

org.telegram.messenger.AndroidUtilities: Множество статических утилит, включая dp().

org.telegram.ui.ActionBar.AlertDialog: Класс для создания диалоговых окон.

org.telegram.ui.Components.BulletinFactory: Создание всплывающих уведомлений.

org.telegram.tgnet.TLRPC: Содержит все модели данных и запросов Telegram (например, TLRPC.User, TLRPC.TL_messages_sendMessage).

B: Исходный код (Справочно)
<details>
<summary>org.telegram.messenger.MessagesController.java</summary>

Generated java
/*
 * This is the source code of Telegram for Android v. 5.x.x.
 * It is licensed under GNU GPL v. 2 or later.
 * You should have received a copy of the license in this archive (see LICENSE).
 *
 * Copyright Nikolai Kudashov, 2013-2018.
 */

package org.telegram.messenger;
// ... (полный код из файла)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END
</details>

<details>
<summary>org.telegram.messenger.AndroidUtilities.java</summary>

Generated java
/*
 * This is the source code of Telegram for Android v. 5.x.x.
 * It is licensed under GNU GPL v. 2 or later.
 * You should have received a copy of the license in this archive (see LICENSE).
 *
 * Copyright Nikolai Kudashov, 2013-2018.
 */

package org.telegram.messenger;
// ... (полный код из файла)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END
</details>

<details>
<summary>org.telegram.messenger.MessageObject.java</summary>

Generated java
/*
 * This is the source code of Telegram for Android v. 5.x.x.
 * It is licensed under GNU GPL v. 2 or later.
 * You should have received a copy of the license in this archive (see LICENSE).
 *
 * Copyright Nikolai Kudashov, 2013-2018.
 */

package org.telegram.messenger;
// ... (полный код из файла)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END
</details>

<details>
<summary>org.telegram.ui.ChatActivity.java</summary>

Generated java
/*
 * This is the source code of Telegram for Android v. 5.x.x.
 * It is licensed under GNU GPL v. 2 or later.
 * You should have received a copy of the license in this archive (see LICENSE).
 *
 * Copyright Nikolai Kudashov, 2013-2018.
 */

package org.telegram.ui;
// ... (полный код из файла)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Java
IGNORE_WHEN_COPYING_END
</details>


Конечно, продолжаем.

Глава 9: Глубокое погружение в API Telegram (Анализ исходного кода)

Для создания по-настоящему мощных плагинов необходимо взаимодействовать с внутренними классами и методами exteraGram. Эта глава, основанная на анализе предоставленного исходного кода, раскрывает наиболее важные классы, с которыми вы будете работать.

9.1. MessagesController — Центр управления

org.telegram.messenger.MessagesController — это, без преувеличения, мозг приложения. Он отвечает за:

Хранение и управление данными: пользователи, чаты, диалоги.

Обработку обновлений с серверов Telegram.

Загрузку истории сообщений, информации о чатах и пользователях.

Множество других аспектов, связанных с состоянием данных в приложении.

Как получить доступ:
Generated python
from client_utils import get_messages_controller

# Получаем экземпляр контроллера для текущего аккаунта
messages_controller = get_messages_controller()

Ключевые методы и их использование в Python:

Получение данных:

getUser(Long id) -> TLRPC.User: Получает объект пользователя из кэша.

Generated python
user = messages_controller.getUser(12345678)
if user:
    self.log(f"Имя пользователя: {user.first_name}")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

getChat(Long id) -> TLRPC.Chat: Получает объект чата или канала из кэша.

Generated python
chat = messages_controller.getChat(1001234567890) # ID канала без префикса -100
if chat:
    self.log(f"Название канала: {chat.title}")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

getUserOrChat(long dialogId) -> TLObject: Получает пользователя или чат по dialog_id.

loadFullUser(TLRPC.User user, int classGuid, boolean force): Асинхронно загружает полную информацию о пользователе (TLRPC.UserFull).

loadFullChat(long chatId, int classGuid, boolean force): Асинхронно загружает полную информацию о чате (TLRPC.ChatFull).

getDialogs(int folderId) -> ArrayList<TLRPC.Dialog>: Возвращает список диалогов для указанной папки.

Манипуляция данными:

putUser(TLRPC.User user, boolean fromCache): Добавляет или обновляет пользователя в кэше.

putChat(TLRPC.Chat chat, boolean fromCache): Добавляет или обновляет чат в кэше.

processUpdates(TLRPC.Updates updates, boolean fromQueue): Позволяет вручную обработать объект Updates, полученный, например, в post_request_hook. Это мощный инструмент для обновления состояния приложения после ваших собственных API-запросов.

Действия с диалогами:

deleteDialog(long did, int onlyHistory): Удаляет диалог или очищает его историю.

pinDialog(long dialogId, boolean pin, TLRPC.InputPeer peer, long taskId): Закрепляет или открепляет диалог.

addUserToChat(long chatId, TLRPC.User user, ...): Добавляет пользователя в чат.

deleteParticipantFromChat(long chatId, TLRPC.User user): Удаляет участника из чата.

Пример использования MessagesController в плагине:

Generated python
# Плагин, который по команде .info @username загружает и логирует полную информацию о пользователе

class UserInfoPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def _fetch_full_info(self, user_id):
        # Эта функция будет вызвана в фоновом потоке
        messages_controller = get_messages_controller()
        
        # Загружаем полную информацию
        # classGuid = 0, force = true
        messages_controller.loadFullUser(messages_controller.getUser(user_id), 0, True)
        
        # Ждем немного, чтобы информация успела загрузиться (в реальном плагине лучше использовать хуки)
        from time import sleep
        sleep(2)
        
        user_full = messages_controller.getUserFull(user_id)
        if user_full:
            self.log(f"Полная информация о {user_id}: {user_full.about}")
        else:
            self.log(f"Не удалось загрузить полную информацию для {user_id}")

    def on_send_message_hook(self, account, params):
        if not params.message.startswith(".info @"):
            return HookResult()
            
        username = params.message.split("@")[1].strip()
        
        # Находим пользователя по username
        user = get_messages_controller().getUser(username)
        if not user:
            params.message = f"Пользователь @{username} не найден."
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
        # Запускаем загрузку полной информации в фоне
        from client_utils import run_on_queue
        run_on_queue(lambda: self._fetch_full_info(user.id))
        
        params.message = f"Запрос информации о @{username}..."
        return HookResult(strategy=HookStrategy.MODIFY, params=params)
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
9.2. MessageObject — Все о сообщениях

org.telegram.messenger.MessageObject — это Java-обертка над стандартным TLRPC.Message. Она содержит огромное количество удобных методов для работы с сообщением, его содержимым, статусом и атрибутами. Это самый часто используемый объект в плагинах, работающих с сообщениями.

Как получить доступ:

Вы получаете MessageObject в различных хуках, таких как:

on_update_hook, если update имеет тип TL_updateNewMessage.

В контекстном словаре в on_click для MenuItemData (context.get("message")).

При итерации по списку сообщений из MessagesController или MessagesStorage.

Ключевые методы и свойства:

Идентификаторы:

getId() -> int: Возвращает ID сообщения.

getDialogId() -> long: Возвращает ID диалога, которому принадлежит сообщение.

getGroupId() -> long: Возвращает ID группы (альбома), если сообщение сгруппировано.

Проверки типа сообщения (is...):

isOut() -> boolean: True, если сообщение исходящее.

isVoice(), isVideo(), isPhoto(), isMusic(), isSticker(), isAnimatedSticker(), isGif(), isRoundVideo(), isDocument(), isPoll(), isLiveLocation(): Простые и понятные методы для определения типа медиа.

isForwarded() -> boolean: True, если сообщение переслано.

isReply() -> boolean: True, если это ответ на другое сообщение.

Доступ к содержимому:

messageText: CharSequence: Основной текст сообщения или подпись к медиа.

caption: CharSequence: Подпись к медиа.

getDocument() -> TLRPC.Document: Возвращает документ, если он есть.

getPhoto() -> TLRPC.Photo: Возвращает фото, если оно есть.

getDuration() -> double: Длительность аудио/видео в секундах.

getMusicTitle() -> String, getMusicAuthor() -> String: Получение метаданных аудиофайла.

replyMessageObject: MessageObject: Ссылка на MessageObject, на который был сделан ответ.

Статусы:

isSent() -> boolean: True, если сообщение успешно отправлено.

isUnread() -> boolean: True, если сообщение не прочитано.

isEditing() -> boolean: True, если сообщение находится в процессе редактирования.

Пример использования MessageObject в плагине:

Generated python
# Плагин, который логирует информацию об ответе на сообщение
class ReplyLoggerPlugin(BasePlugin):
    def on_plugin_load(self):
        # Подписываемся на получение новых сообщений
        self.add_hook("TL_updateNewMessage")

    def on_update_hook(self, update_name, account, update):
        if update_name == "TL_updateNewMessage":
            # Создаем MessageObject из обновления
            message_obj = MessageObject(account, update.message, True, True)
            
            if message_obj.isReply():
                reply_to = message_obj.replyMessageObject
                if reply_to:
                    sender = self.get_sender_name(reply_to)
                    self.log(f"Получен ответ на сообщение от '{sender}' с текстом: '{reply_to.messageText}'")
        
        return HookResult()
        
    def get_sender_name(self, msg_obj):
        # Вспомогательная функция для получения имени отправителя
        from_id = msg_obj.getFromChatId()
        if from_id > 0:
            user = get_messages_controller().getUser(from_id)
            return user.first_name if user else "Unknown User"
        else:
            chat = get_messages_controller().getChat(-from_id)
            return chat.title if chat else "Unknown Chat"
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
9.3. AndroidUtilities — Инструменты для работы с Android

org.telegram.messenger.AndroidUtilities — это класс-«швейцарский нож», содержащий множество статических методов для решения общих задач, связанных с Android, таких как работа с размерами, UI, буфером обмена и т.д.

Как получить доступ:

Методы статические, поэтому вызываются напрямую у класса.

Generated python
from org.telegram.messenger import AndroidUtilities
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Ключевые методы:

dp(float value) -> int: Самый важный метод для UI. Конвертирует аппаратно-независимые пиксели (dp) в физические пиксели экрана. Используйте его для задания размеров, отступов, и т.д.

runOnUIThread(Runnable runnable): Выполняет код в основном потоке. Аналог android_utils.run_on_ui_thread.

hideKeyboard(View view), showKeyboard(View view): Управление экранной клавиатурой.

addToClipboard(CharSequence text): Копирует текст в буфер обмена.

getAlbumDir() -> File: Возвращает директорию, куда Telegram сохраняет медиа.

formatFileSize(long size) -> String: Форматирует размер файла (в байтах) в читаемый вид (КБ, МБ, ГБ).

formatDuration(int duration, boolean isLong) -> String: Форматирует длительность (в секундах) в формат ММ:СС или ЧЧ:ММ:СС.

Пример использования AndroidUtilities в плагине:

Generated python
# Плагин, который добавляет в контекстное меню сообщения пункт "Копировать размер файла"
from org.telegram.messenger import AndroidUtilities

class FileSizePlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_menu_item(
            MenuItemData(
                menu_type=MenuItemType.MESSAGE_CONTEXT_MENU,
                text="Копировать размер файла",
                # Показывать только для сообщений с документами
                condition="message.isDocument() || message.isVideo()", 
                on_click=self.copy_file_size
            )
        )
        
    def copy_file_size(self, context):
        message = context.get("message")
        if message and message.getDocument() is not None:
            doc = message.getDocument()
            # Форматируем размер и копируем в буфер
            formatted_size = AndroidUtilities.formatFileSize(doc.size)
            AndroidUtilities.addToClipboard(formatted_size)
            
            # Показываем уведомление
            from ui.bulletin import BulletinHelper
            BulletinHelper.show_info(f"Размер '{formatted_size}' скопирован")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
9.4. ChatActivity — Окно чата

org.telegram.ui.ChatActivity — это основной фрагмент, где отображается переписка. Получить к нему доступ можно, чтобы взаимодействовать с элементами UI именно этого экрана.

Как получить доступ:

Наиболее надежный способ — через client_utils.

Generated python
from client_utils import get_last_fragment
from org.telegram.ui import ChatActivity

fragment = get_last_fragment()
if isinstance(fragment, ChatActivity):
    chat_activity = fragment
    # ... ваш код
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Ключевые методы:

getChatActivityEnterView() -> ChatActivityEnterView: Возвращает объект панели ввода сообщения. Через него можно изменять текст, управлять кнопками, открывать/закрывать клавиатуру и панель эмодзи.

getUndoView() -> UndoView: Возвращает UndoView (панель внизу экрана), на которой можно показывать кастомные сообщения и действия.

getChatListView() -> RecyclerListView: Возвращает RecyclerListView (список сообщений).

presentFragment(BaseFragment fragment): Открывает новый экран поверх текущего чата.

showDialog(Dialog dialog): Показывает диалоговое окно, привязанное к жизненному циклу ChatActivity.

checkCanOpenChat(Bundle bundle, BaseFragment fragment): Проверяет, можно ли открыть чат с учетом возможных ограничений (например, если пользователь заблокирован).

Пример использования ChatActivity в плагине:

Generated python
# Плагин, добавляющий кнопку в ActionBar, которая вставляет текст в поле ввода

class QuickTextPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_menu_item(MenuItemData(
            menu_type=MenuItemType.CHAT_ACTION_MENU,
            text="Быстрый текст",
            icon="msg_edit",
            on_click=self.insert_quick_text
        ))

    def insert_quick_text(self, context):
        fragment = context.get("fragment")
        if isinstance(fragment, ChatActivity):
            chat_activity = fragment
            
            # Получаем доступ к панели ввода
            enter_view = chat_activity.getChatActivityEnterView()
            if enter_view:
                # Вставляем текст в текущую позицию курсора
                enter_view.setFieldText(enter_view.getFieldText() + " [Это мой быстрый текст]")
                
                # Показываем уведомление
                from ui.bulletin import BulletinHelper
                BulletinHelper.show_success("Текст вставлен!")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END
Что дальше?

Это руководство охватывает основные аспекты разработки плагинов. Для дальнейшего изучения:

Исследуйте исходный код: Предоставленные файлы .java — это ваш лучший друг. Изучайте, как устроены MessagesController, MessageObject и другие классы, чтобы находить новые возможности для своих плагинов.

Изучайте другие плагины: Смотрите на код плагинов от других разработчиков, чтобы перенимать их подходы и идеи. Ссылки на каналы с плагинами есть в исходной документации.

Экспериментируйте: Не бойтесь пробовать разные хуки и методы. Удаленная отладка поможет вам быстро находить и исправлять ошибки.

Удачной разработки!