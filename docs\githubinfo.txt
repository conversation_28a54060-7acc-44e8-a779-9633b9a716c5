import traceback
import json
import re
from datetime import datetime, timedelta
from urllib.request import urlopen
from enum import Enum
from dataclasses import dataclass
from typing import List, Optional

from ui.bulletin import BulletinHelper
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from android_utils import log as _log
from org.telegram.tgnet import TLRPC

__name__ = "GitHubInfo"
__description__ = "GitHub user info, activity and stats [.gh; .gha]"
__icon__ = "gentoocat/17"
__id__ = "githubinfo"
__version__ = "20.06.25"
__author__ = "@koshbko"
__min_version__ = "11.9.1"

API_URL = "https://api.github.com/users/"
CONTRIB_URL = "https://github-contributions-api.deno.dev/"

def log(obj):
    _log(f"{__name__}: " + str(obj))

def show_error_bulletin(message):
    BulletinHelper.show_error(f"{__name__}: " + message)

class EntityType(Enum):
    BOLD = 'bold'
    ITALIC = 'italic'
    CODE = 'code'
    PRE = 'pre'
    TEXT_LINK = 'text_link'
    BLOCKQUOTE = 'blockquote'
    SPOILER = 'spoiler'

@dataclass
class RawEntity:
    type: EntityType
    offset: int
    length: int
    url: Optional[str] = None
    language: Optional[str] = None

    def to_tlrpc(self):
        if self.type == EntityType.BOLD:
            entity = TLRPC.TL_messageEntityBold()
        elif self.type == EntityType.ITALIC:
            entity = TLRPC.TL_messageEntityItalic()
        elif self.type == EntityType.CODE:
            entity = TLRPC.TL_messageEntityCode()
        elif self.type == EntityType.PRE:
            entity = TLRPC.TL_messageEntityPre()
            entity.language = self.language or ""
        elif self.type == EntityType.TEXT_LINK:
            entity = TLRPC.TL_messageEntityTextUrl()
            entity.url = self.url or ""
        elif self.type == EntityType.BLOCKQUOTE:
            entity = TLRPC.TL_messageEntityBlockquote()
        elif self.type == EntityType.SPOILER:
            entity = TLRPC.TL_messageEntitySpoiler()
        else:
            return None
            
        entity.offset = self.offset
        entity.length = self.length
        return entity

class GitHubInfo(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()
        log("Plugin loaded")
    
    def api_request(self, url):
        try:
            with urlopen(url) as response:
                if response.getcode() != 200:
                    return None
                return json.loads(response.read().decode())
        except Exception as e:
            log(f"API request failed: {str(e)}")
            return None
    
    def fetch_user_data(self, username):
        return self.api_request(API_URL + username)
    
    def fetch_events(self, username):
        return self.api_request(f"{API_URL}{username}/events?per_page=5")
    
    def fetch_contributions(self, username):
        return self.api_request(f"{CONTRIB_URL}{username}.json")
    
    def create_user_info_message(self, user_data):
        text = ""
        entities = []
        
        # Header with name and profile link
        name = user_data.get('name', user_data.get('login', 'N/A'))
        text += f"👤 {name} | "
        name_start = text.find(name) - 0  # Position of '*' before name
        entities.append(RawEntity(EntityType.BOLD, name_start, len(name) + 2))
        
        # Profile link
        profile_start = len(text)
        profile_text = "Profile"
        text += profile_text
        entities.append(RawEntity(
            EntityType.TEXT_LINK, 
            profile_start, 
            len(profile_text),
            url=user_data['html_url']
        ))
        
        # Basic info
        text += f"\n🏢 {user_data.get('company', 'N/A')} | "
        text += f"📍 {user_data.get('location', 'N/A')}\n"
        text += f"📝 {user_data.get('bio', 'No bio')}\n\n"
        
        # Stats
        stats_text = (
            f"📦 Repos: {user_data['public_repos']} | "
            f"👥 Followers: {user_data['followers']} | "
            f"👣 Following: {user_data['following']}\n"
        )
        text += stats_text
        
        # Stats bold parts
        repos_start = text.find("Repos:") - 4  # Position before 'Repos'
        entities.append(RawEntity(EntityType.BOLD, repos_start, 6))
        
        followers_start = text.find("Followers:") - 3
        entities.append(RawEntity(EntityType.BOLD, followers_start, 9))
        
        following_start = text.find("Following:") - 3
        entities.append(RawEntity(EntityType.BOLD, following_start, 9))
        
        # Created date
        created_text = f"🕒 Created: {user_data['created_at'][:10]}"
        text += created_text
        date_start = text.find(user_data['created_at'][:10])
        entities.append(RawEntity(EntityType.CODE, date_start, 10))
        
        return text, entities
    
    def create_activity_message(self, events):
        header = "Recent activity:\n"
        text = header
        entities = [RawEntity(EntityType.BOLD, 0, len(header) - 1)]
        
        activity_lines = []
        for event in events:
            event_text = self.format_event(event)
            activity_lines.append(event_text)
        
        # Join all activity lines and calculate positions
        activity_text = "\n".join(activity_lines)
        text += activity_text
        
        # Add blockquote entity for entire activity text
        entities.append(RawEntity(
            EntityType.BLOCKQUOTE,
            len(header),
            len(activity_text)
        ))
        
        return text, entities
    
    def format_event(self, event):
        event_type = event['type']
        repo_name = event['repo']['name']
        repo_url = f"https://github.com/{repo_name}"
        
        if event_type == 'PushEvent':
            branch = re.search(r'refs/heads/(.+)', event['payload']['ref']).group(1)
            commits = event['payload']['commits']
            commit_count = len(commits)
            commit_word = "commit" if commit_count == 1 else "commits"
            return f"🔨 Pushed {commit_count} {commit_word} to {repo_name} on {branch}"
        
        elif event_type == 'CreateEvent':
            ref_type = event['payload']['ref_type']
            return f"✨ Created {ref_type} in {repo_name}"
        
        elif event_type == 'PullRequestEvent':
            pr = event['payload']['pull_request']
            action = event['payload']['action']
            return f"🔄 {action.capitalize()} PR: {pr['title']}"
        
        elif event_type == 'IssuesEvent':
            issue = event['payload']['issue']
            action = event['payload']['action']
            return f"❗ {action.capitalize()} issue: {issue['title']}"
        
        elif event_type == 'WatchEvent':
            return f"⭐ Starred {repo_name}"
        
        elif event_type == 'ForkEvent':
            forked_repo = event['payload']['forkee']['full_name']
            return f"⑂ Forked to {forked_repo}"
        
        else:
            return f"⚡ {event_type} in {repo_name}"
    
    def create_contribution_message(self, username, contrib_data):
        if not isinstance(contrib_data, dict) or not isinstance(contrib_data.get('contributions'), list):
            return "Invalid contribution data format", []
            
        today = datetime.today().date()
        start_date = today - timedelta(days=90)
        graph_lines = []
        days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        
        # Initialize empty matrix with 7 rows (days) and 13 columns (weeks)
        matrix = [["⬛" for _ in range(13)] for _ in range(7)]
        
        # Fill matrix with contributions
        for contrib in contrib_data['contributions']:
            if not isinstance(contrib, dict):
                continue
                
            try:
                date_str = contrib.get('date', '')
                if not date_str:
                    continue
                    
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
                if date < start_date or date > today:
                    continue
                    
                # Calculate exact position in the matrix
                days_diff = (date - start_date).days
                week = days_diff // 7  # Column (0-12)
                day_of_week = date.weekday()  # Monday=0, Sunday=6
                
                # Convert to our matrix format: Sunday=0, Monday=1, etc.
                # Our matrix: [0] = Sunday, [1] = Monday, ... [6] = Saturday
                if day_of_week == 6:  # Sunday
                    row = 0
                else:
                    row = day_of_week + 1  # Monday=1, Tuesday=2, etc.
                
                if 0 <= row < 7 and 0 <= week < 13:
                    count = contrib.get('contributionCount', 0)
                    if count > 0:
                        matrix[row][week] = "🟩"
            except Exception as e:
                log(f"Error processing contribution: {str(e)}")
                continue
        
        # Build graph lines
        for i in range(7):
            graph_lines.append(f"{days[i]} {''.join(matrix[i])}")
        
        graph_text = "\n".join(graph_lines)
        
        # Create full message
        header = f"Activity graph for {username} (last 3 months):\n"
        code_block = f"```\n{graph_text}\n```\n"
        footer = "⬛ = 0 contributions\n🟩 = 1+ contributions"
        text = header + code_block + footer
        
        # Create entities
        entities = [
            # Bold for header
            RawEntity(EntityType.BOLD, 0, len(header) - 1),
            
            # Pre/code block for graph
            RawEntity(
                EntityType.PRE,
                len(header) + 3,  # After opening ```
                len(graph_text) + 1,  # Include newline
                language=""
            ),
            
            # Link for username
            RawEntity(
                EntityType.TEXT_LINK,
                header.find(username),
                len(username),
                url=f"https://github.com/{username}"
            )
        ]
        
        return text, entities
    
    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()
        
        try:
            text = params.message.strip()
            
            # Main user info command
            if text.startswith(".gh "):
                username = text[4:].strip()
                if not username:
                    return HookResult()
                
                user_data = self.fetch_user_data(username)
                if not user_data:
                    show_error_bulletin(f"User not found: {username}")
                    return HookResult(strategy=HookStrategy.CANCEL)
                
                message_text, entities = self.create_user_info_message(user_data)
                params.message = message_text
                for entity in entities:
                    if tl_entity := entity.to_tlrpc():
                        params.entities.add(tl_entity)
                
                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
            
            # Recent activity command
            elif text.startswith(".gha "):
                username = text[5:].strip()
                if not username:
                    return HookResult()
                
                events = self.fetch_events(username)
                if not events or not isinstance(events, list):
                    show_error_bulletin(f"No activity found for {username}")
                    return HookResult(strategy=HookStrategy.CANCEL)
                
                message_text, entities = self.create_activity_message(events)
                params.message = message_text
                for entity in entities:
                    if tl_entity := entity.to_tlrpc():
                        params.entities.add(tl_entity)
                
                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
            
            # Contribution graph command
            elif text.startswith(".ghc "):
                username = text[5:].strip()
                if not username:
                    return HookResult()
                
                contrib_data = self.fetch_contributions(username)
                if not contrib_data or not isinstance(contrib_data, dict):
                    show_error_bulletin(f"No contribution data for {username}")
                    return HookResult(strategy=HookStrategy.CANCEL)
                
                message_text, entities = self.create_contribution_message(username, contrib_data)
                params.message = message_text
                for entity in entities:
                    if tl_entity := entity.to_tlrpc():
                        params.entities.add(tl_entity)
                
                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
            
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            log(traceback.format_exc())
            show_error_bulletin(error_msg)
            return HookResult(strategy=HookStrategy.CANCEL)