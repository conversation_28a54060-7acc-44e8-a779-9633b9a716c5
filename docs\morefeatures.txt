# -*- coding: utf-8 -*-

import re
import random
import time
import traceback
import threading
from java.util import <PERSON><PERSON><PERSON><PERSON><PERSON>, Locale
from java import jboolean, jlong, jarray, jbyte
from java import dynamic_proxy
from java.lang import Runnable, <PERSON>olean as JavaBoolean
from android.app import Activity
from android.content import Context, Intent
from android.media import AudioManager
from android.os import <PERSON><PERSON>, <PERSON><PERSON>
from android.view import View, ViewGroup, KeyEvent
from android.widget import TextView
from org.telegram.ui import ChatActivity
from org.telegram.ui.ActionBar import AlertDialog
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import ApplicationLoader, MediaController, MessageObject, UserConfig
from client_utils import get_last_fragment, get_account_instance, get_user_config
from hook_utils import find_class
from ui.settings import Header, Switch, Divider, Input
from ui.alert import AlertDialogBuilder
from android_utils import run_on_ui_thread

try:
    from cactuslib import <PERSON><PERSON>us<PERSON><PERSON><PERSON>, <PERSON>Hook, command, HookR<PERSON>ult, HookStrategy
except (ImportError, ModuleNotFoundError):
    raise Exception("CactusLib is not installed. Please install it to use this plugin.")

__id__ = "more_features"
__name__ = "MoreFeatures"
__description__ = "A collection of useful features, including call confirmation, hiding the video chat banner, and a device music controller."
__author__ = "@anivdev + CactusLib"
__version__ = "3.1.0"
__min_version__ = "11.9.0"
__icon__ = "anivdev/0"


class MoreFeaturesPlugin(CactusUtils.Plugin):
    strings = {
        "en": {
            "plugin_ui_name": "More Features",
            "call_confirmation_header": "Call Confirmation",
            "setting_enable_title": "Enable call confirmation",
            "setting_enable_subtext": "If enabled, a confirmation dialog will appear before each call.",
            "dialog_title": "Call Confirmation",
            "dialog_message": "Are you sure you want to call {0}?",
            "dialog_yes_button": "Call",
            "dialog_no_button": "Cancel",
            "hide_voip_button_title": "Hide Video Chat Banner",
            "hide_voip_button_subtext": "Hides the video chat banner in group and channel headers.",
            "voip_keywords_title": "Keywords",
            "music_controller_header": "Device Music Controller",
            "music_controller_enable_title": "Enable Music Controller",
            "music_controller_enable_subtext": "Allows controlling device music via the in-app player. Command: .player",
            "music_controller_player_title": "MoreFeatures Sound",
            "music_controller_player_author": "Android System",
            "player_not_active": "Music is not currently active on the device."
        },
        "ru": {
            "plugin_ui_name": "Дополнительные функции",
            "call_confirmation_header": "Подтверждение звонка",
            "setting_enable_title": "Включить подтверждение звонков",
            "setting_enable_subtext": "Если включено, перед каждым звонком будет появляться диалоговое окно.",
            "dialog_title": "Подтверждение звонка",
            "dialog_message": "Вы уверены, что хотите позвонить пользователю {0}?",
            "dialog_yes_button": "Позвонить",
            "dialog_no_button": "Отмена",
            "hide_voip_button_title": "Скрыть плашку видеочата",
            "hide_voip_button_subtext": "Скрывает плашку начала видеочата в заголовках групп и каналов.",
            "voip_keywords_title": "Ключевые слова",
            "music_controller_header": "Управление музыкой устройства",
            "music_controller_enable_title": "Включить управление музыкой",
            "music_controller_enable_subtext": "Позволяет управлять музыкой на устройстве через внутриигровой плеер. Команда: .player",
            "music_controller_player_title": "MoreFeatures Sound",
            "music_controller_player_author": "Android System",
            "player_not_active": "Музыка на устройстве сейчас неактивна."
        }
    }

    def __init__(self):
        super().__init__()
        self.call_hook_refs = []
        self.chat_activity_hook_ref = None
        self.is_call_confirmed_by_user = False
        self.audio_manager = None
        self.player_hook_refs = []

    def on_plugin_load(self):
        super().on_plugin_load()
        if self.get_setting("call_confirmation_enabled", True):
            self.apply_call_hook()
        if self.get_setting("hide_voip_button_enabled", True):
            self.apply_chat_activity_hook()

        try:
            context = ApplicationLoader.applicationContext
            if context:
                self.audio_manager = context.getSystemService(Context.AUDIO_SERVICE)
            if self.audio_manager:
                self.info("AudioManager initialized successfully.")
            else:
                self.warn("Failed to get AudioManager service.")
        except Exception as e:
            self.error(f"Error initializing AudioManager: {e}")

        self.info("Plugin loaded and hooks applied based on settings.")

    def on_plugin_unload(self):
        super().on_plugin_unload()
        self.remove_call_hook()
        self.remove_chat_activity_hook()
        self.remove_player_hooks()
        self.info("Plugin unloaded and all hooks removed.")

    def create_settings(self):
        return [
            Header(text=self.string("call_confirmation_header")),
            Switch(
                key="call_confirmation_enabled",
                text=self.string("setting_enable_title"),
                subtext=self.string("setting_enable_subtext"),
                default=True,
                on_change=self.toggle_call_hook
            ),
            Divider(),
            Header(text=self.string("hide_voip_button_title")),
            Switch(
                key="hide_voip_button_enabled",
                text=self.string("hide_voip_button_title"),
                subtext=self.string("hide_voip_button_subtext"),
                default=True,
                on_change=self.toggle_chat_activity_hook
            ),
            Input(
                key="voip_keywords",
                text=self.string("voip_keywords_title"),
                default="video chat,voice chat,join,видеочат,голосовой чат,войти,вступить"
            ),
            Divider(),
            Header(text=self.string("music_controller_header")),
            Switch(
                key="music_controller_enabled",
                text=self.string("music_controller_enable_title"),
                subtext=self.string("music_controller_enable_subtext"),
                default=True
            )
        ]

    def _create_fake_message_object(self):
        user_id = get_user_config().getClientUserId()

        fake_tl_message = TLRPC.TL_message()
        fake_tl_message.id = -random.randint(10000, 20000)
        fake_tl_message.date = int(time.time())

        peer_user = TLRPC.TL_peerUser()
        peer_user.user_id = user_id
        fake_tl_message.peer_id = peer_user
        fake_tl_message.from_id = peer_user

        fake_document = TLRPC.TL_document()
        fake_document.id = 0
        fake_document.access_hash = 0
        fake_document.file_reference = jarray(jbyte)(0)
        fake_document.date = int(time.time())
        fake_document.mime_type = "audio/mp3"
        fake_document.size = jlong(0)
        fake_document.dc_id = 0
        fake_document.thumbs = ArrayList()

        audio_attribute = TLRPC.TL_documentAttributeAudio()
        audio_attribute.duration = 3600
        audio_attribute.voice = False
        audio_attribute.title = self.string("music_controller_player_title")
        audio_attribute.performer = self.string("music_controller_player_author")

        fake_document.attributes.add(audio_attribute)

        fake_tl_message.media = TLRPC.TL_messageMediaDocument()
        fake_tl_message.media.document = fake_document
        fake_tl_message.message = ""

        return MessageObject(get_account_instance().getCurrentAccount(), fake_tl_message, False, False)

    def _send_media_key_event(self, key_code):
        if not self.audio_manager:
            self.error("AudioManager not available, cannot send media key event.")
            return False
        try:
            self.debug(f"Dispatching media key event: {key_code}")
            down_event = KeyEvent(KeyEvent.ACTION_DOWN, key_code)
            up_event = KeyEvent(KeyEvent.ACTION_UP, key_code)
            self.audio_manager.dispatchMediaKeyEvent(down_event)
            self.audio_manager.dispatchMediaKeyEvent(up_event)
            self.info(f"Successfully dispatched media key event: {key_code}")
            return True
        except Exception as e:
            self.error(f"Error dispatching media key event {key_code}: {e}\n{traceback.format_exc()}")
            return False

    @command("player", enabled="music_controller_enabled")
    def player_command(self, command: CactusUtils.Command):
        is_music_active = self.audio_manager.isMusicActive() if self.audio_manager else False

        playing_msg = MediaController.getInstance().getPlayingMessageObject()
        if playing_msg and playing_msg.isMusic() and playing_msg.getMusicTitle() == self.string(
                "music_controller_player_title"):
            MediaController.getInstance().cleanupPlayer(True, True)
            self.remove_player_hooks()
            return HookResult(strategy=HookStrategy.CANCEL)

        if is_music_active:
            fake_msg = self._create_fake_message_object()
            playlist = ArrayList()
            playlist.add(fake_msg)

            success = MediaController.getInstance().setPlaylist(playlist, fake_msg, command.params.peer)

            if success:
                self.info("Fake playlist set, applying player hooks.")
                self.apply_player_hooks()
            else:
                self.error("Failed to set playlist in MediaController")
        else:
            self.utils.show_info(self.string("player_not_active"))

        return HookResult(strategy=HookStrategy.CANCEL)

    def toggle_call_hook(self, value):
        if value:
            self.apply_call_hook()
        else:
            self.remove_call_hook()

    def apply_call_hook(self):
        if self.call_hook_refs:
            return
        self.debug("Attempting to apply call confirmation hooks...")
        try:
            VoIPHelperClass = find_class("org.telegram.ui.Components.voip.VoIPHelper")

            for method in VoIPHelperClass.getClass().getDeclaredMethods():
                if method.getName() == "startCall":
                    method.setAccessible(True)
                    self.debug(f"Hooking method: {method.toGenericString()}")
                    hook_ref = self.hook_method(method, self.CallConfirmationHook(self))
                    self.call_hook_refs.append(hook_ref)
        except Exception as e:
            self.error(f"Failed to apply call hook(s): {e}\n{traceback.format_exc()}")

    def remove_call_hook(self):
        if self.call_hook_refs:
            for hook_ref in self.call_hook_refs:
                self.unhook_method(hook_ref)
            self.call_hook_refs = []
            self.info("Call confirmation hooks removed.")

    class CallConfirmationHook(MethodHook):
        def __init__(self, plugin_instance):
            self.plugin = plugin_instance

        def before_hooked_method(self, param):
            if self.plugin.is_call_confirmed_by_user:
                self.plugin.is_call_confirmed_by_user = False
                return

            param.setResult(None)
            try:
                user = next((arg for arg in param.args if isinstance(arg, TLRPC.User)), None)
                activity = next((arg for arg in param.args if isinstance(arg, Activity)), None)

                if not user or not activity:
                    self.plugin.warn("Could not find User or Activity in args. Re-invoking.")
                    self.plugin.is_call_confirmed_by_user = True
                    param.method.invoke(None, *param.args)
                    return

                user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()

                builder = AlertDialogBuilder(activity)
                builder.set_title(self.plugin.string("dialog_title"))
                builder.set_message(self.plugin.string("dialog_message", user_name))

                def on_yes_click(dialog_builder, which):
                    self.plugin.is_call_confirmed_by_user = True
                    param.method.invoke(None, *param.args)

                builder.set_positive_button(self.plugin.string("dialog_yes_button"), on_yes_click)
                builder.set_negative_button(self.plugin.string("dialog_no_button"), lambda b, w: b.dismiss())
                builder.show()
            except Exception as e:
                self.plugin.error(f"Hook Error: {e}\n{traceback.format_exc()}")

    def toggle_chat_activity_hook(self, value):
        if value:
            self.apply_chat_activity_hook()
        else:
            self.remove_chat_activity_hook()
        last_fragment = get_last_fragment()
        if last_fragment and isinstance(last_fragment, ChatActivity):
            last_fragment.rebuildAllFragments(True)

    def apply_chat_activity_hook(self):
        if self.chat_activity_hook_ref:
            return
        self.debug("Applying ChatActivity hook (view traversal strategy)...")
        try:
            ChatActivityClass = find_class("org.telegram.ui.ChatActivity")

            found_method = False
            for method in ChatActivityClass.getClass().getDeclaredMethods():
                if method.getName() == "onResume":
                    self.chat_activity_hook_ref = self.hook_method(method, self.HideVoiceChatHook(self))
                    self.info(f"Hooked ChatActivity method: {method.toGenericString()}")
                    found_method = True
                    break
        except Exception as e:
            self.error(f"Failed to apply ChatActivity hook: {e}\n{traceback.format_exc()}")

    def remove_chat_activity_hook(self):
        if self.chat_activity_hook_ref:
            self.unhook_method(self.chat_activity_hook_ref)
            self.chat_activity_hook_ref = None
            self.info("ChatActivity hook removed.")

    def apply_player_hooks(self):
        if self.player_hook_refs:
            return
        self.debug("Applying player control hooks...")
        try:
            MediaControllerClass = find_class("org.telegram.messenger.MediaController")
            MessageObjectClass = find_class("org.telegram.messenger.MessageObject")

            play_method = MediaControllerClass.getClass().getDeclaredMethod("playMessage", MessageObjectClass)
            pause_method = MediaControllerClass.getClass().getDeclaredMethod("pauseMessage", MessageObjectClass,
                                                                             JavaBoolean.TYPE)
            next_method = MediaControllerClass.getClass().getDeclaredMethod("playNextMessage")
            prev_method = MediaControllerClass.getClass().getDeclaredMethod("playPreviousMessage")

            self.player_hook_refs.append(
                self.hook_method(play_method, self.PlayerControlHook(self, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE))
            )
            self.player_hook_refs.append(
                self.hook_method(pause_method, self.PlayerControlHook(self, KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE))
            )
            self.player_hook_refs.append(
                self.hook_method(next_method, self.PlayerControlHook(self, KeyEvent.KEYCODE_MEDIA_NEXT))
            )
            self.player_hook_refs.append(
                self.hook_method(prev_method, self.PlayerControlHook(self, KeyEvent.KEYCODE_MEDIA_PREVIOUS))
            )

            self.info("Player control hooks applied.")
        except Exception as e:
            self.error(f"Failed to apply player hooks: {e}\n{traceback.format_exc()}")

    def remove_player_hooks(self):
        if self.player_hook_refs:
            for hook_ref in self.player_hook_refs:
                self.unhook_method(hook_ref)
            self.player_hook_refs = []
            self.info("Player control hooks removed.")

    class PlayerControlHook(MethodHook):
        def __init__(self, plugin_instance, key_event_code):
            self.plugin = plugin_instance
            self.key_event = key_event_code

        def before_hooked_method(self, param):
            current_message = MediaController.getInstance().getPlayingMessageObject()
            if current_message and current_message.isMusic() and current_message.getMusicTitle() == self.plugin.string(
                    "music_controller_player_title"):
                param.setResult(jboolean(True))
                run_on_ui_thread(lambda: self.plugin._send_media_key_event(self.key_event))

    class HideVoiceChatHook(MethodHook):
        def __init__(self, plugin_instance):
            self.plugin = plugin_instance

        def _get_all_text_from_view(self, view):
            all_text = []
            if view is None:
                return ""

            if isinstance(view, TextView):
                text = view.getText()
                if text:
                    all_text.append(str(text))

            if isinstance(view, ViewGroup):
                for i in range(view.getChildCount()):
                    child_text = self._get_all_text_from_view(view.getChildAt(i))
                    if child_text:
                        all_text.append(child_text)

            return " ".join(all_text)

        class HideActionRunnable(dynamic_proxy(Runnable)):
            def __init__(self, hook, chat_activity):
                super().__init__()
                self.hook = hook
                self.plugin = hook.plugin
                self.chat_activity = chat_activity

            def run(self):
                self.plugin.debug("Running delayed hierarchy traversal to find banner...")
                try:
                    TargetClass = find_class("org.telegram.ui.Components.FragmentContextView")
                    if not TargetClass:
                        self.plugin.error("Target class 'FragmentContextView' not found.")
                        return

                    parent_activity = self.chat_activity.getParentActivity()
                    if not parent_activity:
                        self.plugin.warn("Could not get parent activity from ChatActivity fragment.")
                        return

                    decor_view = parent_activity.getWindow().getDecorView()

                    all_banners = []
                    self.hook.find_all_views_by_class(decor_view, TargetClass, all_banners)

                    if all_banners:
                        hidden_count = 0

                        keywords_str = self.plugin.get_setting("voip_keywords",
                                                               "video chat,voice chat,join,видеочат,голосовой чат,войти,вступить")
                        voip_keywords = [k.strip().lower() for k in keywords_str.split(',') if k.strip()]

                        for banner_view in all_banners:
                            banner_text = self.hook._get_all_text_from_view(banner_view).lower()
                            self.plugin.debug(f"Checking banner text: '{banner_text}'")

                            if any(keyword in banner_text for keyword in voip_keywords):
                                banner_view.setVisibility(View.GONE)
                                hidden_count += 1
                                self.plugin.info(f"Hiding VoIP banner based on text content.")
                            else:
                                self.plugin.info(f"Skipping banner (likely music player) based on text content.")

                        if hidden_count > 0:
                            self.plugin.info(f"Total banners hidden: {hidden_count}")
                    else:
                        self.plugin.debug("Banner view not found in the current view hierarchy.")
                except Exception as e:
                    self.plugin.error(f"Failed to hide banner view in Runnable: {e}\n{traceback.format_exc()}")

        def find_all_views_by_class(self, root, target_class, found_views):
            if not root:
                return

            java_view = root.this if hasattr(root, 'this') else root

            if isinstance(java_view, target_class):
                found_views.append(java_view)

            if isinstance(java_view, ViewGroup):
                for i in range(java_view.getChildCount()):
                    child = java_view.getChildAt(i)
                    self.find_all_views_by_class(child, target_class, found_views)

        def after_hooked_method(self, param):
            if not self.plugin.get_setting("hide_voip_button_enabled", False):
                return

            chat_activity = param.thisObject
            runnable = self.HideActionRunnable(self, chat_activity)
            Handler(Looper.getMainLooper()).postDelayed(runnable, 500)
