import requests
import base64
from io import BytesIO
from java.util import Locale, <PERSON><PERSON>y<PERSON>ist
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Input, Switch, Selector
from client_utils import get_send_messages_helper, run_on_queue
from android_utils import run_on_ui_thread
from org.telegram.messenger import ApplicationLoader
from java.io import File
from markdown_utils import parse_markdown
import uuid
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from org.telegram.ui.ActionBar import AlertDialog
from client_utils import get_last_fragment
import threading

# Powered by @AGeekApple - CrocsLab Production
# nowFy Plugin for exteraGram by @ApplePlugins

__id__ = "nowfy"
__name__ = "nowFy"
__description__ = (
    "Shows the current playing Spotify track with a styled preview.\n\n"
    "How to use:\n"
    "• Use .now to show current playing track\n"
    "• Use .setid, .setsecret, .code to configure\n"
    "• Use .help for instructions"
)
__author__ = "@AGeekApple, @exteraDev"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "ApplePlugins/5"

# Langs
TRANSLATIONS = {
    "settings_header": {
        "en": "nowFy Settings",
        "pt": "Configurações do nowFy",
        "es": "Ajustes de nowFy",
        "ru": "Настройки nowFy",
        "fr": "Paramètres nowFy"
    },
    "client_credentials": {
        "en": "Client Credentials",
        "pt": "Credenciais do Cliente",
        "es": "Credenciales del Cliente",
        "ru": "Данные клиента",
        "fr": "Identifiants client"
    },
    "use_now": {
        "en": "Use .now to get current track",
        "pt": "Use .now para ver a faixa atual",
        "es": "Usa .now para ver la pista actual",
        "ru": "Используйте .now для текущего трека",
        "fr": "Utilisez .now pour voir la piste en cours"
    },
    "footer_caption": {
        "en": "Description",
        "pt": "Rodapé (legenda) abaixo da imagem",
        "es": "Pie de página (leyenda) debajo de la imagen",
        "ru": "Нижний колонтитул (подпись) под изображением",
        "fr": "Pied de page (légende) sous l'image"
    },
    "show_track_link": {
        "en": "Show track link in caption",
        "pt": "Mostrar link da música na legenda",
        "es": "Mostrar enlace de la pista na leyenda",
        "ru": "Показать ссылку на трек в подписи",
        "fr": "Afficher le lien de la piste dans la légende"
    },
    "thanks": {
        "en": "Thank you for using nowFy! Feedback?\nJoin: @exteraDevPlugins",
        "pt": "Obrigado por usar o nowFy! Feedback?\nEntre: @exteraDevPlugins",
        "es": "¡Gracias por usar nowFy! ¿Comentarios?\nÚnete: @exteraDevPlugins",
        "ru": "Спасибо за использование nowFy! Обратная связь?\nПрисоединяйтесь: @exteraDevPlugins",
        "fr": "Merci d'utiliser nowFy ! Retour ?\nRejoignez : @exteraDevPlugins"
    }
}

def tr(key):
    lang = Locale.getDefault().getLanguage()
    return TRANSLATIONS.get(key, {}).get(lang, TRANSLATIONS.get(key, {}).get("en", key))

class nowFyPlugin(BasePlugin):
    _overlay_cache = None
    _overlay_url = "https://i.postimg.cc/kMHq5rZF/640x640.png"
    _overlay_lock = threading.Lock()

    def create_settings(self):
        return [
            Header(text=tr("settings_header")),
            Divider(text=tr("client_credentials")),
            Input(key="client_id", text="Client ID", icon="key", default=""),
            Input(key="client_secret", text="Client Secret", icon="lock", default=""),
            Input(key="refresh_token", text="Refresh Token", icon="bolt", default=""),
            Divider(text=tr("use_now")),
            Input(key="custom_footer_text", text=tr("footer_caption"), icon="msg_link", default="♪ ıllıllı - I'm using nowFy !\n╰── by @exteraDevPlugins"),
            Switch(
                key="show_track_link",
                text=tr("show_track_link"),
                default=True
            ),
            Switch(
                key="show_progress_bar",
                text="Music Bar",
                default=True
            ),
            Divider(text=tr("Font Settings - Coming Soon!")),
            Selector(
                key="font_family",
                text=tr("Font"),
                default=0,
                items=[
                    "Default",
                    "🤨???"
                ]
            ),
            Divider(text=tr("thanks"))
        ]

    def _get_overlay_cached(self):
        with self._overlay_lock:
            if self._overlay_cache is not None:
                return self._overlay_cache
            try:
                resp = requests.get(self._overlay_url, timeout=5)
                if resp.status_code == 200:
                    overlay = Image.open(BytesIO(resp.content)).convert("RGBA")
                    overlay = overlay.resize((640, 640), Image.LANCZOS)
                    self._overlay_cache = overlay
                    return overlay
            except Exception as e:
                print("Overlay error:", e)
            return None

    def _get_font_set(self):
        try:
            font_path = "/system/fonts/NotoSansCJK-Regular.ttc"
            return {
                "now": ImageFont.truetype(font_path, 23),
                "title": ImageFont.truetype(font_path, 28),
                "small": ImageFont.truetype(font_path, 22)
            }
        except:
            f = ImageFont.load_default()
            return {"now": f, "title": f, "small": f}

    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip()

        if msg.startswith(".setid "):
            self.set_setting("client_id", msg.split(" ", 1)[1].strip())
            self._send_msg(params, "✅ Client ID set successfully!")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".setsecret "):
            self.set_setting("client_secret", msg.split(" ", 1)[1].strip())
            self._send_msg(params, "✅ Client Secret set successfully!")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".code "):
            run_on_queue(lambda: self._exchange_code(params, msg.split(" ", 1)[1].strip()))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".check":
            run_on_queue(lambda: self._validate_credentials(params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".now":
            try:
                progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
                progress_dialog.show()
            except Exception:
                progress_dialog = None
            def run_and_close():
                try:
                    t = threading.Thread(target=self._get_current_track, args=(account, params, progress_dialog))
                    t.start()
                finally:
                    pass
            run_on_queue(run_and_close)
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".help":
            self._send_msg(params, (
                "NowFy Login – Step-by-step\n\n"
                "Guide with images:\n"
                "https://github.com/soumaki/nowFy/blob/main/help.md\n\n"
                "1. Go to https://developer.spotify.com/dashboard\n\n"
                "2. Create an app make sure to:\n"
                "    • Set a Redirect URI, e.g. https://example.com/callback (required)\n"
                "    • Check the “I understand” checkbox to confirm Spotify’s terms\n"
                "2.3. Create an app and copy the Client ID and Client Secret\n"
                "3. Use:\n"
                "   • .setid YOUR_CLIENT_ID\n"
                "   • .setsecret YOUR_CLIENT_SECRET\n\n"
                "4. Access this URL:\n"
                "👉 https://accounts.spotify.com/authorize?client_id=YOUR_CLIENT_ID&response_type=code&redirect_uri=https://example.com/callback&scope=user-read-currently-playing%20user-read-playback-state\n\n"
                "5. Copy the code from the URL you’re redirected to\n"
                "6. Use:\n"
                "   • .code YOUR_CODE_HERE\n\n"
                "7. Use .check to validate\n"
                "8. Use .now to see your currently playing track 🎧\n\n"
                "Do you need help? Join @exteraDevPlugins!\n\n"
                "Powered by CrocsLabs"
            ))
            return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()

    def _exchange_code(self, params, code):
        client_id = self.get_setting("client_id", "")
        client_secret = self.get_setting("client_secret", "")
        if not client_id or not client_secret:
            self._send_msg(params, "❌ Missing credentials in settings. Skill issues? 🤨")
            return

        try:
            auth = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()
            resp = requests.post(
                "https://accounts.spotify.com/api/token",
                data={"grant_type": "authorization_code", "code": code, "redirect_uri": "https://example.com/callback"},
                headers={"Authorization": f"Basic {auth}"},
                timeout=10
            )
            data = resp.json()
            if "refresh_token" in data:
                self.set_setting("refresh_token", data["refresh_token"])
                self._send_msg(params, "✅ Refresh Token saved successfully!")
            else:
                self._send_msg(params, "❌ Failed to get refresh token.")
        except Exception:
            self._send_msg(params, "❌ Failed to exchange code.")

    def _validate_credentials(self, params):
        if self._get_access_token():
            self._send_msg(params, "✅ Spotify credentials are valid!")
        else:
            self._send_msg(params, "❌ Error validating Spotify credentials.")

    def _get_access_token(self):
        client_id = self.get_setting("client_id", "")
        client_secret = self.get_setting("client_secret", "")
        refresh_token = self.get_setting("refresh_token", "")
        if not client_id or not client_secret or not refresh_token:
            return None
        try:
            auth = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()
            resp = requests.post(
                "https://accounts.spotify.com/api/token",
                data={"grant_type": "refresh_token", "refresh_token": refresh_token},
                headers={"Authorization": f"Basic {auth}"},
                timeout=5
            )
            return resp.json().get("access_token")
        except Exception:
            return None

    def _truncate(self, text, limit=35):
        return text if len(text) <= limit else text[:limit - 3] + "..."

    def _limit_words(self, text, max_words=4):
        words = text.split()
        return " ".join(words[:max_words]) + ("..." if len(words) > max_words else "")

    def _get_current_track(self, account, params, progress_dialog=None):
        try:
            token = self._get_access_token()
            if not token:
                self._send_msg(params, "❌ Could not authenticate with Spotify.")
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return
            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )
            if resp.status_code == 204 or not resp.content:
                raise Exception("It seems you're not playing anything on Spotify.")
            data = resp.json()
            track = data["item"]
            title = self._truncate(track["name"]).upper()
            artists = ", ".join([a["name"] for a in track["artists"]])
            album = self._limit_words(track["album"]["name"]).upper()
            images = track["album"].get("images", [])
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link
            progress_ms = data.get("progress_ms", 0)
            duration_ms = track["duration_ms"]
            img_resp = requests.get(cover_url, stream=True, timeout=5)
            img_bytes = BytesIO()
            for chunk in img_resp.iter_content(8192):
                img_bytes.write(chunk)
            img_bytes.seek(0)
            img = Image.open(img_bytes).convert("RGBA")
            img = img.resize((640, 640), Image.LANCZOS)

            bar_margin = int(img.width * 0.08)
            bar_width = img.width - 2 * bar_margin
            bar_height = int(img.height * 0.007)
            progress_width = int((progress_ms / duration_ms) * bar_width)
            footer_y = img.height - int(img.height * 0.35)
            bar_y = footer_y - 20
            draw = ImageDraw.Draw(img)
            if self.get_setting("show_progress_bar", True):
                draw.line([(bar_margin, bar_y + bar_height // 2), (bar_margin + bar_width, bar_y + bar_height // 2)], fill=(255, 255, 255, 80), width=bar_height)
                draw.line([(bar_margin, bar_y + bar_height // 2), (bar_margin + progress_width, bar_y + bar_height // 2)], fill=(80, 200, 255), width=bar_height)
                circle_radius = int(bar_height * 1.8)
                circle_cx = bar_margin + progress_width
                circle_cy = bar_y + bar_height // 2
                draw.ellipse((circle_cx - circle_radius, circle_cy - circle_radius, circle_cx + circle_radius, circle_cy + circle_radius), fill=(255, 255, 255))


            blur_box = img.crop((0, footer_y, img.width, img.height)).filter(ImageFilter.GaussianBlur(19))
            overlay = Image.new("RGBA", blur_box.size, (0, 0, 0, 75))
            blur_box = Image.alpha_composite(blur_box.convert("RGBA"), overlay)
            img.paste(blur_box.convert("RGB"), (0, footer_y))
            fonts = self._get_font_set()
            margin = 50
            current_y = footer_y + 25
            draw.text((margin, current_y), title, font=fonts["title"], fill="white")
            current_y += fonts["title"].getsize(title)[1] + 2
            draw.text((margin, current_y), artists, font=fonts["now"], fill="white")
            current_y += fonts["now"].getsize(artists)[1] + 1
            draw.text((margin, current_y), album, font=fonts["small"], fill="white")
            current_y += fonts["small"].getsize(album)[1] + 2
            overlay_img = self._get_overlay_cached()
            if overlay_img:
                img = Image.alpha_composite(img, overlay_img)
            img = img.convert("RGB")
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()
            img.save(temp_path)
            send_helper = get_send_messages_helper()
            footer = self.get_setting("custom_footer_text")
            show_link = self.get_setting("show_track_link", True)
            if show_link:
                caption = f"{footer} | [song.link]({universal_link})"
                caption_md = parse_markdown(caption)
                params.caption = caption_md.text
                params.entities = ArrayList()
                for entity in caption_md.entities:
                    params.entities.add(entity.to_tlrpc_object())
            else:
                params.caption = footer
                params.entities = ArrayList()
            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
        except Exception as e:
            self._send_msg(params, f"""𝗢𝗼𝗽𝘀...\n\n{str(e)}""")
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass

    def _send_msg(self, params, text):
        params.message = text
        params.entities = ArrayList()
        run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))

    def get_universal_song_link(self, spotify_url):
        try:
            api_url = f"https://api.song.link/v1-alpha.1/links?url={spotify_url}"
            resp = requests.get(api_url, timeout=10)
            data = resp.json()
            return data.get("pageUrl")
        except Exception as e:
            print(f"[song.link] {e}")
            return None