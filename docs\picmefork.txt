from base_plugin import <PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MenuItemData, MenuItemType
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Switch, Selector, Text
from ui.bulletin import BulletinHelper
from android_utils import run_on_ui_thread
from client_utils import get_messages_controller, get_last_fragment
from org.telegram.messenger import AndroidUtilities
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity

from java.util import Locale
import traceback
import random
import re

__name__ = "CuteMessages"
__description__ = "Makes your messages extra cute with many adorable styles!"
__icon__ = "ColorfulMessages/28"
__version__ = "1.5.0 [bug fix]"
__id__ = "cutemessagesenhanced"
__author__ = "@mihai<PERSON><PERSON>vski & @mishabotov & idea - @bleizix"
__min_version__ = "11.9.0"

class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"
        
    def get_string(self, string_key):
        return self.strings[self.language].get(string_key, self.strings["en"].get(string_key, string_key))

    def _get_supported_languages(self):
        return self.strings.keys()
    
    strings = {
        "ru": {
            "ENABLED": "✅ Милые сообщения включены",
            "DISABLED": "❌ Милые сообщения отключены",
            "SETTINGS_HEADER": "Настройки милых сообщений",
            "ENABLE_SWITCH": "Включить милые сообщения",
            "IGNORE_DOT_COMMANDS_SWITCH": "Игнорировать команды (.)",
            "IGNORE_DOT_COMMANDS_INFO": "Не форматировать сообщения, начинающиеся с точки. Необходимо для совместимости с другими плагинами. Требуется перезапуск для применения.",
            "EMOJI_FREQUENCY": "Частота классических эффектов",
            "TEXT_STYLE": "Стиль текста (классика)",
            "FREQUENCY_VERY_LOW": "Очень низкая (10%)",
            "FREQUENCY_LOW": "Низкая (25%)",
            "FREQUENCY_MEDIUM": "Средняя (50%)",
            "FREQUENCY_HIGH": "Высокая (75%)",
            "FREQUENCY_MAX": "Максимальная (100%)",
            "STYLE_EMOJIS": "Только эмодзи",
            "STYLE_KAOMOJI": "Каомодзи (◕‿◕)",
            "STYLE_SPARKLES": "Звездочки ✨",
            "STYLE_FULL_CLASSIC": "Все классические эффекты",
            "INFO_TEXT": "Делает ваши сообщения милыми и очаровательными с множеством настроек!",
            "ADVANCED_SETTINGS_HEADER": "Дополнительные эффекты",
            "ENABLE_LOWERCASE_SWITCH": "Преобразовать в строчные буквы",
            "LOWERCASE_INFO": "всё будет написано маленькими буквами для мягкости.",
            "ENABLE_UWU_SPEAK_SWITCH": "Включить UwU-стиль (р/л → в/w)",
            "UWU_SPEAK_INFO": "Заменяет 'р' и 'л' на 'в' (рус) или 'w' (англ).",
            "ENABLE_UWU_SUFFIXES_SWITCH": "Добавлять UwU-суффиксы (nya, owo)",
            "UWU_SUFFIXES_FREQUENCY": "Частота UwU-суффиксов",
            "ENABLE_STUTTERING_SWITCH": "Включить заикание (п-привет)",
            "STUTTERING_FREQUENCY": "Частота заикания",
            "ENABLE_VOWEL_STRETCHING_SWITCH": "Растягивать гласные (милооо)",
            "VOWEL_STRETCHING_FREQUENCY": "Частота растягивания гласных",
            "VOWEL_STRETCHING_MAX_LENGTH": "Макс. длина растянутой гласной",
            "MAX_LENGTH_2X": "Двойная (x2)",
            "MAX_LENGTH_3X": "Тройная (x3)",
            "ENABLE_CUTE_ACTIONS_SWITCH": "Добавлять милые действия (*обнимает*)",
            "CUTE_ACTIONS_FREQUENCY": "Частота милых действий",
            "CUTE_ACTIONS_INFO": "Добавляет действия вроде *обнимает* или *хихикает* в сообщения.",
            "ACTIONS_ON_NEW_LINE": "Действия на новой строке",
            "ACTIONS_ON_NEW_LINE_INFO": "Помещать милые действия (*обнимает*) на новую строку.",
            "ENABLE_CUTE_PUNCTUATION_SWITCH": "Милая пунктуация (. → .~, ? → ?✨)",
            "CUTE_PUNCTUATION_FREQUENCY": "Частота милой пунктуации",
            "CUTE_PUNCTUATION_INFO": "Заменяет обычные точки и вопросы на более милые варианты.",
            "ENABLE_SOFT_SIGN_SWITCH": "Добавлять 'ь' в конце слов (котикь)",
            "SOFT_SIGN_FREQUENCY": "Частота добавления 'ь'",
            "SOFT_SIGN_INFO": "Смягчает слова, добавляя 'ь' (в основном для русского языка).",
            "ERROR_MESSAGE_CUTE": "Ой-ой! 🥺 Что-то пошло не так, когда я пытался сделать сообщение милым... Вот оригинал:",
            "ENABLE_TEXT_BORDERS": "Включить рамки текста",
            "TEXT_BORDERS_FREQUENCY": "Частота рамок текста",
            "THEME_SELECTOR": "Выбор темы",
            "THEME_RANDOM": "Случайная",
            "THEME_PASTEL": "Пастельная",
            "THEME_MAGICAL": "Волшебная",
            "THEME_NATURE": "Природная",
            "DONATE_HEADER": "Поддержать разработку",
            "DONATE_CRYPTO": "Крипто кошелек",
            "DONATE_INFO": "Другая информация и реквизиты",
            "SHOW_SETTINGS_BUTTONS": "Кнопка настроек в меню",
            "SHOW_SETTINGS_BUTTONS_DESC": "Добавляет кнопку открытия настроек плагина в меню"
        },
        "en": {
            "ENABLED": "✅ Cute messages enabled",
            "DISABLED": "❌ Cute messages disabled",
            "SETTINGS_HEADER": "Cute Messages Settings",
            "ENABLE_SWITCH": "Enable cute messages",
            "IGNORE_DOT_COMMANDS_SWITCH": "Ignore dot-commands (.)",
            "IGNORE_DOT_COMMANDS_INFO": "Don't format messages starting with a dot. Needed for compatibility with other plugins. Requires restart to apply.",
            "EMOJI_FREQUENCY": "Classic Effects Frequency",
            "TEXT_STYLE": "Text Style (Classic)",
            "FREQUENCY_VERY_LOW": "Very Low (10%)",
            "FREQUENCY_LOW": "Low (25%)",
            "FREQUENCY_MEDIUM": "Medium (50%)",
            "FREQUENCY_HIGH": "High (75%)",
            "FREQUENCY_MAX": "Maximum (100%)",
            "STYLE_EMOJIS": "Emojis only",
            "STYLE_KAOMOJI": "Kaomoji (◕‿◕)",
            "STYLE_SPARKLES": "Sparkles ✨",
            "STYLE_FULL_CLASSIC": "All classic effects",
            "INFO_TEXT": "Makes your messages cute and adorable with lots of settings!",
            "ADVANCED_SETTINGS_HEADER": "Additional Effects",
            "ENABLE_LOWERCASE_SWITCH": "Convert to lowercase",
            "LOWERCASE_INFO": "everything will be in small letters for softness.",
            "ENABLE_UWU_SPEAK_SWITCH": "Enable UwU-speak (r/l → w)",
            "UWU_SPEAK_INFO": "Replaces 'r' and 'l' with 'w'.",
            "ENABLE_UWU_SUFFIXES_SWITCH": "Add UwU Suffixes (nya, owo)",
            "UWU_SUFFIXES_FREQUENCY": "UwU Suffix Frequency",
            "ENABLE_STUTTERING_SWITCH": "Enable Stuttering (h-hello)",
            "STUTTERING_FREQUENCY": "Stuttering Frequency",
            "ENABLE_VOWEL_STRETCHING_SWITCH": "Stretch Vowels (cuuute)",
            "VOWEL_STRETCHING_FREQUENCY": "Vowel Stretching Frequency",
            "VOWEL_STRETCHING_MAX_LENGTH": "Max Stretched Vowel Length",
            "MAX_LENGTH_2X": "Double (x2)",
            "MAX_LENGTH_3X": "Triple (x3)",
            "ENABLE_CUTE_ACTIONS_SWITCH": "Add Cute Actions (*hugs*)",
            "CUTE_ACTIONS_FREQUENCY": "Cute Actions Frequency",
            "CUTE_ACTIONS_INFO": "Adds actions like *hugs* or *giggles* to messages.",
            "ACTIONS_ON_NEW_LINE": "Actions on New Line",
            "ACTIONS_ON_NEW_LINE_INFO": "Place cute actions (*hugs*) on a new line.",
            "ENABLE_CUTE_PUNCTUATION_SWITCH": "Cute Punctuation (. → .~, ? → ?✨)",
            "CUTE_PUNCTUATION_FREQUENCY": "Cute Punctuation Frequency",
            "CUTE_PUNCTUATION_INFO": "Replaces standard periods and question marks with cuter versions.",
            "ENABLE_SOFT_SIGN_SWITCH": "Add Soft Sign ('ь') to word endings (kotikь)",
            "SOFT_SIGN_FREQUENCY": "Soft Sign Frequency",
            "SOFT_SIGN_INFO": "Softens words by adding 'ь' (primarily for Russian language).",
            "ERROR_MESSAGE_CUTE": "Oopsie! 🥺 Something went wrong while trying to make the message cute... Here's the original:",
            "ENABLE_TEXT_BORDERS": "Enable Text Borders",
            "TEXT_BORDERS_FREQUENCY": "Text Borders Frequency",
            "THEME_SELECTOR": "Theme Selector",
            "THEME_RANDOM": "Random",
            "THEME_PASTEL": "Pastel",
            "THEME_MAGICAL": "Magical",
            "THEME_NATURE": "Nature",
            "DONATE_HEADER": "Support Development",
            "DONATE_CRYPTO": "CRYPTO Wallet",
            "DONATE_INFO": "Other info and requisites",
            "SHOW_SETTINGS_BUTTONS": "Settings button in menu",
            "SHOW_SETTINGS_BUTTONS_DESC": "Adds plugin settings button to menu"
        }
    }
    
locali = LocalizationManager()

class PicMePlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self._drawer_settings_item = None
        self._chat_settings_item = None
        self.emojis = ["🥰", "😊", "💕", "💖", "💗", "🌸", "✨", "🦄", "🌈", "🍭", "🧸", "🌟", "💫", "🌻", "🍬", "🎀", "💝", "💓", "🍨", "🌷", "🦋", "🐇", "🐱", "🐶", "🦊", "🥺", "👉👈", "🍡", "🧁", "🍰", "🌺", "🌹", "💮", "🧚‍♀️", "💘", "💞", "🩷", "🩵", "🌞", "🫧", "🫶", "🦢", "🐹", "🐰", "🌼", "🧿"]
        self.kaomojis = ["(◕‿◕)", "♡(˘▽˘)♡", "(つ≧▽≦)つ", "(≧◡≦)", "(*^ω^*)", "(っ˘ω˘ς)", "(´｡• ω •｡`)", "ʕ•ᴥ•ʔ", "(づ｡◕‿‿◕｡)づ", "ฅ^•ﻌ•^ฅ", "(*˘︶˘*)", "(*¯︶¯*)", "( ˘ ³˘)♥", "(っ•ᴗ•)っ", "ლ(╹◡╹ლ)", "(๑˃ᴗ˂)ﻭ", "(灬ºωº灬)♡", "૮₍˶ᵔ ᵕ ᵔ˶₎ა", "ฅ^•ﻌ•^ฅ", "(*ฅ́˘ฅ̀*)", "(●'◡'●)", "ฅ(^◕ᴥ◕^)ฅ", "(=^･ω･^=)", "ʕっ•ᴥ•ʔっ", "ʕ ꈍᴥꈍʔ", "ʕ´•ᴥ•`ʔ", "(◡ ω ◡)", "(◕ᴗ◕✿)", "꒰⑅ᵕ༚ᵕ꒱˖♡", "ପ(๑•ᴗ•๑)ଓ ♡", "(⁄ ⁄•⁄ω⁄•⁄ ⁄)"]
        self.sparkles = ["✨", "⭐", "★", "☆", "₊˚⊹", "˚₊· ͟͟͞͞➳❥", "⋆｡°✩", "☆彡", "⊰", "⊱", "✧･ﾟ", "♡", "❀", "❁", "❃", "❋", "✿", "♫", "♪", "✧˖°", "⋆｡˚", "⋆⭒˚｡⋆", "✧*。", "⁺˚*•̩̩͙✩•̩̩͙*˚⁺", "‧₊˚✧", "ପ♡ଓ", "✩°｡⋆⸜", "✮", "✩₊˚.⋆", "☽˚｡⋆", "❥", "༘⋆", "⋆⭒˚｡⋆", "✮.°:⋆ₓₒ", "✧･ﾟ:✧･ﾟ", "*ੈ✩‧₊˚", "┊͙ ˘͈ᵕ˘͈", "ೃ࿔₊", "˗ˏˋ ★ ˎˊ˗"]
        
        self.uwu_suffixes = ["~", " nya~", " uwu", " owo", " >w<", " :3", " nyaaa", "σωσ", "◡ ω ◡", " OwO", " UwU~", " hehe~", " rawr~", " mew", " purr~", " ehehe", " uwu~", " (⁄ ⁄>⁄ω⁄<⁄ ⁄)", " kyaa~", " nyaa", " nyuu~", " mya~", " (◕ᴗ◕✿)", " teehee", " hehehe", " awoo~", " *blushes*", " purrr", " pwease", " nya?"]
        self.extended_exclamations = ["~!", "!!", "!!!", "!~", "!❤", "!✨", "!💖", "!⭐", "!!!1!", "! nya~", "!?!?", "!!!💕", "~!!~", "! ꒰◍ᐡᐤᐡ◍꒱", "! ❁◕ ‿ ◕❁", "!!♡", "! (*ฅ́˘ฅ̀*)", "~✿!", "! ♡₊˚", "!⋆₊", "! 🎀", "! 🌸", "!🌟", "!☆"]
        self.vowels_map = {
            'ru': "аеёиоуыэюяАЕЁИОУЫЭЮЯ",
            'en': "aeiouyAEIOUY"
        }
        self.letter_pattern = re.compile(r'^\w', re.UNICODE)

        self.cute_actions_map = {
            "ru": ["*обнимает*", "*нежно обнимает*", "*гладит по голове*", "*хихикает*", "*мурлычет*", "*улыбается*", "*подмигивает*", "*машет лапкой*", "*краснеет*", "*прыгает от радости*", "*делает большие глаза*", "*играет с волосами*", "*качает хвостиком*", "*делает милое личико*", "*танцует от счастья*", "*прячется за лапками*", "*радостно вздыхает*"],
            "en": ["*hugs*", "*gentle hug*", "*pats head*", "*giggles*", "*purrs*", "*smiles*", "*winks*", "*waves paw*", "*blushes*", "*jumps with joy*", "*makes big eyes*", "*plays with hair*", "*wags tail*", "*makes cute face*", "*happy dance*", "*hides behind paws*", "*happy sigh*", "*nuzzles*", "*tippy taps*", "*boops nose*"]
        }
        self.cute_period_replacements = [".~", ".!", ".✨", ".🌸", ".💖", "~~", " .·͙*̩̩͙˚̩̥̩̥*̩̩̥͙·̩̩̥͙*̩̩̥͙˚̩̥̩̥*̩̩͙‧͙ .·˖* நோக்கி", " ^^", "₊˚ʚ ᗢ₊˚✧", "✿", ".♡", ".˚ʚ♡ɞ˚", ".˖°𓋼𓍢𓋼𓍢𓋼", ".ᐟ", ".⋆", ".♬", ".⋆˙⟡♡", ".𓆩♡𓆪", ".*ੈ✩‧₊˚", ".𓍢ִ໋🌷͙֒", ".:₊˚◞♡", "◞♡ₓₒ"]
        self.cute_question_mark_replacements = ["?!!", "???", "❓💖", "?~", "❓✨", " <(°.°<)", " uwu?", "?✧", "?🥺", "?(ㅅ´ ˘ `)", "??♡", "??🌸", "?☆", "?♡", "?꒰◍ᐡᐤᐡ◍꒱", "?⁺◟(◔ั₀◔ั )◞⁺", " (◕ᴗ◕✿)?", "?⋆", "?𓍢ִ໋🌷͙֒", " nyaa?"]
        self.consonants_ru = "бвгджзйклмнпрстфхцчшщ"

        self.text_borders = [
            ["🌸 ", " 🌸"],
            ["✧･ﾟ: ", " :･ﾟ✧"],
            ["— ♡ ", " ♡ —"],
            ["꒰ ", " ꒱"],
            ["₊˚⊹ ", " ⊹˚₊"],
            ["⋆⭒˚｡⋆ ", " ⋆｡˚⭒⋆"],
            ["─── ⋆⋅☆⋅⋆ ", " ⋆⋅☆⋅⋆ ───"],
            ["┊͙ ", " ┊͙"],
            ["ꔛ ", " ꔛ"],
            ["✿ ", " ✿"],
            ["༉‧₊˚ ", " ₊˚✧"],
            ["𓂃 𓈒𓏸 ", " 𓈒𓏸"],
            ["♡⋆.ೃ࿔", "♡⋆.ೃ࿔"]
        ]
        
        self.themes = {
            "pastel": {
                "emojis": ["🌸", "🧁", "🍼", "🩰", "🎀", "🧸", "🍦", "🫧", "🤍", "🩷", "🩵", "🫐", "🐇"],
                "words": ["cute", "soft", "sweet", "pastel", "lovely", "gentle"],
                "prefix": ["✿", "♡", "✧", "⋆"],
                "suffix": ["✧", "♡", "✿", "⋆"]
            },
            "magical": {
                "emojis": ["✨", "🌟", "🔮", "🧚", "⭐", "🌙", "🪄", "🦄", "🧿", "🪞", "🔆"],
                "words": ["magic", "sparkle", "twinkle", "enchant", "fairy", "mystic"],
                "prefix": ["✧", "⋆", "🔮", "☆"],
                "suffix": ["✨", "⋆", "✧", "☆"]
            },
            "nature": {
                "emojis": ["🌷", "🌱", "🍄", "🦋", "🐝", "🌻", "🪴", "🌿", "🍃", "🌺", "🌼"],
                "words": ["bloom", "flower", "garden", "blossom", "meadow", "butterfly"],
                "prefix": ["❀", "🌱", "🌿", "🍃"],
                "suffix": ["❀", "🌱", "🌿", "🍄"]
            }
        }

    def _copy_to_clipboard(self, label, text):
        if AndroidUtilities.addToClipboard(text):
            BulletinHelper.show_info(f"Copied {label} to clipboard")

    def _get_frequency_prob_for_new_effects(self, setting_key, default_index=2): 
        frequency_idx = self.get_setting(setting_key, default_index)
        if frequency_idx == 0: return 0.10
        if frequency_idx == 1: return 0.25
        if frequency_idx == 3: return 0.75
        if frequency_idx == 4: return 1.00
        return 0.5

    def _open_plugin_settings(self, java_plugin):
        try:
            get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
        except Exception as e:
            from client_utils import log
            log(f"[{__id__}] Error opening plugin settings: {e}")

    def _add_settings_menu_items(self):
        try:
            if not self._drawer_settings_item:
                self._drawer_settings_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.DRAWER_MENU,
                    text=locali.get_string("SETTINGS_HEADER"),
                    icon="msg_settings_14",
                    priority=5,
                    on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
                ))
            if not self._chat_settings_item:
                self._chat_settings_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.CHAT_ACTION_MENU,
                    text=locali.get_string("SETTINGS_HEADER"),
                    icon="msg_settings_14",
                    priority=5,
                    on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
                ))
        except Exception as e:
            from client_utils import log
            log(f"[{__id__}] Failed to add settings menu items: {e}")

    def _remove_settings_menu_items(self):
        try:
            if self._drawer_settings_item:
                self.remove_menu_item(self._drawer_settings_item)
                self._drawer_settings_item = None
            if self._chat_settings_item:
                self.remove_menu_item(self._chat_settings_item)
                self._chat_settings_item = None
        except Exception as e:
            from client_utils import log
            log(f"[{__id__}] Failed to remove settings menu items: {e}")

    def _on_show_settings_buttons_change(self, enabled: bool):
        def _toggle():
            try:
                if enabled:
                    self._add_settings_menu_items()
                else:
                    self._remove_settings_menu_items()
            except Exception as e:
                from client_utils import log
                log(f"[{__id__}] Failed toggling settings buttons: {e}")
        run_on_ui_thread(_toggle)

    def create_settings(self):
        classic_frequency_items = [
            locali.get_string("FREQUENCY_LOW"),
            locali.get_string("FREQUENCY_MEDIUM"),
            locali.get_string("FREQUENCY_HIGH"),
            locali.get_string("FREQUENCY_MAX")
        ]

        extended_frequency_items = [
            locali.get_string("FREQUENCY_VERY_LOW"),
            locali.get_string("FREQUENCY_LOW"),
            locali.get_string("FREQUENCY_MEDIUM"),
            locali.get_string("FREQUENCY_HIGH"),
            locali.get_string("FREQUENCY_MAX")
        ]
        
        theme_items = [
            locali.get_string("THEME_RANDOM"),
            locali.get_string("THEME_PASTEL"),
            locali.get_string("THEME_MAGICAL"),
            locali.get_string("THEME_NATURE")
        ]

        settings = [
            Header(text=locali.get_string("SETTINGS_HEADER")),
            Switch(key="enabled", text=locali.get_string("ENABLE_SWITCH"), icon="menu_premium_effects", default=True),
            Switch(
                key="show_settings_buttons",
                text=locali.get_string("SHOW_SETTINGS_BUTTONS"),
                icon="msg_reorder",
                default=True,
                subtext=locali.get_string("SHOW_SETTINGS_BUTTONS_DESC"),
                on_change=self._on_show_settings_buttons_change
            ),
            Switch(key="ignore_dot_commands", text=locali.get_string("IGNORE_DOT_COMMANDS_SWITCH"), icon="msg_stories_stealth", default=True),
            Divider(text=locali.get_string("IGNORE_DOT_COMMANDS_INFO")),
            Selector(
                key="emoji_frequency", 
                text=locali.get_string("EMOJI_FREQUENCY"),
                items=classic_frequency_items,
                default=1,
                icon="msg_forward_replace"
            ),
            Selector(
                key="text_style",
                text=locali.get_string("TEXT_STYLE"),
                items=[
                    locali.get_string("STYLE_EMOJIS"),
                    locali.get_string("STYLE_KAOMOJI"),
                    locali.get_string("STYLE_SPARKLES"),
                    locali.get_string("STYLE_FULL_CLASSIC")
                ],
                default=0,
                icon="msg_palette"
            ),
            Switch(key="enable_text_borders", text=locali.get_string("ENABLE_TEXT_BORDERS"), default=False, icon="floating_check"),
            Selector(
                key="text_borders_frequency",
                text=locali.get_string("TEXT_BORDERS_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Selector(
                key="theme_selector",
                text=locali.get_string("THEME_SELECTOR"),
                items=theme_items,
                default=0,
                icon="msg_theme"
            ),
            Divider(),
            Header(text=locali.get_string("ADVANCED_SETTINGS_HEADER")),
            Switch(key="enable_lowercase", text=locali.get_string("ENABLE_LOWERCASE_SWITCH"), default=False, icon="floating_check"),
            Divider(text=locali.get_string("LOWERCASE_INFO")),
            Switch(key="enable_uwu_speak", text=locali.get_string("ENABLE_UWU_SPEAK_SWITCH"), default=False, icon="floating_check"),
            Divider(text=locali.get_string("UWU_SPEAK_INFO")),
            Switch(key="enable_uwu_suffixes", text=locali.get_string("ENABLE_UWU_SUFFIXES_SWITCH"), default=False, icon="msg_filled_plus"),
            Selector(
                key="uwu_suffix_frequency",
                text=locali.get_string("UWU_SUFFIXES_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Divider(),
            Switch(key="enable_stuttering", text=locali.get_string("ENABLE_STUTTERING_SWITCH"), default=False, icon="floating_check"),
            Selector(
                key="stuttering_frequency",
                text=locali.get_string("STUTTERING_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Divider(),
            Switch(key="enable_vowel_stretching", text=locali.get_string("ENABLE_VOWEL_STRETCHING_SWITCH"), default=False, icon="floating_check"),
            Selector(
                key="vowel_stretching_frequency",
                text=locali.get_string("VOWEL_STRETCHING_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Selector(
                key="vowel_stretching_max_length",
                text=locali.get_string("VOWEL_STRETCHING_MAX_LENGTH"),
                items=[
                    locali.get_string("MAX_LENGTH_2X"),
                    locali.get_string("MAX_LENGTH_3X")
                ],
                default=0,
                icon="msg_noise_on"
            ),
            Divider(),
            Switch(key="enable_soft_sign", text=locali.get_string("ENABLE_SOFT_SIGN_SWITCH"), default=False, icon="floating_check"),
            Selector(
                key="soft_sign_frequency",
                text=locali.get_string("SOFT_SIGN_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Divider(text=locali.get_string("SOFT_SIGN_INFO")),
            
            Switch(key="enable_cute_punctuation", text=locali.get_string("ENABLE_CUTE_PUNCTUATION_SWITCH"), default=False, icon="floating_check"),
            Selector(
                key="cute_punctuation_frequency",
                text=locali.get_string("CUTE_PUNCTUATION_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Divider(text=locali.get_string("CUTE_PUNCTUATION_INFO")),

            Switch(key="enable_cute_actions", text=locali.get_string("ENABLE_CUTE_ACTIONS_SWITCH"), default=False, icon="msg_filled_plus"),
            Selector(
                key="cute_actions_frequency",
                text=locali.get_string("CUTE_ACTIONS_FREQUENCY"),
                items=extended_frequency_items,
                default=2,
                icon="msg_forward_replace"
            ),
            Switch(
                key="actions_on_new_line",
                text=locali.get_string("ACTIONS_ON_NEW_LINE"),
                subtext=locali.get_string("ACTIONS_ON_NEW_LINE_INFO"),
                default=False,
                icon="msg_reorder"
            ),
            Divider(text=locali.get_string("CUTE_ACTIONS_INFO")),

            Divider(text=locali.get_string("INFO_TEXT")),
            Divider(),
            Header(text=locali.get_string("DONATE_HEADER")),
            Text(
                text=locali.get_string("DONATE_CRYPTO"),
                icon="menu_cashtag",
                accent=True,
                on_click=lambda view: run_on_ui_thread(lambda: self._copy_to_clipboard("CRYPTO", "http://t.me/send?start=IVaZsLfW7aSn"))
            ),
            Text(
                text=locali.get_string("DONATE_INFO"),
                icon="msg_info",
                accent=True,
                on_click=lambda view: run_on_ui_thread(lambda: get_messages_controller().openByUserName("mishabotov", get_last_fragment(), 1))
            )
        ]
        
        return settings
        
    def on_plugin_load(self):
        ignore_commands = self.get_setting("ignore_dot_commands", True)
        priority = -100 if ignore_commands else 200
        self.add_on_send_message_hook(priority)
        if self.get_setting("show_settings_buttons", True):
            self._add_settings_menu_items()

    def on_plugin_unload(self):
        self._remove_settings_menu_items()
    
    def get_classic_effects_probability(self):
        frequency = self.get_setting("emoji_frequency", 1) 
        if frequency == 0: return 0.25
        if frequency == 2: return 0.75
        if frequency == 3: return 1.0
        return 0.5

    def on_send_message_hook(self, account, params):
        original_text_for_error = params.message if hasattr(params, 'message') and params.message else (params.caption if hasattr(params, 'caption') and params.caption else None)
        try:
            if hasattr(params, 'message') and params.message and params.message.strip() == ".picme":
                current_state = self.get_setting("enabled", True)
                new_state = not current_state
                self.set_setting("enabled", new_state)
                
                if new_state:
                    BulletinHelper.show_info(locali.get_string("ENABLED"))
                else:
                    BulletinHelper.show_info(locali.get_string("DISABLED"))
                
                return HookResult(strategy=HookStrategy.CANCEL)

            if not self.get_setting("enabled", True):
                return HookResult()
            
            if self.get_setting("ignore_dot_commands", True):
                if original_text_for_error and original_text_for_error.strip().startswith('.'):
                    return HookResult()

            if not original_text_for_error:
                return HookResult()

            classic_effects_prob = self.get_classic_effects_probability()
            text_style_setting = self.get_setting("text_style", 0)
            modified_text = self.apply_cute_transformations(original_text_for_error, classic_effects_prob, text_style_setting)
            
            if hasattr(params, 'message') and params.message:
                params.message = modified_text
            elif hasattr(params, 'caption') and params.caption:
                params.caption = modified_text
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        except Exception:
            from client_utils import log
            log(f"Exception in plugin {__name__}:\n{traceback.format_exc()}")
            error_prefix = locali.get_string("ERROR_MESSAGE_CUTE")
            if hasattr(params, 'message') and params.message is not None:
                params.message = f"{error_prefix}\n\n{original_text_for_error}"
            elif hasattr(params, 'caption') and params.caption is not None:
                params.caption = f"{error_prefix}\n\n{original_text_for_error}"
            return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

    def _transform_lowercase(self, text):
        return text.lower()

    def _transform_uwu_speak(self, text):
        text = text.replace('r', 'w').replace('l', 'w').replace('R', 'W').replace('L', 'W')
        text = text.replace('р', 'в').replace('л', 'в').replace('Р', 'В').replace('Л', 'В')
        return text

    def _transform_stutter(self, word):
        if len(word) > 1 and self.letter_pattern.match(word) and '-' not in word:
            first_char = word[0]
            stutter_prefix = first_char.lower() + '-'
            if random.random() < 0.4: 
                stutter_prefix += first_char.lower() + '-'
            return stutter_prefix + word
        return word

    def _transform_vowel_stretch(self, word, stretch_probability, max_stretch_factor, vowels_for_lang):
        if not word or not vowels_for_lang: return word
        output_chars = []
        for char_w in word:
            output_chars.append(char_w)
            if char_w in vowels_for_lang and random.random() < stretch_probability:
                current_streak = 0
                for k_idx in range(len(output_chars) -1, -1, -1):
                    if output_chars[k_idx] == char_w: current_streak +=1
                    else: break
                if current_streak < max_stretch_factor:
                    num_additional_chars = random.randint(1, max_stretch_factor - current_streak)
                    output_chars.extend([char_w] * num_additional_chars)
        return "".join(output_chars)

    def _transform_exclamations(self, ending_punctuation):
        def replace_match(match): return random.choice(self.extended_exclamations)
        return re.sub(r"!", replace_match, ending_punctuation)

    def apply_cute_transformations(self, text, classic_effects_probability, style_setting):
        apply_lowercase = self.get_setting("enable_lowercase", False)
        apply_uwu_speak = self.get_setting("enable_uwu_speak", False)
        
        enable_uwu_suffixes = self.get_setting("enable_uwu_suffixes", False)
        uwu_suffix_prob = self._get_frequency_prob_for_new_effects("uwu_suffix_frequency")

        enable_stutter = self.get_setting("enable_stuttering", False)
        stutter_prob = self._get_frequency_prob_for_new_effects("stuttering_frequency")

        enable_vowel_stretch = self.get_setting("enable_vowel_stretching", False)
        vowel_stretch_word_prob = self._get_frequency_prob_for_new_effects("vowel_stretching_frequency")
        vowel_stretch_per_vowel_prob = 0.6
        max_vowel_stretch_idx = self.get_setting("vowel_stretching_max_length", 0)
        max_stretch_factor = 2 if max_vowel_stretch_idx == 0 else 3

        enable_soft_sign = self.get_setting("enable_soft_sign", False)
        soft_sign_prob = self._get_frequency_prob_for_new_effects("soft_sign_frequency")
        
        enable_cute_punctuation = self.get_setting("enable_cute_punctuation", False)
        cute_punctuation_prob = self._get_frequency_prob_for_new_effects("cute_punctuation_frequency")

        enable_cute_actions = self.get_setting("enable_cute_actions", False)
        cute_actions_prob = self._get_frequency_prob_for_new_effects("cute_actions_frequency")
        actions_on_new_line = self.get_setting("actions_on_new_line", False)
        current_cute_actions = self.cute_actions_map.get(locali.language, self.cute_actions_map['en'])
        
        enable_text_borders = self.get_setting("enable_text_borders", False)
        text_borders_prob = self._get_frequency_prob_for_new_effects("text_borders_frequency", 2)
        
        theme_idx = self.get_setting("theme_selector", 0)
        current_theme = None
        if theme_idx > 0:
            theme_names = list(self.themes.keys())
            if theme_idx - 1 < len(theme_names):
                current_theme = self.themes[theme_names[theme_idx - 1]]
        elif random.random() < 0.3:
            current_theme = random.choice(list(self.themes.values()))

        if apply_lowercase: text = self._transform_lowercase(text)
        if apply_uwu_speak: text = self._transform_uwu_speak(text)

        sentences = re.split(r'([.!?]+\s*)', text)
        result_parts = []
        current_language_vowels = self.vowels_map.get(locali.language, self.vowels_map['en'])

        if current_theme and random.random() < 0.7:
            if len(text) < 100 and random.random() < 0.5:
                prefix = random.choice(current_theme["prefix"])
                suffix = random.choice(current_theme["suffix"])
                if random.random() < 0.3:
                    text = f"{prefix} {text} {suffix}"
            
            if len(text) > 20 and random.random() < 0.3:
                theme_word = random.choice(current_theme["words"])
                theme_emoji = random.choice(current_theme["emojis"])
                insert_text = f" {theme_word} {theme_emoji} "
                words = text.split()
                if len(words) > 5:
                    insert_pos = random.randint(2, len(words) - 2)
                    words.insert(insert_pos, insert_text)
                    text = " ".join(words)
                    sentences = re.split(r'([.!?]+\s*)', text)

        for i in range(0, len(sentences), 2):
            sentence_part = sentences[i].strip() if i < len(sentences) else ""
            ending_punctuation = sentences[i+1] if i+1 < len(sentences) else ""

            if not sentence_part and ending_punctuation:
                if (style_setting == 2 or style_setting == 3) and "!" in ending_punctuation:
                     if random.random() < classic_effects_probability:
                        ending_punctuation = self._transform_exclamations(ending_punctuation)
                if enable_cute_punctuation and random.random() < cute_punctuation_prob:
                    if "?" in ending_punctuation and self.cute_question_mark_replacements:
                        m = re.search(r"([?!]+)(\s*)$", ending_punctuation)
                        if m and "?" in m.group(1): ending_punctuation = ending_punctuation[:m.start(1)] + random.choice(self.cute_question_mark_replacements) + m.group(2)
                    elif "." in ending_punctuation and "..." not in ending_punctuation and self.cute_period_replacements:
                        m = re.search(r"(\.+)(\s*)$", ending_punctuation)
                        if m and len(m.group(1)) == 1: ending_punctuation = ending_punctuation[:m.start(1)] + random.choice(self.cute_period_replacements) + m.group(2)
                result_parts.append(ending_punctuation)
                continue
            
            if not sentence_part and not ending_punctuation:
                continue

            words = sentence_part.split()
            processed_words = []
            for word_idx, word in enumerate(words):
                leading_punc, temp_word, trailing_punc = "", word, ""
                m_lead = re.match(r"^([^\w\s]+)", temp_word)
                if m_lead: leading_punc, temp_word = m_lead.group(1), temp_word[len(leading_punc):]
                m_trail = re.search(r"([^\w\s]+)$", temp_word)
                if m_trail: trailing_punc, temp_word = m_trail.group(1), temp_word[:-len(trailing_punc)]
                
                if not temp_word:
                    processed_words.append(word)
                    continue

                if enable_stutter and random.random() < stutter_prob:
                    temp_word = self._transform_stutter(temp_word)
                
                if enable_vowel_stretch and random.random() < vowel_stretch_word_prob:
                    temp_word = self._transform_vowel_stretch(temp_word, vowel_stretch_per_vowel_prob, max_stretch_factor, current_language_vowels)
                
                if enable_soft_sign and locali.language == "ru" and temp_word and \
                   temp_word[-1].lower() in self.consonants_ru and \
                   temp_word[-1].lower() not in "ьъ" and \
                   random.random() < soft_sign_prob:
                    if len(temp_word) > 1 or temp_word[-1].lower() != 'й':
                        temp_word += "ь"
                
                processed_words.append(leading_punc + temp_word + trailing_punc)
            sentence_part = " ".join(processed_words)

            if style_setting == 0 or style_setting == 3:
                words_for_emojis = sentence_part.split()
                emoji_list = self.emojis
                if current_theme and random.random() < 0.7:
                    emoji_list = current_theme["emojis"]
                sentence_part = " ".join(w + (f" {random.choice(emoji_list)}" if w and random.random() < classic_effects_probability else "") for w in words_for_emojis).strip()
            
            if style_setting == 1 or style_setting == 3:
                if random.random() < classic_effects_probability: sentence_part = (sentence_part + f" {random.choice(self.kaomojis)}").strip()
            
            if enable_uwu_suffixes and random.random() < uwu_suffix_prob:
                if sentence_part: sentence_part += f" {random.choice(self.uwu_suffixes)}"
                else: sentence_part = random.choice(self.uwu_suffixes)
                sentence_part = sentence_part.strip()

            if enable_cute_punctuation and random.random() < cute_punctuation_prob:
                if "?" in ending_punctuation and self.cute_question_mark_replacements:
                    m = re.search(r"([?!]+)(\s*)$", ending_punctuation)
                    if m and "?" in m.group(1): ending_punctuation = ending_punctuation[:m.start(1)] + random.choice(self.cute_question_mark_replacements) + m.group(2)
                elif "." in ending_punctuation and "..." not in ending_punctuation and self.cute_period_replacements:
                    m = re.search(r"(\.+)(\s*)$", ending_punctuation)
                    if m and len(m.group(1)) == 1:
                         ending_punctuation = ending_punctuation[:m.start(1)] + random.choice(self.cute_period_replacements) + m.group(2)

            if style_setting == 2 or style_setting == 3:
                if random.random() < classic_effects_probability * 0.7: sentence_part = f"{random.choice(self.sparkles)} {sentence_part} {random.choice(self.sparkles)}".strip()
                if "!" in ending_punctuation and random.random() < classic_effects_probability:
                    ending_punctuation = self._transform_exclamations(ending_punctuation)
            
            if enable_text_borders and sentence_part and random.random() < text_borders_prob:
                if len(sentence_part) < 50:
                    borders = random.choice(self.text_borders)
                    sentence_part = f"{borders[0]}{sentence_part}{borders[1]}"
            
            result_parts.append(sentence_part)
            result_parts.append(ending_punctuation)
            
            if enable_cute_actions and sentence_part and current_cute_actions and random.random() < cute_actions_prob:
                action_str = random.choice(current_cute_actions)
                if actions_on_new_line:
                    if result_parts and result_parts[-1]:
                        result_parts[-1] = result_parts[-1].rstrip(' \t')
                    result_parts.append('\n' + action_str)
                else:
                    if result_parts and result_parts[-1] and not result_parts[-1].endswith((' ', '\n', '\t')):
                        result_parts.append(' ' + action_str)
                    else:
                        result_parts.append(action_str)
            
        return "".join(result_parts)