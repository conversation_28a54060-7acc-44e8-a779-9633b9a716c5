from base_plugin import <PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from ui.settings import Head<PERSON>, Selector
from client_utils import get_send_messages_helper, get_messages_controller, send_request, RequestCallback, send_message
from org.telegram.messenger import ApplicationLoader
from org.telegram.tgnet import TLRPC
from java.io import File
from java.util import Locale

from PIL import Image, ImageDraw, ImageFont, ImageOps
import requests, uuid, os, traceback, random, threading

__id__ = "tailed-quotify"
__name__ = "quotify"
__description__ = "quote messages by replying with .q command"
__author__ = "@tailsjs"
__min_version__ = "11.9.0"
__icon__ = "ksdjofsjfosdfjosijf/92"
__version__ = "1.1"

class DebugLogger:
    logs_dir = "/storage/emulated/0/Download/tailed_quotes_logs"
    logs = []
    
    @staticmethod
    def make_log(log):
        DebugLogger.logs.append(log)
        
    @staticmethod
    def save_logs():
        save_dir = DebugLogger.logs_dir + f"/log-{uuid.uuid4()}.txt"
        Filesystem.write_file(save_dir, '\n'.join(DebugLogger.logs).encode('utf-8'))
        return save_dir
    
    @staticmethod
    def format_tl_error(error):
        return f"TLRPC Error: {error.text} ({error.code})"

class QuoterPlugin(BasePlugin):
    def on_plugin_load(self):
        try:
            FontManager.init()
            LocalizationManager.init()
            self.add_on_send_message_hook()
        except Exception as e:
            DebugLogger.make_log("on_plugin_load\n" + str(traceback.format_exc()))
            
    def create_settings(self):
        return [
            Header(
                LocalizationManager.get_string("SETTINGS_TITLE")
            ),
            Selector(
                key="font",
                text=LocalizationManager.get_string("SETTINGS_FONT"),
                default=0,
                items=FontManager.get_fonts_names()
            )
        ]
        
    def on_send_message_hook(self, account, params) -> HookStrategy:
        if not isinstance(params.message, str):
            return HookResult()
        
        if params.message.startswith(".quotedebuglogs"):
            save_dir = DebugLogger.save_logs()
            params.message = f"logs saved into {save_dir}"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        
        if not params.message.startswith(".q"):
            return HookResult()
        
        if not params.replyToMsg:
            params.message = LocalizationManager.get_string("FORGOT_TO_REPLY")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        try:
            font_index = self.get_setting("font", 0)
            quote = QuoteManager(params, font_index)
            
            quote.generate_quote()
            
            return HookResult(strategy=HookStrategy.CANCEL)
        except Exception as e:
            params.message = LocalizationManager.get_string("PLUGIN_ERROR")
            DebugLogger.make_log("on_send_message_hook\n" + str(traceback.format_exc()))
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
       
class LocalizationManager:
    language = "en"
    @staticmethod
    def init():
        language = Locale.getDefault().getLanguage()
        LocalizationManager.language = language if language in LocalizationManager._get_supported_languages() else "en"
        
    @staticmethod
    def get_string(string):
        locali = LocalizationManager.strings[LocalizationManager.language][string] 
        return locali if locali is not None else string
    
    @staticmethod
    def _get_supported_languages():
        return LocalizationManager.strings.keys()
    
    strings = {
        "ru": {
            "FORGOT_TO_REPLY": "⚠️ ты забыл реплайнуть сообщение!",
            "TLRPC_USERPHOTOS_ERROR": "⛔ произошла ошибка при попытке получить аву пользователя!",
            "TLRPC_GETFILE_ERROR": "⛔ произошла ошибка при скачивании авы пользователя!",
            "PLUGIN_ERROR": "⛔ произошла ошибка в коде плагина",
            "USERPHOTOS_ERROR": "⛔ произошла ошибка в коде получения изображения пользователя!",
            "GETFILE_ERROR": "⛔ произошла ошибка в коде скачки изображения пользователя!",
            "SETTINGS_TITLE": "настройки плагина",
            "SETTINGS_FONT": "шрифт",
            "ATTACH_TYPE_1": "[фото]",
            "ATTACH_TYPE_2": "[голосовое сообщение]",
            "ATTACH_TYPE_3": "[видео]", 
            "ATTACH_TYPE_4": "[гео]", 
            "ATTACH_TYPE_5": "[кружок]", 
            "ATTACH_TYPE_6": "[загрузка]", 
            "ATTACH_TYPE_8": "[гиф]", 
            "ATTACH_TYPE_9": "[файл]", 
            "ATTACH_TYPE_10": "[дата]", 
            "ATTACH_TYPE_11": "[фото]", 
            "ATTACH_TYPE_12": "[контакт]", 
            "ATTACH_TYPE_13": "[стикер]", 
            "ATTACH_TYPE_14": "[музыка]", 
            "ATTACH_TYPE_15": "[анимированный стикер]", 
            "ATTACH_TYPE_16": "[звонок]", 
            "ATTACH_TYPE_17": "[опрос]", 
            "ATTACH_TYPE_18": "[премиум]", 
            "ATTACH_TYPE_19": "[эмодзи]", 
            "ATTACH_TYPE_20": "[превью]", 
            "ATTACH_TYPE_21": "[предложение фото]", 
            "ATTACH_TYPE_22": "[задний фон]", 
            "ATTACH_TYPE_23": "[история]", 
            "ATTACH_TYPE_24": "[упоминание в истории]", 
            "ATTACH_TYPE_25": "[премиум]", 
            "ATTACH_TYPE_26": "[раздача]", 
            "ATTACH_TYPE_27": "[зашёл в канал]", 
            "ATTACH_TYPE_28": "[результат раздачи]", 
            "ATTACH_TYPE_29": "[платное вложение]", 
            "ATTACH_TYPE_30": "[звёзды]",
            "IN_REPLY": "[в ответ {0}]"
        },
        "en": {
            "FORGOT_TO_REPLY": "⚠️ you forgot to reply to message!",
            "TLRPC_GETCHANNELS_ERROR": "⛔ an error occurred while trying to fetch the chat data!",
            "TLRPC_GETCHATPHOTO_ERROR": "⛔ an error occurred while trying to fetch the chat thumbnail!",
            "TLRPC_USERPHOTOS_ERROR": "⛔ an error occurred while trying to fetch the user's avatar!",
            "TLRPC_GETFILE_ERROR": "⛔ an error occurred while downloading avatar!",
            "PLUGIN_ERROR": "⛔ an error occurred in plugin code!",
            "SETTINGS_TITLE": "plugin settings",
            "SETTINGS_FONT": "font",
            "ATTACH_TYPE_1": "[photo]",
            "ATTACH_TYPE_2": "[voice message]",
            "ATTACH_TYPE_3": "[video]",
            "ATTACH_TYPE_4": "[geo]",
            "ATTACH_TYPE_5": "[round message]",
            "ATTACH_TYPE_6": "[upload]",
            "ATTACH_TYPE_8": "[gif]",
            "ATTACH_TYPE_9": "[file]",
            "ATTACH_TYPE_10": "[date]",
            "ATTACH_TYPE_11": "[photo]",
            "ATTACH_TYPE_12": "[contact]",
            "ATTACH_TYPE_13": "[sticker]",
            "ATTACH_TYPE_14": "[music]",
            "ATTACH_TYPE_15": "[animated sticker]",
            "ATTACH_TYPE_16": "[call]",
            "ATTACH_TYPE_17": "[poll]",
            "ATTACH_TYPE_18": "[premium]",
            "ATTACH_TYPE_19": "[emoji]",
            "ATTACH_TYPE_20": "[preview]",
            "ATTACH_TYPE_21": "[photo suggestion]",
            "ATTACH_TYPE_22": "[background]",
            "ATTACH_TYPE_23": "[story]",
            "ATTACH_TYPE_24": "[story mention]",
            "ATTACH_TYPE_25": "[premium]",
            "ATTACH_TYPE_26": "[giveaway]",
            "ATTACH_TYPE_27": "[joined channel]",
            "ATTACH_TYPE_28": "[giveaway result]",
            "ATTACH_TYPE_29": "[paid attachment]",
            "ATTACH_TYPE_30": "[stars]",
            "IN_REPLY": "[in reply to {0}]"
        }
    }
    
class Filesystem:
    @staticmethod
    def write_file(file_path, content):
        file_full_path = "/".join(file_path.split("/")[:-1])
        if not os.path.exists(file_full_path):
            os.makedirs(file_full_path)
            
        with open(file_path, 'wb') as file:
            file.write(content)
            
    @staticmethod
    def get_absolute_path(dir, file_name):
        return File(dir, file_name).getAbsolutePath()
            
    @staticmethod
    def get_temp_dir():
        try:
            fixed_dir = ApplicationLoader.getFilesDirFixed()
            
            if not fixed_dir:
                return None
            
            temp_dir = File(fixed_dir, "tailed_quotes").getAbsolutePath()
            
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                
            return temp_dir
            
        except Exception as e:
            DebugLogger.make_log("get_temp_dir\n" + str(traceback.format_exc()))
            return None
        
class TelegramUtils:
    @staticmethod
    def get_user(user_id):
        return get_messages_controller().getUser(user_id)
    
    @staticmethod 
    def get_channel(channel_id, callback):
        req = TLRPC.TL_channels_getChannels()
        input_channel = get_messages_controller().getInputChannel(channel_id)
        req.id.add(input_channel)
        
        cb = RequestCallback(callback)
        
        send_request(req, cb)
        
    @staticmethod
    def get_chat(chat_id, callback):
        req = TLRPC.TL_messages_getChats()
        input_chat = get_messages_controller().getInputChat(chat_id)
        req.id.add(input_chat)
        
        cb = RequestCallback(callback)
        
        send_request(req, cb)
        
    @staticmethod
    def create_input_location(photo, size):
        location = TLRPC.TL_inputPhotoFileLocation()
        
        location.id = photo.id
        location.access_hash = photo.access_hash
        location.file_reference = photo.file_reference
        location.thumb_size = size.type

        return location
    
    @staticmethod
    def create_peer_input_location(chat, peer):
        location = TLRPC.TL_inputPeerPhotoFileLocation()
        location.big = True
        location.peer = get_messages_controller().getInputPeer(peer)
        location.photo_id = chat.photo.photo_id
        
        return location
        
    @staticmethod
    def get_chat_thumbnail(chat, peer, callback):
        req = TLRPC.TL_upload_getFile()
        cb = RequestCallback(callback)
        
        location = TelegramUtils.create_peer_input_location(chat, peer)
        
        req.location = location
        req.offset = 0
        req.limit = 1024 * 1024
        
        send_request(req, cb)
        
    
class ReplyerID:
    def __init__(self, peer):
        if peer is None:
            self.channel_id = 0
            self.chat_id = 0
            self.user_id = -1
            self.peer = None
            return
        
        self.channel_id = peer.channel_id
        self.chat_id = peer.chat_id
        self.user_id = peer.user_id
        
        self.peer = peer
        
    def get_replyer_type(self):
        if self.channel_id is not 0:
            return 2
        elif self.chat_id is not 0:
            return 1
        else:
            return 0
        
    def get_id(self):
        if self.channel_id is not 0:
            return self.channel_id
        elif self.chat_id is not 0:
            return self.chat_id
        else:
            return self.user_id
        
class Replyer:
    def __init__(self):
        self.name = ""

class QuoteManager:
    def __init__(self, params, font_index):
        self.params = params
        self.user = None
        self.replyer_id = None
        self.replyer = Replyer()
        self.font_index = font_index
        
    def generate_quote(self):
        try:
            self.replyer_id = self.init_replyer_id()
            
            replyer_type = self.replyer_id.get_replyer_type()
            
            if replyer_type is 0:
                if self.replyer_id.user_id is -1:
                    return self.create_quote_without_input()
                user = TelegramUtils.get_user(self.replyer_id.user_id)

                self.user = user
                
                self.replyer.name = self.get_full_replyer_name()
                
                req = TLRPC.TL_photos_getUserPhotos()

                cb = RequestCallback(self.handle_user_photos_callback)

                input_user = get_messages_controller().getInputUser(user.id)

                req.user_id = input_user
                req.limit = 1
                
                send_request(req, cb)
            elif replyer_type is 1:
                TelegramUtils.get_chat(self.replyer_id.chat_id, self.handle_get_channels_callback)
            elif replyer_type is 2:
                TelegramUtils.get_channel(self.replyer_id.channel_id, self.handle_get_channels_callback)
        except Exception as e:
            DebugLogger.make_log("generate_quote\n" + str(traceback.format_exc()))
            
    def handle_get_channels_callback(self, response, error):
        if error:
            DebugLogger.make_log(DebugLogger.format_tl_error(error))
            self._send_message(LocalizationManager.get_string("TLRPC_GETCHANNELS_ERROR"))
            return
        
        if not error and response:
            if response.chats is None or response.chats.size() == 0:
                return None
            
            chat = response.chats.get(0)
            
            self.replyer.name = chat.title
            
            if chat.photo.photo_id is 0:
                return self.create_quote_without_input()
            
            TelegramUtils.get_chat_thumbnail(chat, self.replyer_id.peer, self.handle_get_file_callback)
            
    def get_quote_message(self):
        raw_message = self.get_replyer()
        message = raw_message.message if isinstance(raw_message.message, str) else ""
        
        try:
            message_type = self.params.replyToMsg.type
            
            if message_type is not None and message_type in range(1, 30):
                message = LocalizationManager.get_string("ATTACH_TYPE_" + str(message_type)) + "\n" + message
                
            if self.params.replyToMsg.replyMessageObject is not None:
                reply_message = self.params.replyToMsg.replyMessageObject
                message_owner = reply_message.messageOwner
                
                user = TelegramUtils.get_user(message_owner.from_id.user_id)
                
                full_name = self.get_full_replyer_name_from_userobject(user)
                
                message = LocalizationManager.get_string("IN_REPLY").format(full_name) + "\n" + message
            
            return message
        except Exception as e:
            DebugLogger.make_log("get_quote_message\n" + str(traceback.format_exc()))
            
            self._send_message(LocalizationManager.get_string("PLUGIN_ERROR"))
        
    def init_replyer_id(self):
        replyer = self.get_replyer()
        replyer_peer = replyer.from_id
        
        if replyer.fwd_from is not None:
            replyer_peer = replyer.fwd_from.from_id
            
            if replyer.fwd_from.from_id is None:
                self.replyer.name = replyer.fwd_from.saved_from_name
        
        return ReplyerID(replyer_peer)
            
    def get_full_replyer_name(self):
        first_name = self.user.first_name
        first_name = first_name if first_name is not None else ""
        
        last_name = self.user.last_name
        last_name = last_name if last_name is not None else ""
        
        return " ".join([first_name, last_name]).strip()
    
    def get_full_replyer_name_from_userobject(self, user):
        first_name = user.first_name
        first_name = first_name if first_name is not None else ""
        
        last_name = user.last_name
        last_name = last_name if last_name is not None else ""
        
        return " ".join([first_name, last_name]).strip()
            
    def get_replyer(self):
        return self.params.replyToMsg.messageOwner
            
    def get_replyer_id(self):
        return self.replyer_id.get_id()
    
    def _send_message(self, message):
        params = {
            "message": message,
            "peer": self.params.peer
        }
        
        send_message(params)
        
    def handle_user_photos_callback(self, response, error):
        if error:
            DebugLogger.make_log(DebugLogger.format_tl_error(error))
            self._send_message(LocalizationManager.get_string("TLRPC_USERPHOTOS_ERROR"))
            return
        
        if not error and response:
            try:
                if response.photos is None or response.photos.size() == 0:
                    return self.create_quote_without_input()
                photo = response.photos.get(0)
                size = photo.sizes.get(0)
                
                req = TLRPC.TL_upload_getFile()
                cb = RequestCallback(self.handle_get_file_callback)
                
                input_file = TelegramUtils.create_input_location(photo, size)
                
                req.location = input_file
                req.offset = 0
                req.limit = 1024 * 1024
                
                send_request(req, cb)
            except Exception as e:
                try:
                    self.create_quote_without_input()
                except Exception as ee:
                    DebugLogger.make_log("handle_user_photos_callback.user_photo_empty\n" + str(traceback.format_exc()))
                    self._send_message(LocalizationManager.get_string("USERPHOTOS_ERROR"))
                    return
                
                DebugLogger.make_log("handle_user_photos_callback\n" + str(traceback.format_exc()))
                self._send_message(LocalizationManager.get_string("USERPHOTOS_ERROR"))
    
    def create_quote_without_input(self):
        try:
            tmp_file = f"tmp_{self.get_replyer_id()}.jpg"
                        
            temp_dir = Filesystem.get_temp_dir()
                        
            file_path = Filesystem.get_absolute_path(
                temp_dir, tmp_file
            )
                        
            full_replyer_name = self.replyer.name
            message = self.get_quote_message()
                        
            self.create_and_send_image(
                output_path=file_path,
                author=full_replyer_name,
                quote=message
            )
        except Exception as e:
            DebugLogger.make_log("create_quote_without_input\n" + str(traceback.format_exc()))
                
    def handle_get_file_callback(self, response, error):
        if error:
            DebugLogger.make_log(DebugLogger.format_tl_error(error))
            self._send_message(LocalizationManager.get_string("TLRPC_GETFILE_ERROR"))
            return
            
        if not error and response:
            try:
                buffer = response.bytes.buffer
                buffer.rewind()
                length = buffer.remaining()
                
                py_bytes = bytes([buffer.get() & 0xFF for _ in range(length)])
                
                tmp_file = f"img_{self.get_replyer_id()}.jpg"
                
                temp_dir = Filesystem.get_temp_dir()
                
                file_path = Filesystem.get_absolute_path(
                    temp_dir, tmp_file
                )
                
                Filesystem.write_file(file_path, py_bytes)
                
                full_replyer_name = self.replyer.name
                message = self.get_quote_message()
                
                self.create_and_send_image(
                    image_path=file_path,
                    output_path=file_path,
                    author=full_replyer_name,
                    quote=message
                )
            except Exception as e:
                DebugLogger.make_log("handle_get_file_callback\n" + str(traceback.format_exc()))
                self._send_message(LocalizationManager.get_string("GETFILE_ERROR"))
                
    def create_and_send_image(
        self, 
        bg_color=(0, 0, 0),
        image_path=None,
        quote="",
        author="",
        output_path="quote_dynamic_stretch.png",
        img_width=300,
        canvas_width=900,
        min_canvas_height=400,
        padding=30
    ):
        font_path = FontManager.get(self.font_index).get_path()
        quote_font = ImageFont.truetype(font_path, 36)
        author_font = ImageFont.truetype(font_path, 28)
        
        temp_canvas = Image.new("RGB", (canvas_width, min_canvas_height), bg_color)
        draw = ImageDraw.Draw(temp_canvas)

        max_text_width = canvas_width - img_width - padding * 3
        wrapped_quote = "\n".join(
            self._wrap_text(draw, line, quote_font, max_text_width)
            for line in quote.split("\n")
        )
        author_text = f"— {author}"
        
        quote_size = draw.multiline_textsize(wrapped_quote, font=quote_font)
        author_size = draw.textsize(author_text, font=author_font)

        total_text_height = quote_size[1] + author_size[1] + 15

        img = None
        img_height = 0
        if image_path:
            orig_img = Image.open(image_path).convert("RGBA")
            orig_w, orig_h = orig_img.size

            scale_factor = img_width / orig_w
            img_height = int(orig_h * scale_factor)

            canvas_height = max(min_canvas_height, total_text_height + padding * 2, img_height + padding * 2)

            img = orig_img.resize((img_width, img_height))
            
            mask = Image.new("L", (img_width, img_height), 0)
            mask_draw = ImageDraw.Draw(mask)
            mask_draw.ellipse((0, 0, img_width, img_height), fill=255)
            img.putalpha(mask)
        else:
            canvas_height = max(min_canvas_height, total_text_height + padding * 2)

            img = Image.new("RGBA", (img_width, img_width), (0, 0, 0, 0))
            circle_draw = ImageDraw.Draw(img)

            rnd_color = tuple(random.randint(100, 255) for _ in range(3))
            circle_draw.ellipse((0, 0, img_width, img_width), fill=rnd_color)

            letter_font = ImageFont.truetype(font_path, int(img_width * 0.4))
            letter = author.strip()[0].upper() if author.strip() else "?"

            w, h = circle_draw.textsize(letter, font=letter_font)
            circle_draw.text(
                ((img_width - w) // 2, (img_width - h) // 2),
                letter,
                font=letter_font,
                fill=(255, 255, 255)
            )

            img_height = img_width

        canvas = Image.new("RGB", (canvas_width, canvas_height), bg_color)
        draw = ImageDraw.Draw(canvas)

        if img:
            img_pos = (padding, (canvas_height - img_height) // 2)
            canvas.paste(img, img_pos, img)
        else:
            img_pos = (padding, (canvas_height - img_width) // 2)

        text_x = img_pos[0] + img_width + padding
        text_y = (canvas_height - total_text_height) // 2

        draw.multiline_text((text_x, text_y), wrapped_quote, font=quote_font, fill=(255, 255, 255))
        draw.text((text_x, text_y + quote_size[1] + 10), author_text, font=author_font, fill=(180, 180, 180))

        canvas.save(output_path)
        
        self._send_quote(output_path)
        
    def _send_quote(self, output_path):
        send_helper = get_send_messages_helper()
        photo = send_helper.generatePhotoSizes(output_path, None)
        
        params = {
            "message": None,
            "peer": self.params.peer
        }
        
        params["photo"] = photo
        params["path"] = output_path
        
        params["replyToMsg"] = self.params.replyToMsg
        params["replyToTopMsg"] = self.params.replyToTopMsg
        
        send_message(params)
        
    def _wrap_text(self, draw, text, font, max_width):
        lines = []
        words = text.split()
        while words:
            line_words = []
            while words:
                line_words.append(words.pop(0))
                w, _ = draw.textsize(" ".join(line_words + words[:1]), font=font)
                if w > max_width:
                    break
            lines.append(" ".join(line_words))
        return "\n".join(lines)

class FontManager:
    fonts = []
    @staticmethod
    def init():
        FontManager.fonts.append(
            Font("Hack", "https://github.com/source-foundry/Hack/raw/refs/heads/master/build/ttf/Hack-Bold.ttf")
        )
        
        FontManager.fonts.append(
            Font("Monocraft", "https://github.com/IdreesInc/Monocraft/raw/refs/heads/main/dist/Monocraft.ttf")
        )
        
        FontManager.fonts.append(
            Font("Times New Roman", "https://github.com/misuchiru03/font-times-new-roman/raw/refs/heads/master/Times%20New%20Roman.ttf")
        )
        
        threading.Thread(target=FontManager._download_fonts, daemon=True).start()
                
    @staticmethod
    def _download_fonts():
        for font in FontManager.fonts:
            if not font.exists():
                font.download()
                
    @staticmethod
    def get(index):
        return FontManager.fonts[index]
    
    @staticmethod
    def get_fonts_names():
        fonts = []
        
        for font in FontManager.fonts:
            fonts.append(font.name)
            
        return fonts

class Font:
    def __init__(self, name, download_uri):
        self.name = name
        self.download_uri = download_uri
        
    def get_path(self):
        temp_dir = Filesystem.get_temp_dir()
        file_name = self.name + "." + self.get_type()
        return Filesystem.get_absolute_path(temp_dir, file_name)
    
    def get_type(self):
        return self.download_uri.split(".")[-1]
    
    def exists(self):
        return os.path.exists(self.get_path())
        
    def download(self):
        if self.exists():
            return
        
        font_path = self.get_path()
        
        response = requests.get(self.download_uri)
    
        with open(font_path, 'wb') as file:
            file.write(response.content)