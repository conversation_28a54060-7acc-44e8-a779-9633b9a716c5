import json
from base_plugin import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MenuItemData, MenuItemType
from ui.settings import Header, Input, Switch, Divider, Text
from markdown_utils import parse_markdown
from java.util import ArrayList
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from com.exteragram.messenger.plugins import Plugins<PERSON><PERSON>roller
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity

__id__ = "requisites_sender"
__name__ = "rekvplug"
__version__ = "2.0.0"
__author__ = "@shrdevv"
__description__ = "Отправляет указанные в настройках реквизиты по команде .rekv"
__min_version__ = "11.12.0"
__icon__ = "plugin232/0"

REQUISITES = [
    ("tonkeeper", "To<PERSON>eeper"),
    ("wallet", "Wallet"),
    ("tonhub", "TONhub"),
    ("bank_card", "Bank card"),
    ("yumoney", "<PERSON><PERSON>oney"),
    ("donation_alerts", "Donation alerts"),
    ("boosty", "Boosty"),
    ("patreon", "Patreon")
]
MAX_CUSTOM_FIELDS = 5

class RekvPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()
        self.add_menu_item(
            MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text="Настройки реквизитов",
                icon="msg_settings_14",
                on_click=self._open_settings
            )
        )

    def _open_settings(self, ctx):
        def action():
            try:
                java_plugin = PluginsController.getInstance().plugins.get(self.id)
                if java_plugin:
                    get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
            except Exception as e:
                from android_utils import log
                log(f"Error opening plugin settings: {e}")
        run_on_ui_thread(action)

    def create_settings(self):
        settings_list = [
            Header(text="Основные настройки"),
            Input(
                key="username",
                text="Ваш никнейм",
                default="",
                subtext="Отображается в заголовке",
                icon="msg_contacts"
            ),
            Switch(
                key="show_username",
                text="Показывать никнейм",
                default=True,
                icon="msg_info"
            ),
            Divider(),
            Header(text="Платежные реквизиты"),
        ]

        for key, label in REQUISITES:
            settings_list.extend([
                Input(
                    key=key,
                    text=label,
                    default="",
                    icon="msg_pin_code"
                ),
                Switch(
                    key=f"show_{key}",
                    text=f"Показывать {label}",
                    default=True,
                    icon="msg_visibility"
                ),
                Divider()
            ])

        settings_list.append(Header(text="Пользовательские реквизиты"))

        for i in range(1, MAX_CUSTOM_FIELDS + 1):
            settings_list.extend([
                Input(
                    key=f"custom_name_{i}",
                    text=f"Название поля {i}",
                    default="",
                    icon="msg_edit"
                ),
                Input(
                    key=f"custom_value_{i}",
                    text=f"Значение поля {i}",
                    default="",
                    icon="msg_pin_code"
                ),
                Switch(
                    key=f"show_custom_{i}",
                    text=f"Показывать поле {i}",
                    default=True,
                    icon="msg_visibility"
                ),
                Divider()
            ])

        settings_list.extend([
            Header(text="Прочее"),
            Switch(
                key="disable_ad",
                text="Отключить рекламу",
                default=False,
                icon="msg_block"
            )
        ])
        return settings_list

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()

        if params.message.strip().lower() != ".rekv":
            return HookResult()

        message_parts = []
        
        show_username = self.get_setting("show_username", True)
        username = self.get_setting("username", "")
        if show_username and username:
            message_parts.append(f"Payment [{username}]")
            message_parts.append("")

        for key, label in REQUISITES:
            show_item = self.get_setting(f"show_{key}", True)
            item_value = self.get_setting(key, "")
            if show_item and item_value:
                message_parts.append(f"{label}: `{item_value}`")

        for i in range(1, MAX_CUSTOM_FIELDS + 1):
            show_custom = self.get_setting(f"show_custom_{i}", True)
            custom_name = self.get_setting(f"custom_name_{i}", "")
            custom_value = self.get_setting(f"custom_value_{i}", "")
            if show_custom and custom_name and custom_value:
                message_parts.append(f"{custom_name}: `{custom_value}`")

        disable_ad = self.get_setting("disable_ad", False)
        if not disable_ad:
            if message_parts:
                 message_parts.append("")
            message_parts.append("rekv plugin by @linescall")

        if not any(part.strip() for part in message_parts):
            params.message = "Реквизиты не настроены. Заполните их в настройках плагина."
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        
        final_text = "\n".join(message_parts)
        
        parsed = parse_markdown(final_text)
        params.message = parsed.text
        
        if not hasattr(params, "entities") or params.entities is None:
            params.entities = ArrayList()
        else:
            params.entities.clear()
            
        for entity in parsed.entities:
            params.entities.add(entity.to_tlrpc_object())
            
        return HookResult(strategy=HookStrategy.MODIFY, params=params)