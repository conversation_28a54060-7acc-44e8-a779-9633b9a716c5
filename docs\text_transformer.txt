import random
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Selector
from base_plugin import <PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Hook<PERSON>trategy
from android_utils import log
from java.util import Locale

__id__ = "text_transformer"
__name__ = "Text Transformer"
__description__ = "Transform your message to various formats using the .rp command"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
__icon__ = "exteraDevPlugins/6"

LANGS = {
    "en": {
        "transform_modes": ["Emoji", "Morse Code", "Binary", "Bubble Text", "Upside Down", "Vaporwave"],
        "emoji_categories": ["Happy", "Sad", "Love", "Animals", "Food", "Activities", "Mixed"],
        "usage": "Usage: .rp [your message]",
        "settings_header": "Text Transformer Settings",
        "category_key": "Emoji Category (for emoji mode)",
        "mode_key": "Transform Mode",
        "no_text": "Please provide text after the .rp command",
        "error": "Error transforming text: "
    },
    "pt-br": {
        "transform_modes": ["Emoji", "Código Morse", "Binário", "Texto Bolha", "De Cabeça pra Baixo", "Vaporwave"],
        "emoji_categories": ["Feliz", "Triste", "Amor", "Animais", "Comida", "Atividades", "Misto"],
        "usage": "Uso: .rp [sua mensagem]",
        "settings_header": "Configurações do Transformador de Texto",
        "category_key": "Categoria de Emoji (para modo emoji)",
        "mode_key": "Modo de Transformação",
        "no_text": "Por favor, forneça texto após o comando .rp",
        "error": "Erro ao transformar texto: "
    },
    "ru": {
        "transform_modes": ["Эмодзи", "Азбука Морзе", "Бинарный", "Пузырьковый текст", "Вверх ногами", "Вапорвейв"],
        "emoji_categories": ["Счастливый", "Грустный", "Любовь", "Животные", "Еда", "Активности", "Смешанный"],
        "usage": "Использование: .rp [ваше сообщение]",
        "settings_header": "Настройки преобразователя текста",
        "category_key": "Категория эмодзи (для режима эмодзи)",
        "mode_key": "Режим преобразования",
        "no_text": "Пожалуйста, введите текст после команды .rp",
        "error": "Ошибка преобразования текста: "
    }
}

EMOJI_CATEGORIES = {
    "happy": ["😀", "😁", "😂", "🤣", "😃", "😄", "😅", "😆", "😉", "😊", "😋", "😎"],
    "sad": ["☹️", "😣", "😖", "😫", "😩", "😢", "😭", "😤", "😠", "😡", "🤬", "😞"],
    "love": ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "❣️", "💕", "💞", "💓", "💗"],
    "animals": ["🐶", "🐱", "🦊", "🐻", "🐼", "🐨", "🐯", "🦁", "🐮", "🐷", "🐸", "🐵"],
    "food": ["🍎", "🍕", "🍔", "🍟", "🌮", "🍗", "🍖", "🍙", "🍣", "🍩", "🍪", "🍫"],
    "activities": ["⚽", "🏀", "🏈", "⚾", "🎾", "🏐", "🏉", "🎱", "🏓", "🏸", "🥊", "🎮"],
    "mixed": ["💯", "🔥", "✨", "🎉", "🎊", "🎁", "👍", "👌", "👏", "🙏", "🤝", "🌈"]
}

MORSE_CODE = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..',
    '0': '-----', '1': '.----', '2': '..---', '3': '...--', '4': '....-',
    '5': '.....', '6': '-....', '7': '--...', '8': '---..', '9': '----.',
    '.': '.-.-.-', ',': '--..--', '?': '..--..', "'": '.----.', '!': '-.-.--',
    '/': '-..-.', '(': '-.--.', ')': '-.--.-', '&': '.-...', ':': '---...',
    ';': '-.-.-.', '=': '-...-', '+': '.-.-.', '-': '-....-', '_': '..--.-',
    '"': '.-..-.', '$': '...-..-', '@': '.--.-.',
    'А': '.-', 'Б': '-...', 'В': '.--', 'Г': '--.', 'Д': '-..', 'Е': '.', 'Ё': '.',
    'Ж': '...-', 'З': '--..', 'И': '..', 'Й': '.---', 'К': '-.-', 'Л': '.-..',
    'М': '--', 'Н': '-.', 'О': '---', 'П': '.--.', 'Р': '.-.', 'С': '...',
    'Т': '-', 'У': '..-', 'Ф': '..-.', 'Х': '....', 'Ц': '-.-.', 'Ч': '---.',
    'Ш': '----', 'Щ': '--.-', 'Ъ': '.--.-.', 'Ы': '-.--', 'Ь': '-..-',
    'Э': '..-..', 'Ю': '..--', 'Я': '.-.-'
}

def to_binary(text):
    return ' '.join(format(ord(char), '08b') for char in text)

BUBBLE_CHARS = {
    'a': 'ⓐ', 'b': 'ⓑ', 'c': 'ⓒ', 'd': 'ⓓ', 'e': 'ⓔ', 'f': 'ⓕ', 'g': 'ⓖ',
    'h': 'ⓗ', 'i': 'ⓘ', 'j': 'ⓙ', 'k': 'ⓚ', 'l': 'ⓛ', 'm': 'ⓜ', 'n': 'ⓝ',
    'o': 'ⓞ', 'p': 'ⓟ', 'q': 'ⓠ', 'r': 'ⓡ', 's': 'ⓢ', 't': 'ⓣ', 'u': 'ⓤ',
    'v': 'ⓥ', 'w': 'ⓦ', 'x': 'ⓧ', 'y': 'ⓨ', 'z': 'ⓩ',
    'A': 'Ⓐ', 'B': 'Ⓑ', 'C': 'Ⓒ', 'D': 'Ⓓ', 'E': 'Ⓔ', 'F': 'Ⓕ', 'G': 'Ⓖ',
    'H': 'Ⓗ', 'I': 'Ⓘ', 'J': 'Ⓙ', 'K': 'Ⓚ', 'L': 'Ⓛ', 'M': 'Ⓜ', 'N': 'Ⓝ',
    'O': 'Ⓞ', 'P': 'Ⓟ', 'Q': 'Ⓠ', 'R': 'Ⓡ', 'S': 'Ⓢ', 'T': 'Ⓣ', 'U': 'Ⓤ',
    'V': 'Ⓥ', 'W': 'Ⓦ', 'X': 'Ⓧ', 'Y': 'Ⓨ', 'Z': 'Ⓩ',
    '0': '⓪', '1': '①', '2': '②', '3': '③', '4': '④',
    '5': '⑤', '6': '⑥', '7': '⑦', '8': '⑧', '9': '⑨'
}

FLIP_CHARS = {
    'a': 'ɐ', 'b': 'q', 'c': 'ɔ', 'd': 'p', 'e': 'ǝ', 'f': 'ɟ', 'g': 'ƃ',
    'h': 'ɥ', 'i': 'ᴉ', 'j': 'ɾ', 'k': 'ʞ', 'l': 'l', 'm': 'ɯ', 'n': 'u',
    'o': 'o', 'p': 'p', 'q': 'b', 'r': 'ɹ', 's': 's', 't': 'ʇ', 'u': 'n',
    'v': 'ʌ', 'w': 'ʍ', 'x': 'x', 'y': 'ʎ', 'z': 'z',
    'A': '∀', 'B': 'B', 'C': 'Ɔ', 'D': 'D', 'E': 'Ǝ', 'F': 'Ⅎ', 'G': 'פ',
    'H': 'H', 'I': 'I', 'J': 'ſ', 'K': 'K', 'L': '˥', 'M': 'W', 'N': 'N',
    'O': 'O', 'P': 'Ԁ', 'Q': 'Q', 'R': 'R', 'S': 'S', 'T': '┴', 'U': '∩',
    'V': 'Λ', 'W': 'M', 'X': 'X', 'Y': '⅄', 'Z': 'Z',
    '0': '0', '1': 'Ɩ', '2': 'ᄅ', '3': 'Ɛ', '4': 'ㄣ',
    '5': 'ϛ', '6': '9', '7': 'ㄥ', '8': '8', '9': '6',
    '.': '˙', ',': '\'', '?': '¿', '!': '¡', "'": ',',
    '"': ',,', '(': ')', ')': '(', '[': ']', ']': '[',
    '{': '}', '}': '{', '<': '>', '>': '<', '&': '⅋'
}

FLIP_CHARS_CYRILLIC = {
    'А': '∀', 'Б': 'Ԑ', 'В': 'ꓭ', 'Г': '⅁', 'Д': 'ᗡ', 'Е': 'Ǝ', 'Ё': 'Ǝ', 'Ж': 'Ӝ', 'З': 'Ɛ',
    'И': 'И', 'Й': 'И', 'К': 'ʞ', 'Л': '˥', 'М': 'W', 'Н': 'H', 'О': 'O', 'П': 'П', 'Р': 'ꓤ',
    'С': 'Ɔ', 'Т': '┴', 'У': 'ʎ', 'Ф': 'Ф', 'Х': 'X', 'Ц': 'Ц', 'Ч': 'Ч', 'Ш': 'Ш', 'Щ': 'Щ',
    'Ъ': 'Ъ', 'Ы': 'Ы', 'Ь': 'Ь', 'Э': 'Э', 'Ю': 'Ю', 'Я': 'ʁ',
    'а': 'ɐ', 'б': 'ƍ', 'в': 'ʚ', 'г': 'ɹ', 'д': 'ɓ', 'е': 'ǝ', 'ё': 'ǝ', 'ж': 'ж', 'з': 'ε',
    'и': 'и', 'й': 'и', 'к': 'ʞ', 'л': 'ʃ', 'м': 'w', 'н': 'н', 'о': 'o', 'п': 'u', 'р': 'd',
    'с': 'ɔ', 'т': 'ʇ', 'у': 'ʎ', 'ф': 'ф', 'х': 'х', 'ц': 'ц', 'ч': 'ч', 'ш': 'ш', 'щ': 'щ',
    'ъ': 'ъ', 'ы': 'ы', 'ь': 'ь', 'э': 'э', 'ю': 'ю', 'я': 'ʁ'
}

def _apply_mapping(text, *mappings, reverse=False):
    result = ""
    for char in text:
        mapped = None
        for mapping in mappings:
            if char in mapping:
                mapped = mapping[char]
                break
        if mapped is not None:
            if reverse:
                result = mapped + result
            else:
                result += mapped
        else:
            if reverse:
                result = char + result
            else:
                result += char
    return result

def to_vaporwave(text):
    result = ""
    for char in text:
        if ord(char) >= 33 and ord(char) <= 126:
            result += chr(ord(char) + 0xFEE0)
        elif char == " ":
            result += "　"
        elif 'А' <= char <= 'я' or char == 'Ё' or char == 'ё':
            result += char
        else:
            result += char
    return result

class TextTransformerPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.all_emojis = []
        for category in EMOJI_CATEGORIES.values():
            self.all_emojis.extend(category)

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        log("Text Transformer Plugin loaded")

    def on_plugin_unload(self):
        self.remove_on_send_message_hook()

    def _get_lang(self):
        lang = Locale.getDefault().getLanguage().lower()
        if lang.startswith("pt"):
            return "pt-br"
        if lang.startswith("ru"):
            return "ru"
        return "en"

    def create_settings(self):
        lang_key = self._get_lang()
        l10n = LANGS.get(lang_key, LANGS["en"])
        return [
            Header(text=l10n["settings_header"]),
            Selector(
                key="transform_mode",
                text=l10n["mode_key"],
                default=0,
                items=l10n["transform_modes"]
            ),
            Selector(
                key="emoji_category",
                text=l10n["category_key"],
                default=6,
                items=l10n["emoji_categories"]
            ),
            Divider(text=l10n["usage"]),
        ]

    def _transform_text(self, text):
        lang = self._get_lang()
        mode = self.get_setting("transform_mode", 0)
        if mode == 0:
            return self._to_emoji(text)
        elif mode == 1:
            return self._to_morse(text)
        elif mode == 2:
            return to_binary(text)
        elif mode == 3:
            return self._to_bubble(text)
        elif mode == 4:
            return self._to_upside_down(text)
        elif mode == 5:
            return to_vaporwave(text)
        return text

    def _to_emoji(self, text):
        category_index = self.get_setting("emoji_category", 6)
        categories = list(EMOJI_CATEGORIES.keys())
        if 0 <= category_index < len(categories):
            emoji_list = EMOJI_CATEGORIES[categories[category_index]]
        else:
            emoji_list = self.all_emojis
        if not emoji_list:
            emoji_list = ["🙂"]
        result = ""
        for char in text:
            if char.isspace():
                result += char
            else:
                result += random.choice(emoji_list)
        return result

    def _to_morse(self, text):
        result = []
        for char in text:
            upper = char.upper()
            if char == ' ':
                result.append('/')
            elif upper in MORSE_CODE:
                result.append(MORSE_CODE[upper])
            else:
                result.append(char)
        return ' '.join(result)

    def _to_bubble(self, text):
        return _apply_mapping(text, BUBBLE_CHARS)

    def _to_upside_down(self, text):
        result = ""
        for char in text:
            if char in FLIP_CHARS:
                result = FLIP_CHARS[char] + result
            elif char in FLIP_CHARS_CYRILLIC:
                result = FLIP_CHARS_CYRILLIC[char] + result
            else:
                result = char + result
        return result

    def on_send_message_hook(self, account: int, params):
        lang_key = self._get_lang()
        l10n = LANGS.get(lang_key, LANGS["en"])
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
        message = params.message.strip()
        if not message.startswith(".rp "):
            return HookResult()
        original_text = message[4:].strip()
        if not original_text:
            params.message = l10n["no_text"]
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        try:
            result = self._transform_text(original_text)
            params.message = result
            if not hasattr(params, "entities") or params.entities is None:
                params.entities = []
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        except Exception as e:
            log(f"{l10n['error']}{e}")
            params.message = f"{l10n['error']}{str(e)}"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)