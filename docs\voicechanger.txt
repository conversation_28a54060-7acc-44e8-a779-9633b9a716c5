import traceback
from base_plugin import BasePlugin
from hook_utils import find_class
from ui.settings import Selector
from base_plugin import MenuItemData, MenuItemType
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity
from android_utils import run_on_ui_thread
from java import jint

__name__ = "VoiceChanger"
__description__ = "Change your voice"
__version__ = "2.3.0"
__id__ = "voice_changer"
__author__ = "@itsv1eds"
__icon__ = "exteraPluginsSup/2"
__min_version__ = "11.12.0"

VOICE_TYPES = [
    ("Default", 48000, None),  
    ("Male", 16000, None),      
    ("Dinosaur", 8000, None),      
]

class VoiceChangerPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.current_hook = None
        self.current_voice_index = None
        self.menu_item = None

    def create_settings(self):
        return [
            Selector(
                key="voice_type",
                text="Voice type",
                items=[v[0] for v in VOICE_TYPES],
                default=0,
                on_change=lambda v: self._apply_hook()
            )
        ]

    def on_plugin_load(self):
        self._apply_hook()
        self._add_settings_menu_item()

    def on_plugin_unload(self):
        self._remove_hook()
        if self.menu_item:
            self.remove_menu_item(self.menu_item)
            self.menu_item = None

    def _apply_hook(self):
        self._remove_hook()
        voice_index = self.get_setting("voice_type", 0)
        self.current_voice_index = voice_index
        try:
            media_controller_class = find_class("org.telegram.messenger.MediaController")
            if not media_controller_class:
                self.log("MediaController class not found")
                return
            string_class = find_class("java.lang.String")
            int_class = find_class("java.lang.Integer").TYPE
            start_record_method = media_controller_class.getClass().getDeclaredMethod("startRecord", string_class, int_class)
            if start_record_method:
                name, sr, buf = VOICE_TYPES[voice_index]
                self.current_hook = self.hook_method(start_record_method, VoiceTypeHook(self, sr, buf))
                self.log(f"Hooked voice type: {name} ({sr} Hz, bufferSize={buf})")
            else:
                self.log("startRecord method not found")
        except Exception as e:
            self.log(f"Failed to apply hook: {e}\n{traceback.format_exc()}")

    def _remove_hook(self):
        if self.current_hook:
            self.unhook_method(self.current_hook)
            self.current_hook = None

    def _open_plugin_settings(self):
        try:
            run_on_ui_thread(lambda: self._open_settings_activity())
        except Exception as e:
            self.log(f"Error opening plugin settings: {e}")

    def _open_settings_activity(self):
        try:
            PluginsController.getInstance().plugins.get(self.id)
            get_last_fragment = __import__('client_utils').get_last_fragment
            get_last_fragment().presentFragment(PluginSettingsActivity(PluginsController.getInstance().plugins.get(self.id)))
        except Exception as e:
            self.log(f"Error presenting settings activity: {e}")

    def _add_settings_menu_item(self):
        try:
            self.menu_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text="VoiceChanger Settings",
                icon="msg_settings_hw",
                priority=5,
                on_click=lambda ctx: self._open_plugin_settings()
            ))
        except Exception as e:
            self.log(f"Failed to add settings menu item: {e}")

    def log(self, message):
        super().log(f"[{__id__}] {message}")

class VoiceTypeHook:
    def __init__(self, plugin, sample_rate, buffer_size):
        self.plugin = plugin
        self.sample_rate = sample_rate
        self.buffer_size = buffer_size
    def before_hooked_method(self, param):
        param.args[1] = jint(self.sample_rate)
        if self.buffer_size:
            try:
                media_controller = param.thisObject
                media_controller.bufferSize = jint(self.buffer_size)
                print(f"[voice_changer] Voice type: {self.sample_rate} Hz, bufferSize: {self.buffer_size}")
            except Exception as e:
                print(f"[voice_changer] Error setting bufferSize: {e}")
        else:
            print(f"[voice_changer] Voice type: {self.sample_rate} Hz") 