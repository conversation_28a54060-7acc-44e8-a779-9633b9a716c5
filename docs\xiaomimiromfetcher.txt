from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from android_utils import log
from markdown_utils import parse_markdown

import requests

__id__ = "xiaomiotaprober"
__name__ = "Xiaomi Fastboot ROM fetcher"
__description__ = "Fastboot ROM fetcher"
__author__ = "@romashkagene"
__version__ = "1.2"
__min_version__ = "11.9.0"
__icon__ = "technoGeekPack/24"

global_regions = [
    "eea", "ru", "id", "in", "tr", "kr", "tw"
]

def get_rom_info(device: str, branch: str, region: str):
    url = f"https://update.intl.miui.com/updates/miota-fullrom.php?d={device}&b={branch}&r={region.lower()}"
    if "_dc" in device:
        url += "&n=dc"

    r = requests.get(url)

    if r.status_code == 200:
        if len(r.text) != 0:
            json = r.json()
            latest_full_rom = json.get("LatestFullRom")
            if latest_full_rom:
                type = latest_full_rom.get('type')
                device = latest_full_rom.get('device')
                filename = latest_full_rom.get('filename')
                filesize = latest_full_rom.get('filesize')
                codebase = latest_full_rom.get("codebase")
                version = latest_full_rom.get("version")

                return latest_full_rom

    return None

def get_ota_info(device, branch, region):
    codename = device

    if region in global_regions:
        device += f"_{region}_global"
    elif region == "global":
        device += "_global"
    elif region == "cn":
        pass
    else:
        device += f"_{region}"
        region = "cn"

    rom_info = get_rom_info(device, branch, region)

    if rom_info:
        type = rom_info.get('type')
        device = rom_info.get('device')
        filename = rom_info.get('filename')
        filesize = rom_info.get('filesize')
        codebase = rom_info.get("codebase")
        version = rom_info.get("version")
        
        return (
            f"• *Device*: {codename.replace('_', f'{chr(92)}_')}\n" # dumb way to fix 'f-string expression part cannot include a backslash' 
            f"• *Version*: {version}\n"
            f"• *Size*: {filesize}\n\n"
            f"[DOWNLOAD](https://bkt-sgp-miui-ota-update-alisgp.oss-ap-southeast-1.aliyuncs.com/{version}/{filename})"
        )

    return None

class XiaomiOtaPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_send_message_hook(self, account, params) -> HookStrategy:
        try:
            if not isinstance(params.message, str):
                return HookResult()

            if not params.message.startswith(".ximi"):
                return HookResult()

            try:
                device = params.message.split(" ")[1]
                region = params.message.split(" ")[2] if params.message.split(" ").__len__() > 2 else "cn"

                info = get_ota_info(device, branch="F", region=region)
                if not info:
                    params.message = f"No ROM found for {device}"

                    return HookResult(strategy=HookStrategy.MODIFY, params=params)

                if not hasattr(params, "entities") or params.entities is None:
                    params.entities = []

                parsed_message = parse_markdown(info)
                params.message = parsed_message.text
                for i in parsed_message.entities:
                    params.entities.add(i.to_tlrpc_object())

                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            except Exception as e:
                error_msg = f"Error retrieving OTA information: {str(e)}"
                params.message = error_msg
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except Exception as e:
            log(f"DonoMod plugin error: {str(e)}")
            return HookResult()
