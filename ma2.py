#!/usr/bin/env python3
"""
Оптимизированный запуск семи ботов одновременно (включая Подкаст бот и Weather бот)
"""
import asyncio
import logging
import sys
import os
import signal
import multiprocessing
from datetime import datetime

# Фильтр для исключения спам-сообщений из логов
class GlobalSpamFilter(logging.Filter):
    def filter(self, record):
        message = record.getMessage()
        # Исключаем спам-сообщения
        spam_patterns = [
            "Got difference for channel",
            "Обрабатываем сообщение из чата поддержки:",
            "Reply на сообщение",
            "но связанного вопроса в чате сотрудников нет",
            "Отправлен краткий вопрос:",
            "Сохранена связь:",
            "Запущена анимация Deep Research",
            "Конкретизированный вопрос:",
            "Остановлена анимация и восстановлен чистый текст",
            "Записан период",
            "для",
            "Не удалось распарсить строку лога:",
            "Пользователь",
            "нажал кнопку",
            "достиг лимита",
            "Уведомление запланировано",
            "Уведомил пользователя",
            "об окончании кулдауна",
            "Состояние FSM очищено для пользователя",
            "после успешной генерации",
            "В reply обнаружен новый вопрос:",
            "telethon.client.updates",
            "aiogram.event",
            "aiogram.dispatcher",
            "aiohttp.access",
            "Task exception was never retrieved",
            "TimedOut",
            "ConnectTimeout",
            "httpcore.ConnectTimeout",
            "httpx.ConnectTimeout"
        ]
        for pattern in spam_patterns:
            if pattern in message:
                return False  # Не логировать это сообщение
        return True  # Логировать все остальные сообщения

# Оптимизированная настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

# Добавляем фильтр ко всем обработчикам
spam_filter = GlobalSpamFilter()
for handler in logging.getLogger().handlers:
    handler.addFilter(spam_filter)

# Отключаем спам от библиотек
logging.getLogger('telethon.client.updates').setLevel(logging.WARNING)
logging.getLogger('aiogram.event').setLevel(logging.ERROR)
logging.getLogger('aiogram.dispatcher').setLevel(logging.ERROR)
logging.getLogger('aiohttp.access').setLevel(logging.WARNING)
logging.getLogger('telegram.ext.updater').setLevel(logging.WARNING)
logging.getLogger('telegram.ext.application').setLevel(logging.WARNING)
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('httpcore').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.ERROR)

# Сохраняем исходную директорию в глобальной переменной
ORIGINAL_DIR = os.getcwd()

# Глобальное событие для координации остановки всех ботов (будет инициализировано в main)
shutdown_event = None
# Список всех активных задач ботов
active_tasks = []



async def run_pluginmaker_bot():
    """Запуск PluginMaker бота (асинхронного, в основном цикле)"""
    try:
        print("⚙️ Запуск PluginMaker бота...")
        # Бот теперь находится в папке PluginLab
        pluginmaker_dir = ORIGINAL_DIR
        if pluginmaker_dir not in sys.path:
            sys.path.insert(0, pluginmaker_dir)
        # Проверяем, что папка PluginLab существует
        pluginlab_dir = os.path.join(pluginmaker_dir, "PluginLab")
        if not os.path.exists(pluginlab_dir):
            print(f"❌ Папка {pluginlab_dir} не найдена!")
            return
        # Импортируем и запускаем из PluginLab
        from PluginLab import main as pluginmaker_main
        # Запускаем PluginMaker с возможностью отмены
        pluginmaker_task = asyncio.create_task(pluginmaker_main())
        shutdown_task = asyncio.create_task(shutdown_event.wait())

        done, pending = await asyncio.wait(
            [pluginmaker_task, shutdown_task],
            return_when=asyncio.FIRST_COMPLETED
        )

        # Если получен сигнал остановки, отменяем задачу
        if shutdown_task in done:
            print("🛑 PluginMaker бот получил сигнал остановки")
            pluginmaker_task.cancel()
            try:
                await pluginmaker_task
            except asyncio.CancelledError:
                print("🛑 PluginMaker бот остановлен")
        else:
            # Если pluginmaker_task завершился, отменяем shutdown_task
            shutdown_task.cancel()
    except Exception as e:
        print(f"❌ Критическая ошибка в PluginMaker боте: {e}")
        import traceback
        traceback.print_exc()

async def run_userbot():
    """Запуск юзербота (асинхронного, в основном цикле)"""
    try:
        print("🤖 Запуск юзербота...")
        userbot_dir = os.path.join(ORIGINAL_DIR, "usbot")
        if not os.path.exists(userbot_dir):
            print(f"❌ Директория юзербота не найдена: {userbot_dir}")
            return
        if userbot_dir not in sys.path:
            sys.path.insert(0, userbot_dir)
        # Проверяем, что файл userbot.py существует
        bot_file = os.path.join(userbot_dir, "userbot.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        # Временно меняем директорию для импорта
        original_cwd = os.getcwd()
        os.chdir(userbot_dir)
        try:
            # Импортируем и запускаем
            from userbot import start_userbot
            os.chdir(original_cwd)
            # Запускаем юзербота с возможностью отмены
            userbot_task = asyncio.create_task(start_userbot())
            shutdown_task = asyncio.create_task(shutdown_event.wait())

            done, pending = await asyncio.wait(
                [userbot_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Если получен сигнал остановки, отменяем задачу
            if shutdown_task in done:
                print("🛑 Юзербот получил сигнал остановки")
                userbot_task.cancel()
                try:
                    await userbot_task
                except asyncio.CancelledError:
                    print("🛑 Юзербот остановлен")
            else:
                # Если userbot_task завершился, отменяем shutdown_task
                shutdown_task.cancel()
        finally:
            if os.getcwd() != original_cwd:
                os.chdir(original_cwd)
    except Exception as e:
        print(f"❌ Критическая ошибка в юзерботе: {e}")
        import traceback
        traceback.print_exc()




async def run_work_bot():
    """Запуск Work бота (AI Workshop Bot) (асинхронного, в основном цикле)"""
    try:
        print("🔧 Запуск Work бота (AI Workshop Bot)...")
        work_dir = os.path.join(ORIGINAL_DIR, "work")
        if not os.path.exists(work_dir):
            print(f"❌ Директория Work бота не найдена: {work_dir}")
            return
        if work_dir not in sys.path:
            sys.path.insert(0, work_dir)
        # Проверяем, что файл work_bot.py существует
        bot_file = os.path.join(work_dir, "work_bot.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        # Временно меняем директорию для импорта
        original_cwd = os.getcwd()
        os.chdir(work_dir)
        try:
            # Импортируем и запускаем асинхронную функцию
            from work_bot import main as work_main
            os.chdir(original_cwd)
            # Запускаем Work бота с возможностью отмены
            work_task = asyncio.create_task(work_main())
            shutdown_task = asyncio.create_task(shutdown_event.wait())

            done, pending = await asyncio.wait(
                [work_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Если получен сигнал остановки, отменяем задачу
            if shutdown_task in done:
                print("🛑 Work бот получил сигнал остановки")
                work_task.cancel()
                try:
                    await work_task
                except asyncio.CancelledError:
                    print("🛑 Work бот остановлен")
            else:
                # Если work_task завершился, отменяем shutdown_task
                shutdown_task.cancel()
        finally:
            if os.getcwd() != original_cwd:
                os.chdir(original_cwd)
    except Exception as e:
        print(f"❌ Критическая ошибка в Work боте: {e}")
        import traceback
        traceback.print_exc()


async def run_extra_userbot():
    """Запуск Extra юзербота (асинхронного, в основном цикле)"""
    try:
        print("🤖 Запуск Extra юзербота...")
        extra_userbot_dir = os.path.join(ORIGINAL_DIR, "EXTRA USBOT")
        if not os.path.exists(extra_userbot_dir):
            print(f"❌ Директория Extra юзербота не найдена: {extra_userbot_dir}")
            return
        if extra_userbot_dir not in sys.path:
            sys.path.insert(0, extra_userbot_dir)
        # Проверяем, что файл extra_main.py существует
        bot_file = os.path.join(extra_userbot_dir, "extra_main.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        # Временно меняем директорию для импорта
        original_cwd = os.getcwd()
        os.chdir(extra_userbot_dir)
        try:
            # Импортируем и запускаем
            from extra_main import TelegramUserBot
            os.chdir(original_cwd)
            # Создаем экземпляр бота и запускаем его
            extra_bot = TelegramUserBot()
            extra_userbot_task = asyncio.create_task(extra_bot.run())
            shutdown_task = asyncio.create_task(shutdown_event.wait())

            done, pending = await asyncio.wait(
                [extra_userbot_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Если получен сигнал остановки, отменяем задачу
            if shutdown_task in done:
                print("🛑 Extra юзербот получил сигнал остановки")
                extra_userbot_task.cancel()
                try:
                    await extra_userbot_task
                except asyncio.CancelledError:
                    print("🛑 Extra юзербот остановлен")
            else:
                # Если extra_userbot_task завершился, отменяем shutdown_task
                shutdown_task.cancel()
        finally:
            if os.getcwd() != original_cwd:
                os.chdir(original_cwd)
    except Exception as e:
        print(f"❌ Критическая ошибка в Extra юзерботе: {e}")
        import traceback
        traceback.print_exc()


async def run_alina_bot():
    """Запуск Дима бота (асинхронного, в основном цикле)"""
    try:
        print("🤖 Запуск Дима бота...")
        alina_dir = os.path.join(ORIGINAL_DIR, "ali")
        if not os.path.exists(alina_dir):
            print(f"❌ Директория Дима бота не найдена: {alina_dir}")
            return
        if alina_dir not in sys.path:
            sys.path.insert(0, alina_dir)
        # Проверяем, что файл dima_main.py существует
        bot_file = os.path.join(alina_dir, "dima_main.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        # Временно меняем директорию для импорта
        original_cwd = os.getcwd()
        os.chdir(alina_dir)
        try:
            # Импортируем и запускаем асинхронную функцию
            from dima_main import main as dima_main
            os.chdir(original_cwd)
            # Запускаем Дима бота с возможностью отмены
            dima_task = asyncio.create_task(dima_main())
            shutdown_task = asyncio.create_task(shutdown_event.wait())

            done, pending = await asyncio.wait(
                [dima_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Если получен сигнал остановки, отменяем задачу
            if shutdown_task in done:
                print("🛑 Дима бот получил сигнал остановки")
                dima_task.cancel()
                try:
                    await dima_task
                except asyncio.CancelledError:
                    print("🛑 Дима бот остановлен")
            else:
                # Если dima_task завершился нормально, отменяем shutdown_task
                shutdown_task.cancel()
                # Проверяем результат выполнения
                try:
                    result = dima_task.result()
                    print("✅ Дима бот завершился нормально")
                except Exception as task_error:
                    print(f"❌ Дима бот завершился с ошибкой: {task_error}")
        finally:
            if os.getcwd() != original_cwd:
                os.chdir(original_cwd)
    except Exception as e:
        print(f"❌ Критическая ошибка в Дима боте: {e}")
        import traceback
        traceback.print_exc()


async def run_podcast_bot():
    """Запуск Подкаст бота (синхронный бот в асинхронном wrapper)"""
    try:
        print("🎙️ Запуск Подкаст бота...")
        podcast_dir = os.path.join(ORIGINAL_DIR, "Бот Подкаст")
        if not os.path.exists(podcast_dir):
            print(f"❌ Директория Подкаст бота не найдена: {podcast_dir}")
            return
        if podcast_dir not in sys.path:
            sys.path.insert(0, podcast_dir)
        # Проверяем, что файл main.py существует
        bot_file = os.path.join(podcast_dir, "main.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        try:
            # Динамический импорт модуля из конкретного файла
            import importlib.util
            spec = importlib.util.spec_from_file_location("podcast_main", bot_file)
            podcast_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(podcast_module)
            podcast_start_bot = podcast_module.start_bot

            # Запускаем синхронный бот в отдельном потоке
            import concurrent.futures

            # Создаем executor для синхронного кода
            loop = asyncio.get_event_loop()

            def run_sync_bot():
                """Запуск синхронного бота"""
                try:
                    podcast_start_bot()
                except Exception as e:
                    print(f"❌ Ошибка в синхронном Подкаст боте: {e}")
                    import traceback
                    traceback.print_exc()

            # Запускаем в thread pool
            podcast_task = loop.run_in_executor(None, run_sync_bot)
            shutdown_task = asyncio.create_task(shutdown_event.wait())

            done, pending = await asyncio.wait(
                [podcast_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Если получен сигнал остановки
            if shutdown_task in done:
                print("🛑 Подкаст бот получил сигнал остановки")
                # Отменяем задачу
                podcast_task.cancel()
                try:
                    await podcast_task
                except asyncio.CancelledError:
                    print("🛑 Подкаст бот остановлен")
            else:
                # Если podcast_task завершился, отменяем shutdown_task
                shutdown_task.cancel()
                try:
                    result = await podcast_task
                    print("✅ Подкаст бот завершился нормально")
                except Exception as task_error:
                    print(f"❌ Подкаст бот завершился с ошибкой: {task_error}")
        except Exception as import_error:
            print(f"❌ Ошибка импорта Подкаст бота: {import_error}")
            import traceback
            traceback.print_exc()
    except Exception as e:
        print(f"❌ Критическая ошибка в Подкаст боте: {e}")
        import traceback
        traceback.print_exc()


async def run_weather_bot():
    """Запуск Weather бота (асинхронного, в основном цикле)"""
    try:
        print("🌤️ Запуск Weather бота...")
        weather_dir = os.path.join(ORIGINAL_DIR, "Weather")
        if not os.path.exists(weather_dir):
            print(f"❌ Директория Weather бота не найдена: {weather_dir}")
            return
        if weather_dir not in sys.path:
            sys.path.insert(0, weather_dir)
        # Проверяем, что файл weather_main.py существует
        bot_file = os.path.join(weather_dir, "weather_main.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        # Временно меняем директорию для импорта
        original_cwd = os.getcwd()
        os.chdir(weather_dir)
        try:
            # Импортируем и запускаем асинхронную функцию
            from weather_main import run_bot_async
            os.chdir(original_cwd)
            # Запускаем Weather бота с возможностью отмены
            weather_task = asyncio.create_task(run_bot_async())
            shutdown_task = asyncio.create_task(shutdown_event.wait())

            done, pending = await asyncio.wait(
                [weather_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Если получен сигнал остановки, отменяем задачу
            if shutdown_task in done:
                print("🛑 Weather бот получил сигнал остановки")
                weather_task.cancel()
                try:
                    await weather_task
                except asyncio.CancelledError:
                    print("🛑 Weather бот остановлен")
            else:
                # Если weather_task завершился, отменяем shutdown_task
                shutdown_task.cancel()
        finally:
            if os.getcwd() != original_cwd:
                os.chdir(original_cwd)
    except Exception as e:
        print(f"❌ Критическая ошибка в Weather боте: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Оптимизированная главная асинхронная функция для запуска ботов"""
    global active_tasks, shutdown_event

    # Инициализируем событие остановки
    shutdown_event = asyncio.Event()

    print("🚀 Запуск семи ботов (включая Work бот, Extra юзербот, Дима бот, Подкаст бот и Weather бот)...")
    print("=" * 50)
    print(f"📍 Рабочая директория: {ORIGINAL_DIR}")

    try:
        # 1. Запускаем PluginMaker бота (асинхронный) как задачу
        print("⚙️ Запускаем PluginMaker бота как асинхронную задачу...")
        pluginmaker_task = asyncio.create_task(run_pluginmaker_bot(), name="PluginMaker")
        active_tasks.append(pluginmaker_task)

        # Небольшая пауза для инициализации (уменьшена с 2 до 0.5 секунды)
        print("⏳ Ждем инициализации PluginMaker бота...")
        await asyncio.sleep(0.5)

        # 2. Запускаем юзербота (асинхронный) как задачу
        print("🤖 Запускаем юзербота как асинхронную задачу...")
        userbot_task = asyncio.create_task(run_userbot(), name="Userbot")
        active_tasks.append(userbot_task)

        # Небольшая пауза для инициализации
        print("⏳ Ждем инициализации юзербота...")
        await asyncio.sleep(0.5)

        # 3. Запускаем Дима бота (асинхронный) как задачу
        print("🤖 Запускаем Дима бота как асинхронную задачу...")
        dima_task = asyncio.create_task(run_alina_bot(), name="DimaBot")
        active_tasks.append(dima_task)

        # Небольшая пауза для инициализации
        print("⏳ Ждем инициализации Дима бота...")
        await asyncio.sleep(0.5)



        # 4. Запускаем Work бот (асинхронный) как задачу
        print("🔧 Запускаем Work бот как асинхронную задачу...")
        work_task = asyncio.create_task(run_work_bot(), name="WorkBot")
        active_tasks.append(work_task)

        # Небольшая пауза для инициализации
        print("⏳ Ждем инициализации Work бота...")
        await asyncio.sleep(0.5)

        # 5. Запускаем Extra юзербота (асинхронный) как задачу
        print("🤖 Запускаем Extra юзербота как асинхронную задачу...")
        extra_userbot_task = asyncio.create_task(run_extra_userbot(), name="ExtraUserbot")
        active_tasks.append(extra_userbot_task)

        # Небольшая пауза для инициализации
        print("⏳ Ждем инициализации Extra юзербота...")
        await asyncio.sleep(0.5)

        # 6. Запускаем Подкаст бота (синхронный в thread pool) как задачу
        print("🎙️ Запускаем Подкаст бота как асинхронную задачу...")
        podcast_task = asyncio.create_task(run_podcast_bot(), name="PodcastBot")
        active_tasks.append(podcast_task)

        # Небольшая пауза для инициализации
        print("⏳ Ждем инициализации Подкаст бота...")
        await asyncio.sleep(0.5)

        # 7. Запускаем Weather бота (асинхронный) как задачу
        print("🌤️ Запускаем Weather бота как асинхронную задачу...")
        weather_task = asyncio.create_task(run_weather_bot(), name="WeatherBot")
        active_tasks.append(weather_task)

        print("✅ Все боты запущены! Система работает...")

        # Ожидаем только события остановки (боты должны работать бесконечно)
        try:
            # Создаем задачу мониторинга ботов
            async def monitor_bots():
                while not shutdown_event.is_set():
                    for task in active_tasks:
                        if task.done():
                            try:
                                result = task.result()
                                print(f"⚠️ Бот {task.get_name()} завершился: {result}")
                            except Exception as e:
                                print(f"❌ Бот {task.get_name()} завершился с ошибкой: {e}")
                    await asyncio.sleep(1)

            # Запускаем мониторинг в фоне
            monitor_task = asyncio.create_task(monitor_bots())

            # Ждем только сигнала остановки, не завершения ботов
            await shutdown_event.wait()

            # Останавливаем мониторинг
            monitor_task.cancel()

            print("🛑 Получено событие остановки, отменяем все задачи...")
            for task in active_tasks:
                if not task.done():
                    task.cancel()

            # Ждем завершения отмененных задач
            await asyncio.gather(*active_tasks, return_exceptions=True)

        except asyncio.CancelledError:
            print("\n🛑 Главная задача была отменена")
            # Отменяем все дочерние задачи
            for task in active_tasks:
                if not task.done():
                    task.cancel()
            # Ждем завершения отмененных задач
            await asyncio.gather(*active_tasks, return_exceptions=True)

    except KeyboardInterrupt:
        print("\n🛑 Получен сигнал остановки (KeyboardInterrupt)...")
        # Отменяем все задачи
        for task in active_tasks:
            if not task.done():
                task.cancel()
        # Ждем завершения отмененных задач
        await asyncio.gather(*active_tasks, return_exceptions=True)

    finally:
        # Корректное завершение всех процессов
        print("🛑 Завершение работы всех ботов...")
        print("🏁 Завершение работы лаунчера.")

def signal_handler(signum, frame):
    """Обработчик сигналов для корректного завершения"""
    print(f"\n🛑 Получен сигнал {signum}. Завершение работы...")

    # Устанавливаем событие остановки, если оно инициализировано
    if shutdown_event is not None and not shutdown_event.is_set():
        shutdown_event.set()
        print("🛑 Событие остановки установлено")

    # Отменяем все активные задачи
    for task in active_tasks:
        if not task.done():
            task.cancel()
            print(f"🛑 Задача {task.get_name()} отменена")

    print("🛑 Все задачи отменены, ожидаем завершения...")

if __name__ == "__main__":
    # Настройка обработчиков сигналов
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, signal_handler)
    try:
        # Устанавливаем метод запуска для multiprocessing на Windows
        if sys.platform.startswith('win'):
            multiprocessing.set_start_method('spawn', force=True)
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Программа остановлена пользователем.")
    except Exception as e:
        print(f"❌ Критическая ошибка в main: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("🏁 Лаунчер полностью завершен.")