"""
Конфигурация для Telegram бота "sh: Lite"
"""

# Токен Telegram бота
TELEGRAM_BOT_TOKEN = "**********************************************"

# API ключи для Gemini API (31 ключ с автоматическим переключением)
GEMINI_API_KEYS = [
    "AIzaSyBGzoS-mUQgImGlcEaX1QdfyznNwTZSKBo",
    "AIzaSyDwt7D5zJ1jlAxgqh7_cQQl4UwjK2I6m2g",
    "AIzaSyACs7h8c_0m2-WUm6iOvuQIcynKObPwyWc",
    "AIzaSyAsspWKTaD-vBuL4w03GgYfgFMzdSb4qVU",
    "AIzaSyBtWZhKwaA5pKjSkD2IjsxOOCH-SQwpsQU",
    "AIzaSyAgo7YgVRofruAm4Q7-C6LPTR5z9g2sFME",
    "AIzaSyCWQn23gkC42JGL5W5O4vEcKxSsgqm3Lhw",
    "AIzaSyDLbOB5tze88GzRdt2ZI4Us0Mm5gjS_9rI",
    "AIzaSyBMGlOVwfmci8C4Rvm4Gb2kVMaKQr1Z2wc",
    "AIzaSyDGiB3DOmmosBqTqKQu2sKB4xeFf0GXYvY",
    "AIzaSyDGvR1YrmU8Asj9TR0PThjBpJmAetRytqw",
    "AIzaSyBAtHWM-FthYoYxm1lgKX1uh0fmJO5yesw",
    "AIzaSyAhwlKB__mvi_Gp8ciIRQBqs2DqeA8_R4o",
    "AIzaSyAhwlKB__mvi_Gp8ciIRQBqs2DqeA8_R4o",
    "AIzaSyD9UV-_dMGV2sqol5rCEoZqPTXi0dbf7tM",
    "AIzaSyB2JihqbiDE4Z8T3otu14Eqt34vA9_mn5w",
    "AIzaSyB2JihqbiDE4Z8T3otu14Eqt34vA9_mn5w",
    "AIzaSyCTyLtQVsFsdCFl3Kh4BMCgJGoFibKFBlo",
    "AIzaSyBf_zHl2pe-jMc9UVRdvEkWg5A3BmpVn6k",
    "AIzaSyC5zWirvrpVTw_uOK5NusCxhqBypSftNZ0",
    "AIzaSyDOXlNnwkr61RLOdElX3y_KMtbSqpgdLe0",
    "AIzaSyAai8RmvV0srj0b_uZLQwYhP7yuwmDxD-I",
    "AIzaSyA6ROogrGMD8sENVdYTSqiCp4wb_aih_O4",
    "AIzaSyB1ZEgMjh68w6aX6z3rW8TwTd2SLx9lkJc",
    "AIzaSyD2ZOXPpQYPxmGVOWrzJhjQU65KWXC9l7s",
    "AIzaSyAW8nto6mQTCtuNTzRcnod1xWtFe2eHhyg",
    "AIzaSyAYvtNyPQXwDbd8-NqExoi0-X_4Oas2emA",
    "AIzaSyAORhrhY8ECNkPxxMPN3eJlxm1ddPcxzrQ",
    "AIzaSyCkbfaRF2fL-9h6Fc8Bj10UF-Xnn7pKBQ0",
    "AIzaSyBMKQoKx3_2kDuqPRwDfkzWL5lV5hsK4Nw",
    "AIzaSyBO0p7hrpQm2iEFHzNwI5HALwH0_GYz4ds",
]

# Настройки модели Gemini 2.5 Pro
GEMINI_MODEL = "gemini-2.5-pro"
GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta"

# Параметры генерации
GENERATION_CONFIG = {
    "maxOutputTokens": 65536,  # Максимальное количество токенов для вывода
    "temperature": 0.7,        # Температура (креативность)
    "topP": 0.8,              # Nucleus sampling
    "topK": 40,               # Top-k sampling
    "thinkingConfig": {
        "thinkingBudget": 128  # Бюджет токенов для внутреннего рассуждения
    }
}

# Настройки для Google Search grounding
TOOLS_CONFIG = [
    {"google_search": {}}
]

# Настройки бота
BOT_CONFIG = {
    "parse_mode": "HTML",
    "typing_indicator": "⏳",
    "cursor_symbol": "▌",
    "new_chat_button": "💭 Новый чат",
    "max_message_length": 4096,
    "stream_delay": 0.02,  # Задержка между чанками для плавной анимации
    "typing_update_interval": 80,  # Обновлять статус "typing" каждые N чанков (примерно каждые 4 секунды)
    "wait_message": "⏳ Дождитесь окончания предыдущего ответа, прежде чем отправлять новое сообщение."
}

# Настройки HTTP клиента
HTTP_CONFIG = {
    "timeout": 60,
    "retry_codes": [429, 500, 501, 502],
    "max_retries": len(GEMINI_API_KEYS)
}
