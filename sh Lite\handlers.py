"""
Обработчики Telegram сообщений для бота "sh: Lite"
"""

import asyncio
import logging
import io
import re
import time
import mimetypes
from typing import Dict, List, Any, Optional

from aiogram import Dispatcher, F, Bot
from aiogram.types import Message, CallbackQuery
from aiogram.filters import Command
from aiogram.types import ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton

import config
from gemini_client import GeminiClient


logger = logging.getLogger(__name__)

# Словарь для хранения контекста пользователей
# Формат: {user_id: [{'role': 'user', 'parts': [...]}, {'role': 'model', 'parts': [...]}]}
user_contexts: Dict[int, List[Dict[str, Any]]] = {}

# Словарь для хранения медиа-групп (альбомов)
# Формат: {media_group_id: {'messages': [...], 'caption': str, 'user_id': int, 'timer_task': Task}}
media_groups: Dict[str, Dict[str, Any]] = {}

# Словарь для отслеживания активных запросов пользователей
# Формат: {user_id: bool} - True если пользователь в данный момент обрабатывается
user_processing_status: Dict[int, bool] = {}

# Максимальный размер контекста в символах (приблизительно 1M токенов)
MAX_CONTEXT_SIZE = 800000  # ~1M токенов при среднем размере токена 0.8 символа

# Максимальное количество сообщений в контексте
MAX_CONTEXT_MESSAGES = 50

# Время ожидания для сбора медиа-группы (в секундах)
MEDIA_GROUP_TIMEOUT = 1.5


# ─── Вспомогательная утилита ────────────────────────────────────────────────
def _guess_mime_type(file_path: str, default: str = "application/octet-stream") -> str:
    """
    Определить MIME-тип файла по его пути

    Args:
        file_path: Путь к файлу
        default: MIME-тип по умолчанию

    Returns:
        MIME-тип файла
    """
    mime, _ = mimetypes.guess_type(file_path)
    return mime or default


def extract_tags(text: str) -> List[tuple]:
    """
    Извлечь все HTML теги из текста

    Args:
        text: Текст для анализа

    Returns:
        Список кортежей (tag_name, is_closing, full_tag)
    """
    # Паттерн для поиска HTML тегов
    tag_pattern = r'<(/?)([a-zA-Z][a-zA-Z0-9-]*)[^>]*>'
    tags = []

    for match in re.finditer(tag_pattern, text):
        is_closing = bool(match.group(1))  # True если есть /
        tag_name = match.group(2).lower()
        full_tag = match.group(0)
        tags.append((tag_name, is_closing, full_tag))

    return tags


def validate_html_tags(text: str) -> bool:
    """
    Проверить корректность HTML тегов в тексте

    Args:
        text: Текст для проверки

    Returns:
        True если все теги корректно закрыты
    """
    # Поддерживаемые теги Telegram
    supported_tags = {'b', 'i', 'u', 's', 'tg-spoiler', 'a', 'pre', 'code'}

    # Стек для отслеживания открытых тегов
    tag_stack = []

    # Извлекаем все теги
    tags = extract_tags(text)

    for tag_name, is_closing, full_tag in tags:
        # Проверяем только поддерживаемые теги
        if tag_name not in supported_tags:
            continue

        if is_closing:
            # Закрывающий тег
            if not tag_stack:
                # Закрывающий тег без открывающего
                return False

            # Проверяем соответствие последнему открытому тегу
            last_tag = tag_stack.pop()
            if last_tag != tag_name:
                # Неправильный порядок закрытия тегов
                return False
        else:
            # Открывающий тег
            tag_stack.append(tag_name)

    # Все теги должны быть закрыты
    return len(tag_stack) == 0


def is_html_complete(text: str) -> bool:
    """
    Проверить завершенность HTML разметки

    Args:
        text: Текст для проверки

    Returns:
        True если HTML разметка завершена корректно
    """
    # Если нет HTML тегов, считаем корректным
    if '<' not in text:
        return True

    # Проверяем на незавершенные теги (< без >)
    # Ищем все позиции символа <
    open_positions = []
    i = 0
    while i < len(text):
        if text[i] == '<':
            # Ищем соответствующий >
            close_pos = text.find('>', i)
            if close_pos == -1:
                # Нет закрывающего >, тег незавершен
                return False
            i = close_pos + 1
        else:
            i += 1

    # Проверяем валидность тегов
    return validate_html_tags(text)


class StreamingBuffer:
    """
    Буфер для накопления и валидации streaming чанков
    """

    def __init__(self):
        self.buffer = ""
        self.last_sent_text = ""
        self.chunk_count = 0

    def add_chunk(self, chunk: str) -> None:
        """
        Добавить чанк в буфер

        Args:
            chunk: Новый чанк текста
        """
        self.buffer += chunk
        self.chunk_count += 1

    def get_valid_text_for_display(self) -> Optional[str]:
        """
        Получить текст готовый для отображения (с валидным HTML)

        Returns:
            Текст для отправки или None если HTML невалиден или дублируется
        """
        # Проверяем валидность HTML
        if not is_html_complete(self.buffer):
            return None

        # Проверяем на дублирование
        if self.buffer == self.last_sent_text:
            return None

        # Обновляем последний отправленный текст
        self.last_sent_text = self.buffer
        return self.buffer

    def get_final_text(self) -> str:
        """
        Получить финальный текст (без проверки валидности)

        Returns:
            Весь накопленный текст
        """
        return self.buffer

    def get_buffer_length(self) -> int:
        """
        Получить длину буфера

        Returns:
            Длина накопленного текста
        """
        return len(self.buffer)


def is_user_processing(user_id: int) -> bool:
    """
    Проверить, обрабатывается ли в данный момент запрос от пользователя

    Args:
        user_id: ID пользователя

    Returns:
        True если пользователь уже обрабатывается, False иначе
    """
    return user_processing_status.get(user_id, False)


def set_user_processing(user_id: int, status: bool):
    """
    Установить статус обработки для пользователя

    Args:
        user_id: ID пользователя
        status: True для установки флага обработки, False для снятия
    """
    if status:
        user_processing_status[user_id] = True
    else:
        user_processing_status.pop(user_id, None)


async def send_typing_action(bot: Bot, chat_id: int):
    """
    Отправить статус 'печатает' в чат

    Args:
        bot: Экземпляр бота
        chat_id: ID чата
    """
    try:
        await bot.send_chat_action(chat_id=chat_id, action="typing")
    except Exception as e:
        logger.warning(f"Ошибка при отправке статуса typing: {e}")


def get_main_keyboard() -> ReplyKeyboardMarkup:
    """
    Создать основную клавиатуру с кнопкой "Новый чат"

    Returns:
        Reply клавиатура
    """
    keyboard = ReplyKeyboardMarkup(
        keyboard=[
            [KeyboardButton(text=config.BOT_CONFIG["new_chat_button"])]
        ],
        resize_keyboard=True,
        persistent=True
    )
    return keyboard


def get_context_size(context: List[Dict[str, Any]]) -> int:
    """
    Подсчитать приблизительный размер контекста в символах

    Args:
        context: Контекст пользователя

    Returns:
        Размер в символах
    """
    total_size = 0
    for message in context:
        if 'parts' in message:
            for part in message['parts']:
                if 'text' in part:
                    total_size += len(part['text'])
    return total_size








def safe_split_text(text: str, max_length: int = 4000) -> tuple[str, str]:
    """
    Безопасно разделить текст по длине

    Args:
        text: Текст для разделения
        max_length: Максимальная длина первой части

    Returns:
        Кортеж (первая_часть, оставшаяся_часть)
    """
    if len(text) <= max_length:
        return text, ""

    # Ищем последний пробел в разумных пределах для разделения
    split_pos = max_length
    for i in range(max_length - 1, max(0, max_length - 200), -1):
        if text[i] in ' \n\t.,!?;:':
            split_pos = i
            break

    first_part = text[:split_pos].rstrip()
    remaining_part = text[split_pos:].lstrip()

    return first_part, remaining_part


def format_asterisks_to_bullets(text: str) -> str:
    """
    Заменить звездочки в начале строк на красивые точки

    Заменяет одиночные звездочки (*) в начале строк (возможно после пробелов)
    на красивые точки (•) и убирает пробел после точки.
    Звездочки в середине строк не затрагиваются.

    Args:
        text: Текст для обработки

    Returns:
        Текст с замененными звездочками
    """
    # Паттерн: начало строки + возможные пробелы + звездочка + возможные пробелы
    # Заменяем на: те же пробелы + точка (без пробелов после)
    return re.sub(r'^(\s*)\*\s*', r'\1•', text, flags=re.MULTILINE)


def trim_context(context: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Обрезать контекст до допустимого размера, удаляя старые сообщения

    Args:
        context: Контекст пользователя

    Returns:
        Обрезанный контекст
    """
    # Ограничиваем по количеству сообщений
    while len(context) > MAX_CONTEXT_MESSAGES:
        context.pop(0)

    # Ограничиваем по размеру в символах
    while len(context) > 2 and get_context_size(context) > MAX_CONTEXT_SIZE:
        # Удаляем самые старые сообщения (пару user-model)
        if len(context) >= 2:
            context.pop(0)  # Удаляем user сообщение
            if len(context) > 0 and context[0].get('role') == 'model':
                context.pop(0)  # Удаляем соответствующий model ответ

    return context


def get_context_stats(user_id: int) -> Dict[str, Any]:
    """
    Получить статистику контекста пользователя

    Args:
        user_id: ID пользователя

    Returns:
        Словарь со статистикой
    """
    context = user_contexts.get(user_id, [])
    return {
        'message_count': len(context),
        'size_chars': get_context_size(context),
        'size_mb': get_context_size(context) / 1024 / 1024,
        'estimated_tokens': get_context_size(context) * 1.25  # Приблизительная оценка токенов
    }


def add_to_context(user_id: int, role: str, parts: List[Dict[str, Any]]):
    """
    Добавить сообщение в контекст пользователя

    Args:
        user_id: ID пользователя
        role: Роль ('user' или 'model')
        parts: Части сообщения
    """
    if user_id not in user_contexts:
        user_contexts[user_id] = []

    user_contexts[user_id].append({
        'role': role,
        'parts': parts
    })

    # Обрезаем контекст если он слишком большой
    user_contexts[user_id] = trim_context(user_contexts[user_id])


# ─── Унифицированная обработка любого одиночного файла ─────────────────────
async def _process_single_file_message(
    message: Message,
    file_id: str,
    gemini_client: GeminiClient,
    default_prompt: str,
    mime_type: str | None,
):
    """
    Универсальная обработка одиночного файла (видео, аудио, voice, video_note, документ)

    Args:
        message: Сообщение от пользователя
        file_id: ID файла в Telegram
        gemini_client: Клиент Gemini API
        default_prompt: Промпт по умолчанию, если нет caption
        mime_type: MIME-тип файла (None для автоопределения)
    """
    user_id = message.from_user.id
    if is_user_processing(user_id):
        await message.answer(config.BOT_CONFIG["wait_message"], parse_mode="HTML")
        return
    set_user_processing(user_id, True)
    await send_typing_action(message.bot, message.chat.id)
    loading_message = await message.answer(config.BOT_CONFIG["typing_indicator"], parse_mode="HTML")

    try:
        bot = message.bot
        file_info = await bot.get_file(file_id)
        file_bytes = io.BytesIO()
        await bot.download_file(file_info.file_path, file_bytes)
        file_bytes.seek(0)
        if mime_type is None:
            mime_type = _guess_mime_type(file_info.file_path)
        logger.info(f"Скачан файл от {user_id}: {len(file_bytes.getvalue())} байт, MIME={mime_type}")

        file_part = await gemini_client._handle_image(file_bytes.getvalue(), mime_type)
        user_text = message.caption if getattr(message, "caption", None) else default_prompt
        add_to_context(user_id, "user", [{"text": user_text}, file_part])

        context = user_contexts.get(user_id, [])
        streaming_buffer = StreamingBuffer()
        cur_msg, cur_text = loading_message, ""
        chunk_cnt, last_update = 0, 0.0

        async for chunk in gemini_client.generate_stream(context, message.from_user):
            if not chunk:
                continue
            chunk_cnt += 1
            if chunk_cnt % config.BOT_CONFIG["typing_update_interval"] == 0:
                await send_typing_action(message.bot, message.chat.id)
            chunk = format_asterisks_to_bullets(chunk)
            streaming_buffer.add_chunk(chunk)
            cur_text += chunk

            if len(cur_text) + 1 > 4000:
                final, rest = safe_split_text(cur_text, 4000)
                try:
                    await cur_msg.edit_text(final, parse_mode="HTML")
                except Exception as e:
                    logger.warning(f"edit_text split error: {e}")
                cur_msg = await message.answer("▌", parse_mode="HTML")
                cur_text = rest
                continue

            valid = streaming_buffer.get_valid_text_for_display()
            now = time.time()
            if valid is not None and (chunk_cnt % 3 == 0 or chunk_cnt == 1 or now - last_update >= 1):
                try:
                    await cur_msg.edit_text(cur_text + "▌", parse_mode="HTML")
                    last_update = now
                except Exception:
                    continue
            await asyncio.sleep(0.05)

        final_text = streaming_buffer.get_final_text()
        try:
            await cur_msg.edit_text(cur_text, parse_mode="HTML")
        except Exception:
            pass
        add_to_context(user_id, "model", [{"text": final_text}])
    except Exception as e:
        logger.error(f"file‑process error: {e}")
        try:
            await loading_message.edit_text("Произошла ошибка при обработке файла. Попробуйте ещё раз.", parse_mode="HTML")
        except Exception:
            await message.answer("Произошла ошибка при обработке файла. Попробуйте ещё раз.", parse_mode="HTML")
    finally:
        set_user_processing(user_id, False)


async def on_start(message: Message):
    """
    Обработчик команды /start

    Args:
        message: Сообщение от пользователя
    """
    welcome_text = (
        "👋 Привет! Я <b>sh: Lite</b>\n\n"
        "Я умею:\n"
        "• Отвечать на твои вопросы\n"
        "• Видеть изображения и анализировать альбомы\n"
        "• Смотреть видео и видео-кружки\n"
        "• Слушать аудио и голосовые сообщения\n"
        "• Анализировать документы\n"
        "• Искать информацию в интернете\n"
        "• Поддерживать диалог\n\n"
        "Просто пришли сообщение, фото, видео, аудио или документ!\n"
        "Мой приоритет - скорость и стабильность работы!"
    )

    # Создаем inline клавиатуру с кнопкой "Автор"
    inline_keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text="Автор", url="https://t.me/kirillshsh")]
        ]
    )

    await message.answer(
        welcome_text,
        reply_markup=inline_keyboard,
        parse_mode="HTML"
    )


async def on_message(message: Message, gemini_client: GeminiClient):
    """
    Обработчик текстовых сообщений

    Args:
        message: Сообщение от пользователя
        gemini_client: Клиент Gemini API
    """
    user_id = message.from_user.id
    user_text = message.text

    # Проверяем, не обрабатывается ли уже запрос от этого пользователя
    if is_user_processing(user_id):
        await message.answer(config.BOT_CONFIG["wait_message"], parse_mode="HTML")
        return

    # Устанавливаем флаг обработки
    set_user_processing(user_id, True)

    # Отправляем статус "печатает"
    await send_typing_action(message.bot, message.chat.id)

    # Отправляем индикатор загрузки
    loading_message = await message.answer(config.BOT_CONFIG["typing_indicator"], parse_mode="HTML")

    try:
        # Добавляем сообщение пользователя в контекст
        add_to_context(user_id, 'user', [{'text': user_text}])

        # Получаем контекст для запроса
        context = user_contexts.get(user_id, [])
        context_stats = get_context_stats(user_id)

        logger.info(f"Обработка сообщения от пользователя {user_id}, контекст: {context_stats['message_count']} сообщений, "
                   f"{context_stats['size_chars']} символов, ~{context_stats['estimated_tokens']:.0f} токенов")

        # Переменные для streaming ответа
        streaming_buffer = StreamingBuffer()
        current_message_text = ""  # Текст для текущего сообщения
        current_message = loading_message
        message_count = 1
        chunk_count = 0  # Счетчик чанков для обновления typing
        last_update_time = 0  # Время последнего обновления сообщения

        # Запускаем streaming запрос
        async for chunk in gemini_client.generate_stream(context, message.from_user):
            if chunk:
                chunk_count += 1

                # Периодически обновляем статус "typing"
                if chunk_count % config.BOT_CONFIG["typing_update_interval"] == 0:
                    await send_typing_action(message.bot, message.chat.id)

                # Форматируем звездочки в красивые точки
                chunk = format_asterisks_to_bullets(chunk)

                # Добавляем чанк в буфер
                streaming_buffer.add_chunk(chunk)
                current_message_text += chunk

                # Проверяем лимит символов Telegram (4096)
                if len(current_message_text) + 1 > 4000:  # +1 для курсора
                    # Безопасно разделяем текст
                    final_text, remaining_text = safe_split_text(current_message_text, 4000)

                    # Завершаем текущее сообщение
                    try:
                        await current_message.edit_text(final_text, parse_mode="HTML")
                    except Exception as e:
                        logger.warning(f"Ошибка при редактировании сообщения (разделение): {e}")

                    # Создаем новое сообщение для продолжения
                    current_message = await message.answer("▌", parse_mode="HTML")
                    current_message_text = remaining_text
                    message_count += 1
                    continue

                # Получаем валидный текст для отображения
                valid_text = streaming_buffer.get_valid_text_for_display()
                current_time = time.time()

                # Обновляем каждый третий чанк, первый чанк, или если прошло больше секунды с последнего обновления
                should_update = (
                    valid_text is not None and (
                        chunk_count % 3 == 0 or
                        chunk_count == 1 or
                        (current_time - last_update_time) >= 1.0
                    )
                )

                if should_update:
                    # HTML валиден и текст изменился, обновляем сообщение
                    display_text = current_message_text + "▌"
                    try:
                        await current_message.edit_text(display_text, parse_mode="HTML")
                        last_update_time = current_time  # Обновляем время последнего обновления
                    except Exception as e:
                        logger.warning(f"Ошибка при редактировании сообщения (HTML): {e}")
                        # Если ошибка HTML, пропускаем этот чанк и ждем следующий
                        continue

                # Небольшая задержка для плавной анимации
                await asyncio.sleep(0.05)

        # Убираем курсор из финального сообщения
        final_text = streaming_buffer.get_final_text()
        try:
            await current_message.edit_text(current_message_text, parse_mode="HTML")
        except Exception as e:
            logger.warning(f"Ошибка при финальном редактировании сообщения: {e}")

        # Добавляем ответ модели в контекст (используем весь накопленный текст)
        add_to_context(user_id, 'model', [{'text': final_text}])

        logger.info(f"Ответ отправлен пользователю {user_id}, длина: {len(final_text)} символов, сообщений: {message_count}")

    except Exception as e:
        logger.error(f"Ошибка при обработке сообщения: {e}")
        try:
            await loading_message.edit_text(
                "Произошла ошибка при обработке сообщения. Попробуйте еще раз.",
                parse_mode="HTML"
            )
        except:
            # Если не удается отредактировать, отправляем новое сообщение
            await message.answer(
                "Произошла ошибка при обработке сообщения. Попробуйте еще раз.",
                parse_mode="HTML"
            )
    finally:
        # Снимаем флаг обработки
        set_user_processing(user_id, False)


async def on_photo(message: Message, gemini_client: GeminiClient):
    """
    Обработчик изображений и медиа-групп (альбомов)

    Args:
        message: Сообщение с фото от пользователя
        gemini_client: Клиент Gemini API
    """
    user_id = message.from_user.id

    # Проверяем, является ли это частью медиа-группы (альбома)
    if message.media_group_id:
        # Это часть альбома
        media_group_id = message.media_group_id

        # Если группа уже существует, добавляем сообщение
        if media_group_id in media_groups:
            media_groups[media_group_id]['messages'].append(message)

            # Отменяем предыдущий таймер
            if media_groups[media_group_id]['timer_task']:
                media_groups[media_group_id]['timer_task'].cancel()
        else:
            # Создаем новую группу
            media_groups[media_group_id] = {
                'messages': [message],
                'caption': message.caption,  # Подпись обычно есть только у первого сообщения
                'user_id': user_id,
                'timer_task': None
            }

        # Запускаем новый таймер для обработки группы
        async def delayed_process():
            await asyncio.sleep(MEDIA_GROUP_TIMEOUT)
            await process_media_group(media_group_id, gemini_client)

        media_groups[media_group_id]['timer_task'] = asyncio.create_task(delayed_process())
        return

    # Это одиночное фото, обрабатываем как обычно
    # Проверяем, не обрабатывается ли уже запрос от этого пользователя
    if is_user_processing(user_id):
        await message.answer(config.BOT_CONFIG["wait_message"], parse_mode="HTML")
        return

    # Устанавливаем флаг обработки
    set_user_processing(user_id, True)

    # Отправляем статус "печатает"
    await send_typing_action(message.bot, message.chat.id)

    # Отправляем индикатор загрузки
    loading_message = await message.answer(config.BOT_CONFIG["typing_indicator"], parse_mode="HTML")

    try:
        # Получаем информацию о фото (берем самое большое разрешение)
        photo = message.photo[-1]

        # Получаем Bot из message
        bot = message.bot

        # Получаем информацию о файле
        file_info = await bot.get_file(photo.file_id)

        # Скачиваем файл
        file_bytes = io.BytesIO()
        await bot.download_file(file_info.file_path, file_bytes)
        file_bytes.seek(0)

        # Определяем MIME тип по расширению файла
        file_extension = file_info.file_path.split('.')[-1].lower()
        mime_type = "image/jpeg"  # По умолчанию
        if file_extension == "png":
            mime_type = "image/png"
        elif file_extension == "webp":
            mime_type = "image/webp"

        logger.info(f"Скачано изображение от пользователя {user_id}: {len(file_bytes.getvalue())} байт, тип: {mime_type}")

        # Конвертируем изображение через GeminiClient
        image_part = await gemini_client._handle_image(file_bytes.getvalue(), mime_type)

        # Получаем текст сообщения (если есть)
        user_text = message.caption if message.caption else "Что изображено на этой картинке?"

        # Формируем части сообщения (текст + изображение)
        message_parts = [
            {'text': user_text},
            image_part
        ]

        # Добавляем сообщение пользователя в контекст
        add_to_context(user_id, 'user', message_parts)

        # Получаем контекст для запроса
        context = user_contexts.get(user_id, [])
        context_stats = get_context_stats(user_id)

        logger.info(f"Обработка изображения от пользователя {user_id}, контекст: {context_stats['message_count']} сообщений, "
                   f"{context_stats['size_chars']} символов, ~{context_stats['estimated_tokens']:.0f} токенов")

        # Переменные для streaming ответа
        streaming_buffer = StreamingBuffer()
        current_message_text = ""  # Текст для текущего сообщения
        current_message = loading_message
        message_count = 1
        chunk_count = 0  # Счетчик чанков для обновления typing
        last_update_time = 0  # Время последнего обновления сообщения

        # Запускаем streaming запрос
        async for chunk in gemini_client.generate_stream(context, message.from_user):
            if chunk:
                chunk_count += 1

                # Периодически обновляем статус "typing"
                if chunk_count % config.BOT_CONFIG["typing_update_interval"] == 0:
                    await send_typing_action(message.bot, message.chat.id)

                # Форматируем звездочки в красивые точки
                chunk = format_asterisks_to_bullets(chunk)

                # Добавляем чанк в буфер
                streaming_buffer.add_chunk(chunk)
                current_message_text += chunk

                # Проверяем лимит символов Telegram (4096)
                if len(current_message_text) + 1 > 4000:  # +1 для курсора
                    # Безопасно разделяем текст
                    final_text, remaining_text = safe_split_text(current_message_text, 4000)

                    # Завершаем текущее сообщение
                    try:
                        await current_message.edit_text(final_text, parse_mode="HTML")
                    except Exception as e:
                        logger.warning(f"Ошибка при редактировании сообщения (разделение): {e}")

                    # Создаем новое сообщение для продолжения
                    current_message = await message.answer("▌", parse_mode="HTML")
                    current_message_text = remaining_text
                    message_count += 1
                    continue

                # Получаем валидный текст для отображения
                valid_text = streaming_buffer.get_valid_text_for_display()
                current_time = time.time()

                # Обновляем каждый третий чанк, первый чанк, или если прошло больше секунды с последнего обновления
                should_update = (
                    valid_text is not None and (
                        chunk_count % 3 == 0 or
                        chunk_count == 1 or
                        (current_time - last_update_time) >= 1.0
                    )
                )

                if should_update:
                    # HTML валиден и текст изменился, обновляем сообщение
                    display_text = current_message_text + "▌"
                    try:
                        await current_message.edit_text(display_text, parse_mode="HTML")
                        last_update_time = current_time  # Обновляем время последнего обновления
                    except Exception as e:
                        logger.warning(f"Ошибка при редактировании сообщения (HTML): {e}")
                        # Если ошибка HTML, пропускаем этот чанк и ждем следующий
                        continue

                # Небольшая задержка для плавной анимации
                await asyncio.sleep(0.05)

        # Убираем курсор из финального сообщения
        final_text = streaming_buffer.get_final_text()
        try:
            await current_message.edit_text(current_message_text, parse_mode="HTML")
        except Exception as e:
            logger.warning(f"Ошибка при финальном редактировании сообщения: {e}")

        # Добавляем ответ модели в контекст (используем весь накопленный текст)
        add_to_context(user_id, 'model', [{'text': final_text}])

        logger.info(f"Ответ на изображение отправлен пользователю {user_id}, длина: {len(final_text)} символов, сообщений: {message_count}")

    except Exception as e:
        logger.error(f"Ошибка при обработке изображения: {e}")
        try:
            await loading_message.edit_text(
                "Произошла ошибка при обработке изображения. Попробуйте еще раз.",
                parse_mode="HTML"
            )
        except:
            # Если не удается отредактировать, отправляем новое сообщение
            await message.answer(
                "Произошла ошибка при обработке изображения. Попробуйте еще раз.",
                parse_mode="HTML"
            )
    finally:
        # Снимаем флаг обработки
        set_user_processing(user_id, False)


async def process_media_group(media_group_id: str, gemini_client: GeminiClient):
    """
    Обработка медиа-группы (альбома) после сбора всех сообщений

    Args:
        media_group_id: ID медиа-группы
        gemini_client: Клиент Gemini API
    """
    if media_group_id not in media_groups:
        return

    group_data = media_groups[media_group_id]
    messages = group_data['messages']
    caption = group_data['caption']
    user_id = group_data['user_id']

    # Удаляем группу из словаря
    del media_groups[media_group_id]

    # Проверяем, не обрабатывается ли уже запрос от этого пользователя
    if is_user_processing(user_id):
        first_message = messages[0]
        await first_message.answer(config.BOT_CONFIG["wait_message"], parse_mode="HTML")
        return

    # Устанавливаем флаг обработки
    set_user_processing(user_id, True)

    # Отправляем статус "печатает" (используем первое сообщение)
    first_message = messages[0]
    await send_typing_action(first_message.bot, first_message.chat.id)

    # Отправляем индикатор загрузки
    loading_message = await first_message.answer(config.BOT_CONFIG["typing_indicator"], parse_mode="HTML")

    try:
        # Получаем Bot из первого сообщения
        bot = first_message.bot

        # Собираем все изображения из группы
        image_parts = []
        total_size = 0

        for message in messages:
            # Получаем информацию о фото (берем самое большое разрешение)
            photo = message.photo[-1]

            # Получаем информацию о файле
            file_info = await bot.get_file(photo.file_id)

            # Скачиваем файл
            file_bytes = io.BytesIO()
            await bot.download_file(file_info.file_path, file_bytes)
            file_bytes.seek(0)

            # Определяем MIME тип по расширению файла
            file_extension = file_info.file_path.split('.')[-1].lower()
            mime_type = "image/jpeg"  # По умолчанию
            if file_extension == "png":
                mime_type = "image/png"
            elif file_extension == "webp":
                mime_type = "image/webp"

            total_size += len(file_bytes.getvalue())

            # Конвертируем изображение через GeminiClient
            image_part = await gemini_client._handle_image(file_bytes.getvalue(), mime_type)
            image_parts.append(image_part)

        logger.info(f"Скачан альбом от пользователя {user_id}: {len(messages)} изображений, {total_size} байт")

        # Получаем текст сообщения (используем подпись или стандартный текст)
        user_text = caption if caption else f"Что изображено на этих {len(messages)} картинках?"

        # Формируем части сообщения (текст + все изображения)
        message_parts = [{'text': user_text}] + image_parts

        # Добавляем сообщение пользователя в контекст
        add_to_context(user_id, 'user', message_parts)

        # Получаем контекст для запроса
        context = user_contexts.get(user_id, [])
        context_stats = get_context_stats(user_id)

        logger.info(f"Обработка альбома от пользователя {user_id}, контекст: {context_stats['message_count']} сообщений, "
                   f"{context_stats['size_chars']} символов, ~{context_stats['estimated_tokens']:.0f} токенов")

        # Переменные для streaming ответа
        streaming_buffer = StreamingBuffer()
        current_message_text = ""  # Текст для текущего сообщения
        current_message = loading_message
        message_count = 1
        chunk_count = 0  # Счетчик чанков для обновления typing
        last_update_time = 0  # Время последнего обновления сообщения

        # Запускаем streaming запрос
        async for chunk in gemini_client.generate_stream(context, first_message.from_user):
            if chunk:
                chunk_count += 1

                # Периодически обновляем статус "typing"
                if chunk_count % config.BOT_CONFIG["typing_update_interval"] == 0:
                    await send_typing_action(first_message.bot, first_message.chat.id)

                # Форматируем звездочки в красивые точки
                chunk = format_asterisks_to_bullets(chunk)

                # Добавляем чанк в буфер
                streaming_buffer.add_chunk(chunk)
                current_message_text += chunk

                # Проверяем длину сообщения
                if len(current_message_text) + 1 > config.BOT_CONFIG["max_message_length"] - 100:  # +1 для курсора
                    # Убираем курсор и отправляем текущее сообщение
                    try:
                        await current_message.edit_text(current_message_text, parse_mode="HTML")
                    except Exception as e:
                        logger.warning(f"Ошибка при редактировании сообщения (разделение): {e}")

                    # Создаем новое сообщение для продолжения
                    current_message = await first_message.answer("▌", parse_mode="HTML")
                    current_message_text = ""
                    message_count += 1
                    continue

                # Получаем валидный текст для отображения
                valid_text = streaming_buffer.get_valid_text_for_display()
                current_time = time.time()

                # Обновляем каждый третий чанк, первый чанк, или если прошло больше секунды с последнего обновления
                should_update = (
                    valid_text is not None and (
                        chunk_count % 3 == 0 or
                        chunk_count == 1 or
                        (current_time - last_update_time) >= 1.0
                    )
                )

                if should_update:
                    # HTML валиден и текст изменился, обновляем сообщение
                    display_text = current_message_text + "▌"
                    try:
                        await current_message.edit_text(display_text, parse_mode="HTML")
                        last_update_time = current_time  # Обновляем время последнего обновления
                    except Exception as e:
                        logger.warning(f"Ошибка при редактировании сообщения (HTML): {e}")
                        # Если ошибка HTML, пропускаем этот чанк и ждем следующий
                        continue

                # Небольшая задержка для плавной анимации
                await asyncio.sleep(0.05)

        # Убираем курсор из финального сообщения
        final_text = streaming_buffer.get_final_text()
        try:
            await current_message.edit_text(current_message_text, parse_mode="HTML")
        except Exception as e:
            logger.warning(f"Ошибка при финальном редактировании сообщения: {e}")

        # Добавляем ответ модели в контекст (используем весь накопленный текст)
        add_to_context(user_id, 'model', [{'text': final_text}])

        logger.info(f"Ответ на альбом отправлен пользователю {user_id}, длина: {len(final_text)} символов, сообщений: {message_count}")

    except Exception as e:
        logger.error(f"Ошибка при обработке альбома: {e}")
        try:
            await loading_message.edit_text(
                "Произошла ошибка при обработке альбома. Попробуйте еще раз.",
                parse_mode="HTML"
            )
        except:
            # Если не удается отредактировать, отправляем новое сообщение
            await first_message.answer(
                "Произошла ошибка при обработке альбома. Попробуйте еще раз.",
                parse_mode="HTML"
            )
    finally:
        # Снимаем флаг обработки
        set_user_processing(user_id, False)


async def on_new_chat(message: Message):
    """
    Обработчик кнопки "Новый чат"

    Args:
        message: Сообщение с текстом кнопки
    """
    user_id = message.from_user.id

    # Очищаем контекст пользователя
    if user_id in user_contexts:
        del user_contexts[user_id]

    await message.answer(
        "😊 Контекст разговора очищен!",
        reply_markup=get_main_keyboard(),
        parse_mode="HTML"
    )


# ─── Обёртки для конкретных типов ───────────────────────────────────────────
async def on_video(message: Message, gemini_client: GeminiClient):
    """Обработчик видео файлов"""
    await _process_single_file_message(message, message.video.file_id, gemini_client,
                                       "Опиши это видео.", "video/mp4")

async def on_audio(message: Message, gemini_client: GeminiClient):
    """Обработчик аудио файлов"""
    await _process_single_file_message(message, message.audio.file_id, gemini_client,
                                       "Что звучит на этом аудио?", "audio/mpeg")

async def on_voice(message: Message, gemini_client: GeminiClient):
    """Обработчик голосовых сообщений"""
    await _process_single_file_message(message, message.voice.file_id, gemini_client,
                                       "Расшифруй голосовое сообщение.", "audio/ogg")

async def on_video_note(message: Message, gemini_client: GeminiClient):
    """Обработчик видео-кружков"""
    await _process_single_file_message(message, message.video_note.file_id, gemini_client,
                                       "Опиши содержимое кружка.", "video/mp4")

async def on_document(message: Message, gemini_client: GeminiClient):
    """Обработчик документов"""
    await _process_single_file_message(message, message.document.file_id, gemini_client,
                                       "Проанализируй этот документ.", None)


def register_handlers(dp: Dispatcher, gemini_client: GeminiClient):
    """
    Регистрация всех обработчиков

    Args:
        dp: Диспетчер aiogram
        gemini_client: Клиент Gemini API
    """
    # Создаем обертки для передачи gemini_client
    async def message_handler(message: Message):
        await on_message(message, gemini_client)

    async def photo_handler(message: Message):
        await on_photo(message, gemini_client)

    # Команда /start
    dp.message.register(on_start, Command("start"))

    # Обработка кнопки "Новый чат"
    dp.message.register(on_new_chat, F.text == config.BOT_CONFIG["new_chat_button"])

    # Обработка изображений
    dp.message.register(photo_handler, F.photo)

    # ─── Новые типы медиа ────────────────────────────────────────────────────
    async def video_handler(m: Message):      await on_video(m, gemini_client)
    async def audio_handler(m: Message):      await on_audio(m, gemini_client)
    async def voice_handler(m: Message):      await on_voice(m, gemini_client)
    async def video_note_handler(m: Message): await on_video_note(m, gemini_client)
    async def document_handler(m: Message):   await on_document(m, gemini_client)

    dp.message.register(video_handler,      F.video)
    dp.message.register(audio_handler,      F.audio)
    dp.message.register(voice_handler,      F.voice)
    dp.message.register(video_note_handler, F.video_note)
    dp.message.register(document_handler,   F.document)

    # Обработка текстовых сообщений (исключаем команды и кнопку "Новый чат")
    dp.message.register(
        message_handler,
        F.text & ~F.text.startswith('/') & (F.text != config.BOT_CONFIG["new_chat_button"])
    )

    logger.info("Все обработчики зарегистрированы")
