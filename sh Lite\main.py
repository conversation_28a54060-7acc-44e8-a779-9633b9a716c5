"""
Telegram бот "sh: Lite" - асинхронный бот для работы с Gemini 2.5 Pro API
Точка входа приложения
"""

import asyncio
import logging
from aiogram import Bo<PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode

import config
from gemini_client import GeminiClient
from handlers import register_handlers


# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """
    Основная функция запуска бота
    """
    # Инициализация бота с настройками по умолчанию
    bot = Bot(
        token=config.TELEGRAM_BOT_TOKEN,
        default=DefaultBotProperties(parse_mode=ParseMode.HTML)
    )

    # Создание диспетчера
    dp = Dispatcher()

    # Инициализация клиента Gemini API с контекстным менеджером
    async with GeminiClient(config.GEMINI_API_KEYS) as gemini_client:
        try:
            # Регистрация обработчиков
            register_handlers(dp, gemini_client)

            logger.info("Бот запускается...")

            # Запуск polling
            await dp.start_polling(bot)

        except Exception as e:
            logger.error(f"Ошибка при запуске бота: {e}")
            raise
        finally:
            logger.info("Бот остановлен")


if __name__ == "__main__":
    """
    Точка входа приложения
    """
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Получен сигнал остановки")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
