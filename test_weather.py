#!/usr/bin/env python3
"""
Тестовый скрипт для проверки запуска Weather бота через ma2.py
"""
import asyncio
import os
import sys

# Добавляем корневую директорию в путь
ORIGINAL_DIR = os.path.dirname(os.path.abspath(__file__))

async def run_weather_bot():
    """Запуск Weather бота (асинхронного, в основном цикле)"""
    try:
        print("🌤️ Запуск Weather бота...")
        weather_dir = os.path.join(ORIGINAL_DIR, "Weather")
        if not os.path.exists(weather_dir):
            print(f"❌ Директория Weather бота не найдена: {weather_dir}")
            return
        if weather_dir not in sys.path:
            sys.path.insert(0, weather_dir)
        # Проверяем, что файл weather_main.py существует
        bot_file = os.path.join(weather_dir, "weather_main.py")
        if not os.path.exists(bot_file):
            print(f"❌ Файл {bot_file} не найден!")
            return
        # Временно меняем директорию для импорта
        original_cwd = os.getcwd()
        os.chdir(weather_dir)
        try:
            # Импортируем и запускаем асинхронную функцию
            from weather_main import run_bot_async
            os.chdir(original_cwd)
            print("✅ Weather бот импортирован успешно")
            # Запускаем Weather бота на короткое время для теста
            print("🚀 Запуск Weather бота на 5 секунд для теста...")
            weather_task = asyncio.create_task(run_bot_async())
            
            # Ждем 5 секунд, затем отменяем
            await asyncio.sleep(5)
            weather_task.cancel()
            try:
                await weather_task
            except asyncio.CancelledError:
                print("🛑 Weather бот остановлен после теста")
                
        finally:
            if os.getcwd() != original_cwd:
                os.chdir(original_cwd)
    except Exception as e:
        print(f"❌ Критическая ошибка в Weather боте: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Главная функция теста"""
    print("🧪 Тестирование Weather бота...")
    await run_weather_bot()
    print("✅ Тест завершен")

if __name__ == "__main__":
    asyncio.run(main())
