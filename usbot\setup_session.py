#!/usr/bin/env python3
"""
Скрипт для первоначальной настройки сессии Telegram
Запустите этот файл один раз для авторизации в Telegram
"""

import asyncio
from telethon import TelegramClient
from usbot_config import TELEGRAM_API_ID, TELEGRAM_API_HASH, logger


async def setup_session():
    """Настройка сессии Telegram"""
    try:
        print("🔐 Настройка сессии Telegram...")
        print("Вам потребуется ввести номер телефона и код подтверждения")
        
        client = TelegramClient('userbot_session', TELEGRAM_API_ID, TELEGRAM_API_HASH)
        
        await client.start()
        
        me = await client.get_me()
        print(f"✅ Успешно авторизованы как: {me.first_name} (@{me.username})")
        print(f"📱 Номер телефона: {me.phone}")
        
        await client.disconnect()
        
        print("✅ Сессия настроена! Теперь можно запускать юзербота через ma2.py")
        
    except Exception as e:
        logger.error(f"Ошибка настройки сессии: {e}")
        print(f"❌ Ошибка: {e}")


if __name__ == "__main__":
    asyncio.run(setup_session())
