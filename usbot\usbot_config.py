#!/usr/bin/env python3
"""
Конфигурация для юзербота поддержки Huawei
"""

import os

# Telegram API данные
TELEGRAM_API_ID = 3484221
TELEGRAM_API_HASH = "6b7539491c581238c310c17dd56004fb"

# Настройки чатов (будут устанавливаться командами)
SUPPORT_CHAT_ID = None  # Чат поддержки Huawei (команда /in)
STAFF_CHAT_ID = None    # Чат для сотрудников (команда /to)

# Файл для сохранения настроек чатов
SETTINGS_FILE = os.path.join(os.path.dirname(__file__), "settings.json")

# Настройки логирования
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [USERBOT] %(levelname)s: %(message)s',
    datefmt='%H:%M:%S'
)

logger = logging.getLogger('userbot')
