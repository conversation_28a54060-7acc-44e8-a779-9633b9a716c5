#!/usr/bin/env python3
"""
Модуль для отслеживания активности пользователей
"""

import asyncio
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Set, Optional
from telethon import TelegramClient
from telethon.tl.types import UserStatusOnline, UserStatusOffline, UserStatusRecently, UserStatusLastWeek, UserStatusLastMonth
import logging

logger = logging.getLogger(__name__)


class UserTracker:
    """Класс для отслеживания активности пользователей"""
    
    def __init__(self, client: TelegramClient):
        self.client = client
        self.tracked_users: Dict[int, Dict] = {}  # {user_id: {"name": str, "last_status": str, "last_check": datetime, "status_start": datetime}}
        self.tracking_tasks: Dict[int, asyncio.Task] = {}  # {user_id: task}
        self.logs_dir = "user_logs"
        self.settings_file = "tracker_settings.json"

        # Создаем папку для логов если её нет
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)

        self.load_settings()
    
    def load_settings(self):
        """Загрузка настроек отслеживания"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.tracked_users = data.get('tracked_users', {})
                    # Конвертируем строковые ключи обратно в int
                    self.tracked_users = {int(k): v for k, v in self.tracked_users.items()}
                    logger.info(f"Загружены настройки отслеживания для {len(self.tracked_users)} пользователей")
        except Exception as e:
            logger.error(f"Ошибка загрузки настроек отслеживания: {e}")
            self.tracked_users = {}
    
    def save_settings(self):
        """Сохранение настроек отслеживания"""
        try:
            data = {
                'tracked_users': {str(k): v for k, v in self.tracked_users.items()}
            }
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info("Настройки отслеживания сохранены")
        except Exception as e:
            logger.error(f"Ошибка сохранения настроек отслеживания: {e}")
    
    def get_user_filename(self, user_name: str) -> str:
        """Получение имени файла для пользователя"""
        # Очищаем имя от недопустимых символов
        safe_name = "".join(c for c in user_name if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_name = safe_name.replace(' ', '_').lower()
        return f"{safe_name}.txt"
    
    def log_user_status_period(self, user_id: int, user_name: str, status: str, start_time: datetime, end_time: datetime):
        """Запись периода статуса пользователя в файл"""
        try:
            filename = self.get_user_filename(user_name)
            filepath = os.path.join(self.logs_dir, filename)

            # Форматируем дату и время
            start_date = start_time.strftime("%d.%m.%Y")
            start_time_str = start_time.strftime("%H:%M")
            end_time_str = end_time.strftime("%H:%M")

            # Если период в один день
            if start_time.date() == end_time.date():
                log_entry = f"{user_name} {status} {start_date} {start_time_str}-{end_time_str}\n"
            else:
                # Если период переходит на следующий день
                end_date = end_time.strftime("%d.%m.%Y")
                log_entry = f"{user_name} {status} {start_date} {start_time_str} - {end_date} {end_time_str}\n"

            with open(filepath, 'a', encoding='utf-8') as f:
                f.write(log_entry)

            logger.info(f"Записан период {status} для {user_name}: {start_time_str}-{end_time_str}")

        except Exception as e:
            logger.error(f"Ошибка записи статуса пользователя {user_name}: {e}")
    
    def status_to_string(self, status) -> str:
        """Конвертация статуса в строку"""
        if isinstance(status, UserStatusOnline):
            return "онлайн"
        elif isinstance(status, UserStatusOffline):
            return "оффлайн"
        elif isinstance(status, UserStatusRecently):
            return "недавно был онлайн"
        elif isinstance(status, UserStatusLastWeek):
            return "был онлайн на прошлой неделе"
        elif isinstance(status, UserStatusLastMonth):
            return "был онлайн в прошлом месяце"
        else:
            return "неизвестный статус"
    
    async def track_user(self, user_id: int):
        """Отслеживание конкретного пользователя"""
        try:
            logger.info(f"Начинаем отслеживание пользователя {user_id}")

            while user_id in self.tracked_users:
                try:
                    # Получаем информацию о пользователе
                    user = await self.client.get_entity(user_id)
                    user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()

                    if not user_name:
                        user_name = user.username or f"user_{user_id}"

                    # Обновляем имя в настройках
                    self.tracked_users[user_id]["name"] = user_name

                    # Получаем текущий статус
                    current_status = self.status_to_string(user.status)
                    last_status = self.tracked_users[user_id].get("last_status")
                    now = datetime.now()

                    # Если статус изменился, записываем период предыдущего статуса
                    if current_status != last_status and last_status is not None:
                        # Получаем время начала предыдущего статуса
                        status_start = self.tracked_users[user_id].get("status_start")
                        if status_start:
                            if isinstance(status_start, str):
                                status_start = datetime.fromisoformat(status_start)

                            # Записываем период предыдущего статуса
                            self.log_user_status_period(user_id, user_name, last_status, status_start, now)

                    # Если статус изменился, обновляем данные
                    if current_status != last_status:
                        self.tracked_users[user_id]["last_status"] = current_status
                        self.tracked_users[user_id]["status_start"] = now.isoformat()
                        self.tracked_users[user_id]["last_check"] = now.isoformat()
                        self.save_settings()

                    # Ждем 30 секунд перед следующей проверкой
                    await asyncio.sleep(30)

                except Exception as e:
                    logger.error(f"Ошибка при отслеживании пользователя {user_id}: {e}")
                    await asyncio.sleep(60)  # Ждем дольше при ошибке

        except asyncio.CancelledError:
            logger.info(f"Отслеживание пользователя {user_id} остановлено")
        except Exception as e:
            logger.error(f"Критическая ошибка отслеживания пользователя {user_id}: {e}")
    
    async def start_tracking(self, user_id: int) -> str:
        """Начать отслеживание пользователя"""
        try:
            # Проверяем, не отслеживается ли уже
            if user_id in self.tracked_users:
                return f"❌ Пользователь {user_id} уже отслеживается"
            
            # Получаем информацию о пользователе
            try:
                user = await self.client.get_entity(user_id)
                user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
                
                if not user_name:
                    user_name = user.username or f"user_{user_id}"
                    
            except Exception as e:
                return f"❌ Не удалось найти пользователя с ID {user_id}: {e}"
            
            # Добавляем в отслеживаемые
            now = datetime.now()
            current_status = self.status_to_string(user.status)
            self.tracked_users[user_id] = {
                "name": user_name,
                "last_status": current_status,
                "last_check": now.isoformat(),
                "status_start": now.isoformat()
            }
            
            # Запускаем задачу отслеживания
            task = asyncio.create_task(self.track_user(user_id))
            self.tracking_tasks[user_id] = task
            
            # Сохраняем настройки
            self.save_settings()
            
            return f"✅ Начато отслеживание пользователя {user_name} (ID: {user_id})"
            
        except Exception as e:
            logger.error(f"Ошибка начала отслеживания пользователя {user_id}: {e}")
            return f"❌ Ошибка: {e}"
    
    async def stop_tracking(self, user_id: int) -> str:
        """Остановить отслеживание пользователя"""
        try:
            if user_id not in self.tracked_users:
                return f"❌ Пользователь {user_id} не отслеживается"

            user_data = self.tracked_users[user_id]
            user_name = user_data["name"]

            # Записываем финальный период если есть активный статус
            last_status = user_data.get("last_status")
            status_start = user_data.get("status_start")
            if last_status and status_start:
                if isinstance(status_start, str):
                    status_start = datetime.fromisoformat(status_start)

                now = datetime.now()
                self.log_user_status_period(user_id, user_name, last_status, status_start, now)

            # Останавливаем задачу
            if user_id in self.tracking_tasks:
                self.tracking_tasks[user_id].cancel()
                del self.tracking_tasks[user_id]

            # Удаляем из отслеживаемых
            del self.tracked_users[user_id]

            # Сохраняем настройки
            self.save_settings()

            return f"✅ Остановлено отслеживание пользователя {user_name} (ID: {user_id})"

        except Exception as e:
            logger.error(f"Ошибка остановки отслеживания пользователя {user_id}: {e}")
            return f"❌ Ошибка: {e}"
    
    def get_log_files(self) -> list:
        """Получение списка файлов логов"""
        try:
            if not os.path.exists(self.logs_dir):
                return []
            
            files = []
            for filename in os.listdir(self.logs_dir):
                if filename.endswith('.txt'):
                    filepath = os.path.join(self.logs_dir, filename)
                    files.append(filepath)
            
            return files
            
        except Exception as e:
            logger.error(f"Ошибка получения файлов логов: {e}")
            return []
    
    def filter_logs_by_hours(self, filepath: str, hours: int) -> str:
        """Фильтрация логов по количеству часов"""
        try:
            if not os.path.exists(filepath):
                return ""
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            filtered_lines = []
            
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # Парсим дату и время из строки
                        # Новый формат: "Имя статус дд.мм.гггг чч:мм-чч:мм" или "Имя статус дд.мм.гггг чч:мм - дд.мм.гггг чч:мм"
                        parts = line.split()
                        if len(parts) >= 4:
                            # Ищем дату (формат дд.мм.гггг)
                            date_found = False
                            for i, part in enumerate(parts):
                                if '.' in part and len(part) == 10:  # дд.мм.гггг
                                    try:
                                        # Проверяем, что это действительно дата
                                        datetime.strptime(part, "%d.%m.%Y")
                                        date_str = part

                                        # Следующая часть должна быть временем
                                        if i + 1 < len(parts):
                                            time_part = parts[i + 1]
                                            # Извлекаем начальное время (до дефиса если есть)
                                            if '-' in time_part:
                                                start_time = time_part.split('-')[0]
                                            else:
                                                start_time = time_part

                                            # Парсим дату и время
                                            log_datetime = datetime.strptime(f"{date_str} {start_time}", "%d.%m.%Y %H:%M")

                                            if log_datetime >= cutoff_time:
                                                filtered_lines.append(line)
                                            date_found = True
                                            break
                                    except ValueError:
                                        continue

                            if not date_found:
                                # Fallback для старого формата
                                if len(parts) >= 4:
                                    date_str = parts[-2]  # дд.мм.гггг
                                    time_str = parts[-1]  # чч:мм
                                    log_datetime = datetime.strptime(f"{date_str} {time_str}", "%d.%m.%Y %H:%M")
                                    if log_datetime >= cutoff_time:
                                        filtered_lines.append(line)

                    except Exception as e:
                        logger.warning(f"Не удалось распарсить строку лога: {line} - {e}")
                        continue
            
            return "\n".join(filtered_lines)
            
        except Exception as e:
            logger.error(f"Ошибка фильтрации логов: {e}")
            return ""
    
    def get_status_info(self) -> str:
        """Получение информации о текущем статусе отслеживания"""
        if not self.tracked_users:
            return "📊 Никто не отслеживается"
        
        info_lines = ["📊 Отслеживаемые пользователи:"]
        for user_id, data in self.tracked_users.items():
            name = data.get("name", f"user_{user_id}")
            last_status = data.get("last_status", "неизвестно")
            info_lines.append(f"• {name} (ID: {user_id}) - {last_status}")
        
        return "\n".join(info_lines)
