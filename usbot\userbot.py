#!/usr/bin/env python3
"""
Юзербот для поддержки Huawei
"""

import asyncio
import json
import os
import sys
from typing import Optional, Dict, Any
from telethon import TelegramClient, events
from telethon.tl.types import MessageMediaPhoto, MessageMediaDocument, MessageEntityCustomEmoji

# Добавляем текущую директорию в путь для правильного импорта
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from usbot_config import (
    TELEGRAM_API_ID, TELEGRAM_API_HASH, SETTINGS_FILE, logger,
    SUPPORT_CHAT_ID, STAFF_CHAT_ID
)
from user_tracker import UserTracker


class HuaweiSupportBot:
    """Юзербот для поддержки Huawei"""

    def __init__(self):
        self.client = None
        self.support_chat_id = None
        self.staff_chat_id = None
        self.my_user_id = None
        # Модуль отслеживания пользователей
        self.user_tracker = None
        # Быстрые комментарии
        self.fast_channels = set()  # Множество ID каналов для быстрых комментариев
        self.fast_group_id = None   # ID группы для уведомлений о комментариях
        self.load_settings()
        
    def load_settings(self):
        """Загрузка настроек из файла"""
        if os.path.exists(SETTINGS_FILE):
            try:
                with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.support_chat_id = settings.get('support_chat_id')
                    self.staff_chat_id = settings.get('staff_chat_id')
                    # Загружаем настройки быстрых комментариев
                    self.fast_channels = set(settings.get('fast_channels', []))
                    self.fast_group_id = settings.get('fast_group_id')
                    logger.info(f"Настройки загружены: support={self.support_chat_id}, staff={self.staff_chat_id}, fast_channels={len(self.fast_channels)}")
            except Exception as e:
                logger.error(f"Ошибка загрузки настроек: {e}")

    def save_settings(self):
        """Сохранение настроек в файл"""
        try:
            settings = {
                'support_chat_id': self.support_chat_id,
                'staff_chat_id': self.staff_chat_id,
                'fast_channels': list(self.fast_channels) if hasattr(self, 'fast_channels') else [],
                'fast_group_id': getattr(self, 'fast_group_id', None)
            }
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            logger.info("Настройки сохранены")
        except Exception as e:
                logger.error(f"Ошибка сохранения настроек: {e}")

    async def start(self):
        """Запуск юзербота"""
        try:
            # Создаем клиент
            self.client = TelegramClient('userbot_session', TELEGRAM_API_ID, TELEGRAM_API_HASH)
            
            # Подключаемся
            await self.client.start()
            
            # Получаем информацию о себе
            me = await self.client.get_me()
            self.my_user_id = me.id
            logger.info(f"Юзербот запущен от имени: {me.first_name} (@{me.username})")

            # Инициализируем модуль отслеживания пользователей
            self.user_tracker = UserTracker(self.client)

            # Регистрируем обработчики
            self.register_handlers()

            logger.info("🤖 Юзербот готов к работе!")
            
            # Запускаем бесконечный цикл
            await self.client.run_until_disconnected()
            
        except Exception as e:
            logger.error(f"Ошибка запуска юзербота: {e}")
            raise
    
    def register_handlers(self):
        """Регистрация обработчиков событий"""
        
        @self.client.on(events.NewMessage(pattern=r'^/in$', from_users='me'))
        async def set_support_chat(event):
            """Команда /in - установка чата поддержки"""
            try:
                self.support_chat_id = event.chat_id
                self.save_settings()
                await event.delete()  # Удаляем команду сразу
                logger.info(f"Установлен чат поддержки: {self.support_chat_id}")
            except Exception as e:
                logger.error(f"Ошибка установки чата поддержки: {e}")
                await event.delete()  # Удаляем команду даже при ошибке

        @self.client.on(events.NewMessage(pattern=r'^/to$', from_users='me'))
        async def set_staff_chat(event):
            """Команда /to - установка чата сотрудников"""
            try:
                self.staff_chat_id = event.chat_id
                self.save_settings()
                await event.delete()  # Удаляем команду сразу
                logger.info(f"Установлен чат сотрудников: {self.staff_chat_id}")
            except Exception as e:
                logger.error(f"Ошибка установки чата сотрудников: {e}")
                await event.delete()  # Удаляем команду даже при ошибке

        @self.client.on(events.NewMessage(pattern=r'^/fast(?:\s+(.+))?$', from_users='me'))
        async def handle_fast_command(event):
            """Команда /fast [ID] - быстрые комментарии в каналах"""
            try:
                # Получаем аргумент команды
                match = event.pattern_match
                arg = match.group(1).strip() if match.group(1) else None

                if not arg:
                    await event.edit("❌ Укажите ID канала. Используйте: /fast [ID]")
                    return

                if not arg.isdigit():
                    await event.edit("❌ ID канала должен быть числом")
                    return

                channel_id = int(arg)

                # Проверяем, есть ли уже этот канал в отслеживании
                if not hasattr(self, 'fast_channels'):
                    self.fast_channels = set()

                if channel_id in self.fast_channels:
                    # Убираем из отслеживания
                    self.fast_channels.remove(channel_id)
                    await event.edit(f"✅ Канал {channel_id} убран из быстрых комментариев")
                else:
                    # Добавляем в отслеживание
                    try:
                        # Проверяем, что канал существует и доступен
                        channel = await self.client.get_entity(channel_id)
                        self.fast_channels.add(channel_id)
                        await event.edit(f"✅ Канал {channel.title} ({channel_id}) добавлен в быстрые комментарии")
                    except Exception as e:
                        await event.edit(f"❌ Не удалось найти канал {channel_id}: {e}")
                        return

                # Сохраняем настройки
                self.save_settings()

            except Exception as e:
                logger.error(f"Ошибка команды /fast: {e}")
                await event.edit(f"❌ Ошибка: {e}")

        @self.client.on(events.NewMessage(pattern=r'^/fastgroup$', from_users='me'))
        async def handle_fastgroup_command(event):
            """Команда /fastgroup - установка группы для уведомлений"""
            try:
                self.fast_group_id = event.chat_id
                self.save_settings()
                await event.delete()  # Удаляем команду сразу
                logger.info(f"Установлена группа для уведомлений быстрых комментариев: {self.fast_group_id}")
            except Exception as e:
                logger.error(f"Ошибка установки группы для уведомлений: {e}")
                await event.delete()  # Удаляем команду даже при ошибке

        @self.client.on(events.NewMessage(pattern=r'^/id$', from_users='me'))
        async def handle_id_command(event):
            """Команда /id - получение ID Premium эмодзи"""
            try:
                # Проверяем, является ли это ответом на сообщение
                if not event.is_reply:
                    await event.edit("❌ Эта команда должна быть ответом на сообщение с Premium эмодзи")
                    return

                # Получаем сообщение, на которое отвечаем
                replied_message = await event.get_reply_message()

                if not replied_message or not replied_message.text:
                    await event.edit("❌ Сообщение должно содержать текст с Premium эмодзи")
                    return

                # Ищем custom emoji в entities
                custom_emojis = []
                if replied_message.entities:
                    for entity in replied_message.entities:
                        if isinstance(entity, MessageEntityCustomEmoji):
                            # Извлекаем текст эмодзи
                            emoji_text = replied_message.text[entity.offset:entity.offset + entity.length]
                            custom_emojis.append({
                                'emoji': emoji_text,
                                'document_id': entity.document_id,
                                'offset': entity.offset,
                                'length': entity.length
                            })

                if not custom_emojis:
                    await event.edit("❌ В сообщении не найдено Premium эмодзи")
                    return

                # Формируем ответ
                response_lines = ["🎯 **Найденные Premium эмодзи:**\n"]

                for i, emoji_info in enumerate(custom_emojis, 1):
                    response_lines.append(f"**{i}.** {emoji_info['emoji']}")
                    response_lines.append(f"   📋 **ID:** `{emoji_info['document_id']}`")
                    response_lines.append(f"   📍 **Позиция:** {emoji_info['offset']}-{emoji_info['offset'] + emoji_info['length']}")
                    response_lines.append("")

                response_lines.append("💡 **Как использовать:**")
                response_lines.append("В других ботах используйте:")
                response_lines.append(f"`<tg-emoji document-id=\"{custom_emojis[0]['document_id']}\">{custom_emojis[0]['emoji']}</tg-emoji>`")

                response = "\n".join(response_lines)
                await event.edit(response)

                logger.info(f"Извлечены ID эмодзи: {[e['document_id'] for e in custom_emojis]}")

                # Удаляем сообщение через 30 секунд для удобства
                await asyncio.sleep(30)
                try:
                    await event.delete()
                except:
                    pass  # Игнорируем ошибки удаления

            except Exception as e:
                logger.error(f"Ошибка команды /id: {e}")
                await event.edit(f"❌ Ошибка при извлечении ID эмодзи: {e}")
                # Удаляем сообщение с ошибкой через 10 секунд
                await asyncio.sleep(10)
                try:
                    await event.delete()
                except:
                    pass

        @self.client.on(events.NewMessage())
        async def process_message(event):
            """Обработка всех сообщений"""
            try:
                # Игнорируем свои сообщения
                if event.sender_id == self.my_user_id:
                    return

                # Проверяем быстрые комментарии для каналов
                if hasattr(self, 'fast_channels') and event.chat_id in self.fast_channels:
                    await self.handle_fast_comment(event)
                    return

                # Больше никакой обработки сообщений не требуется

            except Exception as e:
                logger.error(f"Ошибка обработки сообщения: {e}")

    async def handle_fast_comment(self, event):
        """Обработка быстрых комментариев в каналах"""
        try:
            # Проверяем, что это новый пост в канале (не комментарий)
            if event.message.reply_to_msg_id:
                return  # Это комментарий, игнорируем

            # Получаем информацию о канале
            channel = await event.get_chat()

            # Получаем полную информацию о канале для доступа к linked_chat_id
            from telethon.tl.functions.channels import GetFullChannelRequest
            try:
                full_channel = await self.client(GetFullChannelRequest(channel))
                linked_chat_id = full_channel.full_chat.linked_chat_id
            except Exception as e:
                logger.error(f"Ошибка получения полной информации о канале: {e}")
                return

            # Проверяем, есть ли у канала связанная группа обсуждений
            if not linked_chat_id:
                logger.warning(f"Канал {channel.title} ({channel.id}) не имеет связанной группы обсуждений")
                return

            # Отправляем комментарий в связанную группу обсуждений
            try:
                comment_text = ")) "

                # Получаем связанную группу
                linked_chat = await self.client.get_entity(linked_chat_id)

                # Отправляем комментарий как ответ на пост
                await self.client.send_message(
                    linked_chat,
                    comment_text,
                    reply_to=event.message.id
                )

                logger.info(f"Оставлен быстрый комментарий в канале {channel.title}")

                # Отправляем уведомление в группу с /fastgroup, если она установлена
                if hasattr(self, 'fast_group_id') and self.fast_group_id:
                    try:
                        # Формируем ссылку на пост
                        if hasattr(channel, 'username') and channel.username:
                            post_link = f"https://t.me/{channel.username}/{event.message.id}"
                        else:
                            # Для приватных каналов используем формат с -100
                            channel_id_str = str(channel.id)
                            if channel_id_str.startswith('-100'):
                                post_link = f"https://t.me/c/{channel_id_str[4:]}/{event.message.id}"
                            else:
                                post_link = f"https://t.me/c/{channel_id_str}/{event.message.id}"

                        notification_text = f"✅ Оставлен первый комментарий в канале {channel.title}\n🔗 {post_link}"

                        await self.client.send_message(self.fast_group_id, notification_text)
                        logger.info(f"Отправлено уведомление о комментарии в группу {self.fast_group_id}")

                    except Exception as e:
                        logger.error(f"Ошибка отправки уведомления: {e}")

            except Exception as e:
                logger.error(f"Ошибка отправки комментария: {e}")

        except Exception as e:
            logger.error(f"Ошибка обработки быстрого комментария: {e}")

    async def run(self):
        """Запуск userbot"""
        logger.info("Запуск userbot...")
        await self.client.run_until_disconnected()


async def start_userbot():
    """Функция запуска юзербота"""
    bot = HuaweiSupportBot()
    await bot.start()


if __name__ == "__main__":
    asyncio.run(start_userbot())
