#!/usr/bin/env python3
"""
Скрипт для автоматической асинхронизации Telegram бота
"""

import os
import shutil
from pathlib import Path

def backup_files():
    """Создаем бэкап оригинальных файлов"""
    backup_dir = Path("backup")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = ["bot.py", "file_manager.py", "code_executor.py", "gemini_client.py"]
    
    for file_name in files_to_backup:
        if Path(file_name).exists():
            shutil.copy2(file_name, backup_dir / file_name)
            print(f"✅ Backed up {file_name}")

def create_async_file_manager():
    """Создаем полностью асинхронный FileManager"""
    content = '''import os
import aiofiles
import aiofiles.os
import asyncio
import shutil
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from config import DATA_DIR, MAX_OUTPUT_FILES

logger = logging.getLogger(__name__)


class FileManager:
    def __init__(self):
        self.data_dir = DATA_DIR
        self.data_dir.mkdir(exist_ok=True)
    
    async def get_user_dir(self, user_id: int) -> Path:
        """Get user's data directory"""
        user_dir = self.data_dir / str(user_id)
        
        # Асинхронная проверка и создание директории
        if not await aiofiles.os.path.exists(user_dir):
            await aiofiles.os.makedirs(user_dir, exist_ok=True)
        
        return user_dir
    
    async def save_file(self, user_id: int, file_name: str, file_data: bytes) -> Path:
        """Save file to user's directory"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name

        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_data)

        # Автоматическая очистка старых файлов
        try:
            removed_count = await self.clean_old_files(user_id)
            if removed_count > 0:
                logger.info(f"Cleaned {removed_count} old files for user {user_id}")
        except Exception as e:
            logger.error(f"Error cleaning old files for user {user_id}: {e}")

        return file_path
    
    async def read_file(self, user_id: int, file_name: str) -> Optional[bytes]:
        """Read file from user's directory"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name
        
        if not await aiofiles.os.path.exists(file_path):
            return None
        
        async with aiofiles.open(file_path, 'rb') as f:
            return await f.read()
    
    async def list_user_files(self, user_id: int) -> List[Dict[str, Any]]:
        """List all files in user's directory"""
        user_dir = await self.get_user_dir(user_id)
        files = []
        
        # Асинхронный обход файлов
        async def process_file(file_path: Path):
            try:
                if await aiofiles.os.path.isfile(file_path):
                    stat = await aiofiles.os.stat(file_path)
                    return {
                        'name': file_path.name,
                        'path': str(file_path.relative_to(user_dir)),
                        'size': stat.st_size,
                        'modified': stat.st_mtime
                    }
            except:
                pass
            return None
        
        # Получаем все файлы асинхронно
        tasks = []
        for file_path in user_dir.rglob('*'):
            tasks.append(process_file(file_path))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            files = [result for result in results if result is not None and not isinstance(result, Exception)]
        
        return files
    
    async def get_file_info(self, user_id: int, file_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific file"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name
        
        if not await aiofiles.os.path.exists(file_path):
            return None
        
        stat = await aiofiles.os.stat(file_path)
        return {
            'name': file_path.name,
            'path': str(file_path.relative_to(user_dir)),
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'full_path': str(file_path)
        }
    
    async def delete_file(self, user_id: int, file_name: str) -> bool:
        """Delete a file from user's directory"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name
        
        if await aiofiles.os.path.exists(file_path) and await aiofiles.os.path.isfile(file_path):
            try:
                await aiofiles.os.remove(file_path)
                return True
            except:
                return False
        return False
    
    async def clean_user_temp_files(self, user_id: int):
        """Clean temporary files but keep user data"""
        user_dir = await self.get_user_dir(user_id)
        
        # Remove temporary files (those starting with temp_ or script_)
        async def remove_temp_files(pattern: str):
            for file_path in user_dir.glob(pattern):
                try:
                    if await aiofiles.os.path.isfile(file_path):
                        await aiofiles.os.remove(file_path)
                    elif await aiofiles.os.path.isdir(file_path):
                        # Для директорий используем синхронный shutil в executor
                        await asyncio.get_event_loop().run_in_executor(
                            None, shutil.rmtree, str(file_path)
                        )
                except:
                    pass
        
        # Удаляем временные файлы параллельно
        await asyncio.gather(
            remove_temp_files('temp_*'),
            remove_temp_files('script_*'),
            return_exceptions=True
        )
    
    async def get_user_context(self, user_id: int) -> str:
        """Get context about user's files for LLM"""
        files = await self.list_user_files(user_id)

        if not files:
            return "В рабочей папке пока нет файлов."

        context = "Доступные файлы в рабочей папке:\\n"
        for file_info in files:
            size_kb = file_info['size'] / 1024

            # Определяем тип файла по расширению
            file_ext = Path(file_info['name']).suffix.lower()
            file_type = self._get_file_type_description(file_ext)

            # Форматируем дату изменения
            import datetime
            modified_date = datetime.datetime.fromtimestamp(file_info['modified']).strftime('%d.%m.%Y %H:%M')

            context += f"- {file_info['name']} ({file_type}, {size_kb:.1f} KB, изменен {modified_date})\\n"

        context += f"\\nВсего файлов: {len(files)}"
        return context

    def _get_file_type_description(self, file_ext: str) -> str:
        """Get human-readable file type description"""
        type_map = {
            '.txt': 'текст',
            '.csv': 'CSV данные',
            '.json': 'JSON данные',
            '.xml': 'XML данные',
            '.xlsx': 'Excel таблица',
            '.xls': 'Excel таблица',
            '.pdf': 'PDF документ',
            '.docx': 'Word документ',
            '.doc': 'Word документ',
            '.mp3': 'MP3 аудио',
            '.wav': 'WAV аудио',
            '.flac': 'FLAC аудио',
            '.ogg': 'OGG аудио',
            '.mp4': 'MP4 видео',
            '.avi': 'AVI видео',
            '.mkv': 'MKV видео',
            '.mov': 'MOV видео',
            '.jpg': 'JPEG изображение',
            '.jpeg': 'JPEG изображение',
            '.png': 'PNG изображение',
            '.gif': 'GIF изображение',
            '.bmp': 'BMP изображение',
            '.tiff': 'TIFF изображение',
            '.zip': 'ZIP архив',
            '.rar': 'RAR архив',
            '.7z': '7Z архив',
            '.tar': 'TAR архив',
            '.gz': 'GZIP архив',
            '.py': 'Python скрипт',
            '.js': 'JavaScript файл',
            '.html': 'HTML файл',
            '.css': 'CSS файл',
            '.sql': 'SQL скрипт',
        }
        return type_map.get(file_ext, 'файл')
    
    async def limit_output_files(self, user_id: int, new_files: List[Path]) -> List[Path]:
        """Limit number of output files according to MAX_OUTPUT_FILES"""
        if len(new_files) <= MAX_OUTPUT_FILES:
            return new_files
        
        # Асинхронно получаем время модификации для всех файлов
        async def get_mtime(file_path: Path):
            try:
                stat = await aiofiles.os.stat(file_path)
                return file_path, stat.st_mtime
            except:
                return file_path, 0
        
        file_times = await asyncio.gather(*[get_mtime(f) for f in new_files])
        
        # Sort by modification time (newest first) and take only the limit
        sorted_files = sorted(file_times, key=lambda x: x[1], reverse=True)
        limited_files = [f[0] for f in sorted_files[:MAX_OUTPUT_FILES]]
        
        # Remove excess files asynchronously
        excess_files = [f[0] for f in sorted_files[MAX_OUTPUT_FILES:]]
        
        async def remove_file(file_path: Path):
            try:
                if await aiofiles.os.path.exists(file_path):
                    await aiofiles.os.remove(file_path)
            except:
                pass
        
        if excess_files:
            await asyncio.gather(*[remove_file(f) for f in excess_files], return_exceptions=True)
        
        return limited_files
    
    async def get_storage_stats(self, user_id: int) -> Dict[str, Any]:
        """Get storage statistics for user"""
        user_dir = await self.get_user_dir(user_id)
        total_size = 0
        file_count = 0

        # Асинхронно обрабатываем все файлы
        async def process_file(file_path: Path):
            try:
                if await aiofiles.os.path.isfile(file_path):
                    stat = await aiofiles.os.stat(file_path)
                    return stat.st_size, 1
            except:
                pass
            return 0, 0

        tasks = [process_file(file_path) for file_path in user_dir.rglob('*')]
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, tuple):
                    size, count = result
                    total_size += size
                    file_count += count

        return {
            'total_size_bytes': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'file_count': file_count,
            'directory': str(user_dir)
        }

    async def clean_old_files(self, user_id: int, max_files: int = 50, max_age_days: int = 30):
        """Clean old files to prevent storage overflow"""
        user_dir = await self.get_user_dir(user_id)
        files = []

        # Асинхронно собираем все файлы с информацией
        async def collect_file_info(file_path: Path):
            try:
                if await aiofiles.os.path.isfile(file_path):
                    stat = await aiofiles.os.stat(file_path)
                    return {
                        'path': file_path,
                        'modified': stat.st_mtime,
                        'size': stat.st_size
                    }
            except:
                pass
            return None

        tasks = [collect_file_info(file_path) for file_path in user_dir.rglob('*')]
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            files = [result for result in results if result is not None and not isinstance(result, Exception)]

        # Удаляем файлы старше max_age_days дней
        import time
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60

        files_to_remove = []
        for file_info in files:
            if current_time - file_info['modified'] > max_age_seconds:
                files_to_remove.append(file_info['path'])

        # Если файлов больше max_files, удаляем самые старые
        if len(files) > max_files:
            files.sort(key=lambda f: f['modified'])
            for file_info in files[max_files:]:
                files_to_remove.append(file_info['path'])

        # Асинхронно удаляем файлы
        async def remove_file(file_path: Path):
            try:
                if await aiofiles.os.path.exists(file_path):
                    await aiofiles.os.remove(file_path)
                    return 1
            except Exception as e:
                logger.error(f"Error removing file {file_path}: {e}")
            return 0

        if files_to_remove:
            results = await asyncio.gather(*[remove_file(f) for f in files_to_remove], return_exceptions=True)
            removed_count = sum(r for r in results if isinstance(r, int))
        else:
            removed_count = 0

        return removed_count
'''
    
    with open("file_manager.py", "w", encoding="utf-8") as f:
        f.write(content)
    print("✅ Created async file_manager.py")

def update_bot_py():
    """Обновляем bot.py для использования асинхронных методов"""

    # Читаем текущий bot.py
    with open("bot.py", "r", encoding="utf-8") as f:
        content = f.read()

    # Заменяем синхронные вызовы на асинхронные
    replacements = [
        ("files = file_manager.list_user_files(user_id)", "files = await file_manager.list_user_files(user_id)"),
        ("file_context = file_manager.get_user_context(user_id)", "file_context = await file_manager.get_user_context(user_id)"),
        ("new_files = file_manager.limit_output_files(user_id, new_files)", "new_files = await file_manager.limit_output_files(user_id, new_files)"),
        ("stats = file_manager.get_storage_stats(user_id)", "stats = await file_manager.get_storage_stats(user_id)"),
        ("file_manager.clean_user_temp_files(user_id)", "await file_manager.clean_user_temp_files(user_id)"),
        ("user_dir = file_manager.get_user_dir(user_id)", "user_dir = await file_manager.get_user_dir(user_id)"),
    ]

    for old, new in replacements:
        content = content.replace(old, new)

    # Записываем обновленный файл
    with open("bot.py", "w", encoding="utf-8") as f:
        f.write(content)

    print("✅ Updated bot.py with async calls")

def update_code_executor():
    """Обновляем CodeExecutor для лучшей асинхронности"""

    content = '''import asyncio
import subprocess
import tempfile
import os
import shutil
import signal
import time
import aiofiles
import aiofiles.os
from pathlib import Path
from typing import Tuple, List, Optional
from config import MAX_EXECUTION_TIME, MAX_STDOUT_SIZE, TEMP_DIR


class CodeExecutor:
    def __init__(self, user_data_dir: Path):
        self.user_data_dir = user_data_dir
        asyncio.create_task(self._ensure_user_dir())

    async def _ensure_user_dir(self):
        """Асинхронно создаем директорию пользователя"""
        if not await aiofiles.os.path.exists(self.user_data_dir):
            await aiofiles.os.makedirs(self.user_data_dir, exist_ok=True)

    def extract_python_code(self, llm_response: str) -> Optional[str]:
        """
        Extract Python code from LLM response.
        Looks for 'python=' at the beginning of a line.
        """
        lines = llm_response.split('\\n')
        code_lines = []
        in_code_block = False
        indent_level = 0

        for i, line in enumerate(lines):
            if line.strip().startswith('python='):
                in_code_block = True
                continue
            elif in_code_block:
                stripped = line.strip()

                # Skip empty lines
                if not stripped:
                    code_lines.append(line)
                    continue

                # Check if this looks like Python code
                is_python_line = (
                    # Python keywords and constructs
                    any(stripped.startswith(kw) for kw in [
                        'import ', 'from ', 'def ', 'class ', 'if ', 'elif ', 'else:',
                        'for ', 'while ', 'try:', 'except', 'finally:', 'with ',
                        'return', 'yield', 'break', 'continue', 'pass', 'raise',
                        'print(', 'open(', 'len(', 'str(', 'int(', 'float(',
                        'range(', 'enumerate(', 'zip(', 'list(', 'dict(', 'set('
                    ]) or
                    # Assignment or method calls
                    '=' in stripped or '(' in stripped or
                    # Indented lines (likely part of a block)
                    line.startswith('    ') or line.startswith('\\t') or
                    # Comments
                    stripped.startswith('#')
                )

                # Stop conditions for natural language
                is_natural_language = (
                    stripped.startswith('```') or
                    stripped.startswith('python=') or
                    # Russian text indicators
                    any(char in stripped for char in 'абвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ') or
                    # Common natural language starters
                    any(stripped.startswith(phrase) for phrase in [
                        'Конвертация', 'Готово', 'Файл', 'Результат', 'Обработка',
                        'Создание', 'Сохранение', 'Загрузка', 'Выполнение'
                    ])
                )

                # If it's clearly natural language and not Python, stop
                if is_natural_language and not is_python_line:
                    break

                code_lines.append(line)

        if code_lines:
            # Remove trailing empty lines
            while code_lines and not code_lines[-1].strip():
                code_lines.pop()
            return '\\n'.join(code_lines)
        return None

    async def execute_code(self, code: str) -> Tuple[str, str, int, List[Path]]:
        """
        Execute Python code in user's directory with safety limits.

        Returns:
            (stdout, stderr, exit_code, created_files)
        """
        # Create temporary script file
        script_path = TEMP_DIR / f"script_{int(time.time())}.py"

        try:
            # Асинхронно записываем код во временный файл
            async with aiofiles.open(script_path, 'w', encoding='utf-8') as f:
                await f.write(code)

            # Get list of files before execution
            files_before = set()
            if await aiofiles.os.path.exists(self.user_data_dir):
                files_before = set(self.user_data_dir.rglob('*'))

            # Execute the script
            process = await asyncio.create_subprocess_exec(
                'python', str(script_path),
                cwd=str(self.user_data_dir),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                limit=MAX_STDOUT_SIZE
            )

            try:
                # Wait for completion with timeout
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=MAX_EXECUTION_TIME
                )
                exit_code = process.returncode

            except asyncio.TimeoutError:
                # Kill the process if it times out
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass

                stdout = b""
                stderr = f"Execution timed out after {MAX_EXECUTION_TIME} seconds".encode()
                exit_code = -1

            # Decode output
            stdout_str = stdout.decode('utf-8', errors='replace')
            stderr_str = stderr.decode('utf-8', errors='replace')

            # Truncate output if too large
            if len(stdout_str) > MAX_STDOUT_SIZE:
                stdout_str = stdout_str[:MAX_STDOUT_SIZE] + "\\n...truncated"

            if len(stderr_str) > MAX_STDOUT_SIZE:
                stderr_str = stderr_str[:MAX_STDOUT_SIZE] + "\\n...truncated"

            # Find newly created files
            files_after = set()
            if await aiofiles.os.path.exists(self.user_data_dir):
                files_after = set(self.user_data_dir.rglob('*'))

            new_files = []
            for file_path in files_after - files_before:
                if await aiofiles.os.path.isfile(file_path):
                    new_files.append(file_path)

            return stdout_str, stderr_str, exit_code, new_files

        except Exception as e:
            return "", f"Execution error: {str(e)}", -1, []

        finally:
            # Clean up temporary script
            try:
                if await aiofiles.os.path.exists(script_path):
                    await aiofiles.os.remove(script_path)
            except:
                pass

    async def get_user_files(self) -> List[Path]:
        """Get list of all files in user's directory"""
        if not await aiofiles.os.path.exists(self.user_data_dir):
            return []

        files = []
        for file_path in self.user_data_dir.rglob('*'):
            if await aiofiles.os.path.isfile(file_path):
                files.append(file_path)
        return files

    async def clean_temp_files(self):
        """Clean temporary files but keep user data"""
        try:
            for temp_file in TEMP_DIR.glob("script_*.py"):
                if await aiofiles.os.path.exists(temp_file):
                    await aiofiles.os.remove(temp_file)
        except:
            pass
'''

    with open("code_executor.py", "w", encoding="utf-8") as f:
        f.write(content)
    print("✅ Updated code_executor.py with full async support")

def add_connection_pool_to_gemini():
    """Добавляем пул соединений к GeminiClient"""

    content = '''import json
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional
from config import GEMINI_API_KEY, GEMINI_API_URL


class GeminiClient:
    def __init__(self):
        self.api_key = GEMINI_API_KEY
        self.api_url = GEMINI_API_URL
        self.headers = {
            'Content-Type': 'application/json',
            'X-goog-api-key': self.api_key
        }
        self._session = None
        self._connector = None

    async def _get_session(self):
        """Получаем или создаем HTTP сессию с пулом соединений"""
        if self._session is None or self._session.closed:
            # Создаем коннектор с пулом соединений
            self._connector = aiohttp.TCPConnector(
                limit=100,  # Максимум соединений в пуле
                limit_per_host=30,  # Максимум соединений на хост
                ttl_dns_cache=300,  # TTL для DNS кэша
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            # Создаем сессию с таймаутами
            timeout = aiohttp.ClientTimeout(
                total=60,  # Общий таймаут
                connect=10,  # Таймаут подключения
                sock_read=30  # Таймаут чтения
            )

            self._session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=timeout,
                headers={'User-Agent': 'AI-Workshop-Bot/1.0'}
            )

        return self._session

    async def close(self):
        """Закрываем сессию и коннектор"""
        if self._session and not self._session.closed:
            await self._session.close()
        if self._connector:
            await self._connector.close()

    async def generate_content(self, messages: List[Dict[str, Any]]) -> Optional[str]:
        """
        Generate content using Gemini 2.5 Pro API with connection pooling

        Args:
            messages: List of message objects with role and content

        Returns:
            Generated text response or None if error
        """
        try:
            # Convert messages to Gemini format
            contents = []
            system_instruction = None

            for message in messages:
                if message.get('role') == 'system':
                    # System messages are handled separately in Gemini
                    system_instruction = {"parts": [{"text": message['content']}]}
                elif message.get('role') == 'user':
                    contents.append({
                        "role": "user",
                        "parts": [{"text": message['content']}]
                    })
                elif message.get('role') == 'assistant':
                    contents.append({
                        "role": "model",
                        "parts": [{"text": message['content']}]
                    })

            payload = {
                "contents": contents,
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 8192,
                }
            }

            if system_instruction:
                payload["systemInstruction"] = system_instruction

            session = await self._get_session()

            async with session.post(
                self.api_url,
                headers=self.headers,
                json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    # Extract text from response
                    if 'candidates' in result and len(result['candidates']) > 0:
                        candidate = result['candidates'][0]
                        if 'content' in candidate and 'parts' in candidate['content']:
                            parts = candidate['content']['parts']
                            if len(parts) > 0 and 'text' in parts[0]:
                                return parts[0]['text']

                    return None
                else:
                    error_text = await response.text()
                    print(f"Gemini API error {response.status}: {error_text}")
                    return None

        except asyncio.TimeoutError:
            print("Gemini API timeout")
            return None
        except Exception as e:
            print(f"Gemini API exception: {e}")
            return None

    async def chat_completion(self, system_prompt: str, user_message: str,
                            conversation_history: List[Dict[str, Any]] = None) -> Optional[str]:
        """
        Simple chat completion method

        Args:
            system_prompt: System instruction
            user_message: User's message
            conversation_history: Previous conversation turns

        Returns:
            Generated response or None if error
        """
        messages = []

        # Add system prompt
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Add conversation history
        if conversation_history:
            messages.extend(conversation_history)

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        return await self.generate_content(messages)
'''

    with open("gemini_client.py", "w", encoding="utf-8") as f:
        f.write(content)
    print("✅ Updated gemini_client.py with connection pooling")

def main():
    print("🚀 Начинаем асинхронизацию Telegram бота...")

    # Создаем бэкап
    backup_files()

    # Создаем асинхронный FileManager
    create_async_file_manager()

    # Обновляем bot.py
    update_bot_py()

    # Обновляем CodeExecutor
    update_code_executor()

    # Добавляем пул соединений к Gemini
    add_connection_pool_to_gemini()

    print("✅ Асинхронизация завершена!")
    print("📁 Бэкапы сохранены в папке 'backup'")
    print("🔧 Все компоненты теперь полностью асинхронны!")
    print("🚀 Можете запускать бота: python run.py")

if __name__ == "__main__":
    main()
