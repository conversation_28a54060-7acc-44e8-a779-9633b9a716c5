import subprocess
import sys
import os

# Определяем путь для установки, чтобы он соответствовал настройкам сервера
# os.path.expanduser('~') вернет /home/<USER>
install_path = os.path.join(os.path.expanduser('~'), '.local')

# Команда для принудительной переустановки moviepy в правильную директорию
command = [
    sys.executable,
    "-m",
    "pip",
    "install",
    "--upgrade",
    "--force-reinstall",
    "--target",  # Используем --target для точного указания пути
    install_path,
    "moviepy"
]

print(f"Подготовка к выполнению команды: {' '.join(command)}")
print("Это может занять некоторое время...")

try:
    # Запускаем команду установки
    result = subprocess.run(
        command,
        capture_output=True,
        text=True,
        check=True  # Вызовет исключение, если pip вернет ошибку
    )
    print("\n--- ВЫВОД УСТАНОВЩИКА (STDOUT) ---")
    print(result.stdout)
    print("\n--- ОШИБКИ УСТАНОВЩИКА (STDERR) ---")
    print(result.stderr if result.stderr else "Ошибок нет.")
    print("\n[SUCCESS] Библиотека moviepy была успешно переустановлена!")
    print("Пожалуйста, перезапустите сервер, чтобы изменения вступили в силу.")

except subprocess.CalledProcessError as e:
    print("\n--- [ERROR] Команда установки pip завершилась с ошибкой! ---")
    print("\n--- ВЫВОД УСТАНОВЩИКА (STDOUT) ---")
    print(e.stdout)
    print("\n--- ОШИБКИ УСТАНОВЩИКА (STDERR) ---")
    print(e.stderr)
    print("\n[FAILURE] Не удалось переустановить moviepy. Проверьте вывод ошибок выше.")

except Exception as e:
    print(f"\n[CRITICAL FAILURE] Произошла непредвиденная ошибка: {e}")