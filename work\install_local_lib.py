import os
import subprocess
import sys
import requests
import tarfile
import io
import shutil

# --- УСТАНОВЩИК С PyPI v7 (ФИНАЛЬНАЯ ВЕРСИЯ) ---

LIBS_DIR = "libs"
ABS_LIBS_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), LIBS_DIR))
MARKER_FILE = os.path.join(ABS_LIBS_DIR, ".install_complete_v8")
# Официальный источник для стабильной версии 1.0.3
MOVIEPY_TAR_GZ_URL = "https://files.pythonhosted.org/packages/source/m/moviepy/moviepy-1.0.3.tar.gz"
# Зависимости для moviepy 1.0.3
PIP_PACKAGES = [
    # Зависимости для moviepy
    "numpy",
    "decorator",
    "tqdm",
    "imageio",
    "proglog",
    "imageio-ffmpeg",
    # Обработка изображений и видео
    "Pillow",
    "opencv-python-headless",
    "scikit-image",
    "pytesseract",
    # Обработка аудио
    "pydub",
    "librosa",
    "soundfile",
    # Генерация речи
    "gTTS",
    # Анализ данных и веб
    "pandas",
    "requests",
    "beautifulsoup4",
    "lxml"
]


def install_dependencies():
    if os.path.exists(MARKER_FILE):
        return

    print(f"[УСТАНОВЩИК С PyPI v7] Начинаю установку в: {ABS_LIBS_DIR}")
    if os.path.isdir(ABS_LIBS_DIR):
        print("  Очистка старой папки libs...")
        shutil.rmtree(ABS_LIBS_DIR)
    os.makedirs(ABS_LIBS_DIR, exist_ok=True)

    # --- Шаг 1: Установка зависимостей ---
    print("\n--- Шаг 1: Установка зависимостей через pip ---")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--target", ABS_LIBS_DIR, *PIP_PACKAGES
        ])
        print("  [УСПЕХ] Все зависимости установлены.")
    except subprocess.CalledProcessError as e:
        print(f"[КРИТИЧЕСКАЯ ОШИБКА] Не удалось установить зависимости: {e}")
        return

    # --- Шаг 2: Скачивание и распаковка официального архива moviepy ---
    print("\n--- Шаг 2: Скачивание moviepy 1.0.3 с PyPI ---")
    temp_extract_dir = os.path.join(ABS_LIBS_DIR, "_temp_moviepy_extract")
    try:
        response = requests.get(MOVIEPY_TAR_GZ_URL, stream=True)
        response.raise_for_status()
        
        # Используем tarfile для .tar.gz архивов
        with tarfile.open(fileobj=io.BytesIO(response.content), mode="r:gz") as tar:
            tar.extractall(temp_extract_dir)
        print("  [УСПЕХ] Архив распакован.")

        # --- Надежное перемещение ---
        # Структура архива предсказуема: moviepy-1.0.3/moviepy
        source_path = os.path.join(temp_extract_dir, 'moviepy-1.0.3', 'moviepy')
        destination_path = os.path.join(ABS_LIBS_DIR, 'moviepy')
        
        print(f"  [ИНФО] Перемещение из '{source_path}' в '{destination_path}'...")
        shutil.move(source_path, destination_path)
        print("  [УСПЕХ] moviepy успешно перемещен.")

    except Exception as e:
        print(f"[КРИТИЧЕСКАЯ ОШИБКА] Не удалось скачать или распаковать moviepy: {e}")
        return
    finally:
        if os.path.isdir(temp_extract_dir):
            shutil.rmtree(temp_extract_dir)

    print("\n[ИНФО] Создание финального файла-маркера v7.")
    with open(MARKER_FILE, "w") as f:
        f.write("PyPI installation v7 complete.")
    
    print("\n[ЗАВЕРШЕНО] Финальная установка окончена.")


if __name__ == "__main__":
    install_dependencies()