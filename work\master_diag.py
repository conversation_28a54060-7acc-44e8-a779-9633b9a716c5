import os
import sys
import traceback

def run_diagnostics():
    print("\n\n---[ МАСТЕР-ДИАГНОСТИКА: НАЧАЛО ]---")

    # 1. Проверяем, какой путь мы считаем правильным
    bot_root_dir = '/home/<USER>/work'
    libs_path = os.path.join(bot_root_dir, 'libs')
    print(f"[1] Целевой путь к библиотекам: {libs_path}")

    # 2. Проверяем, существует ли этот путь и что внутри
    print(f"[2] Проверка существования пути...")
    if os.path.isdir(libs_path):
        print(f"  [УСПЕХ] Директория '{libs_path}' существует.")
        
        moviepy_editor_path = os.path.join(libs_path, 'moviepy', 'editor.py')
        print(f"[3] Проверка наличия файла '{moviepy_editor_path}'...")
        if os.path.isfile(moviepy_editor_path):
            print(f"  [УСПЕХ] Ключевой файл 'editor.py' НАЙДЕН.")
        else:
            print(f"  [КРИТИЧЕСКАЯ ОШИБКА] Файл 'editor.py' НЕ НАЙДЕН в папке moviepy!")
            
        print("[4] Содержимое папки libs/moviepy:")
        try:
            print(f"  {os.listdir(os.path.join(libs_path, 'moviepy'))}")
        except Exception as e:
            print(f"  [ОШИБКА] Не удалось прочитать содержимое папки: {e}")
            
    else:
        print(f"  [КРИТИЧЕСКАЯ ОШИБКА] Директория '{libs_path}' НЕ НАЙДЕНА!")

    # 5. Проверяем, как Python видит пути
    print("[5] Манипуляция с sys.path и проверка...")
    # Вставляем наш путь в самое начало
    sys.path.insert(0, libs_path)
    print(f"  sys.path после добавления нашего пути:")
    # Выводим пути для наглядности
    for i, p in enumerate(sys.path):
        print(f"    {i}: {p}")

    # 6. Финальная попытка импорта
    print("[6] Попытка импорта 'from moviepy.editor import VideoFileClip'...")
    try:
        from moviepy.editor import VideoFileClip
        print("\n  [!!!!!!!!! УСПЕХ !!!!!!!!!]")
        print("  Модуль 'moviepy.editor' был успешно импортирован!")
        print(f"  Расположение модуля: {VideoFileClip.__module__}")
    except Exception as e:
        print("\n  [--- ОШИБКА ИМПОРТА ---]")
        print(traceback.format_exc())
        print("  [ПРЕДПОЛОЖЕНИЕ] Проблема, скорее всего, не в путях, а в зависимостях самой moviepy (например, numpy).")


    print("\n---[ МАСТЕР-ДИАГНОСТИКА: ЗАВЕРШЕНА ]---\n\n")

# Запускаем диагностику сразу при импорте этого файла
run_diagnostics()