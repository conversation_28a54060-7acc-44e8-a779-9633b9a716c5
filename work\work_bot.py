import asyncio
import logging
import re
import aiofiles.os
from pathlib import Path
from typing import List, Dict, Any, Optional

from aiogram import Bo<PERSON>, Dispatcher, types, F
from aiogram.filters import Command
from aiogram.types import Message, Document, BufferedInputFile, ReactionTypeEmoji, FSInputFile
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.storage.memory import MemoryStorage

import install_local_lib
install_local_lib.install_dependencies()

from work_config import TG_TOKEN, SYSTEM_PROMPT_FILE
from work_gemini_client import GeminiClient
from work_code_executor import CodeExecutor
from work_file_manager import FileManager


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize components
bot = Bot(token=TG_TOKEN)
dp = Dispatcher(storage=MemoryStorage())
gemini_client = GeminiClient()
file_manager = FileManager()

# Conversation history storage (in production, use Redis or database)
conversation_history: Dict[int, List[Dict[str, Any]]] = {}


class WorkshopStates(StatesGroup):
    waiting_for_task = State()


def get_system_prompt() -> str:
    """Load system prompt from file"""
    try:
        with open(SYSTEM_PROMPT_FILE, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except Exception as e:
        logger.error(f"Failed to load system prompt: {e}")
        return "Ты — AI-ассистент в Telegram-боте AI Workshop."


def get_conversation_history(user_id: int) -> List[Dict[str, Any]]:
    """Get conversation history for user (last 6 turns)"""
    if user_id not in conversation_history:
        conversation_history[user_id] = []
    
    # Return last 6 turns (3 user + 3 assistant messages)
    return conversation_history[user_id][-6:]


def add_to_conversation_history(user_id: int, role: str, content: str):
    """Add message to conversation history"""
    if user_id not in conversation_history:
        conversation_history[user_id] = []
    
    conversation_history[user_id].append({
        "role": role,
        "content": content
    })
    
    # Keep only last 12 messages (6 turns)
    if len(conversation_history[user_id]) > 12:
        conversation_history[user_id] = conversation_history[user_id][-12:]


@dp.message(Command("start"))
async def cmd_start(message: Message, state: FSMContext):
    """Handle /start command"""
    await state.set_state(WorkshopStates.waiting_for_task)
    
    welcome_text = """
🤖 Добро пожаловать в AI Workshop Bot!

Я могу помочь вам с обработкой файлов и выполнением различных задач с помощью Python.

📎 Отправьте мне файлы и опишите, что нужно сделать
💬 Или просто напишите задачу текстом

Примеры:
• Отправьте MP3 файл + "Конвертируй в WAV 44kHz"
• Отправьте CSV + "Покажи первые 10 строк"
• "Создай график синусоиды"

Начнем! 🚀
"""
    
    await message.answer(welcome_text)


@dp.message(Command("help"))
async def cmd_help(message: Message):
    """Handle /help command"""
    help_text = """
🔧 AI Workshop Bot - Помощь

Возможности:
• Обработка аудио/видео файлов (pydub)
• Работа с изображениями (Pillow)
• Анализ данных (pandas, numpy)
• Создание графиков и визуализаций
• Архивирование и сжатие файлов

Ограничения:
⏱ Время выполнения: до 20 секунд
💾 Память: до 512 MB
📁 Файлов на выходе: до 20

Команды:
/start - Начать работу
/help - Эта справка
/files - Список ваших файлов
/clear - Очистить историю разговора
/clearfiles - Удалить все ваши файлы
"""
    
    await message.answer(help_text)


@dp.message(Command("files"))
async def cmd_files(message: Message):
    """Show user's files"""
    user_id = message.from_user.id
    files = await file_manager.list_user_files(user_id)

    if not files:
        await message.answer("📁 У вас пока нет сохраненных файлов.")
        return

    files_text = "📁 Ваши файлы:\n\n"

    # Сортируем файлы по дате изменения (новые сверху)
    sorted_files = sorted(files, key=lambda f: f['modified'], reverse=True)

    for file_info in sorted_files:
        size_kb = file_info['size'] / 1024

        # Определяем тип файла
        from pathlib import Path
        file_ext = Path(file_info['name']).suffix.lower()
        file_type = file_manager._get_file_type_description(file_ext)

        # Форматируем дату
        import datetime
        modified_date = datetime.datetime.fromtimestamp(file_info['modified']).strftime('%d.%m %H:%M')

        # Выбираем эмодзи в зависимости от типа файла
        emoji = get_file_emoji(file_ext)

        files_text += f"{emoji} {file_info['name']}\n"
        files_text += f"   {file_type} • {size_kb:.1f} KB • {modified_date}\n\n"

    stats = await file_manager.get_storage_stats(user_id)
    files_text += f"📊 Всего: {stats['file_count']} файлов, {stats['total_size_mb']:.1f} MB"

    await message.answer(files_text)


def get_file_emoji(file_ext: str) -> str:
    """Get emoji for file type"""
    emoji_map = {
        '.txt': '📄',
        '.csv': '📊',
        '.json': '📋',
        '.xml': '📋',
        '.xlsx': '📈',
        '.xls': '📈',
        '.pdf': '📕',
        '.docx': '📘',
        '.doc': '📘',
        '.mp3': '🎵',
        '.wav': '🎵',
        '.flac': '🎵',
        '.ogg': '🎵',
        '.mp4': '🎬',
        '.avi': '🎬',
        '.mkv': '🎬',
        '.mov': '🎬',
        '.jpg': '🖼',
        '.jpeg': '🖼',
        '.png': '🖼',
        '.gif': '🖼',
        '.bmp': '🖼',
        '.tiff': '🖼',
        '.zip': '📦',
        '.rar': '📦',
        '.7z': '📦',
        '.tar': '📦',
        '.gz': '📦',
        '.py': '🐍',
        '.js': '📜',
        '.html': '🌐',
        '.css': '🎨',
        '.sql': '🗃',
    }
    return emoji_map.get(file_ext, '📄')


@dp.message(Command("clear"))
async def cmd_clear(message: Message):
    """Clear conversation history"""
    user_id = message.from_user.id
    if user_id in conversation_history:
        conversation_history[user_id] = []

    await message.answer("🗑 История разговора очищена.")


@dp.message(Command("clearfiles"))
async def cmd_clear_files(message: Message):
    """Clear all user files"""
    user_id = message.from_user.id

    try:
        user_dir = await file_manager.get_user_dir(user_id)

        # Удаляем все файлы в папке пользователя асинхронно
        import shutil
        import aiofiles.os

        if await aiofiles.os.path.exists(user_dir):
            # Используем executor для shutil.rmtree
            await asyncio.get_event_loop().run_in_executor(
                None, shutil.rmtree, str(user_dir)
            )
            await aiofiles.os.makedirs(user_dir, exist_ok=True)

        await message.answer("🗑 Все ваши файлы удалены.")

    except Exception as e:
        logger.error(f"Error clearing user files: {e}")
        await message.answer(f"❌ Ошибка при удалении файлов: {str(e)}")


@dp.message(F.document | F.photo | F.video | F.audio | F.voice)
async def handle_files(message: Message, state: FSMContext):
    """Handle document and media uploads"""
    user_id = message.from_user.id
    
    file_to_process = None
    file_name = None
    media_type_str = ""

    if message.document:
        file_to_process = message.document
        file_name = message.document.file_name
        media_type_str = "Файл"
    elif message.photo:
        file_to_process = message.photo[-1]
        media_type_str = "Фото"
    elif message.video:
        file_to_process = message.video
        media_type_str = "Видео"
    elif message.audio:
        file_to_process = message.audio
        media_type_str = "Аудио"
    elif message.voice:
        file_to_process = message.voice
        media_type_str = "Голосовое сообщение"
    
    if not file_to_process:
        return

    try:
        await message.react([ReactionTypeEmoji(emoji="❤")])
        file_info = await bot.get_file(file_to_process.file_id)

        # Use original file name if available, otherwise generate from path
        if not file_name:
            file_name = getattr(file_to_process, 'file_name', Path(file_info.file_path).name)

        # Download and save the file
        file_data = await bot.download_file(file_info.file_path)
        await file_manager.save_file(user_id, file_name, file_data.read())

        reply_text = f"✅ {media_type_str} '{file_name}' сохранен!"

        # If there is a caption, process it immediately.
        if message.caption:
            await message.reply(f"{reply_text} Начинаю обработку задачи...")
            message.text = message.caption
            await handle_task(message, state)
        else:
            await message.reply(f"{reply_text}\n\nТеперь опишите, что нужно с ним сделать.")
            await state.set_state(WorkshopStates.waiting_for_task)

    except Exception as e:
        logger.error(f"Error handling file: {e}")
        await message.answer(f"❌ Ошибка при сохранении файла: {str(e)}")
    finally:
        # Remove reaction after operation
        await message.react([])


@dp.message(WorkshopStates.waiting_for_task)
async def handle_task(message: Message, state: FSMContext):
    """Handle user task with LLM processing"""
    user_id = message.from_user.id
    user_message = message.text # Caption is now passed as text
    
    if not user_message:
        await message.answer("Пожалуйста, опишите задачу текстом.")
        return
    
    # Show typing indicator
    await bot.send_chat_action(message.chat.id, "typing")
    
    try:
        # Prepare context
        system_prompt = get_system_prompt()
        file_context = await file_manager.get_user_context(user_id)
        
        # Format user message with context
        full_user_message = f"{file_context}\n\nЗадача: {user_message or ''}"
        
        # Get conversation history
        history = get_conversation_history(user_id)
        
        # Call Gemini API
        response = await gemini_client.chat_completion(
            system_prompt=system_prompt,
            user_message=full_user_message,
            conversation_history=history
        )
        
        if not response:
            await message.answer("❌ Ошибка при обращении к AI. Попробуйте еще раз.")
            return
        
        # Add to conversation history
        add_to_conversation_history(user_id, "user", user_message)
        add_to_conversation_history(user_id, "assistant", response)
        
        # Process response
        await process_llm_response(message, user_id, response)
        
    except Exception as e:
        logger.error(f"Error processing task: {e}")
        await message.answer(f"❌ Ошибка при обработке задачи: {str(e)}")


async def process_llm_response(message: Message, user_id: int, llm_response: str):
    """Process LLM response and execute code if present"""
    user_dir = await file_manager.get_user_dir(user_id)
    code_executor = CodeExecutor(user_dir)
    
    # Extract Python code
    python_code = code_executor.extract_python_code(llm_response)
    
    if python_code:
        # Execute code
        await bot.send_chat_action(message.chat.id, "typing")
        
        try:
            await message.react([ReactionTypeEmoji(emoji="❤")])
            stdout, stderr, exit_code, new_files = await code_executor.execute_code(python_code)
        finally:
            # Remove reaction after operation
            await message.react([])
        
        # Limit output files
        new_files = await file_manager.limit_output_files(user_id, new_files)
        
        # Формируем текстовый ответ ТОЛЬКО из stdout и stderr, если они есть
        response_parts = []
        if stdout:
            response_parts.append(f"📋 **Результат выполнения:**\n{stdout}")
        if stderr:
            response_parts.append(f"⚠️ **Ошибки:**\n{stderr}")

        response_text = "\n\n".join(response_parts)

        # Отправляем текстовый ответ, только если он не пустой
        if response_text:
            await message.reply(response_text, parse_mode="Markdown")

        # Отправляем все созданные файлы как документы
        if new_files:
            for file_path in new_files:
                try:
                    await bot.send_document(message.chat.id, document=FSInputFile(file_path))
                except Exception as e:
                    await message.reply(f"Не удалось отправить файл {file_path.name}: {e}")
        else:
            # Если файлов нет, но и ошибок не было, сообщаем, что все готово
            if exit_code == 0 and not stderr:
                # Ищем в stdout ключевые слова, чтобы сделать ответ более осмысленным
                summary = "Готово!"
                if "сохранен" in stdout.lower() or "создан" in stdout.lower():
                    # Берем первую строку из вывода как краткий результат
                    summary = stdout.splitlines()[0] if stdout.splitlines() else summary
                
                # Если текстовый ответ уже был, то это сообщение не нужно
                if not response_text:
                     await message.reply(summary)

        # Clean temporary files
        await code_executor.clean_temp_files()
        await file_manager.clean_user_temp_files(user_id)
    else:
        # Если кода для выполнения не было, просто отправляем ответ модели
        response_text = llm_response.strip()
        if response_text:
            await message.answer(response_text)


async def main():
    """Main function"""
    logger.info("Starting AI Workshop Bot...")

    try:
        # Start polling
        await dp.start_polling(bot)
    except asyncio.CancelledError:
        logger.info("Work bot received cancellation signal")
        raise
    except Exception as e:
        logger.error(f"Error in Work bot: {e}")
        raise
    finally:
        # Закрываем HTTP сессию Gemini клиента
        await gemini_client.close()


if __name__ == "__main__":
    asyncio.run(main())
