import asyncio
import subprocess
import tempfile
import os
import shutil
import signal
import time
import aiofiles
import aiofiles.os
import sys
from pathlib import Path
from typing import Tuple, List, Optional
from work_config import MAX_EXECUTION_TIME, MAX_STDOUT_SIZE, TEMP_DIR


class CodeExecutor:
    def __init__(self, user_data_dir: Path):
        self.user_data_dir = user_data_dir
        self.python_paths = self._get_python_paths()
        asyncio.create_task(self._ensure_user_dir())

    def _get_python_paths(self) -> List[str]:
        """Получаем пути к Python библиотекам на сервере"""
        paths = []

        # Получаем пути через Python site module
        try:
            import site
            site_packages = site.getsitepackages()
            paths.extend(site_packages)

            # Добавляем user site-packages
            user_site = site.getusersitepackages()
            if user_site:
                paths.append(user_site)
        except:
            pass

        # Дополнительные пути для Python 3.13 на сервере
        server_paths = [
            "/home/<USER>/.local/lib/python3.13/site-packages",
            "/usr/local/lib/python3.13/site-packages",
            "/usr/lib/python3.13/site-packages",
            "/usr/lib/python3/dist-packages",
            "/home/<USER>/.local/lib/python3.13/dist-packages"
        ]

        # Проверяем какие пути существуют и добавляем их
        for path in server_paths:
            if os.path.exists(path) and path not in paths:
                paths.append(path)

        # Добавляем текущие системные пути
        for path in sys.path:
            if path not in paths:
                paths.append(path)

        return paths

    def _create_python_script_with_paths(self, code: str) -> str:
        """Создаем Python скрипт с правильными путями к библиотекам"""
        python_path_setup = f"import sys; import os; bot_root_dir = '/home/<USER>/work'; libs_path = os.path.join(bot_root_dir, 'libs'); sys.path.insert(0, libs_path); sys.path.extend({self.python_paths})"

        return python_path_setup + "\n" + code

    async def _ensure_user_dir(self):
        """Асинхронно создаем директорию пользователя"""
        if not await aiofiles.os.path.exists(self.user_data_dir):
            await aiofiles.os.makedirs(self.user_data_dir, exist_ok=True)

    def extract_python_code(self, llm_response: str) -> Optional[str]:
        """
        Extract Python code from LLM response.
        Looks for 'python=' at the beginning of a line.
        """
        lines = llm_response.split('\n')
        code_lines = []
        in_code_block = False
        indent_level = 0

        for i, line in enumerate(lines):
            if line.strip().startswith('python='):
                in_code_block = True
                continue
            elif in_code_block:
                stripped = line.strip()

                # Skip empty lines
                if not stripped:
                    code_lines.append(line)
                    continue

                # Check if this looks like Python code
                is_python_line = (
                    # Python keywords and constructs
                    any(stripped.startswith(kw) for kw in [
                        'import ', 'from ', 'def ', 'class ', 'if ', 'elif ', 'else:',
                        'for ', 'while ', 'try:', 'except', 'finally:', 'with ',
                        'return', 'yield', 'break', 'continue', 'pass', 'raise',
                        'print(', 'open(', 'len(', 'str(', 'int(', 'float(',
                        'range(', 'enumerate(', 'zip(', 'list(', 'dict(', 'set('
                    ]) or
                    # Assignment or method calls
                    '=' in stripped or '(' in stripped or
                    # Indented lines (likely part of a block)
                    line.startswith('    ') or line.startswith('\t') or
                    # Comments
                    stripped.startswith('#')
                )

                # Stop conditions for natural language
                is_natural_language = (
                    stripped.startswith('```') or
                    stripped.startswith('python=') or
                    # Russian text indicators
                    any(char in stripped for char in 'абвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ') or
                    # Common natural language starters
                    any(stripped.startswith(phrase) for phrase in [
                        'Конвертация', 'Готово', 'Файл', 'Результат', 'Обработка',
                        'Создание', 'Сохранение', 'Загрузка', 'Выполнение'
                    ])
                )

                # If it's clearly natural language and not Python, stop
                if is_natural_language and not is_python_line:
                    break

                code_lines.append(line)

        if code_lines:
            # Remove trailing empty lines
            while code_lines and not code_lines[-1].strip():
                code_lines.pop()
            return '\n'.join(code_lines)
        return None

    async def execute_code(self, code: str) -> Tuple[str, str, int, List[Path]]:
        """
        Execute Python code in user's directory with safety limits.

        Returns:
            (stdout, stderr, exit_code, created_files)
        """
        # Create temporary script file
        script_path = TEMP_DIR / f"script_{int(time.time())}.py"

        try:
            # Создаем код с правильными путями к библиотекам
            code_with_paths = self._create_python_script_with_paths(code)

            # Асинхронно записываем код во временный файл
            async with aiofiles.open(script_path, 'w', encoding='utf-8') as f:
                await f.write(code_with_paths)

            # Get list of files before execution
            files_before = set()
            if await aiofiles.os.path.exists(self.user_data_dir):
                files_before = set(self.user_data_dir.rglob('*'))

            # Подготавливаем переменные окружения с Python path
            env = os.environ.copy()
            pythonpath_value = ':'.join(self.python_paths)
            env['PYTHONPATH'] = pythonpath_value

            # Также добавляем в PATH пути к Python
            if 'PATH' in env:
                env['PATH'] = '/usr/local/bin:/usr/bin:/bin:' + env['PATH']
            else:
                env['PATH'] = '/usr/local/bin:/usr/bin:/bin'

            # Execute the script - пробуем сначала python3, потом python
            python_commands = ['python3', 'python']
            process = None

            for python_cmd in python_commands:
                try:
                    process = await asyncio.create_subprocess_exec(
                        python_cmd, str(script_path),
                        cwd=str(self.user_data_dir),
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        env=env,
                        limit=MAX_STDOUT_SIZE
                    )
                    break
                except FileNotFoundError:
                    continue

            if process is None:
                return "", "Python interpreter not found", -1, []

            try:
                # Wait for completion with timeout
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=MAX_EXECUTION_TIME
                )
                exit_code = process.returncode

            except asyncio.TimeoutError:
                # Kill the process if it times out
                try:
                    process.kill()
                    await process.wait()
                except:
                    pass

                stdout = b""
                stderr = f"Execution timed out after {MAX_EXECUTION_TIME} seconds".encode()
                exit_code = -1

            # Decode output
            stdout_str = stdout.decode('utf-8', errors='replace')
            stderr_str = stderr.decode('utf-8', errors='replace')

            # Truncate output if too large
            if len(stdout_str) > MAX_STDOUT_SIZE:
                stdout_str = stdout_str[:MAX_STDOUT_SIZE] + "\n...truncated"

            if len(stderr_str) > MAX_STDOUT_SIZE:
                stderr_str = stderr_str[:MAX_STDOUT_SIZE] + "\n...truncated"

            # Find newly created files
            files_after = set()
            if await aiofiles.os.path.exists(self.user_data_dir):
                files_after = set(self.user_data_dir.rglob('*'))

            new_files = []
            for file_path in files_after - files_before:
                if await aiofiles.os.path.isfile(file_path):
                    new_files.append(file_path)

            return stdout_str, stderr_str, exit_code, new_files

        except Exception as e:
            return "", f"Execution error: {str(e)}", -1, []

        finally:
            # Clean up temporary script
            try:
                if await aiofiles.os.path.exists(script_path):
                    await aiofiles.os.remove(script_path)
            except:
                pass

    async def get_user_files(self) -> List[Path]:
        """Get list of all files in user's directory"""
        if not await aiofiles.os.path.exists(self.user_data_dir):
            return []

        files = []
        for file_path in self.user_data_dir.rglob('*'):
            if await aiofiles.os.path.isfile(file_path):
                files.append(file_path)
        return files

    async def clean_temp_files(self):
        """Clean temporary files but keep user data"""
        try:
            for temp_file in TEMP_DIR.glob("script_*.py"):
                if await aiofiles.os.path.exists(temp_file):
                    await aiofiles.os.remove(temp_file)
        except:
            pass
