import os
from pathlib import Path

# Telegram Bot Token
TG_TOKEN = "**********************************************"

# Gemini API Key
GEMINI_API_KEY = "AIzaSyBk9mqHRDGSvMi81X-4ejZJPZXFXDbK2j8"

# Gemini API URL
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent"

# Paths
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "dat"
PROMPTS_DIR = BASE_DIR / "prompts"
TEMP_DIR = BASE_DIR / "temp"

# System prompt file
SYSTEM_PROMPT_FILE = PROMPTS_DIR / "system.txt"

# Limits
MAX_EXECUTION_TIME = 20  # seconds
MAX_RAM_MB = 512
MAX_OUTPUT_FILES = 20
MAX_STDOUT_SIZE = 4 * 1024 * 1024  # 4 MB

# Create directories if they don't exist
DATA_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)
PROMPTS_DIR.mkdir(exist_ok=True)
