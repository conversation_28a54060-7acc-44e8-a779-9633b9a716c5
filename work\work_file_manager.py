import os
import aiofiles
import aiofiles.os
import asyncio
import shutil
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from work_config import DATA_DIR, MAX_OUTPUT_FILES

logger = logging.getLogger(__name__)


class FileManager:
    def __init__(self):
        self.data_dir = DATA_DIR
        self.data_dir.mkdir(exist_ok=True)
    
    async def get_user_dir(self, user_id: int) -> Path:
        """Get user's data directory"""
        user_dir = self.data_dir / str(user_id)
        
        # Асинхронная проверка и создание директории
        if not await aiofiles.os.path.exists(user_dir):
            await aiofiles.os.makedirs(user_dir, exist_ok=True)
        
        return user_dir
    
    async def save_file(self, user_id: int, file_name: str, file_data: bytes) -> Path:
        """Save file to user's directory"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name

        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_data)

        # Автоматическая очистка старых файлов
        try:
            removed_count = await self.clean_old_files(user_id)
            if removed_count > 0:
                logger.info(f"Cleaned {removed_count} old files for user {user_id}")
        except Exception as e:
            logger.error(f"Error cleaning old files for user {user_id}: {e}")

        return file_path
    
    async def read_file(self, user_id: int, file_name: str) -> Optional[bytes]:
        """Read file from user's directory"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name
        
        if not await aiofiles.os.path.exists(file_path):
            return None
        
        async with aiofiles.open(file_path, 'rb') as f:
            return await f.read()
    
    async def list_user_files(self, user_id: int) -> List[Dict[str, Any]]:
        """List all files in user's directory"""
        user_dir = await self.get_user_dir(user_id)
        files = []
        
        # Асинхронный обход файлов
        async def process_file(file_path: Path):
            try:
                if await aiofiles.os.path.isfile(file_path):
                    stat = await aiofiles.os.stat(file_path)
                    return {
                        'name': file_path.name,
                        'path': str(file_path.relative_to(user_dir)),
                        'size': stat.st_size,
                        'modified': stat.st_mtime
                    }
            except:
                pass
            return None
        
        # Получаем все файлы асинхронно
        tasks = []
        for file_path in user_dir.rglob('*'):
            tasks.append(process_file(file_path))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            files = [result for result in results if result is not None and not isinstance(result, Exception)]
        
        return files
    
    async def get_file_info(self, user_id: int, file_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific file"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name
        
        if not await aiofiles.os.path.exists(file_path):
            return None
        
        stat = await aiofiles.os.stat(file_path)
        return {
            'name': file_path.name,
            'path': str(file_path.relative_to(user_dir)),
            'size': stat.st_size,
            'modified': stat.st_mtime,
            'full_path': str(file_path)
        }
    
    async def delete_file(self, user_id: int, file_name: str) -> bool:
        """Delete a file from user's directory"""
        user_dir = await self.get_user_dir(user_id)
        file_path = user_dir / file_name
        
        if await aiofiles.os.path.exists(file_path) and await aiofiles.os.path.isfile(file_path):
            try:
                await aiofiles.os.remove(file_path)
                return True
            except:
                return False
        return False
    
    async def clean_user_temp_files(self, user_id: int):
        """Clean temporary files but keep user data"""
        user_dir = await self.get_user_dir(user_id)
        
        # Remove temporary files (those starting with temp_ or script_)
        async def remove_temp_files(pattern: str):
            for file_path in user_dir.glob(pattern):
                try:
                    if await aiofiles.os.path.isfile(file_path):
                        await aiofiles.os.remove(file_path)
                    elif await aiofiles.os.path.isdir(file_path):
                        # Для директорий используем синхронный shutil в executor
                        await asyncio.get_event_loop().run_in_executor(
                            None, shutil.rmtree, str(file_path)
                        )
                except:
                    pass
        
        # Удаляем временные файлы параллельно
        await asyncio.gather(
            remove_temp_files('temp_*'),
            remove_temp_files('script_*'),
            return_exceptions=True
        )
    
    async def get_user_context(self, user_id: int) -> str:
        """Get context about user's files for LLM"""
        files = await self.list_user_files(user_id)

        if not files:
            return "В рабочей папке пока нет файлов."

        context = "Доступные файлы в рабочей папке:\n"
        for file_info in files:
            size_kb = file_info['size'] / 1024

            # Определяем тип файла по расширению
            file_ext = Path(file_info['name']).suffix.lower()
            file_type = self._get_file_type_description(file_ext)

            # Форматируем дату изменения
            import datetime
            modified_date = datetime.datetime.fromtimestamp(file_info['modified']).strftime('%d.%m.%Y %H:%M')

            context += f"- {file_info['name']} ({file_type}, {size_kb:.1f} KB, изменен {modified_date})\n"

        context += f"\nВсего файлов: {len(files)}"
        return context

    def _get_file_type_description(self, file_ext: str) -> str:
        """Get human-readable file type description"""
        type_map = {
            '.txt': 'текст',
            '.csv': 'CSV данные',
            '.json': 'JSON данные',
            '.xml': 'XML данные',
            '.xlsx': 'Excel таблица',
            '.xls': 'Excel таблица',
            '.pdf': 'PDF документ',
            '.docx': 'Word документ',
            '.doc': 'Word документ',
            '.mp3': 'MP3 аудио',
            '.wav': 'WAV аудио',
            '.flac': 'FLAC аудио',
            '.ogg': 'OGG аудио',
            '.mp4': 'MP4 видео',
            '.avi': 'AVI видео',
            '.mkv': 'MKV видео',
            '.mov': 'MOV видео',
            '.jpg': 'JPEG изображение',
            '.jpeg': 'JPEG изображение',
            '.png': 'PNG изображение',
            '.gif': 'GIF изображение',
            '.bmp': 'BMP изображение',
            '.tiff': 'TIFF изображение',
            '.zip': 'ZIP архив',
            '.rar': 'RAR архив',
            '.7z': '7Z архив',
            '.tar': 'TAR архив',
            '.gz': 'GZIP архив',
            '.py': 'Python скрипт',
            '.js': 'JavaScript файл',
            '.html': 'HTML файл',
            '.css': 'CSS файл',
            '.sql': 'SQL скрипт',
        }
        return type_map.get(file_ext, 'файл')
    
    async def limit_output_files(self, user_id: int, new_files: List[Path]) -> List[Path]:
        """Limit number of output files according to MAX_OUTPUT_FILES"""
        if len(new_files) <= MAX_OUTPUT_FILES:
            return new_files
        
        # Асинхронно получаем время модификации для всех файлов
        async def get_mtime(file_path: Path):
            try:
                stat = await aiofiles.os.stat(file_path)
                return file_path, stat.st_mtime
            except:
                return file_path, 0
        
        file_times = await asyncio.gather(*[get_mtime(f) for f in new_files])
        
        # Sort by modification time (newest first) and take only the limit
        sorted_files = sorted(file_times, key=lambda x: x[1], reverse=True)
        limited_files = [f[0] for f in sorted_files[:MAX_OUTPUT_FILES]]
        
        # Remove excess files asynchronously
        excess_files = [f[0] for f in sorted_files[MAX_OUTPUT_FILES:]]
        
        async def remove_file(file_path: Path):
            try:
                if await aiofiles.os.path.exists(file_path):
                    await aiofiles.os.remove(file_path)
            except:
                pass
        
        if excess_files:
            await asyncio.gather(*[remove_file(f) for f in excess_files], return_exceptions=True)
        
        return limited_files
    
    async def get_storage_stats(self, user_id: int) -> Dict[str, Any]:
        """Get storage statistics for user"""
        user_dir = await self.get_user_dir(user_id)
        total_size = 0
        file_count = 0

        # Асинхронно обрабатываем все файлы
        async def process_file(file_path: Path):
            try:
                if await aiofiles.os.path.isfile(file_path):
                    stat = await aiofiles.os.stat(file_path)
                    return stat.st_size, 1
            except:
                pass
            return 0, 0

        tasks = [process_file(file_path) for file_path in user_dir.rglob('*')]
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, tuple):
                    size, count = result
                    total_size += size
                    file_count += count

        return {
            'total_size_bytes': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'file_count': file_count,
            'directory': str(user_dir)
        }

    async def clean_old_files(self, user_id: int, max_files: int = 50, max_age_days: int = 30):
        """Clean old files to prevent storage overflow"""
        user_dir = await self.get_user_dir(user_id)
        files = []

        # Асинхронно собираем все файлы с информацией
        async def collect_file_info(file_path: Path):
            try:
                if await aiofiles.os.path.isfile(file_path):
                    stat = await aiofiles.os.stat(file_path)
                    return {
                        'path': file_path,
                        'modified': stat.st_mtime,
                        'size': stat.st_size
                    }
            except:
                pass
            return None

        tasks = [collect_file_info(file_path) for file_path in user_dir.rglob('*')]
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            files = [result for result in results if result is not None and not isinstance(result, Exception)]

        # Удаляем файлы старше max_age_days дней
        import time
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60

        files_to_remove = []
        for file_info in files:
            if current_time - file_info['modified'] > max_age_seconds:
                files_to_remove.append(file_info['path'])

        # Если файлов больше max_files, удаляем самые старые
        if len(files) > max_files:
            files.sort(key=lambda f: f['modified'])
            for file_info in files[max_files:]:
                files_to_remove.append(file_info['path'])

        # Асинхронно удаляем файлы
        async def remove_file(file_path: Path):
            try:
                if await aiofiles.os.path.exists(file_path):
                    await aiofiles.os.remove(file_path)
                    return 1
            except Exception as e:
                logger.error(f"Error removing file {file_path}: {e}")
            return 0

        if files_to_remove:
            results = await asyncio.gather(*[remove_file(f) for f in files_to_remove], return_exceptions=True)
            removed_count = sum(r for r in results if isinstance(r, int))
        else:
            removed_count = 0

        return removed_count
