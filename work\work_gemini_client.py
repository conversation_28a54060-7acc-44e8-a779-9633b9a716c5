import json
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional
from work_config import GEMINI_API_KEY, GEMINI_API_URL


class GeminiClient:
    def __init__(self):
        self.api_key = GEMINI_API_KEY
        self.api_url = GEMINI_API_URL
        self.headers = {
            'Content-Type': 'application/json',
            'X-goog-api-key': self.api_key
        }
        self._session = None
        self._connector = None

    async def _get_session(self):
        """Получаем или создаем HTTP сессию с пулом соединений"""
        if self._session is None or self._session.closed:
            # Создаем коннектор с пулом соединений
            self._connector = aiohttp.TCPConnector(
                limit=100,  # Максимум соединений в пуле
                limit_per_host=30,  # Максимум соединений на хост
                ttl_dns_cache=300,  # TTL для DNS кэша
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )

            # Создаем сессию с таймаутами
            timeout = aiohttp.ClientTimeout(
                total=60,  # Общий таймаут
                connect=10,  # Таймаут подключения
                sock_read=30  # Таймаут чтения
            )

            self._session = aiohttp.ClientSession(
                connector=self._connector,
                timeout=timeout,
                headers={'User-Agent': 'AI-Workshop-Bot/1.0'}
            )

        return self._session

    async def close(self):
        """Закрываем сессию и коннектор"""
        if self._session and not self._session.closed:
            await self._session.close()
        if self._connector:
            await self._connector.close()

    async def generate_content(self, messages: List[Dict[str, Any]]) -> Optional[str]:
        """
        Generate content using Gemini 2.5 Pro API with connection pooling

        Args:
            messages: List of message objects with role and content

        Returns:
            Generated text response or None if error
        """
        try:
            # Convert messages to Gemini format
            contents = []
            system_instruction = None

            for message in messages:
                if message.get('role') == 'system':
                    # System messages are handled separately in Gemini
                    system_instruction = {"parts": [{"text": message['content']}]}
                elif message.get('role') == 'user':
                    contents.append({
                        "role": "user",
                        "parts": [{"text": message['content']}]
                    })
                elif message.get('role') == 'assistant':
                    contents.append({
                        "role": "model",
                        "parts": [{"text": message['content']}]
                    })

            payload = {
                "contents": contents,
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 8192,
                }
            }

            if system_instruction:
                payload["systemInstruction"] = system_instruction

            session = await self._get_session()

            async with session.post(
                self.api_url,
                headers=self.headers,
                json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    # Extract text from response
                    if 'candidates' in result and len(result['candidates']) > 0:
                        candidate = result['candidates'][0]
                        if 'content' in candidate and 'parts' in candidate['content']:
                            parts = candidate['content']['parts']
                            if len(parts) > 0 and 'text' in parts[0]:
                                return parts[0]['text']

                    return None
                else:
                    error_text = await response.text()
                    print(f"Gemini API error {response.status}: {error_text}")
                    return None

        except asyncio.TimeoutError:
            print("Gemini API timeout")
            return None
        except Exception as e:
            print(f"Gemini API exception: {e}")
            return None

    async def chat_completion(self, system_prompt: str, user_message: str,
                            conversation_history: List[Dict[str, Any]] = None) -> Optional[str]:
        """
        Simple chat completion method

        Args:
            system_prompt: System instruction
            user_message: User's message
            conversation_history: Previous conversation turns

        Returns:
            Generated response or None if error
        """
        messages = []

        # Add system prompt
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Add conversation history
        if conversation_history:
            messages.extend(conversation_history)

        # Add current user message
        messages.append({"role": "user", "content": user_message})

        return await self.generate_content(messages)
