# YouTube Summary Bot - .gitignore

# ========================================
# КОНФИДЕНЦИАЛЬНЫЕ ФАЙЛЫ (ВАЖНО!)
# ========================================

# Файл с API ключами - НЕ КОММИТИТЬ!
config_and_utils.py

# Резервные копии конфигурации
config_and_utils.py.bak
config_and_utils.py.backup
config.py
settings.py

# Файлы с токенами и ключами
*.token
*.key
*.secret
api_keys.txt
tokens.txt

# ========================================
# БАЗЫ ДАННЫХ И ЛОГИ
# ========================================

# SQLite базы данных
*.db
*.sqlite
*.sqlite3
yt_video_summaries.db*

# Логи
*.log
logs/
log/

# ========================================
# ВРЕМЕННЫЕ ФАЙЛЫ
# ========================================

# Папка для временных файлов
temp/
tmp/
temporary/

# Загруженные видео файлы
downloads/
videos/
media/

# Кэш файлы
cache/
.cache/

# ========================================
# PYTHON СПЕЦИФИЧНЫЕ ФАЙЛЫ
# ========================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ========================================
# ОПЕРАЦИОННАЯ СИСТЕМА
# ========================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ========================================
# IDE И РЕДАКТОРЫ
# ========================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ========================================
# ПРОЧИЕ ФАЙЛЫ
# ========================================

# Архивы
*.zip
*.tar.gz
*.rar
*.7z

# Системные файлы
.DS_Store
Thumbs.db

# Файлы конфигурации IDE
.project
.pydevproject
.settings/

# Файлы мониторинга
monitoring/
metrics/
alerts/

# Файлы резервных копий
*.bak
*.backup
*.old

# Файлы процессов
*.pid
*.lock
