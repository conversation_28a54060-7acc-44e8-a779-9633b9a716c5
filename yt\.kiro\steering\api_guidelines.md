# Правила работы с API для YouTube Summary Bot

## Общие принципы работы с API

### 1. Асинхронные запросы
- Все API запросы должны быть асинхронными через `aiohttp`
- Используем пулы соединений для оптимизации производительности
- Обязательно устанавливаем таймауты для всех запросов
- Правильно закрываем сессии и соединения

```python
# Правильная настройка HTTP клиента
async def create_http_session() -> aiohttp.ClientSession:
    """Создает настроенную HTTP сессию."""
    connector = aiohttp.TCPConnector(
        limit=100,  # Максимум соединений
        limit_per_host=30,  # Максимум соединений на хост
        ttl_dns_cache=300,  # TTL DNS кеша
        use_dns_cache=True,
    )
    
    timeout = aiohttp.ClientTimeout(
        total=30,  # Общий таймаут
        connect=10,  # Таймаут подключения
        sock_read=20  # Таймаут чтения
    )
    
    return aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={
            'User-Agent': 'YouTube-Summary-Bot/1.0',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate'
        }
    )
```

### 2. Обработка ошибок API
- Различаем временные и постоянные ошибки
- Реализуем retry логику с экспоненциальной задержкой
- Логируем все ошибки API с полным контекстом
- Возвращаем понятные ошибки пользователю

```python
class APIError(Exception):
    """Базовая ошибка API."""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data

class RateLimitError(APIError):
    """Ошибка превышения лимита запросов."""
    def __init__(self, retry_after: Optional[int] = None):
        super().__init__("Rate limit exceeded")
        self.retry_after = retry_after

class APIUnavailableError(APIError):
    """Ошибка недоступности API."""
    pass
```

### 3. Rate Limiting и квоты
- Отслеживаем использование квот для каждого API
- Реализуем локальный rate limiting для предотвращения превышения лимитов
- Используем exponential backoff при получении 429 ошибок
- Мониторим остатки квот и предупреждаем о приближении к лимитам

```python
class RateLimiter:
    """Локальный rate limiter для API запросов."""
    
    def __init__(self, max_requests: int, time_window: int):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: List[float] = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Ожидает разрешения на выполнение запроса."""
        async with self._lock:
            now = time.time()
            # Удаляем старые запросы
            self.requests = [req_time for req_time in self.requests 
                           if now - req_time < self.time_window]
            
            if len(self.requests) >= self.max_requests:
                # Ждем до освобождения слота
                sleep_time = self.time_window - (now - self.requests[0])
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
            
            self.requests.append(now)
```

## YouTube Data API

### 1. Конфигурация и аутентификация
- Используем API ключ из переменных окружения
- Ротируем ключи при необходимости
- Мониторим квоты и использование

```python
class YouTubeDataAPI:
    """Клиент для YouTube Data API v3."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.rate_limiter = RateLimiter(max_requests=100, time_window=100)  # 100 запросов в 100 секунд
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = await create_http_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
```

### 2. Получение метаданных видео
- Запрашиваем только необходимые поля для экономии квоты
- Кешируем результаты для повторного использования
- Обрабатываем случаи недоступных или удаленных видео

```python
async def get_video_metadata(self, video_id: str) -> Dict[str, Any]:
    """
    Получает метаданные YouTube видео.
    
    Args:
        video_id: ID YouTube видео
        
    Returns:
        Словарь с метаданными видео
        
    Raises:
        VideoNotFoundError: Если видео не найдено
        APIError: При ошибке API
    """
    await self.rate_limiter.acquire()
    
    params = {
        'part': 'snippet,contentDetails,statistics',
        'id': video_id,
        'key': self.api_key,
        'fields': 'items(id,snippet(title,description,channelTitle,publishedAt),contentDetails(duration),statistics(viewCount))'
    }
    
    try:
        async with self.session.get(f"{self.base_url}/videos", params=params) as response:
            if response.status == 404:
                raise VideoNotFoundError(f"Видео {video_id} не найдено")
            elif response.status == 403:
                data = await response.json()
                if 'quotaExceeded' in str(data):
                    raise RateLimitError()
                raise APIError(f"Доступ запрещен: {data}")
            elif response.status != 200:
                raise APIError(f"Ошибка API: {response.status}")
            
            data = await response.json()
            if not data.get('items'):
                raise VideoNotFoundError(f"Видео {video_id} не найдено")
            
            return data['items'][0]
            
    except aiohttp.ClientError as e:
        raise APIError(f"Ошибка сети при запросе видео {video_id}: {e}")
```

### 3. Работа с каналами и подписками
- Получаем информацию о каналах для системы подписок
- Отслеживаем новые видео на каналах
- Фильтруем видео по длительности и другим критериям

```python
async def get_channel_latest_videos(self, channel_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
    """
    Получает последние видео канала.
    
    Args:
        channel_id: ID YouTube канала
        max_results: Максимальное количество видео
        
    Returns:
        Список видео канала
    """
    await self.rate_limiter.acquire()
    
    # Сначала получаем плейлист uploads канала
    params = {
        'part': 'contentDetails',
        'id': channel_id,
        'key': self.api_key
    }
    
    async with self.session.get(f"{self.base_url}/channels", params=params) as response:
        response.raise_for_status()
        data = await response.json()
        
        if not data.get('items'):
            raise ChannelNotFoundError(f"Канал {channel_id} не найден")
        
        uploads_playlist_id = data['items'][0]['contentDetails']['relatedPlaylists']['uploads']
    
    # Получаем видео из плейлиста uploads
    await self.rate_limiter.acquire()
    
    params = {
        'part': 'snippet,contentDetails',
        'playlistId': uploads_playlist_id,
        'maxResults': max_results,
        'key': self.api_key,
        'order': 'date'
    }
    
    async with self.session.get(f"{self.base_url}/playlistItems", params=params) as response:
        response.raise_for_status()
        data = await response.json()
        
        return data.get('items', [])
```

## Google Gemini API

### 1. Конфигурация и управление ключами
- Используем множественные API ключи для балансировки нагрузки
- Автоматически переключаемся между ключами при ошибках
- Отслеживаем использование каждого ключа

```python
class GeminiAPIManager:
    """Менеджер для работы с множественными Gemini API ключами."""
    
    def __init__(self, api_keys: List[str]):
        self.api_keys = api_keys
        self.current_key_index = 0
        self.key_failures: Dict[str, int] = {key: 0 for key in api_keys}
        self.key_last_used: Dict[str, float] = {key: 0 for key in api_keys}
        self._lock = asyncio.Lock()
    
    async def get_available_key(self) -> str:
        """Возвращает доступный API ключ."""
        async with self._lock:
            # Ищем ключ с наименьшим количеством ошибок
            available_keys = [
                key for key in self.api_keys 
                if self.key_failures[key] < 3  # Максимум 3 ошибки подряд
            ]
            
            if not available_keys:
                # Сбрасываем счетчики ошибок если все ключи заблокированы
                self.key_failures = {key: 0 for key in self.api_keys}
                available_keys = self.api_keys
            
            # Выбираем ключ, который дольше всего не использовался
            key = min(available_keys, key=lambda k: self.key_last_used[k])
            self.key_last_used[key] = time.time()
            
            return key
    
    async def report_key_failure(self, api_key: str) -> None:
        """Сообщает об ошибке использования ключа."""
        async with self._lock:
            self.key_failures[api_key] += 1
    
    async def report_key_success(self, api_key: str) -> None:
        """Сообщает об успешном использовании ключа."""
        async with self._lock:
            self.key_failures[api_key] = 0
```

### 2. Создание сводок видео
- Используем разные промпты для разных типов сводок
- Оптимизируем размер контекста для экономии токенов
- Обрабатываем ограничения по длине текста

```python
async def create_video_summary(
    self, 
    transcript: str, 
    video_metadata: Dict[str, Any],
    summary_type: str = "brief"
) -> str:
    """
    Создает сводку видео через Gemini API.
    
    Args:
        transcript: Транскрипт видео
        video_metadata: Метаданные видео
        summary_type: Тип сводки ('brief' или 'detailed')
        
    Returns:
        Текст сводки
    """
    api_key = await self.api_manager.get_available_key()
    
    # Выбираем промпт в зависимости от типа сводки
    if summary_type == "brief":
        prompt = self._get_brief_summary_prompt(transcript, video_metadata)
    else:
        prompt = self._get_detailed_summary_prompt(transcript, video_metadata)
    
    # Ограничиваем размер транскрипта для экономии токенов
    max_transcript_length = 50000 if summary_type == "detailed" else 20000
    if len(transcript) > max_transcript_length:
        transcript = transcript[:max_transcript_length] + "..."
    
    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.3,
            "topK": 40,
            "topP": 0.95,
            "maxOutputTokens": 2048 if summary_type == "detailed" else 1024,
        }
    }
    
    try:
        async with self.session.post(
            f"{GEMINI_API_URL}?key={api_key}",
            json=payload,
            headers={'Content-Type': 'application/json'}
        ) as response:
            
            if response.status == 429:
                await self.api_manager.report_key_failure(api_key)
                raise RateLimitError()
            elif response.status != 200:
                await self.api_manager.report_key_failure(api_key)
                error_data = await response.json()
                raise APIError(f"Gemini API error: {error_data}")
            
            data = await response.json()
            await self.api_manager.report_key_success(api_key)
            
            if 'candidates' not in data or not data['candidates']:
                raise APIError("Gemini API не вернул результат")
            
            return data['candidates'][0]['content']['parts'][0]['text']
            
    except aiohttp.ClientError as e:
        await self.api_manager.report_key_failure(api_key)
        raise APIError(f"Ошибка сети при обращении к Gemini API: {e}")
```

### 3. Обработка видео файлов
- Загружаем видео файлы через Files API
- Ожидаем обработки файла перед анализом
- Удаляем временные файлы после обработки

```python
async def process_video_file(self, file_path: str, mime_type: str) -> str:
    """
    Обрабатывает видео файл через Gemini Files API.
    
    Args:
        file_path: Путь к видео файлу
        mime_type: MIME тип файла
        
    Returns:
        Анализ видео файла
    """
    api_key = await self.api_manager.get_available_key()
    
    # Загружаем файл
    upload_url = f"{GEMINI_FILES_API_URL}?key={api_key}"
    
    with open(file_path, 'rb') as file:
        form_data = aiohttp.FormData()
        form_data.add_field('file', file, filename=os.path.basename(file_path), content_type=mime_type)
        
        async with self.session.post(upload_url, data=form_data) as response:
            if response.status != 200:
                error_data = await response.json()
                raise APIError(f"Ошибка загрузки файла: {error_data}")
            
            upload_response = await response.json()
            file_uri = upload_response['file']['uri']
            file_name = upload_response['file']['name']
    
    # Ждем обработки файла
    await self._wait_for_file_processing(file_name, api_key)
    
    try:
        # Анализируем файл
        analysis = await self._analyze_video_file(file_uri, api_key)
        return analysis
    finally:
        # Удаляем временный файл
        await self._delete_uploaded_file(file_name, api_key)

async def _wait_for_file_processing(self, file_name: str, api_key: str, max_wait_time: int = 300) -> None:
    """Ожидает завершения обработки файла."""
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        async with self.session.get(f"{GEMINI_FILES_GET_URL}/{file_name}?key={api_key}") as response:
            if response.status == 200:
                data = await response.json()
                if data['file']['state'] == 'ACTIVE':
                    return
                elif data['file']['state'] == 'FAILED':
                    raise APIError("Обработка файла завершилась с ошибкой")
        
        await asyncio.sleep(5)  # Проверяем каждые 5 секунд
    
    raise APIError("Превышено время ожидания обработки файла")
```

## Telegraph API

### 1. Создание статей
- Форматируем контент в HTML для Telegraph
- Добавляем изображения и медиа контент
- Обрабатываем ошибки публикации

```python
class TelegraphPublisher:
    """Публикация статей в Telegraph."""
    
    def __init__(self, access_token: str):
        self.access_token = access_token
        self.telegraph = Telegraph(access_token)
    
    async def create_article(
        self, 
        title: str, 
        content: str, 
        author_name: str = "YouTube Summary Bot"
    ) -> str:
        """
        Создает статью в Telegraph.
        
        Args:
            title: Заголовок статьи
            content: Содержимое статьи в HTML
            author_name: Имя автора
            
        Returns:
            URL созданной статьи
        """
        try:
            # Форматируем контент для Telegraph
            formatted_content = self._format_content_for_telegraph(content)
            
            response = await asyncio.to_thread(
                self.telegraph.create_page,
                title=title,
                html_content=formatted_content,
                author_name=author_name,
                return_content=False
            )
            
            return f"https://telegra.ph/{response['path']}"
            
        except Exception as e:
            raise APIError(f"Ошибка создания Telegraph статьи: {e}")
    
    def _format_content_for_telegraph(self, content: str) -> str:
        """Форматирует контент для Telegraph."""
        # Заменяем markdown на HTML
        content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content)
        content = re.sub(r'\*(.*?)\*', r'<em>\1</em>', content)
        content = re.sub(r'`(.*?)`', r'<code>\1</code>', content)
        
        # Добавляем параграфы
        paragraphs = content.split('\n\n')
        formatted_paragraphs = [f'<p>{p.replace(chr(10), "<br>")}</p>' for p in paragraphs if p.strip()]
        
        return ''.join(formatted_paragraphs)
```

## Мониторинг API

### 1. Метрики производительности
- Отслеживаем время ответа каждого API
- Считаем количество успешных и неуспешных запросов
- Мониторим использование квот

```python
class APIMetricsCollector:
    """Сборщик метрик для API запросов."""
    
    def __init__(self):
        self.request_counts: Dict[str, int] = defaultdict(int)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.response_times: Dict[str, List[float]] = defaultdict(list)
        self.quota_usage: Dict[str, Dict[str, int]] = defaultdict(dict)
    
    async def record_request(self, api_name: str, endpoint: str, response_time: float, success: bool) -> None:
        """Записывает метрики запроса."""
        key = f"{api_name}:{endpoint}"
        
        self.request_counts[key] += 1
        self.response_times[key].append(response_time)
        
        if not success:
            self.error_counts[key] += 1
        
        # Ограничиваем размер списка времен ответа
        if len(self.response_times[key]) > 1000:
            self.response_times[key] = self.response_times[key][-500:]
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Возвращает сводку метрик."""
        summary = {}
        
        for key in self.request_counts:
            api_name, endpoint = key.split(':', 1)
            
            if api_name not in summary:
                summary[api_name] = {}
            
            response_times = self.response_times[key]
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            summary[api_name][endpoint] = {
                'total_requests': self.request_counts[key],
                'error_count': self.error_counts[key],
                'error_rate': self.error_counts[key] / self.request_counts[key] if self.request_counts[key] > 0 else 0,
                'avg_response_time': avg_response_time,
                'quota_usage': self.quota_usage.get(key, {})
            }
        
        return summary
```

### 2. Алерты и уведомления
- Отправляем алерты при превышении лимитов ошибок
- Уведомляем о приближении к квотам API
- Мониторим доступность внешних сервисов

```python
async def check_api_health(self) -> Dict[str, bool]:
    """Проверяет доступность всех API."""
    health_status = {}
    
    # Проверяем YouTube API
    try:
        async with self.youtube_api.session.get(
            f"{self.youtube_api.base_url}/videos",
            params={'part': 'id', 'id': 'dQw4w9WgXcQ', 'key': self.youtube_api.api_key},
            timeout=aiohttp.ClientTimeout(total=10)
        ) as response:
            health_status['youtube_api'] = response.status == 200
    except:
        health_status['youtube_api'] = False
    
    # Проверяем Gemini API
    try:
        api_key = await self.gemini_manager.get_available_key()
        test_payload = {
            "contents": [{"parts": [{"text": "Hello"}]}],
            "generationConfig": {"maxOutputTokens": 10}
        }
        
        async with self.session.post(
            f"{GEMINI_API_URL}?key={api_key}",
            json=test_payload,
            timeout=aiohttp.ClientTimeout(total=15)
        ) as response:
            health_status['gemini_api'] = response.status == 200
    except:
        health_status['gemini_api'] = False
    
    # Проверяем Telegraph API
    try:
        test_response = await asyncio.to_thread(
            self.telegraph_publisher.telegraph.get_account_info
        )
        health_status['telegraph_api'] = bool(test_response)
    except:
        health_status['telegraph_api'] = False
    
    return health_status
```