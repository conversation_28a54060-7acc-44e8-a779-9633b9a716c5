# Архитектурные принципы YouTube Summary Bot

## Общая архитектура системы

### Модульная структура
Система построена по принципу модульной архитектуры с четким разделением ответственности:

- **telegram_bot.py** - основной модуль Telegram бота и обработки сообщений
- **youtube_services.py** - сервисы для работы с YouTube API
- **youtube_transcriber.py** - транскрипция YouTube видео
- **config_and_utils.py** - конфигурация, утилиты и общие импорты
- **yt_database.py** - работа с базой данных SQLite
- **monitoring_dashboard.py** - система мониторинга и метрик
- **alerting_system.py** - система оповещений
- **structured_logging.py** - структурированное логирование

### Принципы проектирования

#### 1. Асинхронность как основа
- Вся система построена на асинхронном программировании
- Используем `asyncio` для координации асинхронных операций
- Никаких блокирующих операций в основном потоке
- Все внешние API вызовы только через `aiohttp`

#### 2. Отказоустойчивость (Resilience)
- Система должна продолжать работать при сбоях отдельных компонентов
- Реализуем circuit breaker pattern для внешних API
- Graceful degradation - снижение функциональности вместо полного отказа
- Автоматическое восстановление после временных сбоев

```python
# Пример реализации circuit breaker
class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
```

#### 3. Масштабируемость
- Горизонтальное масштабирование через множественные инстансы бота
- Вертикальное масштабирование через оптимизацию асинхронных операций
- Использование пулов соединений для базы данных
- Кеширование результатов дорогих операций

#### 4. Наблюдаемость (Observability)
- Структурированное логирование всех операций
- Метрики производительности в реальном времени
- Трассировка запросов через всю систему
- Система алертов для критических событий

## Паттерны проектирования

### 1. Repository Pattern для работы с данными
```python
class VideoRepository:
    """Абстракция для работы с данными видео."""
    
    async def save_video_metadata(self, video_data: VideoMetadata) -> None:
        """Сохраняет метаданные видео."""
        pass
    
    async def get_video_by_id(self, video_id: str) -> Optional[VideoMetadata]:
        """Получает видео по ID."""
        pass
```

### 2. Factory Pattern для создания сервисов
```python
class ServiceFactory:
    """Фабрика для создания сервисов с правильной конфигурацией."""
    
    @staticmethod
    def create_youtube_service() -> YouTubeServices:
        """Создает настроенный YouTube сервис."""
        return YouTubeServices(
            api_key=YOUTUBE_DATA_API_KEY,
            max_requests_per_hour=MAX_API_REQUESTS_PER_HOUR
        )
```

### 3. Strategy Pattern для разных типов обработки
```python
class SummaryStrategy:
    """Базовая стратегия создания сводок."""
    
    async def create_summary(self, transcript: str) -> str:
        raise NotImplementedError

class BriefSummaryStrategy(SummaryStrategy):
    """Стратегия для краткой сводки."""
    
    async def create_summary(self, transcript: str) -> str:
        # Логика создания краткой сводки
        pass

class DetailedSummaryStrategy(SummaryStrategy):
    """Стратегия для подробной сводки."""
    
    async def create_summary(self, transcript: str) -> str:
        # Логика создания подробной сводки
        pass
```

### 4. Observer Pattern для системы событий
```python
class EventBus:
    """Шина событий для слабо связанной архитектуры."""
    
    def __init__(self):
        self._subscribers: Dict[str, List[Callable]] = {}
    
    def subscribe(self, event_type: str, handler: Callable) -> None:
        """Подписывается на события определенного типа."""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(handler)
    
    async def publish(self, event_type: str, data: Any) -> None:
        """Публикует событие всем подписчикам."""
        if event_type in self._subscribers:
            tasks = [handler(data) for handler in self._subscribers[event_type]]
            await asyncio.gather(*tasks, return_exceptions=True)
```

## Управление состоянием

### 1. Состояние пользователя
- Используем in-memory хранилище для временного состояния пользователя
- Персистентное состояние сохраняем в базе данных
- Состояние должно быть сериализуемым и восстанавливаемым

```python
class UserState:
    """Управление состоянием пользователя."""
    
    def __init__(self):
        self._states: Dict[int, Dict[str, Any]] = {}
    
    def set_state(self, user_id: int, key: str, value: Any) -> None:
        """Устанавливает состояние пользователя."""
        if user_id not in self._states:
            self._states[user_id] = {}
        self._states[user_id][key] = value
    
    def get_state(self, user_id: int, key: str, default: Any = None) -> Any:
        """Получает состояние пользователя."""
        return self._states.get(user_id, {}).get(key, default)
```

### 2. Кеширование
- Используем TTL кеш для результатов API запросов
- Кешируем транскрипты видео для повторного использования
- Инвалидация кеша при изменении данных

```python
class TTLCache:
    """Кеш с временем жизни записей."""
    
    def __init__(self, default_ttl: int = 3600):
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._default_ttl = default_ttl
    
    async def get(self, key: str) -> Optional[Any]:
        """Получает значение из кеша."""
        if key in self._cache:
            value, expires_at = self._cache[key]
            if time.time() < expires_at:
                return value
            else:
                del self._cache[key]
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Сохраняет значение в кеш."""
        ttl = ttl or self._default_ttl
        expires_at = time.time() + ttl
        self._cache[key] = (value, expires_at)
```

## Обработка ошибок и восстановление

### 1. Иерархия исключений
- Создаем специфичные исключения для каждого типа ошибок
- Базовое исключение `YouTubeBotError` для всех ошибок системы
- Исключения должны содержать достаточно контекста для диагностики

### 2. Retry механизмы
- Экспоненциальная задержка для повторных попыток
- Максимальное количество попыток для каждого типа операции
- Jitter для предотвращения thundering herd

```python
async def retry_with_backoff(
    func: Callable,
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0
) -> Any:
    """Выполняет функцию с экспоненциальной задержкой при ошибках."""
    
    for attempt in range(max_attempts):
        try:
            return await func()
        except Exception as e:
            if attempt == max_attempts - 1:
                raise
            
            delay = min(base_delay * (exponential_base ** attempt), max_delay)
            # Добавляем jitter
            jitter = random.uniform(0, delay * 0.1)
            await asyncio.sleep(delay + jitter)
            
            logger.warning(
                "Повторная попытка выполнения функции",
                attempt=attempt + 1,
                max_attempts=max_attempts,
                delay=delay,
                error=str(e)
            )
```

### 3. Graceful shutdown
- Корректное завершение всех асинхронных операций
- Сохранение состояния перед выключением
- Обработка сигналов операционной системы

```python
class GracefulShutdown:
    """Управление корректным завершением работы."""
    
    def __init__(self):
        self._shutdown_event = asyncio.Event()
        self._tasks: Set[asyncio.Task] = set()
    
    def register_task(self, task: asyncio.Task) -> None:
        """Регистрирует задачу для отслеживания."""
        self._tasks.add(task)
        task.add_done_callback(self._tasks.discard)
    
    async def shutdown(self) -> None:
        """Инициирует процесс завершения работы."""
        self._shutdown_event.set()
        
        # Ждем завершения всех задач
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
```

## Безопасность

### 1. Валидация входных данных
- Все пользовательские данные должны проходить валидацию
- Санитизация HTML и специальных символов
- Проверка размеров файлов и ограничений

### 2. Rate limiting
- Ограничение количества запросов от одного пользователя
- Защита от спама и злоупотреблений
- Разные лимиты для разных типов операций

### 3. Безопасное хранение конфигурации
- Секретные ключи только в переменных окружения
- Никаких секретов в коде или логах
- Ротация API ключей

## Мониторинг и метрики

### 1. Ключевые метрики
- Время обработки запросов
- Количество успешных/неуспешных операций
- Использование ресурсов (CPU, память)
- Количество активных пользователей

### 2. Алерты
- Критические ошибки системы
- Превышение лимитов API
- Недоступность внешних сервисов
- Аномальное поведение метрик

### 3. Логирование
- Структурированные логи в JSON формате
- Корреляционные ID для трассировки запросов
- Разные уровни логирования для разных сред

## Тестирование

### 1. Архитектура тестов
- Unit тесты для отдельных компонентов
- Integration тесты для взаимодействия компонентов
- End-to-end тесты для критических сценариев

### 2. Моки и заглушки
- Мокирование внешних API для тестов
- Заглушки для базы данных в unit тестах
- Фикстуры для повторяемых тестовых данных

### 3. Тестирование асинхронного кода
- Использование `pytest-asyncio` для асинхронных тестов
- Тестирование race conditions и deadlocks
- Тестирование обработки ошибок в асинхронном коде