# Стандарты кодирования Python для YouTube Summary Bot

## Общие принципы

### Стиль кодирования
- Следуем PEP 8 для всего Python кода
- Максимальная длина строки: 120 символов (учитывая сложность асинхронного кода)
- Используем 4 пробела для отступов, никогда не используем табы
- Пустые строки: 2 между классами и функциями верхнего уровня, 1 между методами класса

### Именование
- **Переменные и функции**: snake_case (например: `video_id`, `process_transcript`)
- **Классы**: PascalCase (например: `YouTubeTranscriber`, `APIMetrics`)
- **Константы**: UPPER_SNAKE_CASE (например: `MAX_VIDEO_DURATION`, `GEMINI_API_URL`)
- **Приватные методы**: начинаются с подчеркивания `_private_method`
- **Модули**: lowercase с подчеркиваниями (например: `youtube_services`, `config_and_utils`)

### Документация кода
- Все публичные функции и классы должны иметь docstring в формате Google Style
- Комментарии на русском языке для объяснения бизнес-логики
- Комментарии на английском для технических деталей

```python
def process_video_transcript(video_id: str, language: str = 'ru') -> Dict[str, Any]:
    """
    Обрабатывает транскрипт YouTube видео и создает сводку.
    
    Args:
        video_id: Идентификатор YouTube видео
        language: Язык для обработки (по умолчанию 'ru')
        
    Returns:
        Словарь с результатами обработки, включая краткую и подробную сводки
        
    Raises:
        YouTubeTranscriberError: При ошибке получения транскрипта
        APIError: При ошибке обращения к Gemini API
    """
```

## Асинхронное программирование

### Правила async/await
- Все функции работы с внешними API должны быть асинхронными
- Используем `aiohttp` для HTTP запросов, никогда не используем `requests` в асинхронном коде
- Всегда используем `async with` для контекстных менеджеров
- Обязательно закрываем все ресурсы (сессии, соединения)

```python
async def fetch_video_data(session: aiohttp.ClientSession, video_id: str) -> Dict[str, Any]:
    """Получает данные видео через YouTube API."""
    async with session.get(f"{YOUTUBE_DATA_API_URL}?id={video_id}") as response:
        response.raise_for_status()
        return await response.json()
```

### Обработка ошибок в асинхронном коде
- Всегда оборачиваем асинхронные операции в try/except
- Используем специфичные исключения для разных типов ошибок
- Логируем все исключения с контекстом

```python
try:
    result = await process_video_async(video_id)
except YouTubeTranscriberError as e:
    logger.error(f"Ошибка транскрипции видео {video_id}: {e}")
    raise
except Exception as e:
    logger.error(f"Неожиданная ошибка при обработке {video_id}: {e}")
    raise APIError(f"Внутренняя ошибка сервера") from e
```

## Типизация

### Обязательные аннотации типов
- Все функции должны иметь аннотации типов для параметров и возвращаемых значений
- Используем `Optional[T]` для необязательных параметров
- Используем `Union[T, U]` или `T | U` (Python 3.10+) для множественных типов
- Импортируем типы из `typing` модуля

```python
from typing import Optional, Dict, List, Any, Union
from datetime import datetime

async def create_video_summary(
    video_id: str,
    summary_type: str = "brief",
    language: Optional[str] = None
) -> Dict[str, Any]:
    """Создает сводку видео указанного типа."""
    pass
```

### Использование TypedDict для структур данных
```python
from typing import TypedDict

class VideoMetadata(TypedDict):
    video_id: str
    title: str
    duration: int
    channel_name: str
    upload_date: datetime
```

## Структура модулей

### Импорты
- Группируем импорты в следующем порядке:
  1. Стандартная библиотека Python
  2. Сторонние библиотеки
  3. Локальные модули проекта
- Разделяем группы пустой строкой
- Сортируем импорты в алфавитном порядке внутри каждой группы

```python
# Стандартная библиотека
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional

# Сторонние библиотеки
import aiohttp
from telegram import Update
from telegram.ext import ContextTypes

# Локальные модули
from config_and_utils import logger, GEMINI_API_KEYS
from youtube_services import YouTubeServices
```

### Структура файлов
- Каждый модуль должен иметь docstring с описанием назначения
- Константы модуля размещаем в начале после импортов
- Классы и функции группируем логически
- Основной код выполнения помещаем в `if __name__ == "__main__":`

## Логирование

### Структурированное логирование
- Используем `structlog` для структурированного логирования
- Все логи должны содержать контекстную информацию
- Используем разные логгеры для разных компонентов

```python
from config_and_utils import video_logger, api_logger

# Логирование обработки видео
video_logger.info(
    "Начата обработка видео",
    video_id=video_id,
    user_id=user_id,
    summary_type=summary_type
)

# Логирование API запросов
api_logger.warning(
    "Превышен лимит API запросов",
    api_name="gemini",
    requests_count=current_requests,
    limit=MAX_API_REQUESTS_PER_HOUR
)
```

### Уровни логирования
- `DEBUG`: Детальная отладочная информация
- `INFO`: Общая информация о работе системы
- `WARNING`: Предупреждения о потенциальных проблемах
- `ERROR`: Ошибки, которые не останавливают работу
- `CRITICAL`: Критические ошибки, требующие немедленного внимания

## Обработка ошибок

### Иерархия исключений
```python
class YouTubeBotError(Exception):
    """Базовое исключение для YouTube бота."""
    pass

class YouTubeTranscriberError(YouTubeBotError):
    """Ошибки транскрипции YouTube видео."""
    pass

class APIError(YouTubeBotError):
    """Ошибки работы с внешними API."""
    pass

class DatabaseError(YouTubeBotError):
    """Ошибки работы с базой данных."""
    pass
```

### Принципы обработки ошибок
- Перехватываем только те исключения, которые можем обработать
- Всегда логируем исключения с полным контекстом
- Используем `raise ... from e` для сохранения цепочки исключений
- Возвращаем пользователю понятные сообщения об ошибках

## Производительность

### Оптимизация асинхронного кода
- Используем `asyncio.gather()` для параллельного выполнения независимых операций
- Ограничиваем количество одновременных соединений через `aiohttp.TCPConnector`
- Используем пулы соединений для базы данных
- Кешируем результаты дорогих операций

### Управление ресурсами
- Всегда закрываем файлы, соединения и сессии
- Используем контекстные менеджеры (`async with`, `with`)
- Ограничиваем размер очередей и буферов
- Мониторим использование памяти и CPU

```python
# Правильное использование ресурсов
async with aiohttp.ClientSession(
    connector=aiohttp.TCPConnector(limit=10),
    timeout=aiohttp.ClientTimeout(total=30)
) as session:
    tasks = [fetch_video_data(session, vid_id) for vid_id in video_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)
```