# Стандарты документации для YouTube Summary Bot

## Общие принципы документации

### 1. Язык документации
- **Пользовательская документация**: на русском языке
- **Техническая документация**: на русском языке с английскими терминами где необходимо
- **Комментарии в коде**: на русском для бизнес-логики, на английском для технических деталей
- **API документация**: на английском языке

### 2. Структура документации
- README.md - основная документация проекта
- docs/ - папка с детальной документацией
- Inline комментарии в коде
- Docstrings для всех публичных функций и классов
- API документация для внешних интеграций

### 3. Форматирование
- Используем Markdown для всей документации
- Соблюдаем единый стиль заголовков и списков
- Добавляем примеры кода в блоках с подсветкой синтаксиса
- Используем эмодзи для улучшения читаемости (умеренно)

## README.md

### Обязательные разделы
1. **Заголовок с описанием** - краткое описание проекта
2. **Основные возможности** - список ключевых функций
3. **Технические требования** - системные требования и зависимости
4. **Установка и настройка** - пошаговая инструкция
5. **Использование** - примеры использования
6. **Конфигурация** - описание настроек
7. **API документация** - если есть внешние API
8. **Разработка** - инструкции для разработчиков
9. **Лицензия** - информация о лицензии

### Пример структуры README.md
```markdown
# 🎥 YouTube Summary Bot

Краткое описание проекта в 1-2 предложениях.

## 🚀 Основные возможности

- **Функция 1** - описание функции
- **Функция 2** - описание функции
- **Функция 3** - описание функции

## 🛠️ Технические требования

### Системные требования
- Python 3.8+
- Операционная система: Windows, Linux, macOS
- Память: минимум 2GB RAM

### Зависимости
```bash
pip install -r requirements.txt
```

## 📦 Установка и настройка

### 1. Клонирование репозитория
```bash
git clone https://github.com/user/youtube-summary-bot.git
cd youtube-summary-bot
```

### 2. Установка зависимостей
```bash
pip install -r requirements.txt
```

### 3. Настройка переменных окружения
```bash
cp .env.example .env
# Отредактируйте .env файл
```

## 🚀 Использование

### Запуск бота
```bash
python start_bot.py
```

### Основные команды
- `/start` - начать работу с ботом
- `/help` - получить справку
- `/summary <url>` - создать сводку видео
```

## Docstrings

### Стандарт Google Style
Используем Google Style для всех docstrings с адаптацией на русский язык:

```python
def process_video_transcript(video_id: str, language: str = 'ru') -> Dict[str, Any]:
    """
    Обрабатывает транскрипт YouTube видео и создает сводку.
    
    Функция получает транскрипт видео через YouTube API, обрабатывает его
    с помощью Gemini AI и возвращает краткую и подробную сводки.
    
    Args:
        video_id: Идентификатор YouTube видео в формате 'dQw4w9WgXcQ'
        language: Язык для обработки транскрипта. Поддерживаемые значения:
            'ru' (русский), 'en' (английский), 'auto' (автоопределение)
            
    Returns:
        Словарь с результатами обработки:
        {
            'brief_summary': str,      # Краткая сводка (2-3 предложения)
            'detailed_summary': str,   # Подробная сводка с временными метками
            'key_points': List[str],   # Список ключевых моментов
            'duration': int,           # Длительность видео в секундах
            'language': str            # Определенный язык транскрипта
        }
        
    Raises:
        YouTubeTranscriberError: Если не удалось получить транскрипт видео.
            Возможные причины:
            - Видео не существует или недоступно
            - Отсутствуют субтитры для видео
            - Превышен лимит API запросов
        APIError: При ошибке обращения к Gemini API.
            Возможные причины:
            - Недоступность Gemini API
            - Превышен лимит токенов
            - Некорректный API ключ
        ValidationError: При некорректном формате video_id
        
    Example:
        >>> result = await process_video_transcript('dQw4w9WgXcQ', 'ru')
        >>> print(result['brief_summary'])
        'Видео содержит музыкальный клип Rick Astley - Never Gonna Give You Up...'
        
    Note:
        Функция кеширует результаты обработки на 24 часа для оптимизации
        производительности. Для принудительного обновления используйте
        параметр force_refresh=True.
        
    See Also:
        get_video_metadata: Получение метаданных видео
        create_telegraph_article: Создание статьи в Telegraph
    """
```

### Docstrings для классов
```python
class YouTubeTranscriber:
    """
    Класс для получения и обработки транскриптов YouTube видео.
    
    YouTubeTranscriber предоставляет асинхронный интерфейс для работы
    с транскриптами YouTube видео. Поддерживает автоматическое определение
    языка, обработку ошибок и кеширование результатов.
    
    Attributes:
        session: Асинхронная HTTP сессия для запросов
        cache: TTL кеш для хранения транскриптов
        rate_limiter: Ограничитель скорости запросов
        
    Example:
        >>> async with YouTubeTranscriber() as transcriber:
        ...     transcript = await transcriber.get_transcript('dQw4w9WgXcQ')
        ...     print(transcript['text'])
        
    Note:
        Класс должен использоваться как асинхронный контекстный менеджер
        для правильного управления ресурсами.
    """
    
    def __init__(self, cache_ttl: int = 3600, max_retries: int = 3):
        """
        Инициализирует транскрибер с заданными параметрами.
        
        Args:
            cache_ttl: Время жизни кеша в секундах (по умолчанию 1 час)
            max_retries: Максимальное количество повторных попыток при ошибках
        """
```

## Комментарии в коде

### Принципы комментирования
1. **Объясняем "почему", а не "что"** - код должен быть самодокументируемым
2. **Комментируем сложную бизнес-логику** на русском языке
3. **Технические детали** комментируем на английском
4. **Обновляем комментарии** при изменении кода

### Примеры хороших комментариев
```python
# Используем экспоненциальную задержку для предотвращения thundering herd эффекта
delay = min(base_delay * (exponential_base ** attempt), max_delay)
jitter = random.uniform(0, delay * 0.1)  # Add jitter to prevent synchronized retries
await asyncio.sleep(delay + jitter)

# Фильтруем видео короче 60 секунд, так как они обычно не содержат полезного контента
if video_duration < MINIMUM_VIDEO_DURATION:
    logger.info(f"Пропускаем короткое видео {video_id}, длительность: {video_duration}с")
    return None

# YouTube API возвращает длительность в ISO 8601 формате (PT4M13S)
# Конвертируем в секунды для удобства работы
duration_seconds = self._parse_iso8601_duration(duration_string)
```

### Примеры плохих комментариев
```python
# Плохо: объясняет очевидное
user_id = update.effective_user.id  # Получаем ID пользователя

# Плохо: устаревший комментарий
# TODO: добавить поддержку английского языка (уже реализовано)
summary = await create_summary(transcript, language='ru')

# Плохо: слишком общий комментарий
# Обрабатываем видео
result = await process_video(video_id)
```

## API документация

### Swagger/OpenAPI спецификация
Для внешних API создаем OpenAPI спецификацию:

```yaml
openapi: 3.0.0
info:
  title: YouTube Summary Bot API
  description: API для создания сводок YouTube видео
  version: 1.0.0
  contact:
    name: YouTube Summary Bot Support
    email: <EMAIL>

servers:
  - url: https://api.youtube-summary-bot.com/v1
    description: Production server

paths:
  /summary:
    post:
      summary: Создать сводку видео
      description: |
        Создает краткую или подробную сводку YouTube видео.
        Поддерживает автоматическое определение языка и кеширование результатов.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - video_url
              properties:
                video_url:
                  type: string
                  format: uri
                  description: URL YouTube видео
                  example: "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                summary_type:
                  type: string
                  enum: [brief, detailed]
                  default: brief
                  description: Тип сводки
                language:
                  type: string
                  enum: [ru, en, auto]
                  default: auto
                  description: Язык обработки
      responses:
        200:
          description: Сводка успешно создана
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SummaryResponse'
        400:
          description: Некорректный запрос
        404:
          description: Видео не найдено
        429:
          description: Превышен лимит запросов
        500:
          description: Внутренняя ошибка сервера

components:
  schemas:
    SummaryResponse:
      type: object
      properties:
        video_id:
          type: string
          description: ID YouTube видео
        title:
          type: string
          description: Название видео
        brief_summary:
          type: string
          description: Краткая сводка
        detailed_summary:
          type: string
          description: Подробная сводка
        key_points:
          type: array
          items:
            type: string
          description: Ключевые моменты
        duration:
          type: integer
          description: Длительность в секундах
        language:
          type: string
          description: Определенный язык
        created_at:
          type: string
          format: date-time
          description: Время создания сводки
```

## Документация для разработчиков

### Архитектурная документация
Создаем отдельные файлы для описания архитектуры:

#### docs/architecture.md
```markdown
# Архитектура YouTube Summary Bot

## Обзор системы

YouTube Summary Bot построен на основе модульной асинхронной архитектуры
с четким разделением ответственности между компонентами.

## Компоненты системы

### 1. Telegram Bot Layer
- **Файл**: `telegram_bot.py`
- **Назначение**: Обработка сообщений пользователей
- **Зависимости**: `python-telegram-bot`, `aiohttp`

### 2. YouTube Services Layer
- **Файл**: `youtube_services.py`
- **Назначение**: Работа с YouTube API
- **Зависимости**: `aiohttp`, `youtube-transcript-api`

## Диаграмма компонентов

```mermaid
graph TD
    A[Telegram User] --> B[Telegram Bot]
    B --> C[YouTube Services]
    B --> D[Gemini AI]
    C --> E[YouTube API]
    D --> F[Google Gemini API]
    B --> G[Database]
    B --> H[Telegraph API]
```

## Потоки данных

### Обработка видео
1. Пользователь отправляет URL видео
2. Извлекается video_id из URL
3. Получаются метаданные через YouTube API
4. Загружается транскрипт видео
5. Создается сводка через Gemini AI
6. Результат сохраняется в базу данных
7. Отправляется ответ пользователю
```

#### docs/deployment.md
```markdown
# Развертывание YouTube Summary Bot

## Требования к серверу

### Минимальные требования
- CPU: 2 ядра
- RAM: 4GB
- Диск: 20GB SSD
- ОС: Ubuntu 20.04+ или CentOS 8+

### Рекомендуемые требования
- CPU: 4 ядра
- RAM: 8GB
- Диск: 50GB SSD
- ОС: Ubuntu 22.04 LTS

## Установка

### 1. Подготовка сервера
```bash
# Обновление системы
sudo apt update && sudo apt upgrade -y

# Установка Python 3.8+
sudo apt install python3.8 python3.8-venv python3.8-dev -y

# Установка дополнительных пакетов
sudo apt install git nginx supervisor -y
```

### 2. Развертывание приложения
```bash
# Клонирование репозитория
git clone https://github.com/user/youtube-summary-bot.git
cd youtube-summary-bot

# Создание виртуального окружения
python3.8 -m venv venv
source venv/bin/activate

# Установка зависимостей
pip install -r requirements.txt
```

### 3. Настройка переменных окружения
```bash
# Создание файла конфигурации
cp .env.example .env

# Редактирование конфигурации
nano .env
```

## Мониторинг и логирование

### Настройка логирования
```bash
# Создание директории для логов
sudo mkdir -p /var/log/youtube-summary-bot
sudo chown $USER:$USER /var/log/youtube-summary-bot

# Настройка ротации логов
sudo nano /etc/logrotate.d/youtube-summary-bot
```

### Мониторинг через Supervisor
```ini
[program:youtube-summary-bot]
command=/path/to/venv/bin/python start_bot.py
directory=/path/to/youtube-summary-bot
user=botuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/youtube-summary-bot/bot.log
```
```

## Changelog и версионирование

### Формат CHANGELOG.md
```markdown
# Changelog

Все значимые изменения в проекте документируются в этом файле.

Формат основан на [Keep a Changelog](https://keepachangelog.com/ru/1.0.0/),
и проект следует [Semantic Versioning](https://semver.org/lang/ru/).

## [Unreleased]

### Добавлено
- Новая функция X
- Поддержка формата Y

### Изменено
- Улучшена производительность Z
- Обновлен интерфейс A

### Исправлено
- Исправлена ошибка B
- Устранена проблема C

### Удалено
- Удалена устаревшая функция D

## [1.2.0] - 2024-01-15

### Добавлено
- Поддержка обработки видео файлов
- Система подписок на каналы
- Интеграция с Telegraph для создания статей

### Изменено
- Улучшена обработка ошибок API
- Оптимизирована работа с базой данных

### Исправлено
- Исправлена ошибка с длинными транскриптами
- Устранены проблемы с кодировкой

## [1.1.0] - 2024-01-01

### Добавлено
- Поддержка множественных API ключей Gemini
- Система мониторинга и алертов
- Структурированное логирование

### Изменено
- Переход на полностью асинхронную архитектуру
- Улучшена обработка rate limiting

## [1.0.0] - 2023-12-15

### Добавлено
- Базовая функциональность создания сводок
- Интеграция с Telegram Bot API
- Поддержка YouTube транскрипции
- Интеграция с Google Gemini AI
```

## Обновление документации

### Процесс обновления
1. **При добавлении новой функции** - обновляем README.md и соответствующие docstrings
2. **При изменении API** - обновляем OpenAPI спецификацию
3. **При релизе** - обновляем CHANGELOG.md
4. **При изменении архитектуры** - обновляем архитектурную документацию

### Проверка документации
- Все ссылки должны быть рабочими
- Примеры кода должны быть актуальными
- Скриншоты должны соответствовать текущему интерфейсу
- Документация должна быть синхронизирована с кодом

### Автоматизация
```python
# Скрипт для проверки актуальности документации
def check_documentation_sync():
    """Проверяет синхронизацию документации с кодом."""
    
    # Проверяем, что все публичные функции имеют docstrings
    missing_docstrings = find_functions_without_docstrings()
    if missing_docstrings:
        print(f"Функции без docstrings: {missing_docstrings}")
    
    # Проверяем актуальность примеров в README
    outdated_examples = check_readme_examples()
    if outdated_examples:
        print(f"Устаревшие примеры: {outdated_examples}")
    
    # Проверяем ссылки в документации
    broken_links = check_documentation_links()
    if broken_links:
        print(f"Нерабочие ссылки: {broken_links}")
```