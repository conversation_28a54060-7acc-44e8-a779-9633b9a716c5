# Правила безопасности для YouTube Summary Bot

## Общие принципы безопасности

### 1. Принцип минимальных привилегий
- Каждый компонент системы должен иметь только те права доступа, которые необходимы для выполнения его функций
- API ключи должны иметь минимально необходимые разрешения
- Пользователи бота получают доступ только к своим данным
- Временные файлы создаются с ограниченными правами доступа

### 2. Защита в глубину (Defense in Depth)
- Многоуровневая система защиты на всех уровнях архитектуры
- Валидация данных на входе и выходе
- Шифрование данных при передаче и хранении
- Мониторинг и логирование всех операций безопасности

### 3. Принцип "Fail Secure"
- При возникновении ошибок система должна переходить в безопасное состояние
- Отказ в доступе по умолчанию при неопределенных ситуациях
- Graceful degradation без компрометации безопасности

## Управление секретами и конфигурацией

### 1. Переменные окружения
Все секретные данные должны храниться только в переменных окружения:

```python
import os
from typing import Optional

class SecureConfig:
    """Безопасное управление конфигурацией."""
    
    @staticmethod
    def get_required_env(key: str) -> str:
        """
        Получает обязательную переменную окружения.
        
        Args:
            key: Название переменной окружения
            
        Returns:
            Значение переменной
            
        Raises:
            ValueError: Если переменная не найдена
        """
        value = os.getenv(key)
        if not value:
            raise ValueError(f"Обязательная переменная окружения {key} не установлена")
        return value
    
    @staticmethod
    def get_optional_env(key: str, default: str = "") -> str:
        """Получает необязательную переменную окружения."""
        return os.getenv(key, default)
    
    @staticmethod
    def get_env_list(key: str, separator: str = ",") -> List[str]:
        """Получает список значений из переменной окружения."""
        value = os.getenv(key, "")
        return [item.strip() for item in value.split(separator) if item.strip()]

# Использование
TELEGRAM_BOT_TOKEN = SecureConfig.get_required_env("TELEGRAM_BOT_TOKEN")
GEMINI_API_KEYS = SecureConfig.get_env_list("GEMINI_API_KEYS")
DATABASE_URL = SecureConfig.get_optional_env("DATABASE_URL", "sqlite:///bot.db")
```

### 2. Файл .env
Создаем шаблон .env.example без реальных секретов:

```bash
# .env.example - шаблон конфигурации (БЕЗ реальных ключей)

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Google Gemini AI Configuration
GEMINI_API_KEYS=key1,key2,key3

# YouTube Data API Configuration
YOUTUBE_DATA_API_KEY=your_youtube_api_key_here

# Telegraph Configuration
TELEGRAPH_ACCESS_TOKEN=your_telegraph_token_here
TELEGRAPH_AUTHOR_NAME=YouTube Summary Bot
TELEGRAPH_AUTHOR_URL=https://t.me/your_bot

# Database Configuration
DATABASE_URL=sqlite:///bot.db

# Security Configuration
ALLOWED_USER_IDS=123456789,987654321
ADMIN_USER_IDS=123456789
MAX_REQUESTS_PER_USER_PER_HOUR=10

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=/var/log/youtube-summary-bot/bot.log
```

### 3. Ротация ключей
```python
class APIKeyRotator:
    """Система ротации API ключей."""
    
    def __init__(self, keys: List[str]):
        self.keys = keys
        self.current_index = 0
        self.key_usage_count: Dict[str, int] = {key: 0 for key in keys}
        self.key_last_rotation: Dict[str, datetime] = {key: datetime.now() for key in keys}
        self._lock = asyncio.Lock()
    
    async def get_next_key(self) -> str:
        """Возвращает следующий ключ для использования."""
        async with self._lock:
            # Проверяем, нужна ли ротация ключей
            current_key = self.keys[self.current_index]
            
            # Ротируем ключ каждые 1000 использований или каждые 24 часа
            if (self.key_usage_count[current_key] >= 1000 or 
                datetime.now() - self.key_last_rotation[current_key] > timedelta(hours=24)):
                
                self.current_index = (self.current_index + 1) % len(self.keys)
                current_key = self.keys[self.current_index]
                self.key_last_rotation[current_key] = datetime.now()
                
                logger.info(f"Ротация API ключа, новый индекс: {self.current_index}")
            
            self.key_usage_count[current_key] += 1
            return current_key
    
    async def mark_key_compromised(self, key: str) -> None:
        """Помечает ключ как скомпрометированный."""
        async with self._lock:
            if key in self.keys:
                self.keys.remove(key)
                logger.critical(f"API ключ помечен как скомпрометированный и удален")
                
                # Отправляем алерт администратору
                await self._send_security_alert(f"API ключ скомпрометирован: {key[:8]}...")
```

## Валидация и санитизация входных данных

### 1. Валидация URL и video_id
```python
import re
from urllib.parse import urlparse, parse_qs
from typing import Optional

class InputValidator:
    """Валидация пользовательских данных."""
    
    # Регулярные выражения для валидации
    VIDEO_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{11}$')
    CHANNEL_ID_PATTERN = re.compile(r'^UC[a-zA-Z0-9_-]{22}$')
    SAFE_TEXT_PATTERN = re.compile(r'^[a-zA-Zа-яА-Я0-9\s\-_.,!?()]+$')
    
    @staticmethod
    def validate_youtube_url(url: str) -> Optional[str]:
        """
        Валидирует YouTube URL и извлекает video_id.
        
        Args:
            url: URL для валидации
            
        Returns:
            video_id если URL валиден, None в противном случае
        """
        try:
            # Санитизация URL
            url = url.strip()
            if len(url) > 2048:  # Максимальная длина URL
                return None
            
            parsed = urlparse(url)
            
            # Проверяем домен
            allowed_domains = ['youtube.com', 'www.youtube.com', 'youtu.be', 'm.youtube.com']
            if parsed.netloc not in allowed_domains:
                return None
            
            video_id = None
            
            # Извлекаем video_id в зависимости от формата URL
            if parsed.netloc == 'youtu.be':
                video_id = parsed.path[1:]  # Убираем первый слеш
            elif 'watch' in parsed.path:
                query_params = parse_qs(parsed.query)
                video_id = query_params.get('v', [None])[0]
            elif '/embed/' in parsed.path:
                video_id = parsed.path.split('/embed/')[1].split('?')[0]
            
            # Валидируем video_id
            if video_id and InputValidator.VIDEO_ID_PATTERN.match(video_id):
                return video_id
                
        except Exception as e:
            logger.warning(f"Ошибка валидации URL {url}: {e}")
        
        return None
    
    @staticmethod
    def validate_channel_id(channel_id: str) -> bool:
        """Валидирует ID YouTube канала."""
        return bool(InputValidator.CHANNEL_ID_PATTERN.match(channel_id.strip()))
    
    @staticmethod
    def sanitize_text(text: str, max_length: int = 1000) -> str:
        """
        Санитизирует текстовый ввод пользователя.
        
        Args:
            text: Текст для санитизации
            max_length: Максимальная длина текста
            
        Returns:
            Санитизированный текст
        """
        if not text:
            return ""
        
        # Обрезаем до максимальной длины
        text = text[:max_length]
        
        # Удаляем потенциально опасные символы
        text = re.sub(r'[<>"\']', '', text)
        
        # Удаляем множественные пробелы
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    @staticmethod
    def validate_file_upload(file_path: str, max_size: int, allowed_mime_types: List[str]) -> bool:
        """
        Валидирует загруженный файл.
        
        Args:
            file_path: Путь к файлу
            max_size: Максимальный размер в байтах
            allowed_mime_types: Разрешенные MIME типы
            
        Returns:
            True если файл валиден
        """
        try:
            import magic
            
            # Проверяем размер файла
            file_size = os.path.getsize(file_path)
            if file_size > max_size:
                logger.warning(f"Файл {file_path} превышает максимальный размер: {file_size} > {max_size}")
                return False
            
            # Проверяем MIME тип
            mime_type = magic.from_file(file_path, mime=True)
            if mime_type not in allowed_mime_types:
                logger.warning(f"Недопустимый MIME тип файла {file_path}: {mime_type}")
                return False
            
            # Проверяем расширение файла
            allowed_extensions = {
                'video/mp4': ['.mp4'],
                'video/avi': ['.avi'],
                'video/quicktime': ['.mov'],
                'video/webm': ['.webm']
            }
            
            file_extension = os.path.splitext(file_path)[1].lower()
            if mime_type in allowed_extensions:
                if file_extension not in allowed_extensions[mime_type]:
                    logger.warning(f"Несоответствие расширения и MIME типа: {file_extension} vs {mime_type}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка валидации файла {file_path}: {e}")
            return False
```

### 2. Rate Limiting и защита от злоупотреблений
```python
class SecurityRateLimiter:
    """Rate limiter с функциями безопасности."""
    
    def __init__(self):
        self.user_requests: Dict[int, List[float]] = {}
        self.blocked_users: Dict[int, float] = {}  # user_id -> время разблокировки
        self.suspicious_activity: Dict[int, int] = {}  # user_id -> счетчик подозрительной активности
        self._lock = asyncio.Lock()
    
    async def check_rate_limit(self, user_id: int, max_requests: int = 10, time_window: int = 3600) -> bool:
        """
        Проверяет rate limit для пользователя.
        
        Args:
            user_id: ID пользователя
            max_requests: Максимальное количество запросов
            time_window: Временное окно в секундах
            
        Returns:
            True если запрос разрешен
        """
        async with self._lock:
            now = time.time()
            
            # Проверяем, заблокирован ли пользователь
            if user_id in self.blocked_users:
                if now < self.blocked_users[user_id]:
                    logger.warning(f"Пользователь {user_id} заблокирован до {self.blocked_users[user_id]}")
                    return False
                else:
                    # Разблокируем пользователя
                    del self.blocked_users[user_id]
                    self.suspicious_activity.pop(user_id, 0)
            
            # Инициализируем список запросов для нового пользователя
            if user_id not in self.user_requests:
                self.user_requests[user_id] = []
            
            # Удаляем старые запросы
            self.user_requests[user_id] = [
                req_time for req_time in self.user_requests[user_id]
                if now - req_time < time_window
            ]
            
            # Проверяем лимит
            if len(self.user_requests[user_id]) >= max_requests:
                # Увеличиваем счетчик подозрительной активности
                self.suspicious_activity[user_id] = self.suspicious_activity.get(user_id, 0) + 1
                
                # Блокируем пользователя при повторных нарушениях
                if self.suspicious_activity[user_id] >= 3:
                    block_duration = 3600 * (2 ** (self.suspicious_activity[user_id] - 3))  # Экспоненциальная блокировка
                    self.blocked_users[user_id] = now + block_duration
                    
                    logger.critical(f"Пользователь {user_id} заблокирован на {block_duration} секунд за превышение лимитов")
                    await self._send_security_alert(f"Пользователь {user_id} заблокирован за спам")
                
                return False
            
            # Добавляем текущий запрос
            self.user_requests[user_id].append(now)
            return True
    
    async def _send_security_alert(self, message: str) -> None:
        """Отправляет алерт безопасности администраторам."""
        admin_ids = SecureConfig.get_env_list("ADMIN_USER_IDS")
        
        for admin_id in admin_ids:
            try:
                # Здесь должна быть логика отправки сообщения администратору
                logger.critical(f"Security Alert для админа {admin_id}: {message}")
            except Exception as e:
                logger.error(f"Ошибка отправки security alert: {e}")
```

## Безопасность базы данных

### 1. Подготовленные запросы (Prepared Statements)
```python
class SecureDatabase:
    """Безопасная работа с базой данных."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.connection_pool = None
    
    async def execute_query(self, query: str, params: Tuple = ()) -> List[Dict[str, Any]]:
        """
        Выполняет безопасный SQL запрос с параметрами.
        
        Args:
            query: SQL запрос с плейсхолдерами
            params: Параметры для запроса
            
        Returns:
            Результат запроса
        """
        try:
            async with aiosqlite.connect(self.database_url) as db:
                db.row_factory = aiosqlite.Row
                
                # Используем параметризованные запросы для предотвращения SQL injection
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"Ошибка выполнения запроса: {e}")
            raise DatabaseError(f"Database query failed") from e
    
    async def save_user_data(self, user_id: int, data: Dict[str, Any]) -> None:
        """
        Безопасно сохраняет данные пользователя.
        
        Args:
            user_id: ID пользователя
            data: Данные для сохранения
        """
        # Валидируем user_id
        if not isinstance(user_id, int) or user_id <= 0:
            raise ValueError("Invalid user_id")
        
        # Санитизируем данные
        sanitized_data = self._sanitize_user_data(data)
        
        # Шифруем чувствительные данные
        encrypted_data = await self._encrypt_sensitive_data(sanitized_data)
        
        query = """
        INSERT OR REPLACE INTO user_data (user_id, data, updated_at)
        VALUES (?, ?, ?)
        """
        
        await self.execute_query(query, (
            user_id,
            json.dumps(encrypted_data),
            datetime.now().isoformat()
        ))
    
    def _sanitize_user_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Санитизирует пользовательские данные перед сохранением."""
        sanitized = {}
        
        for key, value in data.items():
            # Валидируем ключи
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                continue
            
            # Санитизируем значения
            if isinstance(value, str):
                sanitized[key] = InputValidator.sanitize_text(value, max_length=10000)
            elif isinstance(value, (int, float, bool)):
                sanitized[key] = value
            elif isinstance(value, list):
                sanitized[key] = [InputValidator.sanitize_text(str(item)) for item in value[:100]]  # Лимит 100 элементов
        
        return sanitized
    
    async def _encrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Шифрует чувствительные данные."""
        # Здесь должна быть реализация шифрования для чувствительных полей
        # Например, используя cryptography библиотеку
        return data
```

### 2. Аудит и логирование безопасности
```python
class SecurityAuditor:
    """Система аудита безопасности."""
    
    def __init__(self):
        self.security_logger = logging.getLogger('security')
        self.failed_attempts: Dict[int, List[float]] = {}
    
    async def log_authentication_attempt(self, user_id: int, success: bool, ip_address: str = None) -> None:
        """Логирует попытку аутентификации."""
        event_data = {
            'event_type': 'authentication_attempt',
            'user_id': user_id,
            'success': success,
            'ip_address': ip_address,
            'timestamp': datetime.now().isoformat()
        }
        
        if success:
            self.security_logger.info(f"Successful authentication", extra=event_data)
            # Очищаем счетчик неудачных попыток
            self.failed_attempts.pop(user_id, None)
        else:
            self.security_logger.warning(f"Failed authentication attempt", extra=event_data)
            
            # Отслеживаем неудачные попытки
            now = time.time()
            if user_id not in self.failed_attempts:
                self.failed_attempts[user_id] = []
            
            self.failed_attempts[user_id].append(now)
            
            # Удаляем старые попытки (старше 1 часа)
            self.failed_attempts[user_id] = [
                attempt for attempt in self.failed_attempts[user_id]
                if now - attempt < 3600
            ]
            
            # Алерт при множественных неудачных попытках
            if len(self.failed_attempts[user_id]) >= 5:
                await self._send_security_alert(f"Multiple failed authentication attempts from user {user_id}")
    
    async def log_suspicious_activity(self, user_id: int, activity_type: str, details: Dict[str, Any]) -> None:
        """Логирует подозрительную активность."""
        event_data = {
            'event_type': 'suspicious_activity',
            'user_id': user_id,
            'activity_type': activity_type,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        
        self.security_logger.critical(f"Suspicious activity detected", extra=event_data)
        await self._send_security_alert(f"Suspicious activity: {activity_type} from user {user_id}")
    
    async def log_data_access(self, user_id: int, resource: str, action: str) -> None:
        """Логирует доступ к данным."""
        event_data = {
            'event_type': 'data_access',
            'user_id': user_id,
            'resource': resource,
            'action': action,
            'timestamp': datetime.now().isoformat()
        }
        
        self.security_logger.info(f"Data access", extra=event_data)
```

## Безопасность API и внешних интеграций

### 1. Безопасные HTTP запросы
```python
class SecureHTTPClient:
    """Безопасный HTTP клиент для внешних API."""
    
    def __init__(self):
        self.session = None
        self.trusted_hosts = [
            'www.googleapis.com',
            'api.telegram.org',
            'telegra.ph'
        ]
    
    async def __aenter__(self):
        # Настраиваем безопасную сессию
        connector = aiohttp.TCPConnector(
            ssl=True,  # Принудительное использование SSL
            verify_ssl=True,  # Проверка SSL сертификатов
            limit=50,  # Ограничение соединений
            limit_per_host=10,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(
            total=30,
            connect=10,
            sock_read=20
        )
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'YouTube-Summary-Bot/1.0 (Security-Enhanced)',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate'
            }
        )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def safe_request(self, method: str, url: str, **kwargs) -> aiohttp.ClientResponse:
        """
        Выполняет безопасный HTTP запрос.
        
        Args:
            method: HTTP метод
            url: URL для запроса
            **kwargs: Дополнительные параметры
            
        Returns:
            HTTP ответ
            
        Raises:
            SecurityError: При нарушении правил безопасности
        """
        # Валидируем URL
        parsed_url = urlparse(url)
        
        # Проверяем протокол
        if parsed_url.scheme != 'https':
            raise SecurityError(f"Only HTTPS requests are allowed: {url}")
        
        # Проверяем хост
        if parsed_url.netloc not in self.trusted_hosts:
            raise SecurityError(f"Untrusted host: {parsed_url.netloc}")
        
        # Ограничиваем размер ответа
        if 'max_response_size' not in kwargs:
            kwargs['max_response_size'] = 10 * 1024 * 1024  # 10MB
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                # Проверяем размер ответа
                content_length = response.headers.get('Content-Length')
                if content_length and int(content_length) > kwargs['max_response_size']:
                    raise SecurityError(f"Response too large: {content_length}")
                
                return response
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP request failed: {e}")
            raise APIError(f"Request failed") from e
```

### 2. Защита от SSRF атак
```python
class SSRFProtection:
    """Защита от Server-Side Request Forgery атак."""
    
    BLOCKED_NETWORKS = [
        ipaddress.IPv4Network('*********/8'),      # Localhost
        ipaddress.IPv4Network('10.0.0.0/8'),       # Private network
        ipaddress.IPv4Network('**********/12'),    # Private network
        ipaddress.IPv4Network('***********/16'),   # Private network
        ipaddress.IPv4Network('***********/16'),   # Link-local
        ipaddress.IPv4Network('*********/4'),      # Multicast
    ]
    
    @staticmethod
    async def validate_url(url: str) -> bool:
        """
        Валидирует URL на предмет SSRF атак.
        
        Args:
            url: URL для проверки
            
        Returns:
            True если URL безопасен
        """
        try:
            parsed = urlparse(url)
            
            # Проверяем протокол
            if parsed.scheme not in ['http', 'https']:
                return False
            
            # Резолвим hostname в IP адрес
            try:
                ip_address = ipaddress.IPv4Address(socket.gethostbyname(parsed.hostname))
            except (socket.gaierror, ipaddress.AddressValueError):
                return False
            
            # Проверяем, не находится ли IP в заблокированных сетях
            for blocked_network in SSRFProtection.BLOCKED_NETWORKS:
                if ip_address in blocked_network:
                    logger.warning(f"Blocked SSRF attempt to {ip_address} ({url})")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating URL {url}: {e}")
            return False
```

## Мониторинг безопасности

### 1. Детекция аномалий
```python
class SecurityMonitor:
    """Мониторинг безопасности и детекция аномалий."""
    
    def __init__(self):
        self.baseline_metrics = {}
        self.alert_thresholds = {
            'failed_requests_per_minute': 10,
            'new_users_per_hour': 50,
            'api_errors_per_minute': 5,
            'large_responses_per_hour': 20
        }
    
    async def analyze_user_behavior(self, user_id: int, action: str, metadata: Dict[str, Any]) -> None:
        """Анализирует поведение пользователя на предмет аномалий."""
        
        # Проверяем частоту действий
        action_frequency = await self._get_action_frequency(user_id, action)
        if action_frequency > self._get_normal_frequency(action) * 3:
            await self._trigger_anomaly_alert(
                f"Unusual activity frequency for user {user_id}: {action} - {action_frequency}/min"
            )
        
        # Проверяем паттерны использования
        if await self._detect_bot_behavior(user_id, action, metadata):
            await self._trigger_anomaly_alert(
                f"Bot-like behavior detected for user {user_id}"
            )
        
        # Проверяем географические аномалии (если доступна информация о местоположении)
        if 'ip_address' in metadata:
            if await self._detect_location_anomaly(user_id, metadata['ip_address']):
                await self._trigger_anomaly_alert(
                    f"Location anomaly detected for user {user_id}"
                )
    
    async def _detect_bot_behavior(self, user_id: int, action: str, metadata: Dict[str, Any]) -> bool:
        """Детектирует поведение, характерное для ботов."""
        
        # Проверяем регулярность запросов (боты часто делают запросы через равные интервалы)
        request_intervals = await self._get_request_intervals(user_id)
        if len(request_intervals) > 10:
            # Вычисляем стандартное отклонение интервалов
            mean_interval = sum(request_intervals) / len(request_intervals)
            variance = sum((x - mean_interval) ** 2 for x in request_intervals) / len(request_intervals)
            std_dev = variance ** 0.5
            
            # Если стандартное отклонение очень маленькое, это может указывать на бота
            if std_dev < mean_interval * 0.1:  # Менее 10% от среднего интервала
                return True
        
        # Проверяем однообразность запросов
        unique_actions = await self._get_unique_actions(user_id)
        total_actions = await self._get_total_actions(user_id)
        
        if total_actions > 20 and len(unique_actions) / total_actions < 0.3:  # Менее 30% уникальных действий
            return True
        
        return False
    
    async def _trigger_anomaly_alert(self, message: str) -> None:
        """Отправляет алерт об обнаруженной аномалии."""
        logger.critical(f"Security anomaly detected: {message}")
        
        # Отправляем уведомление администраторам
        admin_ids = SecureConfig.get_env_list("ADMIN_USER_IDS")
        for admin_id in admin_ids:
            try:
                # Здесь должна быть логика отправки уведомления
                pass
            except Exception as e:
                logger.error(f"Failed to send anomaly alert: {e}")
```

### 2. Автоматическое реагирование на угрозы
```python
class ThreatResponse:
    """Автоматическое реагирование на угрозы безопасности."""
    
    def __init__(self):
        self.blocked_ips: Set[str] = set()
        self.quarantined_users: Set[int] = set()
        self.threat_levels = {
            'LOW': 1,
            'MEDIUM': 2,
            'HIGH': 3,
            'CRITICAL': 4
        }
    
    async def handle_threat(self, threat_type: str, threat_level: str, details: Dict[str, Any]) -> None:
        """
        Обрабатывает обнаруженную угрозу.
        
        Args:
            threat_type: Тип угрозы
            threat_level: Уровень угрозы (LOW, MEDIUM, HIGH, CRITICAL)
            details: Детали угрозы
        """
        level = self.threat_levels.get(threat_level, 1)
        
        if level >= self.threat_levels['HIGH']:
            # Высокий уровень угрозы - немедленные действия
            if 'user_id' in details:
                await self._quarantine_user(details['user_id'])
            
            if 'ip_address' in details:
                await self._block_ip(details['ip_address'])
        
        elif level >= self.threat_levels['MEDIUM']:
            # Средний уровень - увеличиваем мониторинг
            if 'user_id' in details:
                await self._increase_monitoring(details['user_id'])
        
        # Логируем все угрозы
        await self._log_threat(threat_type, threat_level, details)
    
    async def _quarantine_user(self, user_id: int) -> None:
        """Помещает пользователя в карантин."""
        self.quarantined_users.add(user_id)
        logger.critical(f"User {user_id} quarantined due to security threat")
        
        # Уведомляем администраторов
        await self._notify_admins(f"User {user_id} has been quarantined")
    
    async def _block_ip(self, ip_address: str) -> None:
        """Блокирует IP адрес."""
        self.blocked_ips.add(ip_address)
        logger.critical(f"IP address {ip_address} blocked due to security threat")
        
        # Здесь можно добавить интеграцию с файрволом или WAF
        
    async def is_user_quarantined(self, user_id: int) -> bool:
        """Проверяет, находится ли пользователь в карантине."""
        return user_id in self.quarantined_users
    
    async def is_ip_blocked(self, ip_address: str) -> bool:
        """Проверяет, заблокирован ли IP адрес."""
        return ip_address in self.blocked_ips
```