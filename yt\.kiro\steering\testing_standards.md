# Стандарты тестирования для YouTube Summary Bot

## Общие принципы тестирования

### 1. Пирамида тестирования
- **Unit тесты (70%)** - тестирование отдельных функций и методов
- **Integration тесты (20%)** - тестирование взаимодействия компонентов
- **End-to-end тесты (10%)** - тестирование полных пользовательских сценариев

### 2. Принципы качественных тестов
- **FIRST принципы**: Fast, Independent, Repeatable, Self-validating, Timely
- **AAA паттерн**: Arrange, Act, Assert
- **Один тест - одна проверка**: каждый тест должен проверять только одну функциональность
- **Описательные имена тестов**: имя теста должно четко описывать что тестируется

### 3. Покрытие кода
- **Минимальное покрытие**: 80% для всего кода
- **Критический код**: 95% покрытие для core функциональности
- **Новый код**: 100% покрытие для всех новых функций
- **Исключения**: UI код и простые геттеры/сеттеры могут иметь меньшее покрытие

## Настройка тестовой среды

### 1. Структура тестов
```
tests/
├── unit/
│   ├── test_youtube_services.py
│   ├── test_youtube_transcriber.py
│   ├── test_config_utils.py
│   └── test_database.py
├── integration/
│   ├── test_api_integration.py
│   ├── test_database_integration.py
│   └── test_telegram_integration.py
├── e2e/
│   ├── test_video_processing_flow.py
│   └── test_subscription_flow.py
├── fixtures/
│   ├── sample_videos.json
│   ├── mock_responses.json
│   └── test_data.py
├── conftest.py
└── requirements-test.txt
```

### 2. Зависимости для тестирования
```python
# requirements-test.txt
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-xdist>=3.0.0
aioresponses>=0.7.4
factory-boy>=3.2.0
freezegun>=1.2.0
responses>=0.23.0
```

### 3. Конфигурация pytest
```python
# conftest.py
import asyncio
import pytest
import aiohttp
from unittest.mock import AsyncMock, MagicMock
from typing import AsyncGenerator, Generator

from config_and_utils import logger
from youtube_services import YouTubeServices
from youtube_transcriber import YouTubeTranscriber

# Настройка для асинхронных тестов
@pytest.fixture(scope="session")
def event_loop():
    """Создает event loop для всей сессии тестирования."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def http_session() -> AsyncGenerator[aiohttp.ClientSession, None]:
    """Создает HTTP сессию для тестов."""
    async with aiohttp.ClientSession() as session:
        yield session

@pytest.fixture
def mock_youtube_api():
    """Мок для YouTube API."""
    return MagicMock()

@pytest.fixture
def mock_gemini_api():
    """Мок для Gemini API."""
    return AsyncMock()

@pytest.fixture
def sample_video_data():
    """Тестовые данные видео."""
    return {
        "video_id": "dQw4w9WgXcQ",
        "title": "Rick Astley - Never Gonna Give You Up",
        "duration": 213,
        "channel_name": "RickAstleyVEVO",
        "upload_date": "2009-10-25T06:57:33Z"
    }

@pytest.fixture
def sample_transcript():
    """Тестовый транскрипт."""
    return {
        "text": "Never gonna give you up, never gonna let you down...",
        "language": "en",
        "duration": 213,
        "segments": [
            {"start": 0.0, "end": 5.0, "text": "Never gonna give you up"},
            {"start": 5.0, "end": 10.0, "text": "never gonna let you down"}
        ]
    }

# Настройка логирования для тестов
@pytest.fixture(autouse=True)
def setup_test_logging():
    """Настраивает логирование для тестов."""
    logger.setLevel("WARNING")  # Уменьшаем verbosity в тестах
```

## Unit тесты

### 1. Тестирование YouTube Services
```python
# tests/unit/test_youtube_services.py
import pytest
from unittest.mock import AsyncMock, patch
from aioresponses import aioresponses

from youtube_services import YouTubeServices
from config_and_utils import YouTubeTranscriberError, APIError

class TestYouTubeServices:
    """Тесты для YouTube Services."""
    
    @pytest.fixture
    def youtube_service(self):
        """Создает экземпляр YouTubeServices для тестов."""
        return YouTubeServices(api_key="test_api_key")
    
    @pytest.mark.asyncio
    async def test_get_video_metadata_success(self, youtube_service, sample_video_data):
        """Тест успешного получения метаданных видео."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        expected_response = {
            "items": [sample_video_data]
        }
        
        with aioresponses() as mock_aiohttp:
            mock_aiohttp.get(
                f"https://www.googleapis.com/youtube/v3/videos",
                payload=expected_response,
                status=200
            )
            
            # Act
            result = await youtube_service.get_video_metadata(video_id)
            
            # Assert
            assert result == sample_video_data
            assert result["video_id"] == video_id
            assert result["title"] == "Rick Astley - Never Gonna Give You Up"
    
    @pytest.mark.asyncio
    async def test_get_video_metadata_not_found(self, youtube_service):
        """Тест обработки случая, когда видео не найдено."""
        # Arrange
        video_id = "nonexistent"
        
        with aioresponses() as mock_aiohttp:
            mock_aiohttp.get(
                f"https://www.googleapis.com/youtube/v3/videos",
                payload={"items": []},
                status=200
            )
            
            # Act & Assert
            with pytest.raises(YouTubeTranscriberError, match="не найдено"):
                await youtube_service.get_video_metadata(video_id)
    
    @pytest.mark.asyncio
    async def test_get_video_metadata_api_error(self, youtube_service):
        """Тест обработки ошибки API."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        
        with aioresponses() as mock_aiohttp:
            mock_aiohttp.get(
                f"https://www.googleapis.com/youtube/v3/videos",
                status=403,
                payload={"error": {"code": 403, "message": "Forbidden"}}
            )
            
            # Act & Assert
            with pytest.raises(APIError):
                await youtube_service.get_video_metadata(video_id)
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, youtube_service):
        """Тест работы rate limiting."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        
        # Мокаем rate limiter
        with patch.object(youtube_service.rate_limiter, 'acquire') as mock_acquire:
            mock_acquire.return_value = None
            
            with aioresponses() as mock_aiohttp:
                mock_aiohttp.get(
                    f"https://www.googleapis.com/youtube/v3/videos",
                    payload={"items": [{"id": video_id}]},
                    status=200
                )
                
                # Act
                await youtube_service.get_video_metadata(video_id)
                
                # Assert
                mock_acquire.assert_called_once()
    
    @pytest.mark.parametrize("video_id,expected_valid", [
        ("dQw4w9WgXcQ", True),
        ("invalid_id", False),
        ("", False),
        ("too_long_video_id_123", False),
        ("short", False)
    ])
    def test_validate_video_id(self, youtube_service, video_id, expected_valid):
        """Тест валидации video_id."""
        # Act
        result = youtube_service._validate_video_id(video_id)
        
        # Assert
        assert result == expected_valid
```

### 2. Тестирование транскрибера
```python
# tests/unit/test_youtube_transcriber.py
import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from youtube_transcriber import YouTubeTranscriber, TranscriptNotFoundError

class TestYouTubeTranscriber:
    """Тесты для YouTube Transcriber."""
    
    @pytest.fixture
    def transcriber(self):
        """Создает экземпляр YouTubeTranscriber для тестов."""
        return YouTubeTranscriber()
    
    @pytest.mark.asyncio
    async def test_get_transcript_success(self, transcriber, sample_transcript):
        """Тест успешного получения транскрипта."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        
        with patch('youtube_transcript_api.YouTubeTranscriptApi.get_transcript') as mock_get_transcript:
            mock_get_transcript.return_value = [
                {'text': 'Never gonna give you up', 'start': 0.0, 'duration': 5.0},
                {'text': 'never gonna let you down', 'start': 5.0, 'duration': 5.0}
            ]
            
            # Act
            result = await transcriber.get_transcript(video_id)
            
            # Assert
            assert 'text' in result
            assert 'segments' in result
            assert len(result['segments']) == 2
            assert 'Never gonna give you up' in result['text']
    
    @pytest.mark.asyncio
    async def test_get_transcript_not_found(self, transcriber):
        """Тест обработки случая отсутствия транскрипта."""
        # Arrange
        video_id = "no_transcript"
        
        with patch('youtube_transcript_api.YouTubeTranscriptApi.get_transcript') as mock_get_transcript:
            mock_get_transcript.side_effect = Exception("No transcript found")
            
            # Act & Assert
            with pytest.raises(TranscriptNotFoundError):
                await transcriber.get_transcript(video_id)
    
    @pytest.mark.asyncio
    async def test_transcript_caching(self, transcriber):
        """Тест кеширования транскриптов."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        mock_transcript = [{'text': 'Test', 'start': 0.0, 'duration': 1.0}]
        
        with patch('youtube_transcript_api.YouTubeTranscriptApi.get_transcript') as mock_get_transcript:
            mock_get_transcript.return_value = mock_transcript
            
            # Act - первый вызов
            result1 = await transcriber.get_transcript(video_id)
            # Act - второй вызов (должен использовать кеш)
            result2 = await transcriber.get_transcript(video_id)
            
            # Assert
            assert result1 == result2
            mock_get_transcript.assert_called_once()  # API должен быть вызван только один раз
    
    @pytest.mark.asyncio
    async def test_language_detection(self, transcriber):
        """Тест автоматического определения языка."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        
        with patch('youtube_transcript_api.YouTubeTranscriptApi.list_transcripts') as mock_list:
            mock_transcript_list = MagicMock()
            mock_transcript_list.find_transcript.return_value.fetch.return_value = [
                {'text': 'Hello world', 'start': 0.0, 'duration': 1.0}
            ]
            mock_list.return_value = mock_transcript_list
            
            # Act
            result = await transcriber.get_transcript(video_id, language='auto')
            
            # Assert
            assert 'language' in result
            mock_transcript_list.find_transcript.assert_called()
```

### 3. Тестирование утилит и конфигурации
```python
# tests/unit/test_config_utils.py
import pytest
import os
from unittest.mock import patch

from config_and_utils import validate_video_id, create_safe_video_url, SecureConfig

class TestConfigUtils:
    """Тесты для утилит конфигурации."""
    
    @pytest.mark.parametrize("video_id,expected", [
        ("dQw4w9WgXcQ", True),
        ("invalid", False),
        ("", False),
        (None, False),
        ("123456789ab", True),
        ("special-chars_", True),
        ("with.dots", False)
    ])
    def test_validate_video_id(self, video_id, expected):
        """Тест валидации video_id."""
        # Act
        result = validate_video_id(video_id)
        
        # Assert
        assert result == expected
    
    def test_create_safe_video_url(self):
        """Тест создания безопасного URL видео."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        expected_url = f"https://www.youtube.com/watch?v={video_id}"
        
        # Act
        result = create_safe_video_url(video_id)
        
        # Assert
        assert result == expected_url
    
    def test_secure_config_required_env_success(self):
        """Тест успешного получения обязательной переменной окружения."""
        # Arrange
        key = "TEST_REQUIRED_VAR"
        value = "test_value"
        
        with patch.dict(os.environ, {key: value}):
            # Act
            result = SecureConfig.get_required_env(key)
            
            # Assert
            assert result == value
    
    def test_secure_config_required_env_missing(self):
        """Тест обработки отсутствующей обязательной переменной."""
        # Arrange
        key = "MISSING_VAR"
        
        with patch.dict(os.environ, {}, clear=True):
            # Act & Assert
            with pytest.raises(ValueError, match="не установлена"):
                SecureConfig.get_required_env(key)
    
    def test_secure_config_env_list(self):
        """Тест получения списка из переменной окружения."""
        # Arrange
        key = "TEST_LIST_VAR"
        value = "item1,item2,item3"
        expected = ["item1", "item2", "item3"]
        
        with patch.dict(os.environ, {key: value}):
            # Act
            result = SecureConfig.get_env_list(key)
            
            # Assert
            assert result == expected
```

## Integration тесты

### 1. Тестирование интеграции с API
```python
# tests/integration/test_api_integration.py
import pytest
from unittest.mock import patch
from aioresponses import aioresponses

from youtube_services import YouTubeServices
from youtube_transcriber import YouTubeTranscriber

class TestAPIIntegration:
    """Интеграционные тесты для API."""
    
    @pytest.mark.asyncio
    async def test_full_video_processing_flow(self, sample_video_data, sample_transcript):
        """Тест полного потока обработки видео."""
        # Arrange
        video_id = "dQw4w9WgXcQ"
        youtube_service = YouTubeServices(api_key="test_key")
        transcriber = YouTubeTranscriber()
        
        # Мокаем YouTube API
        with aioresponses() as mock_aiohttp:
            mock_aiohttp.get(
                "https://www.googleapis.com/youtube/v3/videos",
                payload={"items": [sample_video_data]},
                status=200
            )
            
            # Мокаем транскрипт API
            with patch('youtube_transcript_api.YouTubeTranscriptApi.get_transcript') as mock_transcript:
                mock_transcript.return_value = [
                    {'text': 'Test transcript', 'start': 0.0, 'duration': 5.0}
                ]
                
                # Act
                metadata = await youtube_service.get_video_metadata(video_id)
                transcript = await transcriber.get_transcript(video_id)
                
                # Assert
                assert metadata['video_id'] == video_id
                assert 'text' in transcript
                assert len(transcript['segments']) > 0
    
    @pytest.mark.asyncio
    async def test_gemini_api_integration(self):
        """Тест интеграции с Gemini API."""
        # Arrange
        from config_and_utils import GeminiAPIManager
        
        api_manager = GeminiAPIManager(["test_key_1", "test_key_2"])
        
        with aioresponses() as mock_aiohttp:
            mock_aiohttp.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                payload={
                    "candidates": [{
                        "content": {
                            "parts": [{"text": "Test summary"}]
                        }
                    }]
                },
                status=200
            )
            
            # Act
            api_key = await api_manager.get_available_key()
            
            # Assert
            assert api_key in ["test_key_1", "test_key_2"]
    
    @pytest.mark.asyncio
    async def test_database_integration(self):
        """Тест интеграции с базой данных."""
        # Arrange
        from yt_database import DatabaseManager
        
        db_manager = DatabaseManager(":memory:")  # Используем in-memory БД для тестов
        await db_manager.initialize()
        
        test_data = {
            "user_id": 123456789,
            "video_id": "dQw4w9WgXcQ",
            "summary": "Test summary"
        }
        
        # Act
        await db_manager.save_video_summary(**test_data)
        result = await db_manager.get_video_summary(test_data["user_id"], test_data["video_id"])
        
        # Assert
        assert result is not None
        assert result["summary"] == test_data["summary"]
```

### 2. Тестирование Telegram интеграции
```python
# tests/integration/test_telegram_integration.py
import pytest
from unittest.mock import AsyncMock, MagicMock
from telegram import Update, Message, User, Chat
from telegram.ext import ContextTypes

from telegram_bot import handle_video_url, handle_start_command

class TestTelegramIntegration:
    """Интеграционные тесты для Telegram бота."""
    
    @pytest.fixture
    def mock_update(self):
        """Создает мок Update объекта."""
        update = MagicMock(spec=Update)
        update.effective_user = MagicMock(spec=User)
        update.effective_user.id = 123456789
        update.effective_user.first_name = "Test"
        update.effective_chat = MagicMock(spec=Chat)
        update.effective_chat.id = 123456789
        update.message = MagicMock(spec=Message)
        return update
    
    @pytest.fixture
    def mock_context(self):
        """Создает мок Context объекта."""
        context = MagicMock(spec=ContextTypes.DEFAULT_TYPE)
        context.bot = AsyncMock()
        return context
    
    @pytest.mark.asyncio
    async def test_start_command_handler(self, mock_update, mock_context):
        """Тест обработчика команды /start."""
        # Arrange
        mock_update.message.text = "/start"
        
        # Act
        await handle_start_command(mock_update, mock_context)
        
        # Assert
        mock_context.bot.send_message.assert_called_once()
        call_args = mock_context.bot.send_message.call_args
        assert call_args[1]['chat_id'] == 123456789
        assert 'Добро пожаловать' in call_args[1]['text']
    
    @pytest.mark.asyncio
    async def test_video_url_handler_success(self, mock_update, mock_context):
        """Тест успешной обработки URL видео."""
        # Arrange
        mock_update.message.text = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        with patch('telegram_bot.process_video_request') as mock_process:
            mock_process.return_value = {
                'brief_summary': 'Test summary',
                'detailed_summary': 'Detailed test summary'
            }
            
            # Act
            await handle_video_url(mock_update, mock_context)
            
            # Assert
            mock_process.assert_called_once_with("dQw4w9WgXcQ", 123456789)
            mock_context.bot.send_message.assert_called()
    
    @pytest.mark.asyncio
    async def test_video_url_handler_invalid_url(self, mock_update, mock_context):
        """Тест обработки некорректного URL."""
        # Arrange
        mock_update.message.text = "https://invalid-url.com/video"
        
        # Act
        await handle_video_url(mock_update, mock_context)
        
        # Assert
        mock_context.bot.send_message.assert_called_once()
        call_args = mock_context.bot.send_message.call_args
        assert 'некорректный' in call_args[1]['text'].lower()
```

## End-to-End тесты

### 1. Тестирование полного потока обработки видео
```python
# tests/e2e/test_video_processing_flow.py
import pytest
from unittest.mock import patch, AsyncMock
import asyncio

class TestVideoProcessingFlow:
    """End-to-end тесты для обработки видео."""
    
    @pytest.mark.asyncio
    @pytest.mark.slow  # Помечаем медленные тесты
    async def test_complete_video_processing_flow(self):
        """Тест полного потока обработки видео от URL до сводки."""
        # Arrange
        video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        user_id = 123456789
        
        # Мокаем все внешние зависимости
        with patch('youtube_services.YouTubeServices.get_video_metadata') as mock_metadata, \
             patch('youtube_transcriber.YouTubeTranscriber.get_transcript') as mock_transcript, \
             patch('telegram_bot.create_video_summary') as mock_summary, \
             patch('yt_database.DatabaseManager.save_video_summary') as mock_save:
            
            # Настраиваем моки
            mock_metadata.return_value = {
                'video_id': 'dQw4w9WgXcQ',
                'title': 'Test Video',
                'duration': 213
            }
            
            mock_transcript.return_value = {
                'text': 'Test transcript content',
                'language': 'en',
                'segments': []
            }
            
            mock_summary.return_value = {
                'brief_summary': 'Brief test summary',
                'detailed_summary': 'Detailed test summary'
            }
            
            mock_save.return_value = None
            
            # Act
            from telegram_bot import process_video_request
            result = await process_video_request('dQw4w9WgXcQ', user_id)
            
            # Assert
            assert result is not None
            assert 'brief_summary' in result
            assert 'detailed_summary' in result
            
            # Проверяем, что все компоненты были вызваны
            mock_metadata.assert_called_once()
            mock_transcript.assert_called_once()
            mock_summary.assert_called_once()
            mock_save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling_in_flow(self):
        """Тест обработки ошибок в полном потоке."""
        # Arrange
        video_id = "invalid_video"
        user_id = 123456789
        
        with patch('youtube_services.YouTubeServices.get_video_metadata') as mock_metadata:
            mock_metadata.side_effect = Exception("Video not found")
            
            # Act & Assert
            from telegram_bot import process_video_request
            with pytest.raises(Exception):
                await process_video_request(video_id, user_id)
    
    @pytest.mark.asyncio
    async def test_concurrent_video_processing(self):
        """Тест одновременной обработки множественных видео."""
        # Arrange
        video_ids = ["video1", "video2", "video3"]
        user_id = 123456789
        
        with patch('telegram_bot.process_video_request') as mock_process:
            mock_process.return_value = {'brief_summary': 'Test'}
            
            # Act
            tasks = [
                asyncio.create_task(mock_process(vid_id, user_id))
                for vid_id in video_ids
            ]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Assert
            assert len(results) == 3
            assert all(isinstance(result, dict) for result in results)
            assert mock_process.call_count == 3
```

## Тестирование производительности

### 1. Нагрузочные тесты
```python
# tests/performance/test_load.py
import pytest
import asyncio
import time
from unittest.mock import patch, AsyncMock

class TestPerformance:
    """Тесты производительности."""
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_concurrent_api_requests(self):
        """Тест производительности при множественных API запросах."""
        # Arrange
        num_requests = 100
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {"items": [{"id": "test"}]}
            mock_get.return_value.__aenter__.return_value = mock_response
            
            from youtube_services import YouTubeServices
            service = YouTubeServices("test_key")
            
            # Act
            start_time = time.time()
            tasks = [
                service.get_video_metadata(f"video_{i}")
                for i in range(num_requests)
            ]
            await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Assert
            execution_time = end_time - start_time
            assert execution_time < 10.0  # Должно выполниться менее чем за 10 секунд
            
            # Проверяем rate limiting
            assert mock_get.call_count <= num_requests  # Не больше запросов чем ожидалось
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_memory_usage_during_processing(self):
        """Тест использования памяти при обработке."""
        import psutil
        import os
        
        # Arrange
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Симулируем обработку большого количества данных
        large_transcript = "Test text " * 10000  # ~100KB текста
        
        with patch('telegram_bot.create_video_summary') as mock_summary:
            mock_summary.return_value = {'brief_summary': 'Test'}
            
            # Act
            tasks = []
            for i in range(50):  # 50 одновременных обработок
                task = asyncio.create_task(mock_summary(large_transcript))
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            # Assert
            final_memory = process.memory_info().rss
            memory_increase = final_memory - initial_memory
            
            # Увеличение памяти не должно превышать 100MB
            assert memory_increase < 100 * 1024 * 1024
```

## Моки и фикстуры

### 1. Фабрики тестовых данных
```python
# tests/fixtures/test_data.py
import factory
from datetime import datetime, timedelta
from typing import Dict, Any

class VideoDataFactory(factory.Factory):
    """Фабрика для создания тестовых данных видео."""
    
    class Meta:
        model = dict
    
    video_id = factory.Sequence(lambda n: f"video_{n:011d}")
    title = factory.Faker('sentence', nb_words=4)
    duration = factory.Faker('random_int', min=60, max=3600)
    channel_name = factory.Faker('company')
    upload_date = factory.Faker('date_time_between', 
                               start_date='-1y', 
                               end_date='now')
    view_count = factory.Faker('random_int', min=1000, max=1000000)

class TranscriptDataFactory(factory.Factory):
    """Фабрика для создания тестовых транскриптов."""
    
    class Meta:
        model = dict
    
    text = factory.Faker('text', max_nb_chars=5000)
    language = factory.Faker('random_element', elements=['en', 'ru', 'es'])
    duration = factory.Faker('random_int', min=60, max=3600)
    
    @factory.post_generation
    def segments(obj, create, extracted, **kwargs):
        """Создает сегменты транскрипта."""
        if not create:
            return
        
        segments = []
        words = obj['text'].split()
        segment_length = 10  # слов в сегменте
        
        for i in range(0, len(words), segment_length):
            start_time = i * 2.0  # 2 секунды на сегмент
            end_time = start_time + segment_length * 2.0
            segment_text = ' '.join(words[i:i+segment_length])
            
            segments.append({
                'start': start_time,
                'end': end_time,
                'text': segment_text
            })
        
        obj['segments'] = segments

class UserDataFactory(factory.Factory):
    """Фабрика для создания тестовых данных пользователей."""
    
    class Meta:
        model = dict
    
    user_id = factory.Faker('random_int', min=100000000, max=999999999)
    first_name = factory.Faker('first_name')
    username = factory.Faker('user_name')
    language_code = factory.Faker('random_element', elements=['en', 'ru'])
    is_premium = factory.Faker('boolean', chance_of_getting_true=20)
```

### 2. Контекстные менеджеры для тестов
```python
# tests/fixtures/context_managers.py
import asyncio
from contextlib import asynccontextmanager
from unittest.mock import patch, AsyncMock

@asynccontextmanager
async def mock_youtube_api():
    """Контекстный менеджер для мокирования YouTube API."""
    with patch('youtube_services.YouTubeServices') as mock_service:
        mock_instance = AsyncMock()
        mock_service.return_value = mock_instance
        
        # Настраиваем стандартные ответы
        mock_instance.get_video_metadata.return_value = {
            'video_id': 'test_video',
            'title': 'Test Video',
            'duration': 300
        }
        
        yield mock_instance

@asynccontextmanager
async def mock_database():
    """Контекстный менеджер для мокирования базы данных."""
    with patch('yt_database.DatabaseManager') as mock_db:
        mock_instance = AsyncMock()
        mock_db.return_value = mock_instance
        
        # Настраиваем стандартные методы
        mock_instance.save_video_summary.return_value = None
        mock_instance.get_video_summary.return_value = None
        
        yield mock_instance

@asynccontextmanager
async def temporary_test_environment():
    """Создает временную тестовую среду."""
    # Настраиваем тестовые переменные окружения
    test_env = {
        'TELEGRAM_BOT_TOKEN': 'test_token',
        'GEMINI_API_KEYS': 'test_key1,test_key2',
        'YOUTUBE_DATA_API_KEY': 'test_youtube_key'
    }
    
    with patch.dict('os.environ', test_env):
        # Инициализируем тестовые компоненты
        async with mock_youtube_api() as youtube_mock, \
                   mock_database() as db_mock:
            
            yield {
                'youtube_service': youtube_mock,
                'database': db_mock
            }
```

## Запуск тестов

### 1. Конфигурация pytest.ini
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --cov=.
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    performance: marks tests as performance tests
    integration: marks tests as integration tests
    unit: marks tests as unit tests
asyncio_mode = auto
```

### 2. Скрипты для запуска тестов
```bash
#!/bin/bash
# scripts/run_tests.sh

# Запуск всех тестов
echo "Запуск всех тестов..."
pytest

# Запуск только unit тестов
echo "Запуск unit тестов..."
pytest -m unit

# Запуск тестов с покрытием
echo "Запуск тестов с анализом покрытия..."
pytest --cov=. --cov-report=html --cov-report=term

# Запуск тестов производительности
echo "Запуск тестов производительности..."
pytest -m performance

# Запуск быстрых тестов (исключая медленные)
echo "Запуск быстрых тестов..."
pytest -m "not slow"

# Параллельный запуск тестов
echo "Параллельный запуск тестов..."
pytest -n auto
```

### 3. CI/CD интеграция
```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', 3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=. --cov-report=xml
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ -v
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: true
```

## Отчетность и мониторинг тестов

### 1. Генерация отчетов
```python
# scripts/generate_test_report.py
import subprocess
import json
from datetime import datetime

def generate_test_report():
    """Генерирует подробный отчет о тестировании."""
    
    # Запускаем тесты с JSON выводом
    result = subprocess.run([
        'pytest', '--json-report', '--json-report-file=test_report.json'
    ], capture_output=True, text=True)
    
    # Читаем результаты
    with open('test_report.json', 'r') as f:
        report_data = json.load(f)
    
    # Генерируем HTML отчет
    html_report = f"""
    <html>
    <head><title>Test Report - {datetime.now().strftime('%Y-%m-%d %H:%M')}</title></head>
    <body>
        <h1>Test Results Summary</h1>
        <p>Total Tests: {report_data['summary']['total']}</p>
        <p>Passed: {report_data['summary']['passed']}</p>
        <p>Failed: {report_data['summary']['failed']}</p>
        <p>Duration: {report_data['duration']:.2f}s</p>
        
        <h2>Failed Tests</h2>
        <ul>
    """
    
    for test in report_data['tests']:
        if test['outcome'] == 'failed':
            html_report += f"<li>{test['nodeid']}: {test['call']['longrepr']}</li>"
    
    html_report += """
        </ul>
    </body>
    </html>
    """
    
    with open('test_report.html', 'w') as f:
        f.write(html_report)
    
    print("Test report generated: test_report.html")

if __name__ == "__main__":
    generate_test_report()
```