# Изменения в YouTube Summary Bot

## Переход на прямую обработку видео через Gemini API

### Основные изменения:

1. **Убрана транскрипция YouTube видео**
   - Удален весь код получения субтитров через `youtube-transcript-api`
   - Убрана логика ожидания появления субтитров
   - Удален класс `SubtitleWaitingQueue`

2. **Добавлена прямая обработка видео через Gemini**
   - Используется новый Google GenAI SDK (`google-genai`)
   - Видео прикрепляется напрямую к запросу в Gemini
   - Поддержка моделей `gemini-2.5-flash-lite` (по умолчанию) и `gemini-2.0-flash` (fallback)

3. **Изменения в архитектуре:**
   - Функция `generate_summary_with_gemini()` теперь принимает `video_url` вместо `transcript_text`
   - Функция `create_summary_with_ai()` больше не требует `transcript_text`
   - Упрощена логика в `handle_youtube_url()` - убрано получение транскрипции
   - Упрощен `ChannelMonitor` - убрана очередь ожидания субтитров

4. **Fallback механизм:**
   - При 3+ ошибках с `gemini-2.5-flash-lite` автоматически переключается на `gemini-2.0-flash`
   - Сохранена ротация API ключей при ошибках

5. **Обновленный промпт:**
   - Промпт изменен для работы с прикрепленным видео
   - Убраны ссылки на транскрипцию
   - Добавлена инструкция "Создай сводку на основе прикрепленного видео"

### Технические детали:

- **Новая зависимость:** `google-genai>=0.5.0`
- **FPS видео:** всегда `0.01` для оптимизации
- **MIME тип:** `video/*` для YouTube видео
- **Конфигурация thinking:** только для `gemini-2.5-flash-lite`

### Файлы изменены:

- `telegram_bot.py` - основные изменения логики
- `batch_video_processor.py` - убрана транскрипция
- `requirements.txt` - добавлена новая зависимость
- `test_gemini_video.py` - тестовый скрипт (новый)

### Что удалено:

- Класс `SubtitleWaitingQueue`
- Методы работы с очередью ожидания субтитров
- Логика получения транскрипции в основном потоке
- Обработка `TranscriptNotFoundError`

### Преимущества:

1. **Быстрее:** нет ожидания появления субтитров
2. **Надежнее:** не зависит от наличия субтитров
3. **Точнее:** Gemini анализирует видео напрямую
4. **Проще:** убрана сложная логика очередей

### Использование:

Бот теперь работает так:
1. Пользователь отправляет YouTube ссылку
2. Получается информация о видео и комментарии
3. Видео прикрепляется к запросу в Gemini
4. Gemini создает сводку на основе видео
5. Результат отправляется пользователю

Время обработки сократилось с 30-60 секунд до 15-30 секунд.