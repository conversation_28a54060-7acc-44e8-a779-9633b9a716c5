"""
Alerting System для YouTube бота
Этап 4 плана исправления ошибок с ссылками на видео
"""

import asyncio
import smtplib
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json
import logging

from structured_logging import alert_logger, EventType

logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Уровни серьезности алертов"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertChannel(Enum):
    """Каналы отправки алертов"""
    TELEGRAM = "telegram"
    EMAIL = "email"
    WEBHOOK = "webhook"
    LOG = "log"


@dataclass
class Alert:
    """Структура алерта"""
    id: str
    title: str
    message: str
    severity: AlertSeverity
    component: str
    timestamp: datetime
    details: Dict[str, Any]
    recovery_suggestions: List[str] = None
    correlation_id: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "title": self.title,
            "message": self.message,
            "severity": self.severity.value,
            "component": self.component,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "recovery_suggestions": self.recovery_suggestions or [],
            "correlation_id": self.correlation_id
        }


class AlertThreshold:
    """Пороговые значения для алертов"""
    
    def __init__(
        self,
        metric_name: str,
        warning_threshold: float,
        error_threshold: float,
        critical_threshold: float,
        comparison: str = "gt",  # gt, lt, eq
        time_window_minutes: int = 5
    ):
        self.metric_name = metric_name
        self.warning_threshold = warning_threshold
        self.error_threshold = error_threshold
        self.critical_threshold = critical_threshold
        self.comparison = comparison
        self.time_window_minutes = time_window_minutes

    def evaluate(self, value: float) -> Optional[AlertSeverity]:
        """Оценивает значение метрики и возвращает уровень алерта"""
        if self.comparison == "gt":
            if value >= self.critical_threshold:
                return AlertSeverity.CRITICAL
            elif value >= self.error_threshold:
                return AlertSeverity.ERROR
            elif value >= self.warning_threshold:
                return AlertSeverity.WARNING
        elif self.comparison == "lt":
            if value <= self.critical_threshold:
                return AlertSeverity.CRITICAL
            elif value <= self.error_threshold:
                return AlertSeverity.ERROR
            elif value <= self.warning_threshold:
                return AlertSeverity.WARNING
        
        return None


class TelegramAlerter:
    """Отправка алертов в Telegram"""
    
    def __init__(self, bot_token: str, admin_chat_ids: List[int]):
        self.bot_token = bot_token
        self.admin_chat_ids = admin_chat_ids

    async def send_alert(self, alert: Alert) -> bool:
        """Отправляет алерт в Telegram"""
        try:
            # Формируем сообщение
            severity_emoji = {
                AlertSeverity.INFO: "ℹ️",
                AlertSeverity.WARNING: "⚠️",
                AlertSeverity.ERROR: "❌",
                AlertSeverity.CRITICAL: "🚨"
            }
            
            emoji = severity_emoji.get(alert.severity, "📢")
            
            message = f"{emoji} <b>АЛЕРТ: {alert.title}</b>\n\n"
            message += f"<b>Компонент:</b> {alert.component}\n"
            message += f"<b>Серьезность:</b> {alert.severity.value.upper()}\n"
            message += f"<b>Время:</b> {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            message += f"<b>Описание:</b>\n{alert.message}\n"
            
            if alert.details:
                message += f"\n<b>Детали:</b>\n"
                for key, value in alert.details.items():
                    message += f"• {key}: {value}\n"
            
            if alert.recovery_suggestions:
                message += f"\n<b>Рекомендации по восстановлению:</b>\n"
                for suggestion in alert.recovery_suggestions:
                    message += f"• {suggestion}\n"
            
            if alert.correlation_id:
                message += f"\n<code>ID: {alert.correlation_id}</code>"
            
            # Отправляем во все админские чаты
            success_count = 0
            for chat_id in self.admin_chat_ids:
                try:
                    url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
                    data = {
                        "chat_id": chat_id,
                        "text": message,
                        "parse_mode": "HTML",
                        "disable_web_page_preview": True
                    }
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.post(url, json=data) as response:
                            if response.status == 200:
                                success_count += 1
                            else:
                                logger.error(f"Ошибка отправки алерта в Telegram чат {chat_id}: {response.status}")
                
                except Exception as e:
                    logger.error(f"Исключение при отправке алерта в Telegram чат {chat_id}: {e}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Ошибка отправки Telegram алерта: {e}")
            return False


class EmailAlerter:
    """Отправка алертов по email"""
    
    def __init__(
        self,
        smtp_server: str,
        smtp_port: int,
        username: str,
        password: str,
        from_email: str,
        to_emails: List[str],
        use_tls: bool = True
    ):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
        self.use_tls = use_tls

    async def send_alert(self, alert: Alert) -> bool:
        """Отправляет алерт по email"""
        try:
            # Создаем сообщение
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{alert.severity.value.upper()}] YouTube Bot Alert: {alert.title}"
            
            # Формируем тело письма
            body = f"""
YouTube Bot Alert

Component: {alert.component}
Severity: {alert.severity.value.upper()}
Time: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}

Description:
{alert.message}

Details:
"""
            
            if alert.details:
                for key, value in alert.details.items():
                    body += f"  {key}: {value}\n"
            
            if alert.recovery_suggestions:
                body += f"\nRecovery Suggestions:\n"
                for suggestion in alert.recovery_suggestions:
                    body += f"  - {suggestion}\n"
            
            if alert.correlation_id:
                body += f"\nCorrelation ID: {alert.correlation_id}"
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Отправляем письмо в отдельном потоке
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._send_email, msg)
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки email алерта: {e}")
            return False

    def _send_email(self, msg: MIMEMultipart):
        """Синхронная отправка email"""
        server = smtplib.SMTP(self.smtp_server, self.smtp_port)
        
        if self.use_tls:
            server.starttls()
        
        server.login(self.username, self.password)
        server.send_message(msg)
        server.quit()


class WebhookAlerter:
    """Отправка алертов через webhook"""
    
    def __init__(self, webhook_url: str, headers: Optional[Dict[str, str]] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {"Content-Type": "application/json"}

    async def send_alert(self, alert: Alert) -> bool:
        """Отправляет алерт через webhook"""
        try:
            payload = {
                "alert": alert.to_dict(),
                "timestamp": datetime.now().isoformat(),
                "source": "youtube_bot"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers=self.headers
                ) as response:
                    return response.status < 400
                    
        except Exception as e:
            logger.error(f"Ошибка отправки webhook алерта: {e}")
            return False


class AlertingSystem:
    """
    Основная система алертов
    """
    
    def __init__(self):
        self.alerters: Dict[AlertChannel, List] = {
            AlertChannel.TELEGRAM: [],
            AlertChannel.EMAIL: [],
            AlertChannel.WEBHOOK: [],
            AlertChannel.LOG: []
        }
        
        self.thresholds: Dict[str, AlertThreshold] = {}
        self.alert_history: List[Alert] = []
        self.active_alerts: Dict[str, Alert] = {}
        self.suppression_rules: Dict[str, timedelta] = {}
        
        # Настройка алертов по умолчанию
        self._setup_default_thresholds()

    def _setup_default_thresholds(self):
        """Настраивает пороговые значения по умолчанию"""
        default_thresholds = [
            AlertThreshold("error_rate", 5.0, 15.0, 30.0, "gt", 5),
            AlertThreshold("success_rate", 90.0, 70.0, 50.0, "lt", 10),
            AlertThreshold("api_quota_usage", 80.0, 90.0, 95.0, "gt", 5),
            AlertThreshold("processing_time_ms", 30000, 60000, 120000, "gt", 5),
            AlertThreshold("queue_size", 50, 100, 200, "gt", 3),
            AlertThreshold("failed_requests", 10, 25, 50, "gt", 5),
        ]
        
        for threshold in default_thresholds:
            self.thresholds[threshold.metric_name] = threshold

    def add_telegram_alerter(self, bot_token: str, admin_chat_ids: List[int]):
        """Добавляет Telegram алертер"""
        alerter = TelegramAlerter(bot_token, admin_chat_ids)
        self.alerters[AlertChannel.TELEGRAM].append(alerter)
        
        alert_logger.info(
            EventType.ALERT_TRIGGERED,
            "Добавлен Telegram алертер",
            context={"admin_chats_count": len(admin_chat_ids)}
        )

    def add_email_alerter(
        self,
        smtp_server: str,
        smtp_port: int,
        username: str,
        password: str,
        from_email: str,
        to_emails: List[str]
    ):
        """Добавляет email алертер"""
        alerter = EmailAlerter(smtp_server, smtp_port, username, password, from_email, to_emails)
        self.alerters[AlertChannel.EMAIL].append(alerter)
        
        alert_logger.info(
            EventType.ALERT_TRIGGERED,
            "Добавлен email алертер",
            context={"recipients_count": len(to_emails)}
        )

    def add_webhook_alerter(self, webhook_url: str, headers: Optional[Dict[str, str]] = None):
        """Добавляет webhook алертер"""
        alerter = WebhookAlerter(webhook_url, headers)
        self.alerters[AlertChannel.WEBHOOK].append(alerter)
        
        alert_logger.info(
            EventType.ALERT_TRIGGERED,
            "Добавлен webhook алертер",
            context={"webhook_url": webhook_url}
        )

    def set_suppression_rule(self, alert_type: str, suppression_duration: timedelta):
        """Устанавливает правило подавления повторных алертов"""
        self.suppression_rules[alert_type] = suppression_duration

    async def check_metric_and_alert(self, metric_name: str, value: float, context: Optional[Dict[str, Any]] = None):
        """Проверяет метрику и отправляет алерт при необходимости"""
        if metric_name not in self.thresholds:
            return
        
        threshold = self.thresholds[metric_name]
        severity = threshold.evaluate(value)
        
        if severity:
            alert_id = f"{metric_name}_{severity.value}"
            
            # Проверяем подавление
            if self._is_suppressed(alert_id):
                return
            
            alert = Alert(
                id=alert_id,
                title=f"Превышен порог метрики {metric_name}",
                message=f"Значение метрики {metric_name} составляет {value}, что превышает порог {threshold.__dict__}",
                severity=severity,
                component="metrics",
                timestamp=datetime.now(),
                details={
                    "metric_name": metric_name,
                    "current_value": value,
                    "threshold": threshold.__dict__,
                    **(context or {})
                },
                recovery_suggestions=[
                    "Проверить логи системы",
                    "Анализировать производительность",
                    "Проверить ресурсы сервера"
                ]
            )
            
            await self.send_alert(alert)

    async def send_alert(self, alert: Alert, channels: Optional[List[AlertChannel]] = None):
        """Отправляет алерт через указанные каналы"""
        
        # Проверяем подавление
        if self._is_suppressed(alert.id):
            return
        
        # Добавляем в историю
        self.alert_history.append(alert)
        self.active_alerts[alert.id] = alert
        
        # Логируем алерт
        alert_logger.critical(
            EventType.ALERT_TRIGGERED,
            f"Отправка алерта: {alert.title}",
            context=alert.to_dict()
        )
        
        # Определяем каналы для отправки
        if channels is None:
            channels = list(AlertChannel)
        
        # Отправляем через все каналы
        for channel in channels:
            if channel == AlertChannel.LOG:
                logger.critical(f"ALERT: {alert.title} - {alert.message}")
                continue
            
            alerters = self.alerters.get(channel, [])
            for alerter in alerters:
                try:
                    success = await alerter.send_alert(alert)
                    if success:
                        alert_logger.info(
                            EventType.ALERT_TRIGGERED,
                            f"Алерт отправлен через {channel.value}",
                            context={"alert_id": alert.id, "channel": channel.value}
                        )
                    else:
                        alert_logger.error(
                            EventType.ALERT_TRIGGERED,
                            f"Ошибка отправки алерта через {channel.value}",
                            context={"alert_id": alert.id, "channel": channel.value}
                        )
                except Exception as e:
                    alert_logger.error(
                        EventType.ALERT_TRIGGERED,
                        f"Исключение при отправке алерта через {channel.value}",
                        context={"alert_id": alert.id, "channel": channel.value},
                        error_details={"exception": str(e)}
                    )

    def _is_suppressed(self, alert_id: str) -> bool:
        """Проверяет, подавлен ли алерт"""
        if alert_id not in self.suppression_rules:
            return False
        
        if alert_id not in self.active_alerts:
            return False
        
        last_alert = self.active_alerts[alert_id]
        suppression_duration = self.suppression_rules[alert_id]
        
        return datetime.now() - last_alert.timestamp < suppression_duration

    def resolve_alert(self, alert_id: str):
        """Снимает активный алерт"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts.pop(alert_id)
            
            alert_logger.info(
                EventType.ALERT_TRIGGERED,
                f"Алерт снят: {alert.title}",
                context={"alert_id": alert_id}
            )

    def get_active_alerts(self) -> List[Alert]:
        """Возвращает список активных алертов"""
        return list(self.active_alerts.values())

    def get_alert_statistics(self) -> Dict[str, Any]:
        """Возвращает статистику алертов"""
        now = datetime.now()
        last_24h = now - timedelta(hours=24)
        
        recent_alerts = [alert for alert in self.alert_history if alert.timestamp >= last_24h]
        
        severity_counts = {}
        for severity in AlertSeverity:
            severity_counts[severity.value] = len([
                alert for alert in recent_alerts if alert.severity == severity
            ])
        
        return {
            "total_alerts_24h": len(recent_alerts),
            "active_alerts_count": len(self.active_alerts),
            "severity_breakdown": severity_counts,
            "most_common_components": self._get_most_common_components(recent_alerts),
            "alert_rate_per_hour": len(recent_alerts) / 24
        }

    def _get_most_common_components(self, alerts: List[Alert]) -> Dict[str, int]:
        """Возвращает компоненты с наибольшим количеством алертов"""
        component_counts = {}
        for alert in alerts:
            component_counts[alert.component] = component_counts.get(alert.component, 0) + 1
        
        return dict(sorted(component_counts.items(), key=lambda x: x[1], reverse=True)[:5])


# Глобальный экземпляр системы алертов
alerting_system = AlertingSystem()
