"""
Batch Video Processor для эффективной обработки множественных видео
Этап 3 плана исправления ошибок с ссылками на видео
"""

import asyncio
import time
import logging
from typing import List, Dict, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import json

logger = logging.getLogger(__name__)


@dataclass
class VideoProcessingTask:
    """Задача обработки видео"""
    video_id: str
    channel_id: str
    video_info: Dict[str, Any]
    subscribers: List[int]
    priority: int = 0  # Чем выше число, тем выше приоритет
    created_at: datetime = field(default_factory=datetime.now)
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        # Автоматическое определение приоритета на основе времени публикации
        if 'published_at' in self.video_info:
            try:
                published_time = datetime.fromisoformat(self.video_info['published_at'].replace('Z', '+00:00'))
                # Новые видео (последние 24 часа) получают высокий приоритет
                hours_since_published = (datetime.now() - published_time.replace(tzinfo=None)).total_seconds() / 3600
                if hours_since_published < 24:
                    self.priority = 100 - int(hours_since_published)  # 100-76 для видео последних 24 часов
                else:
                    days_since_published = hours_since_published / 24
                    self.priority = max(0, 75 - int(days_since_published * 10))  # Более быстрое снижение приоритета
            except Exception as e:
                logger.warning(f"Не удалось определить приоритет для видео {self.video_id}: {e}")
                self.priority = 50  # Средний приоритет по умолчанию


@dataclass
class BatchProcessingStats:
    """Статистика batch обработки"""
    total_videos: int = 0
    processed_videos: int = 0
    failed_videos: int = 0
    skipped_videos: int = 0
    api_requests_made: int = 0
    processing_time: float = 0.0
    start_time: datetime = field(default_factory=datetime.now)
    
    @property
    def success_rate(self) -> float:
        """Процент успешно обработанных видео"""
        if self.total_videos == 0:
            return 0.0
        return (self.processed_videos / self.total_videos) * 100
    
    @property
    def videos_per_second(self) -> float:
        """Скорость обработки видео в секунду"""
        if self.processing_time == 0:
            return 0.0
        return self.processed_videos / self.processing_time


class RateLimiter:
    """Rate limiter для YouTube API"""
    
    def __init__(self, requests_per_hour: int = 90, burst_limit: int = 10):
        self.requests_per_hour = requests_per_hour
        self.burst_limit = burst_limit
        self.request_times: List[float] = []
        self.burst_count = 0
        self.last_reset = time.time()
        
    async def acquire(self) -> bool:
        """Получить разрешение на API запрос"""
        current_time = time.time()
        
        # Сброс burst счетчика каждую минуту
        if current_time - self.last_reset > 60:
            self.burst_count = 0
            self.last_reset = current_time
        
        # Проверка burst лимита
        if self.burst_count >= self.burst_limit:
            wait_time = 60 - (current_time - self.last_reset)
            if wait_time > 0:
                logger.info(f"Rate limiter: ожидание {wait_time:.1f}с до сброса burst лимита")
                await asyncio.sleep(wait_time)
                self.burst_count = 0
                self.last_reset = time.time()
        
        # Удаляем старые запросы (старше часа)
        hour_ago = current_time - 3600
        self.request_times = [t for t in self.request_times if t > hour_ago]
        
        # Проверка часового лимита
        if len(self.request_times) >= self.requests_per_hour:
            oldest_request = min(self.request_times)
            wait_time = 3600 - (current_time - oldest_request)
            if wait_time > 0:
                logger.info(f"Rate limiter: ожидание {wait_time:.1f}с до сброса часового лимита")
                await asyncio.sleep(wait_time)
                # Обновляем время после ожидания
                current_time = time.time()
                hour_ago = current_time - 3600
                self.request_times = [t for t in self.request_times if t > hour_ago]
        
        # Записываем время запроса
        self.request_times.append(current_time)
        self.burst_count += 1
        return True


class BatchVideoProcessor:
    """
    Класс для эффективной batch-обработки видео
    
    Основные возможности:
    - Группировка API запросов для экономии квот
    - Параллельная обработка с контролем concurrency
    - Приоритизация видео (новые видео обрабатываются первыми)
    - Rate limiting для YouTube API
    - Batch операции с базой данных
    - Детальная статистика и мониторинг
    """
    
    def __init__(self, bot_instance, max_concurrent_videos: int = 5, max_concurrent_api_requests: int = 3):
        self.bot = bot_instance
        self.max_concurrent_videos = max_concurrent_videos
        self.max_concurrent_api_requests = max_concurrent_api_requests
        
        # Rate limiter для YouTube API
        self.rate_limiter = RateLimiter(requests_per_hour=90, burst_limit=10)
        
        # Семафоры для контроля concurrency
        self.video_semaphore = asyncio.Semaphore(max_concurrent_videos)
        self.api_semaphore = asyncio.Semaphore(max_concurrent_api_requests)
        
        # Очереди задач
        self.high_priority_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self.normal_priority_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        
        # Статистика
        self.stats = BatchProcessingStats()
        
        # Кэш для batch API запросов
        self.video_info_cache: Dict[str, Dict] = {}
        self.cache_ttl = 300  # 5 минут
        self.cache_timestamps: Dict[str, float] = {}
        
        logger.info(f"BatchVideoProcessor инициализирован: max_concurrent_videos={max_concurrent_videos}, max_concurrent_api_requests={max_concurrent_api_requests}")
    
    def _is_cache_valid(self, video_id: str) -> bool:
        """Проверяет валидность кэша для video_id"""
        if video_id not in self.cache_timestamps:
            return False
        return time.time() - self.cache_timestamps[video_id] < self.cache_ttl
    
    def _cache_video_info(self, video_id: str, info: Dict):
        """Кэширует информацию о видео"""
        self.video_info_cache[video_id] = info
        self.cache_timestamps[video_id] = time.time()
    
    def _get_cached_video_info(self, video_id: str) -> Optional[Dict]:
        """Получает информацию о видео из кэша"""
        if self._is_cache_valid(video_id):
            return self.video_info_cache[video_id]
        return None
    
    async def add_video_task(self, video_id: str, channel_id: str, video_info: Dict, subscribers: List[int], high_priority: bool = False):
        """
        Добавляет задачу обработки видео в очередь
        
        Args:
            video_id: ID видео
            channel_id: ID канала
            video_info: Информация о видео
            subscribers: Список подписчиков
            high_priority: Высокий приоритет (для новых видео)
        """
        task = VideoProcessingTask(
            video_id=video_id,
            channel_id=channel_id,
            video_info=video_info,
            subscribers=subscribers
        )
        
        # Выбираем очередь на основе приоритета
        queue = self.high_priority_queue if high_priority or task.priority > 75 else self.normal_priority_queue
        
        # Добавляем в очередь (отрицательный приоритет для правильной сортировки)
        await queue.put((-task.priority, task))
        
        logger.info(f"Видео {video_id} добавлено в {'высокоприоритетную' if high_priority or task.priority > 75 else 'обычную'} очередь (приоритет: {task.priority})")
    
    async def get_next_task(self) -> Optional[VideoProcessingTask]:
        """Получает следующую задачу из очереди с учетом приоритета"""
        # Сначала проверяем высокоприоритетную очередь
        if not self.high_priority_queue.empty():
            try:
                _, task = self.high_priority_queue.get_nowait()
                return task
            except asyncio.QueueEmpty:
                pass
        
        # Затем обычную очередь
        if not self.normal_priority_queue.empty():
            try:
                _, task = self.normal_priority_queue.get_nowait()
                return task
            except asyncio.QueueEmpty:
                pass
        
        return None
    
    def get_queue_stats(self) -> Dict[str, int]:
        """Получает статистику очередей"""
        return {
            'high_priority_queue_size': self.high_priority_queue.qsize(),
            'normal_priority_queue_size': self.normal_priority_queue.qsize(),
            'total_queue_size': self.high_priority_queue.qsize() + self.normal_priority_queue.qsize()
        }

    async def batch_get_video_info(self, video_ids: List[str]) -> Dict[str, Dict]:
        """
        Получает информацию о нескольких видео одним API запросом

        Args:
            video_ids: Список ID видео (максимум 50 за раз)

        Returns:
            Словарь {video_id: video_info}
        """
        if not video_ids:
            return {}

        # Ограничиваем количество видео в одном запросе (YouTube API лимит)
        batch_size = min(len(video_ids), 50)
        video_ids = video_ids[:batch_size]

        # Проверяем кэш
        cached_results = {}
        uncached_ids = []

        for video_id in video_ids:
            cached_info = self._get_cached_video_info(video_id)
            if cached_info:
                cached_results[video_id] = cached_info
            else:
                uncached_ids.append(video_id)

        logger.info(f"Batch video info: {len(cached_results)} из кэша, {len(uncached_ids)} требуют API запроса")

        # Если все данные в кэше, возвращаем их
        if not uncached_ids:
            return cached_results

        # Получаем разрешение от rate limiter
        await self.rate_limiter.acquire()

        # Делаем API запрос для некэшированных видео
        async with self.api_semaphore:
            try:
                self.stats.api_requests_made += 1

                # Используем существующий метод бота, но адаптируем для batch
                api_results = await self._batch_api_request_video_info(uncached_ids)

                # Кэшируем результаты
                for video_id, info in api_results.items():
                    self._cache_video_info(video_id, info)

                # Объединяем кэшированные и новые результаты
                all_results = {**cached_results, **api_results}

                logger.info(f"Batch video info завершен: получено {len(all_results)} видео")
                return all_results

            except Exception as e:
                logger.error(f"Ошибка batch получения информации о видео: {e}")
                return cached_results

    async def _batch_api_request_video_info(self, video_ids: List[str]) -> Dict[str, Dict]:
        """
        Внутренний метод для batch API запроса информации о видео
        """
        try:
            # Формируем параметры запроса
            video_ids_str = ",".join(video_ids)
            params = {
                "part": "snippet,contentDetails",
                "id": video_ids_str,
                "key": self.bot.youtube_api_key
            }

            # Используем существующий retry механизм бота через youtube_services
            data = await self.bot.youtube_services._retry_api_request(
                self.bot.youtube_services._make_youtube_api_request,
                "https://www.googleapis.com/youtube/v3/videos",
                params
            )

            if not data or 'items' not in data:
                logger.error(f"Некорректный ответ API для batch запроса видео: {data}")
                return {}

            results = {}
            for item in data['items']:
                try:
                    video_id = item['id']
                    snippet = item.get('snippet', {})
                    content_details = item.get('contentDetails', {})

                    # Формируем информацию о видео в том же формате, что и get_video_info
                    video_info = {
                        'title': snippet.get('title'),
                        'author': snippet.get('channelTitle'),
                        'description': snippet.get('description'),
                        'channel_id': snippet.get('channelId'),
                        'channel_url': f"https://www.youtube.com/channel/{snippet.get('channelId')}" if snippet.get('channelId') else None,
                        'published_at': snippet.get('publishedAt'),
                        'duration': content_details.get('duration'),
                        'thumbnail_url': snippet.get('thumbnails', {}).get('high', {}).get('url')
                    }

                    results[video_id] = video_info

                except Exception as e:
                    logger.warning(f"Ошибка обработки видео {item.get('id', 'unknown')} в batch запросе: {e}")
                    continue

            logger.info(f"Batch API запрос успешен: обработано {len(results)} из {len(video_ids)} видео")
            return results

        except Exception as e:
            logger.error(f"Критическая ошибка в batch API запросе: {e}")
            return {}

    async def process_video_batch(self, max_videos: int = 10) -> BatchProcessingStats:
        """
        Обрабатывает batch видео из очереди

        Args:
            max_videos: Максимальное количество видео для обработки

        Returns:
            Статистика обработки
        """
        batch_start_time = time.time()
        batch_stats = BatchProcessingStats()

        # Собираем задачи для обработки
        tasks_to_process = []
        for _ in range(max_videos):
            task = await self.get_next_task()
            if task is None:
                break
            tasks_to_process.append(task)

        if not tasks_to_process:
            logger.debug("Нет задач для batch обработки")
            return batch_stats

        batch_stats.total_videos = len(tasks_to_process)
        logger.info(f"Начинаем batch обработку {len(tasks_to_process)} видео")

        # Группируем видео по каналам для оптимизации
        videos_by_channel = defaultdict(list)
        for task in tasks_to_process:
            videos_by_channel[task.channel_id].append(task)

        # Обрабатываем видео параллельно с ограничением concurrency
        semaphore = asyncio.Semaphore(self.max_concurrent_videos)
        processing_tasks = []

        for channel_id, channel_tasks in videos_by_channel.items():
            processing_task = asyncio.create_task(
                self._process_channel_videos(channel_id, channel_tasks, semaphore, batch_stats)
            )
            processing_tasks.append(processing_task)

        # Ждем завершения всех задач
        await asyncio.gather(*processing_tasks, return_exceptions=True)

        # Финализируем статистику
        batch_stats.processing_time = time.time() - batch_start_time

        logger.info(f"Batch обработка завершена: {batch_stats.processed_videos}/{batch_stats.total_videos} успешно, "
                   f"время: {batch_stats.processing_time:.2f}с, скорость: {batch_stats.videos_per_second:.2f} видео/с")

        return batch_stats

    async def _process_channel_videos(self, channel_id: str, tasks: List[VideoProcessingTask],
                                    semaphore: asyncio.Semaphore, batch_stats: BatchProcessingStats):
        """
        Обрабатывает видео одного канала

        Args:
            channel_id: ID канала
            tasks: Список задач для обработки
            semaphore: Семафор для контроля concurrency
            batch_stats: Статистика для обновления
        """
        async with semaphore:
            try:
                logger.info(f"Обработка {len(tasks)} видео канала {channel_id}")

                # Получаем информацию о всех видео канала одним batch запросом
                video_ids = [task.video_id for task in tasks]
                video_infos = await self.batch_get_video_info(video_ids)

                # Обрабатываем каждое видео
                for task in tasks:
                    try:
                        video_info = video_infos.get(task.video_id)
                        if not video_info:
                            logger.warning(f"Не удалось получить информацию о видео {task.video_id}")
                            batch_stats.failed_videos += 1
                            continue

                        # Проверяем, не обработано ли уже видео
                        existing_summary = await self.bot.db.get_video_summary(task.video_id)
                        if existing_summary:
                            logger.info(f"Видео {task.video_id} уже обработано, пропускаем")
                            batch_stats.skipped_videos += 1
                            continue

                        # Обрабатываем видео
                        success = await self._process_single_video(task, video_info)
                        if success:
                            batch_stats.processed_videos += 1
                        else:
                            batch_stats.failed_videos += 1

                    except Exception as e:
                        logger.error(f"Ошибка обработки видео {task.video_id}: {e}")
                        batch_stats.failed_videos += 1

            except Exception as e:
                logger.error(f"Критическая ошибка обработки канала {channel_id}: {e}")
                # Отмечаем все задачи канала как неудачные
                batch_stats.failed_videos += len(tasks)

    async def _process_single_video(self, task: VideoProcessingTask, video_info: Dict) -> bool:
        """
        Обрабатывает одно видео

        Args:
            task: Задача обработки видео
            video_info: Информация о видео

        Returns:
            True если успешно обработано, False иначе
        """
        try:
            video_id = task.video_id
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # Валидируем video_id
            if not self.bot.validate_video_id(video_id):
                logger.error(f"Некорректный video_id: {video_id}")
                return False

            logger.info(f"Обработка видео {video_id}: {video_info.get('title', 'Без названия')}")

            # Пытаемся получить транскрипцию
            try:
                transcript_data = await self.bot.transcriber.get_transcript(video_url)
                transcript_text = " ".join([item.get('text', '') for item in transcript_data])

                # Получаем комментарии
                comments = await self.bot.get_video_comments(video_id, max_comments=30)

                # Создаём сводку через AI
                summary_result = await self.bot.create_summary_with_ai(
                    video_url=video_url,
                    transcript_text=transcript_text,
                    video_info=video_info,
                    comments=comments
                )

                if summary_result:
                    # Отправляем уведомления подписчикам
                    await self._send_notifications_to_subscribers(task, video_info, summary_result)

                    # Отмечаем видео как обработанное
                    await self.bot.db.mark_video_processed(
                        task.channel_id,
                        video_id,
                        video_info.get('title', ''),
                        video_info.get('published_at', '')
                    )

                    logger.info(f"✅ Видео {video_id} успешно обработано")
                    return True
                else:
                    logger.error(f"❌ Не удалось создать сводку для видео {video_id}")
                    return False

            except Exception as transcript_error:
                # Субтитры не найдены - добавляем в очередь ожидания
                logger.info(f"📝 Субтитры для видео {video_id} не найдены, добавляем в очередь ожидания: {transcript_error}")

                success = await self.bot.db.add_video_to_pending_queue(
                    video_id, task.channel_id, video_info, task.subscribers
                )

                if success:
                    logger.info(f"📝 Видео {video_id} добавлено в очередь ожидания субтитров")
                    return True
                else:
                    logger.error(f"❌ Не удалось добавить видео {video_id} в очередь ожидания")
                    return False

        except Exception as e:
            logger.error(f"Критическая ошибка обработки видео {task.video_id}: {e}")
            return False

    async def _send_notifications_to_subscribers(self, task: VideoProcessingTask, video_info: Dict, summary_result: Dict):
        """
        Отправляет уведомления подписчикам о новом видео

        Args:
            task: Задача обработки видео
            video_info: Информация о видео
            summary_result: Результат создания сводки
        """
        try:
            video_id = task.video_id
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # Формируем сообщение для подписчиков
            channel_name = video_info.get('author', 'Неизвестный канал')
            video_title = video_info.get('title', 'Без названия')
            short_summary = summary_result.get('short_summary', 'Сводка недоступна')
            telegraph_url = summary_result.get('telegraph_url', '')

            # Отправляем уведомления всем подписчикам
            notification_tasks = []
            for user_id in task.subscribers:
                notification_task = asyncio.create_task(
                    self.bot.send_new_video_notification(
                        user_id, channel_name, video_title, video_url,
                        short_summary, telegraph_url
                    )
                )
                notification_tasks.append(notification_task)

            # Ждем отправки всех уведомлений
            if notification_tasks:
                await asyncio.gather(*notification_tasks, return_exceptions=True)
                logger.info(f"Отправлено {len(notification_tasks)} уведомлений для видео {video_id}")

        except Exception as e:
            logger.error(f"Ошибка отправки уведомлений для видео {task.video_id}: {e}")

    async def batch_mark_videos_processed(self, video_data_list: List[Tuple[str, str, str, str]]) -> bool:
        """
        Batch отметка видео как обработанных в базе данных

        Args:
            video_data_list: Список кортежей (channel_id, video_id, video_title, published_at)

        Returns:
            True если успешно, False иначе
        """
        try:
            if not video_data_list:
                return True

            # Используем batch операцию для базы данных
            import aiosqlite

            async with aiosqlite.connect(self.bot.db.db_path) as db:
                await db.executemany("""
                    INSERT OR REPLACE INTO channel_videos
                    (channel_id, video_id, video_title, published_at, processed_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, video_data_list)

                await db.commit()
                logger.info(f"✅ Batch отметка: {len(video_data_list)} видео отмечены как обработанные")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка batch отметки видео как обработанных: {e}")
            return False

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        Получает детальную статистику обработки

        Returns:
            Словарь со статистикой
        """
        queue_stats = self.get_queue_stats()

        return {
            'batch_stats': {
                'total_videos': self.stats.total_videos,
                'processed_videos': self.stats.processed_videos,
                'failed_videos': self.stats.failed_videos,
                'skipped_videos': self.stats.skipped_videos,
                'success_rate': self.stats.success_rate,
                'videos_per_second': self.stats.videos_per_second,
                'api_requests_made': self.stats.api_requests_made,
                'processing_time': self.stats.processing_time
            },
            'queue_stats': queue_stats,
            'rate_limiter_stats': {
                'requests_in_last_hour': len(self.rate_limiter.request_times),
                'burst_count': self.rate_limiter.burst_count,
                'requests_per_hour_limit': self.rate_limiter.requests_per_hour,
                'burst_limit': self.rate_limiter.burst_limit
            },
            'cache_stats': {
                'cached_videos': len(self.video_info_cache),
                'cache_hit_rate': self._calculate_cache_hit_rate()
            },
            'concurrency_stats': {
                'max_concurrent_videos': self.max_concurrent_videos,
                'max_concurrent_api_requests': self.max_concurrent_api_requests,
                'current_video_semaphore_value': self.video_semaphore._value,
                'current_api_semaphore_value': self.api_semaphore._value
            }
        }

    def _calculate_cache_hit_rate(self) -> float:
        """Вычисляет процент попаданий в кэш"""
        # Простая эвристика на основе количества кэшированных элементов
        if not hasattr(self, '_cache_requests'):
            return 0.0

        if self._cache_requests == 0:
            return 0.0

        return (self._cache_hits / self._cache_requests) * 100

    async def clear_expired_cache(self):
        """Очищает устаревшие записи из кэша"""
        current_time = time.time()
        expired_keys = []

        for video_id, timestamp in self.cache_timestamps.items():
            if current_time - timestamp > self.cache_ttl:
                expired_keys.append(video_id)

        for key in expired_keys:
            del self.video_info_cache[key]
            del self.cache_timestamps[key]

        if expired_keys:
            logger.debug(f"Очищено {len(expired_keys)} устаревших записей из кэша")

    async def process_pending_videos_batch(self) -> BatchProcessingStats:
        """
        Обрабатывает видео из очереди ожидания (pending_videos) в batch режиме

        Returns:
            Статистика обработки
        """
        try:
            # Получаем видео готовые для повторной попытки
            pending_videos = await self.bot.db.get_videos_ready_for_retry()

            if not pending_videos:
                logger.debug("Нет видео в очереди ожидания для batch обработки")
                return BatchProcessingStats()

            logger.info(f"Начинаем batch обработку {len(pending_videos)} видео из очереди ожидания")

            # Добавляем видео в очередь batch процессора
            for video_data in pending_videos:
                await self.add_video_task(
                    video_id=video_data['video_id'],
                    channel_id=video_data['channel_id'],
                    video_info=video_data['video_info'],
                    subscribers=video_data['subscribers'],
                    high_priority=True  # Видео из очереди ожидания имеют высокий приоритет
                )

            # Обрабатываем batch
            return await self.process_video_batch(max_videos=len(pending_videos))

        except Exception as e:
            logger.error(f"Ошибка batch обработки видео из очереди ожидания: {e}")
            return BatchProcessingStats()

    async def optimize_processing_parameters(self):
        """
        Автоматически оптимизирует параметры обработки на основе статистики
        """
        try:
            stats = self.get_processing_stats()

            # Анализируем производительность
            success_rate = stats['batch_stats']['success_rate']
            videos_per_second = stats['batch_stats']['videos_per_second']
            api_requests = stats['rate_limiter_stats']['requests_in_last_hour']

            # Оптимизация concurrency на основе успешности
            if success_rate > 90 and videos_per_second < 0.5:
                # Высокая успешность, но низкая скорость - увеличиваем concurrency
                if self.max_concurrent_videos < 10:
                    self.max_concurrent_videos += 1
                    self.video_semaphore = asyncio.Semaphore(self.max_concurrent_videos)
                    logger.info(f"Увеличена concurrency видео до {self.max_concurrent_videos}")

            elif success_rate < 70:
                # Низкая успешность - уменьшаем concurrency
                if self.max_concurrent_videos > 2:
                    self.max_concurrent_videos -= 1
                    self.video_semaphore = asyncio.Semaphore(self.max_concurrent_videos)
                    logger.info(f"Уменьшена concurrency видео до {self.max_concurrent_videos}")

            # Оптимизация API запросов
            if api_requests > 80:  # Близко к лимиту
                # Уменьшаем burst лимит
                if self.rate_limiter.burst_limit > 5:
                    self.rate_limiter.burst_limit -= 1
                    logger.info(f"Уменьшен burst лимит до {self.rate_limiter.burst_limit}")

            elif api_requests < 30:  # Далеко от лимита
                # Увеличиваем burst лимит
                if self.rate_limiter.burst_limit < 15:
                    self.rate_limiter.burst_limit += 1
                    logger.info(f"Увеличен burst лимит до {self.rate_limiter.burst_limit}")

        except Exception as e:
            logger.error(f"Ошибка оптимизации параметров обработки: {e}")

    async def health_check(self) -> Dict[str, Any]:
        """
        Проверяет состояние batch процессора

        Returns:
            Словарь с результатами проверки
        """
        health_status = {
            'status': 'healthy',
            'issues': [],
            'stats': self.get_processing_stats(),
            'timestamp': datetime.now().isoformat()
        }

        try:
            # Проверяем очереди
            queue_stats = self.get_queue_stats()
            if queue_stats['total_queue_size'] > 100:
                health_status['issues'].append(f"Большая очередь: {queue_stats['total_queue_size']} видео")
                health_status['status'] = 'warning'

            # Проверяем rate limiter
            rate_stats = health_status['stats']['rate_limiter_stats']
            if rate_stats['requests_in_last_hour'] > 85:
                health_status['issues'].append(f"Близко к лимиту API: {rate_stats['requests_in_last_hour']}/90")
                health_status['status'] = 'warning'

            # Проверяем успешность обработки
            batch_stats = health_status['stats']['batch_stats']
            if batch_stats['success_rate'] < 70 and batch_stats['total_videos'] > 0:
                health_status['issues'].append(f"Низкая успешность: {batch_stats['success_rate']:.1f}%")
                health_status['status'] = 'error'

            # Проверяем кэш
            await self.clear_expired_cache()

        except Exception as e:
            health_status['status'] = 'error'
            health_status['issues'].append(f"Ошибка health check: {e}")

        return health_status
