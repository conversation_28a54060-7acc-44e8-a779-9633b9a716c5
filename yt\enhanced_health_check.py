"""
Enhanced Health Check System для YouTube бота
Этап 4 плана исправления ошибок с ссылками на видео
"""

import asyncio
import time
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging

from structured_logging import health_logger, EventType

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Статусы здоровья компонентов"""
    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Результат проверки здоровья компонента"""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    duration_ms: float
    recovery_actions: List[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "component": self.component,
            "status": self.status.value,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
            "duration_ms": self.duration_ms,
            "recovery_actions": self.recovery_actions or []
        }


class HealthChecker:
    """
    Базовый класс для проверки здоровья компонентов
    """

    def __init__(self, name: str, timeout_seconds: int = 30):
        self.name = name
        self.timeout_seconds = timeout_seconds
        self.last_check_time: Optional[datetime] = None
        self.last_result: Optional[HealthCheckResult] = None

    async def check(self) -> HealthCheckResult:
        """Выполняет проверку здоровья компонента"""
        start_time = time.time()
        
        try:
            # Выполняем проверку с таймаутом
            result = await asyncio.wait_for(
                self._perform_check(),
                timeout=self.timeout_seconds
            )
            
            duration_ms = (time.time() - start_time) * 1000
            
            health_result = HealthCheckResult(
                component=self.name,
                status=result.get("status", HealthStatus.UNKNOWN),
                message=result.get("message", ""),
                details=result.get("details", {}),
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                recovery_actions=result.get("recovery_actions", [])
            )
            
            self.last_check_time = datetime.now()
            self.last_result = health_result
            
            return health_result
            
        except asyncio.TimeoutError:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.ERROR,
                message=f"Проверка превысила таймаут {self.timeout_seconds}с",
                details={"timeout_seconds": self.timeout_seconds},
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                recovery_actions=["Увеличить таймаут", "Проверить производительность компонента"]
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component=self.name,
                status=HealthStatus.CRITICAL,
                message=f"Ошибка проверки: {str(e)}",
                details={"exception": str(e), "exception_type": type(e).__name__},
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                recovery_actions=["Перезапустить компонент", "Проверить логи"]
            )

    async def _perform_check(self) -> Dict[str, Any]:
        """Абстрактный метод для выполнения конкретной проверки"""
        raise NotImplementedError("Subclasses must implement _perform_check")


class YouTubeAPIHealthChecker(HealthChecker):
    """Проверка здоровья YouTube API"""

    def __init__(self, api_key: str, test_channel_id: str = "UCBJycsmduvYEL83R_U4JriQ"):
        super().__init__("YouTube API", timeout_seconds=15)
        self.api_key = api_key
        self.test_channel_id = test_channel_id

    async def _perform_check(self) -> Dict[str, Any]:
        """Проверяет доступность YouTube API"""
        
        # Тестовый запрос к YouTube API
        url = "https://www.googleapis.com/youtube/v3/channels"
        params = {
            "part": "snippet",
            "id": self.test_channel_id,
            "key": self.api_key
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                response_data = await response.json()
                
                if response.status == 200:
                    if "items" in response_data and len(response_data["items"]) > 0:
                        return {
                            "status": HealthStatus.HEALTHY,
                            "message": "YouTube API работает корректно",
                            "details": {
                                "status_code": response.status,
                                "test_channel_found": True,
                                "quota_remaining": response.headers.get("X-RateLimit-Remaining", "unknown")
                            }
                        }
                    else:
                        return {
                            "status": HealthStatus.WARNING,
                            "message": "YouTube API доступен, но тестовый канал не найден",
                            "details": {
                                "status_code": response.status,
                                "test_channel_found": False,
                                "response": response_data
                            },
                            "recovery_actions": ["Проверить test_channel_id", "Проверить права доступа API ключа"]
                        }
                
                elif response.status == 403:
                    error_details = response_data.get("error", {})
                    if "quotaExceeded" in str(error_details):
                        return {
                            "status": HealthStatus.CRITICAL,
                            "message": "Превышена квота YouTube API",
                            "details": {
                                "status_code": response.status,
                                "error": error_details
                            },
                            "recovery_actions": ["Ждать сброса квоты", "Использовать дополнительные API ключи"]
                        }
                    else:
                        return {
                            "status": HealthStatus.ERROR,
                            "message": "Доступ к YouTube API запрещен",
                            "details": {
                                "status_code": response.status,
                                "error": error_details
                            },
                            "recovery_actions": ["Проверить API ключ", "Проверить права доступа"]
                        }
                
                else:
                    return {
                        "status": HealthStatus.ERROR,
                        "message": f"YouTube API вернул ошибку: {response.status}",
                        "details": {
                            "status_code": response.status,
                            "response": response_data
                        },
                        "recovery_actions": ["Проверить статус YouTube API", "Повторить запрос позже"]
                    }


class DatabaseHealthChecker(HealthChecker):
    """Проверка здоровья базы данных"""

    def __init__(self, database):
        super().__init__("Database", timeout_seconds=10)
        self.database = database

    async def _perform_check(self) -> Dict[str, Any]:
        """Проверяет доступность и состояние базы данных"""
        
        try:
            # Простой тест подключения
            test_subscriptions = await self.database.get_all_active_subscriptions()
            subscription_count = len(test_subscriptions)
            
            # Тест записи/чтения
            test_start = time.time()
            await self.database.execute_query("SELECT 1")
            query_time_ms = (time.time() - test_start) * 1000
            
            status = HealthStatus.HEALTHY
            message = "База данных работает корректно"
            recovery_actions = []
            
            # Проверяем производительность
            if query_time_ms > 1000:  # Более 1 секунды
                status = HealthStatus.WARNING
                message = "База данных работает медленно"
                recovery_actions = ["Оптимизировать запросы", "Проверить индексы", "Очистить старые данные"]
            
            return {
                "status": status,
                "message": message,
                "details": {
                    "subscription_count": subscription_count,
                    "query_time_ms": round(query_time_ms, 2),
                    "connection_status": "connected"
                },
                "recovery_actions": recovery_actions
            }
            
        except Exception as e:
            return {
                "status": HealthStatus.CRITICAL,
                "message": f"Ошибка базы данных: {str(e)}",
                "details": {
                    "exception": str(e),
                    "connection_status": "failed"
                },
                "recovery_actions": ["Перезапустить базу данных", "Проверить файл базы данных", "Восстановить из бэкапа"]
            }


class TelegramBotHealthChecker(HealthChecker):
    """Проверка здоровья Telegram бота"""

    def __init__(self, bot_token: str):
        super().__init__("Telegram Bot", timeout_seconds=10)
        self.bot_token = bot_token

    async def _perform_check(self) -> Dict[str, Any]:
        """Проверяет доступность Telegram Bot API"""
        
        url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("ok"):
                    bot_info = response_data.get("result", {})
                    return {
                        "status": HealthStatus.HEALTHY,
                        "message": "Telegram Bot API работает корректно",
                        "details": {
                            "bot_username": bot_info.get("username"),
                            "bot_id": bot_info.get("id"),
                            "can_join_groups": bot_info.get("can_join_groups"),
                            "can_read_all_group_messages": bot_info.get("can_read_all_group_messages")
                        }
                    }
                else:
                    return {
                        "status": HealthStatus.ERROR,
                        "message": f"Ошибка Telegram Bot API: {response.status}",
                        "details": {
                            "status_code": response.status,
                            "response": response_data
                        },
                        "recovery_actions": ["Проверить токен бота", "Проверить статус Telegram API"]
                    }


class MonitoringHealthChecker(HealthChecker):
    """Проверка здоровья системы мониторинга"""

    def __init__(self, channel_monitor):
        super().__init__("Channel Monitoring", timeout_seconds=5)
        self.channel_monitor = channel_monitor

    async def _perform_check(self) -> Dict[str, Any]:
        """Проверяет состояние мониторинга каналов"""
        
        if not self.channel_monitor:
            return {
                "status": HealthStatus.CRITICAL,
                "message": "Мониторинг каналов не инициализирован",
                "details": {"initialized": False},
                "recovery_actions": ["Инициализировать мониторинг каналов"]
            }
        
        is_running = getattr(self.channel_monitor, 'is_running', False)
        
        if not is_running:
            return {
                "status": HealthStatus.ERROR,
                "message": "Мониторинг каналов остановлен",
                "details": {"is_running": False},
                "recovery_actions": ["Запустить мониторинг каналов", "Проверить ошибки в логах"]
            }
        
        # Проверяем метрики производительности
        performance_metrics = getattr(self.channel_monitor, 'performance_metrics', {})
        
        status = HealthStatus.HEALTHY
        message = "Мониторинг каналов работает корректно"
        recovery_actions = []
        
        # Анализируем метрики
        if performance_metrics:
            total_checks = performance_metrics.get('total_checks', 0)
            failed_checks = performance_metrics.get('failed_checks', 0)
            
            if total_checks > 0:
                failure_rate = (failed_checks / total_checks) * 100
                
                if failure_rate > 20:  # Более 20% неудач
                    status = HealthStatus.WARNING
                    message = f"Высокий процент неудач в мониторинге: {failure_rate:.1f}%"
                    recovery_actions = ["Проверить подключение к YouTube API", "Анализировать логи ошибок"]
                elif failure_rate > 50:  # Более 50% неудач
                    status = HealthStatus.ERROR
                    message = f"Критический процент неудач в мониторинге: {failure_rate:.1f}%"
                    recovery_actions = ["Перезапустить мониторинг", "Проверить API ключи", "Проверить сетевое подключение"]
        
        return {
            "status": status,
            "message": message,
            "details": {
                "is_running": is_running,
                "performance_metrics": performance_metrics
            },
            "recovery_actions": recovery_actions
        }


class EnhancedHealthCheckSystem:
    """
    Расширенная система проверки здоровья всех компонентов
    """

    def __init__(self):
        self.checkers: List[HealthChecker] = []
        self.check_interval_seconds = 60  # Проверка каждую минуту
        self.is_running = False
        self.monitoring_task: Optional[asyncio.Task] = None
        self.recovery_handlers: Dict[str, Callable] = {}

    def add_checker(self, checker: HealthChecker):
        """Добавляет проверку здоровья компонента"""
        self.checkers.append(checker)
        health_logger.info(
            EventType.HEALTH_CHECK,
            f"Добавлена проверка здоровья: {checker.name}",
            context={"component": checker.name, "timeout": checker.timeout_seconds}
        )

    def add_recovery_handler(self, component_name: str, handler: Callable):
        """Добавляет обработчик восстановления для компонента"""
        self.recovery_handlers[component_name] = handler
        health_logger.info(
            EventType.HEALTH_CHECK,
            f"Добавлен обработчик восстановления для {component_name}"
        )

    async def run_all_checks(self) -> Dict[str, HealthCheckResult]:
        """Выполняет все проверки здоровья"""
        results = {}
        
        health_logger.info(
            EventType.HEALTH_CHECK,
            f"Запуск проверки здоровья {len(self.checkers)} компонентов"
        )
        
        # Выполняем все проверки параллельно
        tasks = [checker.check() for checker in self.checkers]
        check_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(check_results):
            checker = self.checkers[i]
            
            if isinstance(result, Exception):
                # Обработка исключения при проверке
                results[checker.name] = HealthCheckResult(
                    component=checker.name,
                    status=HealthStatus.CRITICAL,
                    message=f"Исключение при проверке: {str(result)}",
                    details={"exception": str(result)},
                    timestamp=datetime.now(),
                    duration_ms=0,
                    recovery_actions=["Проверить конфигурацию", "Перезапустить компонент"]
                )
            else:
                results[checker.name] = result
            
            # Логируем результат
            health_logger.info(
                EventType.HEALTH_CHECK,
                f"Проверка {checker.name}: {results[checker.name].status.value}",
                context=results[checker.name].to_dict()
            )
            
            # Пытаемся восстановить компонент при ошибке
            if (results[checker.name].status in [HealthStatus.ERROR, HealthStatus.CRITICAL] and
                checker.name in self.recovery_handlers):
                await self._attempt_recovery(checker.name, results[checker.name])
        
        return results

    async def _attempt_recovery(self, component_name: str, health_result: HealthCheckResult):
        """Пытается восстановить компонент"""
        try:
            recovery_handler = self.recovery_handlers[component_name]
            
            health_logger.info(
                EventType.HEALTH_CHECK,
                f"Попытка восстановления компонента {component_name}",
                context={"component": component_name, "status": health_result.status.value}
            )
            
            await recovery_handler(health_result)
            
            health_logger.info(
                EventType.HEALTH_CHECK,
                f"Восстановление компонента {component_name} завершено"
            )
            
        except Exception as e:
            health_logger.error(
                EventType.HEALTH_CHECK,
                f"Ошибка восстановления компонента {component_name}",
                error_details={"exception": str(e)}
            )

    async def start_monitoring(self):
        """Запускает непрерывный мониторинг здоровья"""
        if self.is_running:
            return
        
        self.is_running = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        health_logger.info(
            EventType.HEALTH_CHECK,
            "Запущен непрерывный мониторинг здоровья системы",
            context={"check_interval_seconds": self.check_interval_seconds}
        )

    async def stop_monitoring(self):
        """Останавливает мониторинг здоровья"""
        self.is_running = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        health_logger.info(EventType.HEALTH_CHECK, "Мониторинг здоровья остановлен")

    async def _monitoring_loop(self):
        """Основной цикл мониторинга"""
        while self.is_running:
            try:
                await self.run_all_checks()
                await asyncio.sleep(self.check_interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as e:
                health_logger.error(
                    EventType.HEALTH_CHECK,
                    "Ошибка в цикле мониторинга здоровья",
                    error_details={"exception": str(e)}
                )
                await asyncio.sleep(30)  # Короткая пауза при ошибке

    def get_system_health_summary(self) -> Dict[str, Any]:
        """Возвращает сводку здоровья всей системы"""
        if not self.checkers:
            return {
                "overall_status": HealthStatus.UNKNOWN.value,
                "message": "Нет настроенных проверок",
                "components": {}
            }
        
        component_statuses = []
        components = {}
        
        for checker in self.checkers:
            if checker.last_result:
                components[checker.name] = checker.last_result.to_dict()
                component_statuses.append(checker.last_result.status)
            else:
                components[checker.name] = {
                    "status": HealthStatus.UNKNOWN.value,
                    "message": "Проверка еще не выполнялась"
                }
                component_statuses.append(HealthStatus.UNKNOWN)
        
        # Определяем общий статус системы
        if HealthStatus.CRITICAL in component_statuses:
            overall_status = HealthStatus.CRITICAL
            message = "Система в критическом состоянии"
        elif HealthStatus.ERROR in component_statuses:
            overall_status = HealthStatus.ERROR
            message = "Обнаружены ошибки в системе"
        elif HealthStatus.WARNING in component_statuses:
            overall_status = HealthStatus.WARNING
            message = "Система работает с предупреждениями"
        elif HealthStatus.HEALTHY in component_statuses:
            overall_status = HealthStatus.HEALTHY
            message = "Система работает нормально"
        else:
            overall_status = HealthStatus.UNKNOWN
            message = "Статус системы неизвестен"
        
        return {
            "overall_status": overall_status.value,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "components": components,
            "total_components": len(self.checkers)
        }
