"""
Monitoring Dashboard для YouTube бота
Этап 4 плана исправления ошибок с ссылками на видео
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
import aiohttp
from aiohttp import web
import logging

from structured_logging import monitoring_logger, EventType

logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """Точка метрики с временной меткой"""
    timestamp: datetime
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class AlertRule:
    """Правило для алертов"""
    name: str
    metric_name: str
    threshold: float
    comparison: str  # "gt", "lt", "eq"
    duration_minutes: int
    severity: str  # "warning", "error", "critical"
    enabled: bool = True


class MetricsCollector:
    """
    Сборщик метрик для мониторинга системы
    """

    def __init__(self, retention_hours: int = 24):
        self.retention_hours = retention_hours
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = defaultdict(float)
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        
        # Метрики производительности
        self.start_time = datetime.now()
        
        monitoring_logger.info(
            EventType.HEALTH_CHECK,
            "Инициализирован сборщик метрик",
            context={"retention_hours": retention_hours}
        )

    def increment_counter(self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None):
        """Увеличивает счетчик"""
        key = self._create_metric_key(name, labels)
        self.counters[key] += value
        
        # Добавляем точку в временной ряд
        point = MetricPoint(datetime.now(), self.counters[key], labels or {})
        self.metrics[name].append(point)
        
        self._cleanup_old_metrics()

    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Устанавливает значение gauge метрики"""
        key = self._create_metric_key(name, labels)
        self.gauges[key] = value
        
        # Добавляем точку в временной ряд
        point = MetricPoint(datetime.now(), value, labels or {})
        self.metrics[name].append(point)
        
        self._cleanup_old_metrics()

    def observe_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Добавляет наблюдение в гистограмму"""
        key = self._create_metric_key(name, labels)
        self.histograms[key].append(value)
        
        # Ограничиваем размер гистограммы
        if len(self.histograms[key]) > 1000:
            self.histograms[key] = self.histograms[key][-1000:]
        
        # Добавляем точку в временной ряд
        point = MetricPoint(datetime.now(), value, labels or {})
        self.metrics[name].append(point)
        
        self._cleanup_old_metrics()

    def get_metric_summary(self, name: str, hours: int = 1) -> Dict[str, Any]:
        """Получает сводку по метрике за указанный период"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        if name not in self.metrics:
            return {"error": f"Метрика {name} не найдена"}
        
        # Фильтруем точки по времени
        recent_points = [
            point for point in self.metrics[name]
            if point.timestamp >= cutoff_time
        ]
        
        if not recent_points:
            return {"error": f"Нет данных за последние {hours} часов"}
        
        values = [point.value for point in recent_points]
        
        return {
            "name": name,
            "period_hours": hours,
            "count": len(values),
            "min": min(values),
            "max": max(values),
            "avg": sum(values) / len(values),
            "latest": values[-1] if values else 0,
            "first_timestamp": recent_points[0].timestamp.isoformat(),
            "last_timestamp": recent_points[-1].timestamp.isoformat()
        }

    def get_all_metrics_summary(self) -> Dict[str, Any]:
        """Получает сводку по всем метрикам"""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "uptime_hours": (datetime.now() - self.start_time).total_seconds() / 3600,
            "metrics": {}
        }
        
        for metric_name in self.metrics.keys():
            summary["metrics"][metric_name] = self.get_metric_summary(metric_name, hours=1)
        
        return summary

    def _create_metric_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Создает ключ метрики с учетом лейблов"""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"

    def _cleanup_old_metrics(self):
        """Удаляет старые метрики"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        for metric_name, points in self.metrics.items():
            # Удаляем старые точки
            while points and points[0].timestamp < cutoff_time:
                points.popleft()


class AlertManager:
    """
    Менеджер алертов для мониторинга критических состояний
    """

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.alert_rules: List[AlertRule] = []
        self.active_alerts: Dict[str, datetime] = {}
        self.alert_history: deque = deque(maxlen=1000)
        
        # Настройка алертов по умолчанию
        self._setup_default_alerts()

    def _setup_default_alerts(self):
        """Настраивает алерты по умолчанию"""
        default_rules = [
            AlertRule("high_error_rate", "error_rate", 10.0, "gt", 5, "warning"),
            AlertRule("critical_error_rate", "error_rate", 25.0, "gt", 2, "critical"),
            AlertRule("low_success_rate", "success_rate", 70.0, "lt", 10, "warning"),
            AlertRule("api_quota_exhausted", "api_quota_usage", 90.0, "gt", 1, "error"),
            AlertRule("high_processing_time", "avg_processing_time", 60000.0, "gt", 5, "warning"),
            AlertRule("queue_overflow", "queue_size", 100.0, "gt", 3, "error"),
        ]
        
        self.alert_rules.extend(default_rules)
        
        monitoring_logger.info(
            EventType.HEALTH_CHECK,
            f"Настроено {len(default_rules)} правил алертов по умолчанию"
        )

    async def check_alerts(self):
        """Проверяет все правила алертов"""
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
                
            try:
                await self._check_single_alert(rule)
            except Exception as e:
                monitoring_logger.error(
                    EventType.ALERT_TRIGGERED,
                    f"Ошибка проверки алерта {rule.name}",
                    error_details={"error": str(e)}
                )

    async def _check_single_alert(self, rule: AlertRule):
        """Проверяет одно правило алерта"""
        metric_summary = self.metrics_collector.get_metric_summary(
            rule.metric_name, 
            hours=rule.duration_minutes / 60
        )
        
        if "error" in metric_summary:
            return  # Метрика не найдена
        
        current_value = metric_summary.get("avg", 0)
        
        # Проверяем условие
        alert_triggered = False
        if rule.comparison == "gt" and current_value > rule.threshold:
            alert_triggered = True
        elif rule.comparison == "lt" and current_value < rule.threshold:
            alert_triggered = True
        elif rule.comparison == "eq" and abs(current_value - rule.threshold) < 0.01:
            alert_triggered = True
        
        if alert_triggered:
            await self._trigger_alert(rule, current_value, metric_summary)
        else:
            # Снимаем алерт если он был активен
            if rule.name in self.active_alerts:
                await self._resolve_alert(rule, current_value)

    async def _trigger_alert(self, rule: AlertRule, current_value: float, metric_summary: Dict[str, Any]):
        """Срабатывает алерт"""
        alert_key = rule.name
        
        # Проверяем, не активен ли уже этот алерт
        if alert_key in self.active_alerts:
            return  # Алерт уже активен
        
        self.active_alerts[alert_key] = datetime.now()
        
        alert_data = {
            "rule_name": rule.name,
            "metric_name": rule.metric_name,
            "current_value": current_value,
            "threshold": rule.threshold,
            "severity": rule.severity,
            "metric_summary": metric_summary
        }
        
        self.alert_history.append({
            "timestamp": datetime.now().isoformat(),
            "action": "triggered",
            **alert_data
        })
        
        monitoring_logger.critical(
            EventType.ALERT_TRIGGERED,
            f"🚨 АЛЕРТ: {rule.name}",
            context=alert_data
        )

    async def _resolve_alert(self, rule: AlertRule, current_value: float):
        """Снимает алерт"""
        alert_key = rule.name
        
        if alert_key not in self.active_alerts:
            return
        
        start_time = self.active_alerts.pop(alert_key)
        duration = datetime.now() - start_time
        
        alert_data = {
            "rule_name": rule.name,
            "metric_name": rule.metric_name,
            "current_value": current_value,
            "duration_minutes": duration.total_seconds() / 60
        }
        
        self.alert_history.append({
            "timestamp": datetime.now().isoformat(),
            "action": "resolved",
            **alert_data
        })
        
        monitoring_logger.info(
            EventType.ALERT_TRIGGERED,
            f"✅ Алерт снят: {rule.name}",
            context=alert_data
        )

    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Возвращает список активных алертов"""
        active = []
        for alert_name, start_time in self.active_alerts.items():
            duration = datetime.now() - start_time
            active.append({
                "name": alert_name,
                "start_time": start_time.isoformat(),
                "duration_minutes": duration.total_seconds() / 60
            })
        return active


class MonitoringDashboard:
    """
    Веб-дашборд для мониторинга системы
    """

    def __init__(self, metrics_collector: MetricsCollector, alert_manager: AlertManager, port: int = 8080):
        self.metrics_collector = metrics_collector
        self.alert_manager = alert_manager
        self.port = port
        self.app = web.Application()
        self._setup_routes()

    def _setup_routes(self):
        """Настраивает маршруты веб-приложения"""
        self.app.router.add_get('/', self._dashboard_handler)
        self.app.router.add_get('/api/metrics', self._metrics_api_handler)
        self.app.router.add_get('/api/alerts', self._alerts_api_handler)
        self.app.router.add_get('/api/health', self._health_api_handler)

    async def _dashboard_handler(self, request):
        """Обработчик главной страницы дашборда"""
        html = self._generate_dashboard_html()
        return web.Response(text=html, content_type='text/html')

    async def _metrics_api_handler(self, request):
        """API для получения метрик"""
        summary = self.metrics_collector.get_all_metrics_summary()
        return web.json_response(summary)

    async def _alerts_api_handler(self, request):
        """API для получения алертов"""
        active_alerts = self.alert_manager.get_active_alerts()
        return web.json_response({
            "active_alerts": active_alerts,
            "alert_history": list(self.alert_manager.alert_history)[-50:]  # Последние 50
        })

    async def _health_api_handler(self, request):
        """API для проверки здоровья системы"""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "uptime_hours": (datetime.now() - self.metrics_collector.start_time).total_seconds() / 3600,
            "active_alerts_count": len(self.alert_manager.active_alerts),
            "metrics_count": len(self.metrics_collector.metrics)
        }
        
        # Проверяем наличие критических алертов
        critical_alerts = [
            alert for alert in self.alert_manager.get_active_alerts()
            if any(rule.severity == "critical" and rule.name == alert["name"] 
                  for rule in self.alert_manager.alert_rules)
        ]
        
        if critical_alerts:
            health_status["status"] = "critical"
        elif self.alert_manager.active_alerts:
            health_status["status"] = "warning"
        
        return web.json_response(health_status)

    def _generate_dashboard_html(self) -> str:
        """Генерирует HTML для дашборда"""
        return """
<!DOCTYPE html>
<html>
<head>
    <title>YouTube Bot Monitoring Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #e3f2fd; border-radius: 4px; }
        .alert { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .alert.critical { background: #ffebee; border-left: 4px solid #f44336; }
        .alert.warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .alert.error { background: #fce4ec; border-left: 4px solid #e91e63; }
        .status.healthy { color: #4caf50; }
        .status.warning { color: #ff9800; }
        .status.critical { color: #f44336; }
        h1, h2 { color: #333; }
        .refresh-btn { background: #2196f3; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 YouTube Bot Monitoring Dashboard</h1>
        
        <div class="card">
            <h2>Статус системы</h2>
            <div id="system-status">Загрузка...</div>
            <button class="refresh-btn" onclick="refreshData()">Обновить</button>
        </div>
        
        <div class="card">
            <h2>Активные алерты</h2>
            <div id="active-alerts">Загрузка...</div>
        </div>
        
        <div class="card">
            <h2>Метрики системы</h2>
            <div id="metrics">Загрузка...</div>
        </div>
    </div>

    <script>
        async function fetchData(url) {
            try {
                const response = await fetch(url);
                return await response.json();
            } catch (error) {
                console.error('Ошибка загрузки данных:', error);
                return null;
            }
        }

        async function refreshData() {
            // Загружаем статус системы
            const health = await fetchData('/api/health');
            if (health) {
                document.getElementById('system-status').innerHTML = `
                    <div class="status ${health.status}">
                        <strong>Статус:</strong> ${health.status.toUpperCase()}<br>
                        <strong>Время работы:</strong> ${health.uptime_hours.toFixed(1)} часов<br>
                        <strong>Активных алертов:</strong> ${health.active_alerts_count}<br>
                        <strong>Метрик:</strong> ${health.metrics_count}
                    </div>
                `;
            }

            // Загружаем алерты
            const alerts = await fetchData('/api/alerts');
            if (alerts) {
                const alertsHtml = alerts.active_alerts.length > 0 
                    ? alerts.active_alerts.map(alert => `
                        <div class="alert warning">
                            <strong>${alert.name}</strong><br>
                            Активен: ${alert.duration_minutes.toFixed(1)} минут
                        </div>
                    `).join('')
                    : '<div style="color: #4caf50;">✅ Активных алертов нет</div>';
                
                document.getElementById('active-alerts').innerHTML = alertsHtml;
            }

            // Загружаем метрики
            const metrics = await fetchData('/api/metrics');
            if (metrics && metrics.metrics) {
                const metricsHtml = Object.entries(metrics.metrics).map(([name, data]) => {
                    if (data.error) return '';
                    return `
                        <div class="metric">
                            <strong>${name}</strong><br>
                            Текущее: ${data.latest?.toFixed(2) || 'N/A'}<br>
                            Среднее: ${data.avg?.toFixed(2) || 'N/A'}<br>
                            Точек: ${data.count || 0}
                        </div>
                    `;
                }).join('');
                
                document.getElementById('metrics').innerHTML = metricsHtml || 'Нет данных';
            }
        }

        // Автоматическое обновление каждые 30 секунд
        setInterval(refreshData, 30000);
        
        // Первоначальная загрузка
        refreshData();
    </script>
</body>
</html>
        """

    async def start(self):
        """Запускает веб-сервер дашборда"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        
        monitoring_logger.info(
            EventType.HEALTH_CHECK,
            f"Monitoring dashboard запущен на порту {self.port}",
            context={"port": self.port, "url": f"http://localhost:{self.port}"}
        )


# Глобальные экземпляры
metrics_collector = MetricsCollector()
alert_manager = AlertManager(metrics_collector)
dashboard = MonitoringDashboard(metrics_collector, alert_manager)
