"""
Request Tracing System для YouTube бота
Этап 4 плана исправления ошибок с ссылками на видео
"""

import asyncio
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from collections import defaultdict, deque
import json
import logging

from structured_logging import get_logger, EventType, set_correlation_id, get_correlation_id

logger = logging.getLogger(__name__)
trace_logger = get_logger("request_tracing")


@dataclass
class TraceSpan:
    """Отрезок трассировки (span)"""
    span_id: str
    trace_id: str
    parent_span_id: Optional[str]
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_ms: Optional[float] = None
    status: str = "started"  # started, success, error
    tags: Dict[str, Any] = field(default_factory=dict)
    logs: List[Dict[str, Any]] = field(default_factory=list)
    error_details: Optional[Dict[str, Any]] = None

    def finish(self, status: str = "success", error_details: Optional[Dict[str, Any]] = None):
        """Завершает span"""
        self.end_time = datetime.now()
        self.duration_ms = (self.end_time - self.start_time).total_seconds() * 1000
        self.status = status
        if error_details:
            self.error_details = error_details

    def add_tag(self, key: str, value: Any):
        """Добавляет тег к span"""
        self.tags[key] = value

    def add_log(self, message: str, level: str = "info", **kwargs):
        """Добавляет лог к span"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "message": message,
            **kwargs
        }
        self.logs.append(log_entry)

    def to_dict(self) -> Dict[str, Any]:
        """Преобразует span в словарь"""
        return {
            "span_id": self.span_id,
            "trace_id": self.trace_id,
            "parent_span_id": self.parent_span_id,
            "operation_name": self.operation_name,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_ms": self.duration_ms,
            "status": self.status,
            "tags": self.tags,
            "logs": self.logs,
            "error_details": self.error_details
        }


@dataclass
class Trace:
    """Полная трассировка запроса"""
    trace_id: str
    root_span_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    spans: Dict[str, TraceSpan] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def add_span(self, span: TraceSpan):
        """Добавляет span к трассировке"""
        self.spans[span.span_id] = span

    def get_duration_ms(self) -> Optional[float]:
        """Возвращает общую длительность трассировки"""
        if not self.end_time:
            return None
        return (self.end_time - self.start_time).total_seconds() * 1000

    def get_span_tree(self) -> Dict[str, Any]:
        """Возвращает дерево spans"""
        root_span = self.spans.get(self.root_span_id)
        if not root_span:
            return {}

        def build_tree(span: TraceSpan) -> Dict[str, Any]:
            children = [
                build_tree(child_span)
                for child_span in self.spans.values()
                if child_span.parent_span_id == span.span_id
            ]
            
            return {
                "span": span.to_dict(),
                "children": children
            }

        return build_tree(root_span)

    def to_dict(self) -> Dict[str, Any]:
        """Преобразует трассировку в словарь"""
        return {
            "trace_id": self.trace_id,
            "root_span_id": self.root_span_id,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_ms": self.get_duration_ms(),
            "spans_count": len(self.spans),
            "metadata": self.metadata,
            "span_tree": self.get_span_tree()
        }


class RequestTracer:
    """
    Система трассировки запросов
    """

    def __init__(self, retention_hours: int = 24):
        self.retention_hours = retention_hours
        self.active_traces: Dict[str, Trace] = {}
        self.completed_traces: deque = deque(maxlen=10000)
        self.current_span_stack: Dict[str, List[str]] = defaultdict(list)  # trace_id -> stack of span_ids
        
        # Статистика
        self.stats = {
            "total_traces": 0,
            "active_traces_count": 0,
            "completed_traces_count": 0,
            "avg_trace_duration_ms": 0,
            "error_rate": 0
        }

    def start_trace(
        self,
        operation_name: str,
        trace_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Начинает новую трассировку"""
        
        if not trace_id:
            trace_id = str(uuid.uuid4())
        
        # Устанавливаем correlation ID
        set_correlation_id(trace_id)
        
        # Создаем root span
        root_span_id = str(uuid.uuid4())
        root_span = TraceSpan(
            span_id=root_span_id,
            trace_id=trace_id,
            parent_span_id=None,
            operation_name=operation_name,
            start_time=datetime.now()
        )
        
        # Создаем трассировку
        trace = Trace(
            trace_id=trace_id,
            root_span_id=root_span_id,
            start_time=datetime.now(),
            metadata=metadata or {}
        )
        
        trace.add_span(root_span)
        self.active_traces[trace_id] = trace
        self.current_span_stack[trace_id].append(root_span_id)
        
        self.stats["total_traces"] += 1
        self.stats["active_traces_count"] = len(self.active_traces)
        
        trace_logger.info(
            EventType.VIDEO_PROCESSING_START,
            f"Начата трассировка: {operation_name}",
            context={
                "trace_id": trace_id,
                "root_span_id": root_span_id,
                "operation_name": operation_name
            },
            correlation_id=trace_id
        )
        
        return trace_id

    def start_span(
        self,
        operation_name: str,
        trace_id: Optional[str] = None,
        parent_span_id: Optional[str] = None,
        tags: Optional[Dict[str, Any]] = None
    ) -> str:
        """Начинает новый span"""
        
        if not trace_id:
            trace_id = get_correlation_id()
        
        if trace_id not in self.active_traces:
            # Если трассировка не найдена, создаем новую
            self.start_trace(operation_name, trace_id)
        
        trace = self.active_traces[trace_id]
        
        # Определяем родительский span
        if not parent_span_id and self.current_span_stack[trace_id]:
            parent_span_id = self.current_span_stack[trace_id][-1]
        
        span_id = str(uuid.uuid4())
        span = TraceSpan(
            span_id=span_id,
            trace_id=trace_id,
            parent_span_id=parent_span_id,
            operation_name=operation_name,
            start_time=datetime.now(),
            tags=tags or {}
        )
        
        trace.add_span(span)
        self.current_span_stack[trace_id].append(span_id)
        
        trace_logger.debug(
            EventType.API_REQUEST_START,
            f"Начат span: {operation_name}",
            context={
                "trace_id": trace_id,
                "span_id": span_id,
                "parent_span_id": parent_span_id,
                "operation_name": operation_name
            },
            correlation_id=trace_id
        )
        
        return span_id

    def finish_span(
        self,
        span_id: str,
        status: str = "success",
        tags: Optional[Dict[str, Any]] = None,
        error_details: Optional[Dict[str, Any]] = None
    ):
        """Завершает span"""
        
        # Находим span
        span = None
        trace_id = None
        
        for tid, trace in self.active_traces.items():
            if span_id in trace.spans:
                span = trace.spans[span_id]
                trace_id = tid
                break
        
        if not span:
            logger.warning(f"Span {span_id} не найден")
            return
        
        # Завершаем span
        span.finish(status, error_details)
        
        # Добавляем теги
        if tags:
            span.tags.update(tags)
        
        # Удаляем из стека
        if trace_id in self.current_span_stack and span_id in self.current_span_stack[trace_id]:
            self.current_span_stack[trace_id].remove(span_id)
        
        trace_logger.debug(
            EventType.API_REQUEST_END,
            f"Завершен span: {span.operation_name}",
            context={
                "trace_id": trace_id,
                "span_id": span_id,
                "status": status,
                "duration_ms": span.duration_ms
            },
            correlation_id=trace_id,
            duration_ms=span.duration_ms
        )

    def finish_trace(self, trace_id: str, status: str = "success"):
        """Завершает трассировку"""
        
        if trace_id not in self.active_traces:
            logger.warning(f"Трассировка {trace_id} не найдена")
            return
        
        trace = self.active_traces[trace_id]
        trace.end_time = datetime.now()
        
        # Завершаем все незавершенные spans
        for span in trace.spans.values():
            if span.end_time is None:
                span.finish(status)
        
        # Перемещаем в завершенные
        self.completed_traces.append(trace)
        del self.active_traces[trace_id]
        
        # Очищаем стек spans
        if trace_id in self.current_span_stack:
            del self.current_span_stack[trace_id]
        
        # Обновляем статистику
        self.stats["active_traces_count"] = len(self.active_traces)
        self.stats["completed_traces_count"] = len(self.completed_traces)
        
        # Вычисляем среднюю длительность
        if self.completed_traces:
            total_duration = sum(
                t.get_duration_ms() or 0 
                for t in list(self.completed_traces)[-100:]  # Последние 100 трассировок
            )
            self.stats["avg_trace_duration_ms"] = total_duration / min(len(self.completed_traces), 100)
        
        # Вычисляем процент ошибок
        recent_traces = list(self.completed_traces)[-100:]
        error_traces = sum(
            1 for t in recent_traces 
            if any(span.status == "error" for span in t.spans.values())
        )
        self.stats["error_rate"] = (error_traces / len(recent_traces) * 100) if recent_traces else 0
        
        trace_logger.info(
            EventType.VIDEO_PROCESSING_END,
            f"Завершена трассировка: {trace.spans[trace.root_span_id].operation_name}",
            context={
                "trace_id": trace_id,
                "duration_ms": trace.get_duration_ms(),
                "spans_count": len(trace.spans),
                "status": status
            },
            correlation_id=trace_id,
            duration_ms=trace.get_duration_ms()
        )

    def add_span_tag(self, span_id: str, key: str, value: Any):
        """Добавляет тег к span"""
        for trace in self.active_traces.values():
            if span_id in trace.spans:
                trace.spans[span_id].add_tag(key, value)
                return

    def add_span_log(self, span_id: str, message: str, level: str = "info", **kwargs):
        """Добавляет лог к span"""
        for trace in self.active_traces.values():
            if span_id in trace.spans:
                trace.spans[span_id].add_log(message, level, **kwargs)
                return

    @asynccontextmanager
    async def trace_operation(
        self,
        operation_name: str,
        trace_id: Optional[str] = None,
        tags: Optional[Dict[str, Any]] = None
    ):
        """Контекстный менеджер для трассировки операции"""
        
        span_id = self.start_span(operation_name, trace_id, tags=tags)
        
        try:
            yield span_id
            self.finish_span(span_id, "success")
        except Exception as e:
            error_details = {
                "exception_type": type(e).__name__,
                "exception_message": str(e)
            }
            self.finish_span(span_id, "error", error_details=error_details)
            raise

    @asynccontextmanager
    async def trace_request(
        self,
        operation_name: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Контекстный менеджер для трассировки полного запроса"""
        
        trace_id = self.start_trace(operation_name, metadata=metadata)
        
        try:
            yield trace_id
            self.finish_trace(trace_id, "success")
        except Exception as e:
            self.finish_trace(trace_id, "error")
            raise

    def get_trace(self, trace_id: str) -> Optional[Trace]:
        """Получает трассировку по ID"""
        
        # Ищем в активных
        if trace_id in self.active_traces:
            return self.active_traces[trace_id]
        
        # Ищем в завершенных
        for trace in self.completed_traces:
            if trace.trace_id == trace_id:
                return trace
        
        return None

    def search_traces(
        self,
        operation_name: Optional[str] = None,
        status: Optional[str] = None,
        min_duration_ms: Optional[float] = None,
        max_duration_ms: Optional[float] = None,
        hours_back: int = 1,
        limit: int = 100
    ) -> List[Trace]:
        """Поиск трассировок по критериям"""
        
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        results = []
        
        # Поиск в завершенных трассировках
        for trace in reversed(list(self.completed_traces)):
            if len(results) >= limit:
                break
            
            if trace.start_time < cutoff_time:
                continue
            
            # Фильтры
            if operation_name:
                root_span = trace.spans.get(trace.root_span_id)
                if not root_span or operation_name not in root_span.operation_name:
                    continue
            
            if status:
                has_status = any(
                    span.status == status for span in trace.spans.values()
                )
                if not has_status:
                    continue
            
            duration = trace.get_duration_ms()
            if duration:
                if min_duration_ms and duration < min_duration_ms:
                    continue
                if max_duration_ms and duration > max_duration_ms:
                    continue
            
            results.append(trace)
        
        return results

    def get_statistics(self) -> Dict[str, Any]:
        """Возвращает статистику трассировки"""
        
        # Очищаем старые трассировки
        self._cleanup_old_traces()
        
        return {
            **self.stats,
            "retention_hours": self.retention_hours,
            "memory_usage": {
                "active_traces": len(self.active_traces),
                "completed_traces": len(self.completed_traces),
                "total_spans": sum(len(t.spans) for t in self.active_traces.values()) +
                              sum(len(t.spans) for t in self.completed_traces)
            }
        }

    def _cleanup_old_traces(self):
        """Очищает старые трассировки"""
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        # Удаляем старые завершенные трассировки
        while self.completed_traces and self.completed_traces[0].start_time < cutoff_time:
            self.completed_traces.popleft()
        
        # Завершаем старые активные трассировки
        old_active_traces = [
            trace_id for trace_id, trace in self.active_traces.items()
            if trace.start_time < cutoff_time
        ]
        
        for trace_id in old_active_traces:
            logger.warning(f"Принудительное завершение старой трассировки {trace_id}")
            self.finish_trace(trace_id, "timeout")


# Глобальный экземпляр трассировщика
request_tracer = RequestTracer()


# Декораторы для удобства использования
def trace_function(operation_name: Optional[str] = None):
    """Декоратор для трассировки функций"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            async with request_tracer.trace_operation(op_name) as span_id:
                request_tracer.add_span_tag(span_id, "function", func.__name__)
                request_tracer.add_span_tag(span_id, "module", func.__module__)
                return await func(*args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            # Для синхронных функций создаем простую трассировку
            span_id = request_tracer.start_span(op_name)
            request_tracer.add_span_tag(span_id, "function", func.__name__)
            request_tracer.add_span_tag(span_id, "module", func.__module__)
            
            try:
                result = func(*args, **kwargs)
                request_tracer.finish_span(span_id, "success")
                return result
            except Exception as e:
                error_details = {
                    "exception_type": type(e).__name__,
                    "exception_message": str(e)
                }
                request_tracer.finish_span(span_id, "error", error_details=error_details)
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
