# Основные зависимости для YouTube Summary Bot

# Telegram Bot API
python-telegram-bot>=20.0

# Асинхронные HTTP запросы
aiohttp>=3.8.0

# Асинхронная работа с SQLite
aiosqlite>=0.19.0

# YouTube транскрипция
youtube-transcript-api>=0.6.0

# Google Generative AI (Gemini)
google-generativeai>=0.3.0
google-genai>=0.5.0

# Telegraph для создания статей
telegraph>=2.2.0

# Дополнительные утилиты
requests>=2.28.0
urllib3>=1.26.0

# Работа с JSON и типами
typing-extensions>=4.0.0

# Логирование и мониторинг
structlog>=22.0.0

# Работа с датами и временем
python-dateutil>=2.8.0

# Работа с прокси (для транскрипции)
requests[socks]>=2.28.0

# Дополнительные зависимости для стабильной работы
certifi>=2022.0.0
charset-normalizer>=3.0.0
idna>=3.0.0

# Для работы с видео файлами
python-magic>=0.4.27

# Для обработки HTML
beautifulsoup4>=4.11.0
lxml>=4.9.0

# Для работы с регулярными выражениями и текстом
regex>=2022.0.0

# Для криптографии и безопасности
cryptography>=3.4.0

# Для работы с конфигурацией
python-dotenv>=0.19.0

# Для мониторинга производительности
psutil>=5.9.0

# Для работы с временными зонами
pytz>=2022.0

# Для валидации данных
pydantic>=1.10.0

# Для работы с асинхронными задачами
asyncio-throttle>=1.0.0

# Для работы с очередями
aiodns>=3.0.0

# Для улучшенной работы с HTTP
httpx>=0.24.0

# Для работы с мультимедиа
Pillow>=9.0.0

# Для работы с архивами
py7zr>=0.20.0

# Для улучшенного логирования
colorlog>=6.7.0

# Для работы с метриками
prometheus-client>=0.15.0

# Для работы с конкурентностью
concurrent-futures>=3.1.1

# Для работы с сетевыми запросами
dnspython>=2.2.0

# Для работы с кодировками
chardet>=5.0.0

# Для работы с URL
yarl>=1.8.0

# Для работы с многопоточностью
multidict>=6.0.0

# Для работы с сигналами
frozenlist>=1.3.0

# Для работы с атрибутами
attrs>=22.0.0

# Для работы с асинхронными контекстными менеджерами
async-timeout>=4.0.0

# Для работы с сокетами
aiosignal>=1.2.0
