#!/usr/bin/env python3
"""
Скрипт для запуска YouTube Summary Bot
Проверяет конфигурацию и запускает бота
"""

import os
import sys
import logging
from pathlib import Path

def check_config():
    """Проверяет наличие и корректность конфигурации"""
    print("🔍 Проверка конфигурации...")
    
    # Проверяем наличие config_and_utils.py
    if not os.path.exists("config_and_utils.py"):
        print("❌ Файл config_and_utils.py не найден!")
        print("📝 Скопируйте config_example.py в config_and_utils.py и настройте API ключи")
        return False
    
    try:
        # Импортируем конфигурацию
        from config_and_utils import (
            TELEGRAM_BOT_TOKEN, 
            GEMINI_API_KEYS, 
            YOUTUBE_DATA_API_KEY
        )
        
        # Проверяем Telegram Bot Token
        if not TELEGRAM_BOT_TOKEN or TELEGRAM_BOT_TOKEN == "...":
            print("❌ TELEGRAM_BOT_TOKEN не настроен!")
            print("📝 Получите токен у @BotFather и добавьте в config_and_utils.py")
            return False
        
        # Проверяем Gemini API ключи
        if not GEMINI_API_KEYS or GEMINI_API_KEYS == ["..."]:
            print("❌ GEMINI_API_KEYS не настроены!")
            print("📝 Получите ключи в Google AI Studio и добавьте в config_and_utils.py")
            return False
        
        # Проверяем YouTube API ключ
        if not YOUTUBE_DATA_API_KEY or YOUTUBE_DATA_API_KEY == "...":
            print("❌ YOUTUBE_DATA_API_KEY не настроен!")
            print("📝 Получите ключ в Google Cloud Console и добавьте в config_and_utils.py")
            return False
        
        print("✅ Конфигурация корректна!")
        return True
        
    except ImportError as e:
        print(f"❌ Ошибка импорта конфигурации: {e}")
        print("📝 Проверьте синтаксис файла config_and_utils.py")
        return False
    except Exception as e:
        print(f"❌ Ошибка проверки конфигурации: {e}")
        return False

def check_dependencies():
    """Проверяет установленные зависимости"""
    print("📦 Проверка зависимостей...")
    
    required_packages = [
        "telegram",
        "aiohttp", 
        "aiosqlite",
        "youtube_transcript_api",
        "google.generativeai",
        "telegraph"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Отсутствуют зависимости: {', '.join(missing_packages)}")
        print("📝 Установите зависимости: pip install -r requirements.txt")
        return False
    
    print("✅ Все зависимости установлены!")
    return True

def create_temp_directory():
    """Создает папку для временных файлов"""
    temp_dir = Path("temp")
    try:
        temp_dir.mkdir(exist_ok=True)
        print("✅ Папка temp создана/проверена")
        return True
    except Exception as e:
        print(f"❌ Ошибка создания папки temp: {e}")
        return False

def start_bot():
    """Запускает бота"""
    print("🚀 Запуск YouTube Summary Bot...")
    
    try:
        # Импортируем и запускаем основной модуль
        from telegram_bot import main
        main()
    except KeyboardInterrupt:
        print("\n🛑 Бот остановлен пользователем")
    except Exception as e:
        print(f"❌ Ошибка запуска бота: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Основная функция"""
    print("🎥 YouTube Summary Bot - Запуск")
    print("=" * 50)
    
    # Проверяем все требования
    if not check_dependencies():
        sys.exit(1)
    
    if not check_config():
        sys.exit(1)
    
    if not create_temp_directory():
        sys.exit(1)
    
    print("=" * 50)
    print("🎉 Все проверки пройдены успешно!")
    print("🚀 Запускаем бота...")
    print("=" * 50)
    
    # Запускаем бота
    start_bot()

if __name__ == "__main__":
    main()
