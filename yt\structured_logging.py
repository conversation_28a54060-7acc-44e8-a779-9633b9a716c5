"""
Structured Logging System для YouTube бота
Этап 4 плана исправления ошибок с ссылками на видео
"""

import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from threading import local
import traceback
from enum import Enum

# Глобальное хранилище для correlation ID в каждом потоке
_thread_local = local()


class LogLevel(Enum):
    """Уровни логирования"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class EventType(Enum):
    """Типы событий для структурированного логирования"""
    VIDEO_PROCESSING_START = "video_processing_start"
    VIDEO_PROCESSING_END = "video_processing_end"
    VIDEO_PROCESSING_ERROR = "video_processing_error"
    API_REQUEST_START = "api_request_start"
    API_REQUEST_END = "api_request_end"
    API_REQUEST_ERROR = "api_request_error"
    BATCH_PROCESSING_START = "batch_processing_start"
    BATCH_PROCESSING_END = "batch_processing_end"
    SUBSCRIPTION_CHECK = "subscription_check"
    HEALTH_CHECK = "health_check"
    ALERT_TRIGGERED = "alert_triggered"
    USER_INTERACTION = "user_interaction"
    DATABASE_OPERATION = "database_operation"
    TRANSCRIPT_FETCH = "transcript_fetch"
    SUMMARY_GENERATION = "summary_generation"
    NOTIFICATION_SENT = "notification_sent"


class StructuredLogger:
    """
    Класс для структурированного логирования с поддержкой correlation ID
    и контекстных полей
    """

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.name = name

    def _get_correlation_id(self) -> str:
        """Получает correlation ID для текущего потока"""
        if not hasattr(_thread_local, 'correlation_id'):
            _thread_local.correlation_id = str(uuid.uuid4())
        return _thread_local.correlation_id

    def _set_correlation_id(self, correlation_id: str):
        """Устанавливает correlation ID для текущего потока"""
        _thread_local.correlation_id = correlation_id

    def _create_log_entry(
        self,
        level: LogLevel,
        event_type: EventType,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Создает структурированную запись лога"""

        # Краткая структурированная запись
        log_entry = {
            "ts": datetime.utcnow().strftime('%H:%M:%S'),
            "lvl": level.value[:4],
            "type": event_type.value,
            "msg": message,
        }

        # Добавляем только важные поля
        if context and len(context) <= 3:  # Ограничиваем размер контекста
            log_entry["ctx"] = context
        elif context:
            # Для больших контекстов берем только ключевые поля
            key_fields = ["video_id", "channel_id", "error_type", "user_id"]
            filtered_context = {k: v for k, v in context.items() if k in key_fields}
            if filtered_context:
                log_entry["ctx"] = filtered_context

        if duration_ms is not None and duration_ms > 1000:  # Логируем только долгие операции
            log_entry["dur"] = round(duration_ms, 0)

        if error_details:
            log_entry["err"] = error_details.get("exception", str(error_details))

        return log_entry

    def _log(
        self,
        level: LogLevel,
        event_type: EventType,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        correlation_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        error_details: Optional[Dict[str, Any]] = None
    ):
        """Выполняет структурированное логирование"""

        # Создаем краткое сообщение вместо JSON
        compact_message = self._create_compact_message(
            event_type, message, context, duration_ms, error_details
        )

        # Выбираем уровень логирования
        log_level = getattr(logging, level.value)
        self.logger.log(log_level, compact_message)

    def _create_compact_message(
        self,
        event_type: EventType,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        duration_ms: Optional[float] = None,
        error_details: Optional[Dict[str, Any]] = None
    ) -> str:
        """Создает краткое сообщение вместо JSON"""

        # Основное сообщение
        parts = [message]

        # Добавляем ключевую информацию из контекста
        if context:
            key_info = []
            if "video_id" in context:
                key_info.append(f"video:{context['video_id'][:8]}")
            if "channel_id" in context:
                key_info.append(f"channel:{context['channel_id'][:8]}")
            if "user_id" in context:
                key_info.append(f"user:{context['user_id']}")
            if "error_type" in context:
                key_info.append(f"error:{context['error_type']}")

            if key_info:
                parts.append(f"({', '.join(key_info)})")

        # Добавляем длительность если значительная
        if duration_ms and duration_ms > 1000:
            parts.append(f"[{duration_ms/1000:.1f}s]")

        # Добавляем ошибку если есть
        if error_details:
            if isinstance(error_details, dict) and "exception" in error_details:
                error_msg = str(error_details["exception"])
            else:
                error_msg = str(error_details)

            # Очищаем от лишних префиксов
            error_msg = error_msg.replace("TestException: ", "").replace("Exception: ", "")
            if len(error_msg) > 40:
                error_msg = error_msg[:37] + "..."
            parts.append(f"❌ {error_msg}")

        return " ".join(parts)

    def debug(self, event_type: EventType, message: str, **kwargs):
        """Debug уровень логирования"""
        self._log(LogLevel.DEBUG, event_type, message, **kwargs)

    def info(self, event_type: EventType, message: str, **kwargs):
        """Info уровень логирования"""
        self._log(LogLevel.INFO, event_type, message, **kwargs)

    def warning(self, event_type: EventType, message: str, **kwargs):
        """Warning уровень логирования"""
        self._log(LogLevel.WARNING, event_type, message, **kwargs)

    def error(self, event_type: EventType, message: str, **kwargs):
        """Error уровень логирования"""
        self._log(LogLevel.ERROR, event_type, message, **kwargs)

    def critical(self, event_type: EventType, message: str, **kwargs):
        """Critical уровень логирования"""
        self._log(LogLevel.CRITICAL, event_type, message, **kwargs)

    def log_exception(
        self,
        event_type: EventType,
        message: str,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None
    ):
        """Логирует исключение с полной информацией"""
        
        error_details = {
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "traceback": traceback.format_exc()
        }

        self.error(
            event_type,
            message,
            context=context,
            error_details=error_details
        )

    @contextmanager
    def operation_context(
        self,
        operation_name: str,
        start_event: EventType,
        end_event: EventType,
        error_event: EventType,
        context: Optional[Dict[str, Any]] = None
    ):
        """
        Контекстный менеджер для логирования операций с измерением времени
        """
        correlation_id = str(uuid.uuid4())
        self._set_correlation_id(correlation_id)
        
        start_time = time.time()
        
        # Логируем начало операции
        self.info(
            start_event,
            f"Начало операции: {operation_name}",
            context=context,
            correlation_id=correlation_id
        )

        try:
            yield correlation_id
            
            # Логируем успешное завершение
            duration_ms = (time.time() - start_time) * 1000
            self.info(
                end_event,
                f"Операция завершена успешно: {operation_name}",
                context=context,
                correlation_id=correlation_id,
                duration_ms=duration_ms
            )
            
        except Exception as e:
            # Логируем ошибку
            duration_ms = (time.time() - start_time) * 1000
            self.log_exception(
                error_event,
                f"Ошибка в операции: {operation_name}",
                e,
                context={**(context or {}), "duration_ms": duration_ms}
            )
            raise

    def log_video_processing(
        self,
        video_id: str,
        channel_id: str,
        action: str,
        status: str,
        details: Optional[Dict[str, Any]] = None
    ):
        """Специализированное логирование обработки видео"""
        
        context = {
            "video_id": video_id,
            "channel_id": channel_id,
            "action": action,
            "status": status
        }
        
        if details:
            context.update(details)

        if status == "success":
            self.info(EventType.VIDEO_PROCESSING_END, f"Видео обработано: {action}", context=context)
        elif status == "error":
            self.error(EventType.VIDEO_PROCESSING_ERROR, f"Ошибка обработки видео: {action}", context=context)
        else:
            self.info(EventType.VIDEO_PROCESSING_START, f"Начало обработки видео: {action}", context=context)

    def log_api_request(
        self,
        endpoint: str,
        method: str,
        status_code: Optional[int] = None,
        duration_ms: Optional[float] = None,
        error: Optional[str] = None
    ):
        """Специализированное логирование API запросов"""
        
        context = {
            "endpoint": endpoint,
            "method": method
        }
        
        if status_code:
            context["status_code"] = status_code

        if error:
            self.error(
                EventType.API_REQUEST_ERROR,
                f"Ошибка API запроса: {method} {endpoint}",
                context=context,
                error_details={"error_message": error}
            )
        else:
            event_type = EventType.API_REQUEST_END if status_code else EventType.API_REQUEST_START
            message = f"API запрос: {method} {endpoint}"
            
            self.info(event_type, message, context=context, duration_ms=duration_ms)


# Глобальные экземпляры логгеров
video_logger = StructuredLogger("video_processing")
api_logger = StructuredLogger("api_requests")
monitoring_logger = StructuredLogger("monitoring")
health_logger = StructuredLogger("health_check")
alert_logger = StructuredLogger("alerts")


def get_logger(name: str) -> StructuredLogger:
    """Получает структурированный логгер по имени"""
    return StructuredLogger(name)


def set_correlation_id(correlation_id: str):
    """Устанавливает correlation ID для текущего потока"""
    _thread_local.correlation_id = correlation_id


def get_correlation_id() -> str:
    """Получает correlation ID для текущего потока"""
    if not hasattr(_thread_local, 'correlation_id'):
        _thread_local.correlation_id = str(uuid.uuid4())
    return _thread_local.correlation_id


def configure_structured_logging():
    """Настраивает структурированное логирование для всего приложения"""

    # Импортируем re для фильтра
    import re

    # Фильтр для подавления спам-логов
    class SpamLogFilter(logging.Filter):
        def filter(self, record):
            message = str(record.getMessage())

            # Список спам-паттернов для подавления
            spam_patterns = [
                'send_long_message sent final response',
                'final_reply_markup is None',
                'Auto-forwarding message',
                'Group chat detected',
                'Skipping.*button for group chat',
                'Utils remove_reaction',
                'API метрики: успешный запрос',
                'API метрики: ошибка.*для.*неудачных'
            ]

            # Проверяем каждый паттерн
            for pattern in spam_patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    return False  # Подавляем спам-логи

            return True  # Пропускаем все остальные сообщения

    # Создаем форматтер для кратких логов
    class StructuredFormatter(logging.Formatter):
        def format(self, record):
            # Краткий формат для всех логов
            timestamp = datetime.utcnow().strftime('%H:%M:%S')
            level = record.levelname[:4]  # Сокращаем уровень
            logger_name = record.name.split('.')[-1]  # Только последняя часть имени
            message = record.getMessage()

            # Краткий формат: время [уровень] логгер: сообщение
            return f"{timestamp} [{level}] {logger_name}: {message}"

    # Настраиваем корневой логгер
    root_logger = logging.getLogger()

    # Удаляем существующие обработчики
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Создаем новый обработчик с структурированным форматированием
    handler = logging.StreamHandler()
    handler.setFormatter(StructuredFormatter())
    handler.addFilter(SpamLogFilter())  # Добавляем фильтр к обработчику
    root_logger.addHandler(handler)
    root_logger.setLevel(logging.INFO)
