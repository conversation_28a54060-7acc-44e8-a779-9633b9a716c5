#!/usr/bin/env python3
"""
Оптимизации для функции подписок на YouTube каналы
Этап 5: Тестирование и оптимизация

Этот файл содержит улучшения и оптимизации для функции подписок:
1. Оптимизация частоты проверок
2. Улучшенная обработка ошибок
3. Кэширование и rate limiting
4. Мониторинг производительности
"""

import asyncio
import logging
import time
import json
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import aiohttp

logger = logging.getLogger(__name__)

# Валидация video_id (импортируем паттерн из telegram_bot.py)
VIDEO_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{11}$')  # YouTube video ID всегда 11 символов

def validate_video_id(video_id: Optional[str]) -> bool:
    """
    Валидирует video_id для YouTube видео

    Args:
        video_id: ID видео для проверки

    Returns:
        True если video_id корректный, False в противном случае
    """
    if not video_id:
        return False

    if not isinstance(video_id, str):
        return False

    # Проверяем соответствие паттерну YouTube video ID (11 символов)
    return bool(VIDEO_ID_PATTERN.match(video_id))

@dataclass
class ChannelStats:
    """Статистика канала для оптимизации проверок"""
    channel_id: str
    avg_videos_per_day: float
    last_video_time: Optional[datetime]
    check_failures: int
    last_successful_check: Optional[datetime]
    
class OptimizedChannelMonitor:
    """Оптимизированный мониторинг каналов с адаптивной частотой проверок"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.monitoring_task = None
        self.is_running = False
        
        # Адаптивные интервалы проверки
        self.base_check_interval = 300  # 5 минут базовый интервал
        self.min_check_interval = 180   # 3 минуты минимум
        self.max_check_interval = 1800  # 30 минут максимум
        
        # Rate limiting для YouTube API
        self.api_requests_count = 0
        self.api_requests_limit = 90  # Оставляем запас от лимита 100
        self.api_requests_reset_time = time.time() + 3600
        
        # Кэш для информации о каналах
        self.channel_cache = {}
        self.cache_ttl = 600  # 10 минут
        
        # Статистика каналов для оптимизации
        self.channel_stats: Dict[str, ChannelStats] = {}
        
        # Метрики производительности
        self.performance_metrics = {
            "total_checks": 0,
            "successful_checks": 0,
            "failed_checks": 0,
            "videos_processed": 0,
            "avg_check_time": 0,
            "last_reset": time.time()
        }
        
    async def start_monitoring(self):
        """Запускает оптимизированный мониторинг"""
        if self.is_running:
            logger.warning("🔄 Мониторинг каналов уже запущен")
            return
            
        logger.info("🔄 Запуск оптимизированного мониторинга каналов...")
        self.is_running = True
        
        # Загружаем статистику каналов
        await self.load_channel_stats()
        
        self.monitoring_task = asyncio.create_task(self._optimized_monitoring_loop())
        logger.info("✅ Оптимизированный мониторинг каналов запущен")
        
    async def stop_monitoring(self):
        """Останавливает мониторинг"""
        if not self.is_running:
            return
            
        logger.info("🛑 Остановка оптимизированного мониторинга каналов...")
        self.is_running = False
        
        # Сохраняем статистику каналов
        await self.save_channel_stats()
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✅ Оптимизированный мониторинг каналов остановлен")
        
    async def _optimized_monitoring_loop(self):
        """Оптимизированный цикл мониторинга с адаптивными интервалами"""
        while self.is_running:
            try:
                start_time = time.time()
                
                # Проверяем и сбрасываем счетчик API запросов
                await self._check_api_rate_limit()
                
                # Получаем каналы для проверки с приоритизацией
                channels_to_check = await self._get_prioritized_channels()
                
                if channels_to_check:
                    await self._process_channels_batch(channels_to_check)
                    
                # Обновляем метрики производительности
                check_time = time.time() - start_time
                await self._update_performance_metrics(check_time)
                
                # Вычисляем адаптивный интервал для следующей проверки
                next_interval = await self._calculate_adaptive_interval()
                
                logger.info(f"🔄 Следующая проверка через {next_interval} секунд")
                await asyncio.sleep(next_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Ошибка в оптимизированном цикле мониторинга: {e}")
                await asyncio.sleep(60)  # Ждём минуту при ошибке
                
    async def _check_api_rate_limit(self):
        """Проверяет и управляет лимитами API"""
        current_time = time.time()
        
        # Сбрасываем счетчик каждый час
        if current_time >= self.api_requests_reset_time:
            self.api_requests_count = 0
            self.api_requests_reset_time = current_time + 3600
            logger.info("🔄 Счетчик API запросов сброшен")
            
        # Проверяем, не превышен ли лимит
        if self.api_requests_count >= self.api_requests_limit:
            wait_time = self.api_requests_reset_time - current_time
            logger.warning(f"⚠️ Достигнут лимит API запросов. Ожидание {wait_time:.0f} секунд")
            await asyncio.sleep(wait_time)
            
    async def _get_prioritized_channels(self) -> List[Dict]:
        """Получает каналы для проверки с приоритизацией"""
        try:
            all_subscriptions = await self.bot.db.get_all_active_subscriptions()
            
            if not all_subscriptions:
                return []
                
            # Группируем по каналам и приоритизируем
            channels_data = {}
            for sub in all_subscriptions:
                channel_id = sub['channel_id']
                if channel_id not in channels_data:
                    channels_data[channel_id] = {
                        'channel_id': channel_id,
                        'channel_name': sub['channel_name'],
                        'last_video_id': sub['last_video_id'],
                        'subscribers': [],
                        'priority': await self._calculate_channel_priority(channel_id)
                    }
                channels_data[channel_id]['subscribers'].append(sub['user_id'])
                
            # Сортируем по приоритету (высокий приоритет = чаще проверяем)
            sorted_channels = sorted(
                channels_data.values(),
                key=lambda x: x['priority'],
                reverse=True
            )
            
            # Ограничиваем количество каналов для проверки на основе API лимитов
            max_channels = min(
                len(sorted_channels),
                self.api_requests_limit - self.api_requests_count
            )
            
            return sorted_channels[:max_channels]
            
        except Exception as e:
            logger.error(f"❌ Ошибка получения приоритизированных каналов: {e}")
            return []
            
    async def _calculate_channel_priority(self, channel_id: str) -> float:
        """Вычисляет приоритет канала для проверки"""
        stats = self.channel_stats.get(channel_id)
        
        if not stats:
            return 1.0  # Средний приоритет для новых каналов
            
        priority = 1.0
        
        # Увеличиваем приоритет для активных каналов
        if stats.avg_videos_per_day > 0.5:  # Более 1 видео в 2 дня
            priority += 0.5
            
        # Уменьшаем приоритет для каналов с ошибками
        if stats.check_failures > 3:
            priority -= 0.3
            
        # Увеличиваем приоритет для недавно проверенных каналов
        if stats.last_successful_check:
            hours_since_check = (datetime.now() - stats.last_successful_check).total_seconds() / 3600
            if hours_since_check > 6:  # Не проверялись более 6 часов
                priority += 0.2
                
        return max(0.1, priority)  # Минимальный приоритет 0.1
        
    async def _process_channels_batch(self, channels: List[Dict]):
        """Обрабатывает пакет каналов с оптимизацией"""
        logger.info(f"🔍 Проверка {len(channels)} каналов...")
        
        # Обрабатываем каналы параллельно, но с ограничением
        semaphore = asyncio.Semaphore(3)  # Максимум 3 канала одновременно
        
        tasks = []
        for channel_data in channels:
            task = asyncio.create_task(
                self._process_single_channel_with_semaphore(semaphore, channel_data)
            )
            tasks.append(task)
            
        # Ждём завершения всех задач
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Обрабатываем результаты
        successful = sum(1 for r in results if r is True)
        failed = len(results) - successful
        
        logger.info(f"📊 Обработано каналов: {successful} успешно, {failed} с ошибками")
        
    async def _process_single_channel_with_semaphore(self, semaphore: asyncio.Semaphore, channel_data: Dict) -> bool:
        """Обрабатывает один канал с семафором"""
        async with semaphore:
            return await self._process_single_channel_optimized(channel_data)
            
    async def _process_single_channel_optimized(self, channel_data: Dict) -> bool:
        """Оптимизированная обработка одного канала"""
        channel_id = channel_data['channel_id']
        
        try:
            start_time = time.time()
            
            # Проверяем кэш
            cached_info = self._get_cached_channel_info(channel_id)
            if cached_info:
                logger.debug(f"📋 Используем кэшированную информацию для канала {channel_id}")
                uploads_playlist_id = cached_info['uploads_playlist_id']
            else:
                # Получаем информацию о канале
                uploads_playlist_id = await self.bot.get_channel_uploads_playlist(channel_id)
                if uploads_playlist_id:
                    self._cache_channel_info(channel_id, {'uploads_playlist_id': uploads_playlist_id})
                    
            if not uploads_playlist_id:
                await self._update_channel_stats(channel_id, success=False)
                return False
                
            self.api_requests_count += 1
            
            # Получаем последние видео
            latest_videos = await self.bot.get_latest_videos_from_playlist(uploads_playlist_id, max_results=3)
            
            if latest_videos:
                # Обрабатываем новые видео
                new_videos = await self._filter_new_videos(channel_data, latest_videos)
                
                if new_videos:
                    logger.info(f"🎬 Найдено {len(new_videos)} новых видео на канале {channel_data['channel_name']}")

                    # Используем batch обработку для эффективности
                    if len(new_videos) > 1:
                        await self._process_videos_batch(channel_id, channel_data, new_videos)
                    else:
                        # Для одного видео используем обычную обработку
                        for video in reversed(new_videos):
                            await self.bot.channel_monitor.process_new_video(channel_id, channel_data, video)

                    # Обновляем last_video_id с валидацией
                    newest_video_id = new_videos[0].get('video_id')
                    if newest_video_id and validate_video_id(newest_video_id):
                        await self.bot.db.update_last_video_check(channel_id, newest_video_id)
                    elif newest_video_id:
                        logger.warning(f"Некорректный newest_video_id для канала {channel_id}: {newest_video_id}")
                        
                # Обновляем статистику канала
                await self._update_channel_stats(channel_id, success=True, videos_count=len(latest_videos))
                
            processing_time = time.time() - start_time
            logger.debug(f"⏱️ Канал {channel_id} обработан за {processing_time:.2f}с")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Ошибка обработки канала {channel_id}: {e}")
            await self._update_channel_stats(channel_id, success=False)
            return False
            
    def _get_cached_channel_info(self, channel_id: str) -> Optional[Dict]:
        """Получает информацию о канале из кэша"""
        if channel_id in self.channel_cache:
            cache_entry = self.channel_cache[channel_id]
            if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                return cache_entry['data']
            else:
                # Удаляем устаревший кэш
                del self.channel_cache[channel_id]
        return None
        
    def _cache_channel_info(self, channel_id: str, data: Dict):
        """Кэширует информацию о канале"""
        self.channel_cache[channel_id] = {
            'data': data,
            'timestamp': time.time()
        }
        
    async def _filter_new_videos(self, channel_data: Dict, latest_videos: List[Dict]) -> List[Dict]:
        """Фильтрует новые видео"""
        last_video_id = channel_data.get('last_video_id')
        
        if not last_video_id:
            # Если это первая проверка, берём только последнее видео
            return latest_videos[:1] if latest_videos else []
            
        new_videos = []
        for video in latest_videos:
            video_id = video.get('video_id')
            if video_id == last_video_id:
                break
            new_videos.append(video)
            
        return new_videos
        
    async def _update_channel_stats(self, channel_id: str, success: bool, videos_count: int = 0):
        """Обновляет статистику канала"""
        if channel_id not in self.channel_stats:
            self.channel_stats[channel_id] = ChannelStats(
                channel_id=channel_id,
                avg_videos_per_day=0.0,
                last_video_time=None,
                check_failures=0,
                last_successful_check=None
            )
            
        stats = self.channel_stats[channel_id]
        
        if success:
            stats.last_successful_check = datetime.now()
            stats.check_failures = 0
            
            # Обновляем среднее количество видео в день
            if videos_count > 0:
                # Простое скользящее среднее
                stats.avg_videos_per_day = (stats.avg_videos_per_day * 0.9) + (videos_count * 0.1)
        else:
            stats.check_failures += 1
            
    async def _calculate_adaptive_interval(self) -> int:
        """Вычисляет адаптивный интервал для следующей проверки"""
        # Базовый интервал
        interval = self.base_check_interval
        
        # Корректируем на основе загрузки API
        api_usage_ratio = self.api_requests_count / self.api_requests_limit
        if api_usage_ratio > 0.8:
            interval = int(interval * 1.5)  # Увеличиваем интервал при высокой загрузке
        elif api_usage_ratio < 0.3:
            interval = int(interval * 0.8)  # Уменьшаем интервал при низкой загрузке
            
        # Корректируем на основе производительности
        if self.performance_metrics['avg_check_time'] > 30:  # Если проверка занимает более 30 секунд
            interval = int(interval * 1.2)
            
        # Ограничиваем интервал
        interval = max(self.min_check_interval, min(self.max_check_interval, interval))
        
        return interval
        
    async def _update_performance_metrics(self, check_time: float):
        """Обновляет метрики производительности"""
        self.performance_metrics['total_checks'] += 1
        
        # Обновляем среднее время проверки
        current_avg = self.performance_metrics['avg_check_time']
        new_avg = (current_avg * 0.9) + (check_time * 0.1)
        self.performance_metrics['avg_check_time'] = new_avg
        
        # Сбрасываем метрики каждые 24 часа
        if time.time() - self.performance_metrics['last_reset'] > 86400:
            self.performance_metrics = {
                "total_checks": 0,
                "successful_checks": 0,
                "failed_checks": 0,
                "videos_processed": 0,
                "avg_check_time": 0,
                "last_reset": time.time()
            }
            
    async def load_channel_stats(self):
        """Загружает статистику каналов из файла"""
        try:
            # В реальной реализации можно загружать из БД или файла
            pass
        except Exception as e:
            logger.error(f"❌ Ошибка загрузки статистики каналов: {e}")
            
    async def save_channel_stats(self):
        """Сохраняет статистику каналов в файл"""
        try:
            # В реальной реализации можно сохранять в БД или файл
            pass
        except Exception as e:
            logger.error(f"❌ Ошибка сохранения статистики каналов: {e}")
            
    def get_performance_report(self) -> str:
        """Возвращает отчет о производительности"""
        metrics = self.performance_metrics
        
        report = f"""
📊 ОТЧЕТ О ПРОИЗВОДИТЕЛЬНОСТИ МОНИТОРИНГА

🔍 Всего проверок: {metrics['total_checks']}
✅ Успешных: {metrics['successful_checks']}
❌ Неудачных: {metrics['failed_checks']}
🎬 Видео обработано: {metrics['videos_processed']}
⏱️ Среднее время проверки: {metrics['avg_check_time']:.2f}с

🌐 API запросы: {self.api_requests_count}/{self.api_requests_limit}
📋 Каналов в кэше: {len(self.channel_cache)}
📈 Каналов в статистике: {len(self.channel_stats)}
        """
        
        return report.strip()

class SubscriptionErrorHandler:
    """Улучшенная обработка ошибок для функции подписок"""

    def __init__(self):
        self.error_counts = {}
        self.error_threshold = 5
        self.cooldown_period = 3600  # 1 час

    async def handle_youtube_api_error(self, error: Exception, channel_id: str) -> bool:
        """Обрабатывает ошибки YouTube API"""
        error_key = f"youtube_api_{channel_id}"

        if "quotaExceeded" in str(error):
            logger.error(f"❌ Превышена квота YouTube API для канала {channel_id}")
            return False

        elif "channelNotFound" in str(error):
            logger.warning(f"⚠️ Канал {channel_id} не найден")
            # Деактивируем подписки на несуществующий канал
            await self._deactivate_channel_subscriptions(channel_id)
            return False

        elif "forbidden" in str(error).lower():
            logger.warning(f"⚠️ Доступ к каналу {channel_id} запрещен")
            return False

        else:
            # Общая обработка ошибок с подсчетом
            return await self._handle_generic_error(error_key, error)

    async def handle_network_error(self, error: Exception, operation: str) -> bool:
        """Обрабатывает сетевые ошибки"""
        error_key = f"network_{operation}"

        if "timeout" in str(error).lower():
            logger.warning(f"⚠️ Таймаут при выполнении {operation}")
            await asyncio.sleep(5)  # Короткая пауза при таймауте
            return True  # Можно повторить

        elif "connection" in str(error).lower():
            logger.warning(f"⚠️ Ошибка соединения при выполнении {operation}")
            await asyncio.sleep(10)  # Более длинная пауза при проблемах с соединением
            return True

        else:
            return await self._handle_generic_error(error_key, error)

    async def _handle_generic_error(self, error_key: str, error: Exception) -> bool:
        """Общая обработка ошибок с подсчетом"""
        current_time = time.time()

        if error_key not in self.error_counts:
            self.error_counts[error_key] = {
                'count': 0,
                'first_error': current_time,
                'last_error': current_time
            }

        error_info = self.error_counts[error_key]
        error_info['count'] += 1
        error_info['last_error'] = current_time

        # Если превышен порог ошибок
        if error_info['count'] >= self.error_threshold:
            time_since_first = current_time - error_info['first_error']

            if time_since_first < self.cooldown_period:
                logger.error(f"❌ Превышен порог ошибок для {error_key}. Операция приостановлена.")
                return False
            else:
                # Сбрасываем счетчик после периода охлаждения
                error_info['count'] = 1
                error_info['first_error'] = current_time

        logger.warning(f"⚠️ Ошибка {error_key} ({error_info['count']}/{self.error_threshold}): {error}")
        return True

    async def _deactivate_channel_subscriptions(self, channel_id: str):
        """Деактивирует подписки на несуществующий канал"""
        try:
            # В реальной реализации нужно обновить БД
            logger.info(f"🔄 Деактивация подписок на канал {channel_id}")
        except Exception as e:
            logger.error(f"❌ Ошибка деактивации подписок на канал {channel_id}: {e}")

class SubscriptionHealthChecker:
    """Проверка здоровья системы подписок"""

    def __init__(self, bot_instance):
        self.bot = bot_instance

    async def run_health_check(self) -> Dict[str, Any]:
        """Выполняет проверку здоровья системы"""
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'database_status': 'unknown',
            'api_status': 'unknown',
            'monitoring_status': 'unknown',
            'subscription_count': 0,
            'issues': []
        }

        # Проверка базы данных
        try:
            test_subscriptions = await self.bot.db.get_all_active_subscriptions()
            health_report['database_status'] = 'healthy'
            health_report['subscription_count'] = len(test_subscriptions)
        except Exception as e:
            health_report['database_status'] = 'error'
            health_report['issues'].append(f"Database error: {e}")

        # Проверка YouTube API
        try:
            # Простой тест API
            test_result = await self.bot.get_channel_uploads_playlist("UCBJycsmduvYEL83R_U4JriQ")
            health_report['api_status'] = 'healthy' if test_result else 'degraded'
        except Exception as e:
            health_report['api_status'] = 'error'
            health_report['issues'].append(f"YouTube API error: {e}")

        # Проверка мониторинга
        if hasattr(self.bot, 'channel_monitor') and self.bot.channel_monitor:
            health_report['monitoring_status'] = 'running' if self.bot.channel_monitor.is_running else 'stopped'
        else:
            health_report['monitoring_status'] = 'not_initialized'
            health_report['issues'].append("Channel monitor not initialized")

        return health_report

    async def _process_videos_batch(self, channel_id: str, channel_data: Dict, new_videos: List[Dict]):
        """
        Обрабатывает множественные видео канала с использованием batch процессора

        Args:
            channel_id: ID канала
            channel_data: Данные канала с подписчиками
            new_videos: Список новых видео для обработки
        """
        try:
            logger.info(f"🔄 Начинаем batch обработку {len(new_videos)} видео канала {channel_id}")

            # Получаем список подписчиков канала
            subscribers = channel_data.get('subscribers', [])

            # Добавляем все видео в batch процессор
            for video in reversed(new_videos):  # Обрабатываем в хронологическом порядке
                video_id = video.get('video_id')
                if not video_id:
                    logger.warning(f"Пропуск видео без video_id: {video}")
                    continue

                # Добавляем задачу в batch процессор с высоким приоритетом (новые видео)
                await self.bot.batch_processor.add_video_task(
                    video_id=video_id,
                    channel_id=channel_id,
                    video_info=video,
                    subscribers=subscribers,
                    high_priority=True  # Новые видео имеют высокий приоритет
                )

            # Запускаем batch обработку
            batch_stats = await self.bot.batch_processor.process_video_batch(max_videos=len(new_videos))

            logger.info(f"✅ Batch обработка канала {channel_id} завершена: "
                       f"{batch_stats.processed_videos}/{batch_stats.total_videos} успешно, "
                       f"время: {batch_stats.processing_time:.2f}с")

        except Exception as e:
            logger.error(f"❌ Ошибка batch обработки видео канала {channel_id}: {e}")
            # Fallback на обычную обработку
            logger.info(f"🔄 Переключение на обычную обработку для канала {channel_id}")
            for video in reversed(new_videos):
                try:
                    await self.bot.channel_monitor.process_new_video(channel_id, channel_data, video)
                except Exception as fallback_error:
                    logger.error(f"❌ Ошибка fallback обработки видео {video.get('video_id', 'unknown')}: {fallback_error}")
