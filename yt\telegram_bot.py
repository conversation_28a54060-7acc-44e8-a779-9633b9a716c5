"""
Telegram Bot для транскрипции YouTube видео и создания сводок
ПОЛНОСТЬЮ АСИНХРОННАЯ ВЕРСИЯ - БЕЗ ТАЙМАУТОВ И ОГРАНИЧЕНИЙ
Поддерживает НЕОГРАНИЧЕННОЕ количество одновременных запросов транскрипции
Каждый запрос обрабатывается полностью асинхронно через прокси
"""

# Импорт конфигурации и утилит из отдельного модуля
from config_and_utils import (
    # Основные импорты
    asyncio, logging, random, json, aiohttp, time, html, signal, sys, re, os, uuid, mimetypes,
    Optional, Dict, Any, List, Tuple,
    Update, InlineKeyboardMarkup, InlineKeyboardButton, ReplyParameters, ReactionTypeEmoji,
    Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes,
    ChatAction, ChatType, Telegraph,
    YouTubeTranscriber, YouTubeTranscriberError, TranscriptNotFoundError,


    datetime, timedelta,

    # Константы конфигурации
    TELEGRAM_BOT_TOKEN, GEMINI_API_KEYS, GEMINI_API_URL,
    GEMINI_FILES_API_URL, GEMINI_FILES_GET_URL, MAX_VIDEO_FILE_SIZE, MAX_VIDEO_DURATION, SUPPORTED_VIDEO_MIME_TYPES,
    TELEGRAPH_ACCESS_TOKEN, TELEGRAPH_AUTHOR_NAME, TELEGRAPH_AUTHOR_URL,
    YOUTUBE_DATA_API_KEY, YOUTUBE_DATA_API_URL, YOUTUBE_COMMENTS_API_URL,
    YOUTUBE_CHANNELS_API_URL, YOUTUBE_SEARCH_API_URL, YOUTUBE_PLAYLISTS_API_URL,
    MINIMUM_VIDEO_DURATION, MAX_SUBSCRIPTIONS_PER_USER, CHANNEL_CHECK_INTERVAL,
    MAX_API_REQUESTS_PER_HOUR, VIDEO_ID_PATTERN,
    MAX_RETRY_ATTEMPTS, RETRY_BASE_DELAY, RETRY_MAX_DELAY, RETRY_EXPONENTIAL_BASE,
    RETRYABLE_HTTP_STATUSES, USER_STATE_WAITING_CHANNEL_URL, SUBSCRIPTION_MESSAGES,

    # Классы и утилиты
    APIMetrics, validate_video_id, create_safe_video_url, trace_function,
    EnhancedHealthCheckSystem, YouTubeAPIHealthChecker, DatabaseHealthChecker, 
    TelegramBotHealthChecker, MonitoringHealthChecker,
    
    # Логгеры и системы мониторинга
    video_logger, api_logger, monitoring_logger, EventType,
    metrics_collector, alert_manager, dashboard,
    alerting_system, AlertSeverity, Alert,
    request_tracer,

    # Логгер уже настроен в config_and_utils
    logger
)

# Все константы, классы и настройки импортированы из config_and_utils

# Импорт YouTube сервисов
from youtube_services import YouTubeServices

# Импорт Google GenAI SDK
from google import genai


class SubtitleWaitingQueue:
    """Класс для управления очередью ожидания субтитров с экспоненциальной задержкой"""

    def __init__(self, bot):
        self.bot = bot
        self.delay_schedule = [5, 15, 30, 60, 120, 240]  # минуты для попыток 2-7
        logger.info("🔄 Инициализирован SubtitleWaitingQueue")

    async def process_pending_videos(self):
        """Обрабатывает видео в очереди ожидания"""
        try:
            # Получаем видео готовые для повторной попытки
            videos_to_retry = await self.bot.db.get_videos_ready_for_retry()

            if not videos_to_retry:
                logger.debug("📭 Нет видео готовых для повторной попытки")
                return

            logger.info(f"🔄 Обработка {len(videos_to_retry)} видео из очереди ожидания")

            # Используем batch обработку для эффективности
            if len(videos_to_retry) > 1:
                batch_stats = await self.bot.batch_processor.process_pending_videos_batch()
                logger.info(f"✅ Batch обработка очереди ожидания: {batch_stats.processed_videos}/{batch_stats.total_videos} успешно")
            else:
                # Для одного видео используем обычную обработку
                for video_data in videos_to_retry:
                    await self.retry_video_processing(video_data)

        except Exception as e:
            logger.error(f"❌ Ошибка обработки очереди ожидания: {e}")

    async def retry_video_processing(self, video_data: Dict):
        """Повторная попытка обработки одного видео"""
        try:
            video_id = video_data['video_id']
            channel_id = video_data['channel_id']
            video_info = video_data['video_info']
            subscribers = video_data['subscribers']
            attempt_count = video_data['attempt_count']
            max_attempts = video_data['max_attempts']

            logger.info(f"🔄 Попытка {attempt_count + 1}/{max_attempts} для видео {video_id}")

            # Создаём URL видео
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            try:
                # Пытаемся получить субтитры
                transcript_data = await self.bot.transcriber.get_transcript(video_url)
                transcript_text = " ".join([item.get('text', '') for item in transcript_data])

                # Субтитры найдены! Создаем сводку
                logger.info(f"✅ Субтитры найдены для видео {video_id} на попытке {attempt_count + 1} "
                          f"(видео '{video_info.get('title', 'Без названия')[:50]}...')")

                # Создаем данные канала для совместимости с существующим кодом
                channel_data = {
                    'channel_name': video_info.get('author', 'Неизвестный канал'),
                    'subscribers': subscribers
                }

                # Создаем и отправляем сводку
                await self._create_and_send_summary_for_pending(
                    video_id, video_url, transcript_text, video_info, channel_data
                )

                # Удаляем видео из очереди ожидания
                await self.bot.db.remove_video_from_queue(video_id)

                logger.info(f"📤 Сводка для видео {video_id} создана и отправлена {len(subscribers)} подписчикам")

            except TranscriptNotFoundError:
                # Субтитры все еще не найдены
                new_attempt_count = attempt_count + 1

                if new_attempt_count >= max_attempts:
                    # Достигнуто максимальное количество попыток
                    logger.warning(f"⏰ Максимальное количество попыток ({max_attempts}) достигнуто для видео {video_id} "
                                 f"('{video_info.get('title', 'Без названия')[:50]}...'), создаем сводку без субтитров")

                    # Создаем сводку без субтитров
                    channel_data = {
                        'channel_name': video_info.get('author', 'Неизвестный канал'),
                        'subscribers': subscribers
                    }

                    await self._create_and_send_summary_for_pending(
                        video_id, video_url, "", video_info, channel_data
                    )

                    # Удаляем видео из очереди ожидания
                    await self.bot.db.remove_video_from_queue(video_id)

                    logger.info(f"📤 Сводка без субтитров для видео {video_id} создана и отправлена {len(subscribers)} подписчикам")
                else:
                    # Обновляем счетчик попыток и планируем следующую попытку
                    await self.bot.db.update_video_attempt(video_id, new_attempt_count)

                    next_delay = self.delay_schedule[min(new_attempt_count - 1, len(self.delay_schedule) - 1)]
                    logger.info(f"📝 Субтитры для видео {video_id} все еще не готовы, следующая попытка через {next_delay} минут")

            except Exception as e:
                logger.error(f"❌ Неожиданная ошибка при повторной попытке для видео {video_id}: {e}")
                # В случае неожиданной ошибки увеличиваем счетчик попыток
                new_attempt_count = attempt_count + 1
                if new_attempt_count < max_attempts:
                    await self.bot.db.update_video_attempt(video_id, new_attempt_count)

        except Exception as e:
            logger.error(f"❌ Ошибка повторной обработки видео {video_data.get('video_id')}: {e}")

    async def _create_and_send_summary_for_pending(self, video_id: str, video_url: str, transcript_text: str, video_info: Dict, channel_data: Dict):
        """
        Создает сводку и отправляет уведомления для видео из очереди ожидания

        Args:
            video_id: ID видео YouTube
            video_url: URL видео
            transcript_text: Текст транскрипции (может быть пустым)
            video_info: Информация о видео
            channel_data: Данные канала с подписчиками
        """
        try:
            # Получаем комментарии
            comments = await self.bot.get_video_comments(video_id, max_comments=30)

            # Создаём сводку через AI
            summary_result = await self.bot.create_summary_with_ai(
                video_url=video_url,
                video_info=video_info,
                comments=comments
            )

            if not summary_result:
                logger.error(f"❌ Не удалось создать сводку для видео {video_id} из очереди ожидания")
                return

            summary = summary_result.get('short_summary', 'Сводка недоступна')
            telegraph_url = summary_result.get('telegraph_url', '')

            # Отправляем уведомления всем подписчикам
            for user_id in channel_data['subscribers']:
                await self._send_new_video_notification(
                    user_id,
                    channel_data['channel_name'],
                    video_info,
                    summary,
                    telegraph_url
                )

        except Exception as e:
            logger.error(f"❌ Ошибка создания и отправки сводки для видео {video_id} из очереди ожидания: {e}")

    async def _send_new_video_notification(self, user_id: int, channel_name: str, video_info: Dict, summary: str, telegraph_url: str):
        """Отправляет уведомление о новом видео пользователю (копия метода из ChannelMonitor)"""
        try:
            video_title = video_info.get('title', 'Без названия')
            video_id = video_info.get('video_id')

            logger.info(f"Формирование уведомления (_send): video_id={video_id}, title={video_title}")

            # Безопасное создание URL с валидацией video_id
            if validate_video_id(video_id):
                video_url = f"https://www.youtube.com/watch?v={video_id}"
                logger.info(f"Сформирован корректный URL (_send): {video_url}")
            else:
                logger.error(f"Некорректный video_id в уведомлении (_send): {video_id}")
                video_url = "https://youtube.com"  # Fallback на главную страницу YouTube
                logger.warning(f"Используется fallback URL (_send): {video_url}")

            # Формируем сообщение в том же формате, что и обычные сводки
            message_text = f"""🎬 На канале <b>{html.escape(channel_name)}</b> вышло новое видео

<a href="{video_url}">{html.escape(video_title)}</a>

📝 <b>Краткая сводка:</b>

{html.escape(summary)}"""

            # Добавляем ссылку на подробную сводку как кликабельный текст (как в обычных сводках)
            if telegraph_url:
                message_text += f"\n\n<a href='{telegraph_url}'><b>📖 Подробная сводка</b></a>"

            # Отправляем сообщение БЕЗ кнопок и БЕЗ превью ссылки
            if hasattr(self.bot, 'application') and self.bot.application:
                await self.bot.application.bot.send_message(
                    chat_id=user_id,
                    text=message_text,
                    parse_mode='HTML',
                    reply_markup=None,  # Убираем кнопки
                    disable_web_page_preview=True  # Убираем превью ссылки
                )
            else:
                logger.error("❌ Application не найден для отправки уведомления")
                return

            logger.info(f"✅ Уведомление о новом видео отправлено пользователю {user_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка отправки уведомления пользователю {user_id}: {e}")


class ChannelMonitor:
    """Класс для фонового мониторинга новых видео на YouTube каналах"""

    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.monitoring_task = None
        self.subtitle_queue_task = None
        self.check_interval = CHANNEL_CHECK_INTERVAL
        self.api_requests_count = 0
        self.api_requests_reset_time = time.time() + 3600
        self.is_running = False
        # Очередь ожидания субтитров больше не нужна

    async def start_monitoring(self):
        """Запускает фоновый мониторинг"""
        if self.is_running:
            logger.warning("🔄 Мониторинг каналов уже запущен")
            return

        logger.info("🔄 Запуск мониторинга каналов...")
        self.is_running = True

        # Запускаем основной мониторинг каналов
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())

        logger.info("✅ Мониторинг каналов запущен")

    async def stop_monitoring(self):
        """Останавливает мониторинг"""
        if not self.is_running:
            return

        logger.info("🛑 Остановка мониторинга каналов...")
        self.is_running = False

        # Останавливаем основной мониторинг
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass

        logger.info("✅ Мониторинг каналов остановлен")

    async def _monitoring_loop(self):
        """Основной цикл мониторинга"""
        while self.is_running:
            try:
                await self.check_new_videos()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Ошибка в цикле мониторинга: {e}")
                await asyncio.sleep(60)  # Ждём минуту при ошибке



    async def check_new_videos(self):
        """Основной метод проверки новых видео"""
        try:
            # Проверяем лимит API запросов
            current_time = time.time()
            if current_time > self.api_requests_reset_time:
                self.api_requests_count = 0
                self.api_requests_reset_time = current_time + 3600

            if self.api_requests_count >= MAX_API_REQUESTS_PER_HOUR:
                logger.warning(f"⚠️ Достигнут лимит API запросов ({MAX_API_REQUESTS_PER_HOUR}/час)")
                return

            # Получаем все активные подписки
            subscriptions = await self.bot.db.get_all_active_subscriptions()

            if not subscriptions:
                logger.debug("📭 Нет активных подписок для мониторинга")
                return

            logger.info(f"🔍 Проверка {len(subscriptions)} активных подписок...")

            # Группируем подписки по каналам для оптимизации
            channels_to_check = {}
            for subscription in subscriptions:
                channel_id = subscription['channel_id']
                if channel_id not in channels_to_check:
                    channels_to_check[channel_id] = {
                        'channel_name': subscription['channel_name'],
                        'last_video_id': subscription['last_video_id'],
                        'subscribers': []
                    }
                channels_to_check[channel_id]['subscribers'].append(subscription['user_id'])

            # Проверяем каждый канал
            for channel_id, channel_data in channels_to_check.items():
                await self.process_channel(channel_id, channel_data)

        except Exception as e:
            logger.error(f"❌ Ошибка проверки новых видео: {e}")

    async def process_channel(self, channel_id: str, channel_data: Dict):
        """Обрабатывает один канал"""
        try:
            # Получаем плейлист uploads
            uploads_playlist_id = await self.bot.get_channel_uploads_playlist(channel_id)
            if not uploads_playlist_id:
                logger.warning(f"⚠️ Не удалось получить плейлист uploads для канала {channel_id}")
                return

            self.api_requests_count += 1

            # Получаем последние видео
            latest_videos = await self.bot.youtube_services.get_latest_videos_from_playlist(uploads_playlist_id, max_results=5)
            if not latest_videos:
                logger.debug(f"📭 Нет новых видео на канале {channel_data['channel_name']}")
                return

            self.api_requests_count += 1

            # Проверяем новые видео
            last_video_id = channel_data['last_video_id']
            new_videos = []

            for video in latest_videos:
                video_id = video.get('video_id')
                if not video_id:
                    continue

                # Если это первая проверка канала
                if not last_video_id:
                    # Обрабатываем только самое новое видео при первой проверке
                    new_videos = [video]
                    break

                # Если нашли последнее обработанное видео, останавливаемся
                if video_id == last_video_id:
                    break

                # Проверяем, не обрабатывали ли мы это видео ранее
                is_processed = await self.bot.db.is_video_processed(channel_id, video_id)
                if not is_processed:
                    new_videos.append(video)

            if new_videos:
                logger.info(f"🎬 Найдено {len(new_videos)} новых видео на канале {channel_data['channel_name']}")

                # Счетчики для статистики
                processed_count = 0
                skipped_short_count = 0
                error_count = 0

                # Обрабатываем новые видео (в обратном порядке - от старых к новым)
                for video in reversed(new_videos):
                    result = await self.process_new_video(channel_id, channel_data, video)
                    if result == 'processed':
                        processed_count += 1
                    elif result == 'skipped_short':
                        skipped_short_count += 1
                    elif result == 'error':
                        error_count += 1

                # Логируем статистику (только если есть что сообщить)
                if processed_count > 0:
                    if skipped_short_count > 0 or error_count > 0:
                        logger.info(f"📊 {channel_data['channel_name']}: {processed_count} обработано, {skipped_short_count} пропущено, {error_count} ошибок")
                    else:
                        logger.debug(f"✅ {channel_data['channel_name']}: {processed_count} видео обработано")

                # Обновляем last_video_id
                newest_video_id = new_videos[0].get('video_id')
                if newest_video_id:
                    await self.bot.db.update_last_video_check(channel_id, newest_video_id)

        except Exception as e:
            logger.error(f"❌ Ошибка обработки канала {channel_id}: {e}")

    @trace_function("process_new_video")
    async def process_new_video(self, channel_id: str, channel_data: Dict, video_info: Dict) -> str:
        """
        Обрабатывает новое видео (создает сводку и отправляет)

        Автоматически фильтрует короткие видео (шортсы) длительностью менее MINIMUM_VIDEO_DURATION секунд.
        Такие видео пропускаются и не обрабатываются.

        Args:
            channel_id: ID канала YouTube
            channel_data: Данные канала включая подписчиков
            video_info: Информация о видео включая duration в формате ISO 8601

        Returns:
            'processed' - видео обработано успешно
            'skipped_short' - видео пропущено (короткое, менее MINIMUM_VIDEO_DURATION секунд)
            'error' - ошибка обработки
        """
        video_id = video_info.get('video_id')
        video_title = video_info.get('title', 'Без названия')
        published_at = video_info.get('published_at', '')

        # Краткое логирование начала обработки (только для отладки)
        video_logger.debug(
            EventType.VIDEO_PROCESSING_START,
            f"Обработка видео: {video_title[:30]}...",
            context={
                "video_id": video_id,
                "channel_id": channel_id
            }
        )

        # Обновляем метрики
        metrics_collector.increment_counter("videos_processing_started")

        try:

            logger.info(f"🎬 Обработка нового видео: {video_title} ({video_id})")

            # Проверяем длительность видео (фильтруем шортсы)
            duration_str = video_info.get('duration')
            if duration_str:
                duration_seconds = self.bot.youtube_services.parse_iso8601_duration(duration_str)
                if duration_seconds is not None and duration_seconds < MINIMUM_VIDEO_DURATION:
                    video_logger.info(
                        EventType.VIDEO_PROCESSING_END,
                        f"Пропущено короткое видео: {video_title}",
                        context={
                            "video_id": video_id,
                            "duration_seconds": duration_seconds,
                            "minimum_duration": MINIMUM_VIDEO_DURATION,
                            "reason": "short_video"
                        }
                    )
                    metrics_collector.increment_counter("videos_skipped_short")
                    return 'skipped_short'
                elif duration_seconds is not None:
                    video_logger.debug(
                        EventType.VIDEO_PROCESSING_START,
                        f"Длительность видео проверена: {duration_seconds}с",
                        context={"video_id": video_id, "duration_seconds": duration_seconds}
                    )

            # Отмечаем видео как обрабатываемое
            await self.bot.db.mark_video_processed(channel_id, video_id, video_title, published_at)

            # Создаём URL видео с валидацией
            if not validate_video_id(video_id):
                video_logger.error(
                    EventType.VIDEO_PROCESSING_ERROR,
                    f"Некорректный video_id при обработке видео",
                    context={
                        "video_id": video_id,
                        "video_title": video_title,
                        "error_type": "invalid_video_id"
                    }
                )
                metrics_collector.increment_counter("videos_processing_errors", labels={"error_type": "invalid_video_id"})
                await alerting_system.send_alert(
                    Alert(
                        id=f"invalid_video_id_{video_id}",
                        title="Некорректный video_id",
                        message=f"Обнаружен некорректный video_id: {video_id}",
                        severity=AlertSeverity.ERROR,
                        component="video_processing",
                        timestamp=datetime.now(),
                        details={"video_id": video_id, "video_title": video_title}
                    )
                )
                return 'error_invalid_video_id'

            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # Проверяем, есть ли уже сводка в базе данных
            existing_summary = await self.bot.db.get_video_summary(video_id)
            if existing_summary:
                logger.info(f"✅ Сводка для видео {video_id} уже существует, используем её")
                summary = existing_summary['short_summary']
                telegraph_url = existing_summary['telegraph_url']
            else:
                # Создаём новую сводку через Gemini с прикрепленным видео
                logger.info(f"🤖 Создание сводки для видео {video_id} через Gemini...")

                video_logger.info(
                    EventType.SUMMARY_GENERATION,
                    f"Начало создания сводки для видео {video_id}",
                    context={"video_id": video_id, "video_url": video_url}
                )

                await self._create_and_send_summary(
                    video_id, video_url, "", channel_data
                )

            video_logger.info(
                EventType.VIDEO_PROCESSING_END,
                f"✅ Видео обработано",
                context={"video_id": video_id}
            )
            metrics_collector.increment_counter("videos_processed_success")
            return 'processed'

        except Exception as e:
            video_logger.error(
                EventType.VIDEO_PROCESSING_ERROR,
                f"Критическая ошибка обработки видео",
                context={
                    "video_id": video_info.get('video_id'),
                    "video_title": video_info.get('title', 'Unknown'),
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                },
                error_details={"exception": str(e)}
            )
            metrics_collector.increment_counter("videos_processing_errors", labels={"error_type": "critical_error"})

            # Отправляем критический алерт
            await alerting_system.send_alert(
                Alert(
                    id=f"video_processing_critical_{video_info.get('video_id')}",
                    title="Критическая ошибка обработки видео",
                    message=f"Не удалось обработать видео {video_info.get('video_id')}: {str(e)}",
                    severity=AlertSeverity.CRITICAL,
                    component="video_processing",
                    timestamp=datetime.now(),
                    details={
                        "video_id": video_info.get('video_id'),
                        "video_title": video_info.get('title', 'Unknown'),
                        "error": str(e)
                    },
                    recovery_suggestions=[
                        "Проверить логи для детальной информации",
                        "Проверить доступность YouTube API",
                        "Проверить состояние базы данных"
                    ]
                )
            )
            return 'error'

    async def _create_and_send_summary(self, video_id: str, video_url: str, transcript_text: str, channel_data: Dict):
        """
        Создает сводку и отправляет уведомления подписчикам

        Args:
            video_id: ID видео YouTube
            video_url: URL видео
            transcript_text: Текст транскрипции (может быть пустым)
            channel_data: Данные канала с подписчиками
        """
        try:
            # Получаем информацию о видео
            video_info_detailed = await self.bot.get_video_info(video_url)

            # Получаем комментарии
            comments = await self.bot.get_video_comments(video_id, max_comments=30)

            # Создаём сводку через AI
            summary_result = await self.bot.create_summary_with_ai(
                video_url=video_url,
                video_info=video_info_detailed,
                comments=comments
            )

            if not summary_result:
                logger.error(f"❌ Не удалось создать сводку для видео {video_id}")
                return

            summary = summary_result.get('short_summary', 'Сводка недоступна')
            telegraph_url = summary_result.get('telegraph_url', '')

            # Отправляем уведомления всем подписчикам
            for user_id in channel_data['subscribers']:
                await self.send_new_video_notification(
                    user_id,
                    channel_data['channel_name'],
                    video_info_detailed,  # Используем подробную информацию о видео
                    summary,
                    telegraph_url
                )

        except Exception as e:
            logger.error(f"❌ Ошибка создания и отправки сводки для видео {video_id}: {e}")

    async def send_new_video_notification(self, user_id: int, channel_name: str, video_info: Dict, summary: str, telegraph_url: str):
        """Отправляет уведомление о новом видео пользователю"""
        try:
            video_title = video_info.get('title', 'Без названия')
            video_id = video_info.get('video_id')

            logger.info(f"Формирование уведомления: video_id={video_id}, title={video_title}")

            # Безопасное создание URL с валидацией video_id
            if validate_video_id(video_id):
                video_url = f"https://www.youtube.com/watch?v={video_id}"
                logger.info(f"Сформирован корректный URL: {video_url}")
            else:
                logger.error(f"Некорректный video_id в уведомлении: {video_id}")
                video_url = "https://youtube.com"  # Fallback на главную страницу YouTube
                logger.warning(f"Используется fallback URL: {video_url}")

            # Формируем сообщение в том же формате, что и обычные сводки
            message_text = f"""🎬 На канале <b>{html.escape(channel_name)}</b> вышло новое видео

<a href="{video_url}">{html.escape(video_title)}</a>

📝 <b>Краткая сводка:</b>

{html.escape(summary)}"""

            # Добавляем ссылку на подробную сводку как кликабельный текст (как в обычных сводках)
            if telegraph_url:
                message_text += f"\n\n<a href='{telegraph_url}'><b>📖 Подробная сводка</b></a>"

            # Отправляем сообщение БЕЗ кнопок и БЕЗ превью ссылки
            from telegram.ext import ContextTypes

            # Получаем application из бота
            if hasattr(self.bot, 'application') and self.bot.application:
                await self.bot.application.bot.send_message(
                    chat_id=user_id,
                    text=message_text,
                    parse_mode='HTML',
                    reply_markup=None,  # Убираем кнопки
                    disable_web_page_preview=True  # Убираем превью ссылки
                )
            else:
                logger.error("❌ Application не найден для отправки уведомления")
                return

            logger.info(f"✅ Уведомление о новом видео отправлено пользователю {user_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка отправки уведомления пользователю {user_id}: {e}")


class YouTubeSummaryBot:
    """ПОЛНОСТЬЮ АСИНХРОННЫЙ телеграм бот для транскрипции YouTube"""

    def __init__(self):
        # Импортируем VideoDatabase локально, чтобы избежать конфликтов с другими database.py файлами
        import sys
        import os
        from pathlib import Path

        # Добавляем текущую папку в sys.path для корректного импорта
        current_dir = Path(__file__).parent
        if str(current_dir) not in sys.path:
            sys.path.insert(0, str(current_dir))

        # Импортируем VideoDatabase из локального файла yt_database.py
        from yt_database import VideoDatabase
        # Импортируем BatchVideoProcessor для эффективной обработки множественных видео
        from batch_video_processor import BatchVideoProcessor
        # ПОЛНОСТЬЮ АСИНХРОННЫЙ транскрибер с увеличенным пулом воркеров
        self.transcriber = YouTubeTranscriber(max_workers=50)  # Увеличиваем до 50 воркеров
        self.current_api_key_index = 0
        self._session_no_proxy = None  # Сессия без прокси для Gemini API
        # БЕЗ БЛОКИРОВОК - полностью асинхронно
        self.user_transcripts = {}  # Словарь для хранения транскрипций пользователей
        self.user_conversations = {}  # Словарь для хранения истории диалогов
        self.user_video_info = {}  # Словарь для хранения информации о видео для каждого пользователя
        self.user_comments = {}  # Словарь для хранения комментариев к видео для каждого пользователя
        self.telegraph = None  # Telegraph клиент
        self.telegraph_token = TELEGRAPH_ACCESS_TOKEN
        # Счетчик активных запросов для мониторинга
        self.active_requests = 0
        # База данных для хранения сводок (инициализируем после импорта)
        self.db = VideoDatabase("yt_video_summaries.db")  # Уникальное имя для YouTube бота
        # Убираем глобальные переменные для информации о видео
        # Теперь информация передается напрямую в функции

        # Новые переменные для подписок
        self.user_states = {}  # Словарь состояний пользователей
        self.channel_monitor = None  # Будет инициализирован позже

        # Инициализация метрик API
        self.api_metrics = APIMetrics()
        logger.info("📊 Инициализированы метрики API")

        # Инициализация YouTube сервисов
        self.youtube_services = YouTubeServices(self.api_metrics)
        logger.info("🔧 Инициализированы YouTube сервисы")

        # Gemini клиенты создаются по требованию для оптимизации запуска
        logger.info(f"✅ Готово к работе с {len(GEMINI_API_KEYS)} Gemini API ключами")

        # Инициализация системы мониторинга
        self.health_check_system = EnhancedHealthCheckSystem()
        self._setup_monitoring_system()

        monitoring_logger.info(
            EventType.HEALTH_CHECK,
            "Инициализирована система мониторинга YouTube бота",
            context={"bot_initialized": True}
        )

        # Инициализация BatchVideoProcessor для эффективной обработки множественных видео
        self.batch_processor = BatchVideoProcessor(
            bot_instance=self,
            max_concurrent_videos=5,  # Максимум 5 видео одновременно
            max_concurrent_api_requests=3  # Максимум 3 API запроса одновременно
        )
        logger.info("🔄 Инициализирован BatchVideoProcessor")

        # Создаем папку для временных файлов
        self._ensure_temp_directory()

    def _ensure_temp_directory(self):
        """Создает папку temp если она не существует"""
        temp_dir = "temp"
        try:
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir, exist_ok=True)
                logger.info(f"📁 Создана папка для временных файлов: {temp_dir}")
            else:
                logger.debug(f"📁 Папка для временных файлов уже существует: {temp_dir}")

            # Проверяем права доступа
            if not os.access(temp_dir, os.W_OK):
                logger.error(f"❌ Нет прав записи в папку: {temp_dir}")
            else:
                logger.debug(f"✅ Права записи в папку {temp_dir} подтверждены")

        except Exception as e:
            logger.error(f"❌ Ошибка создания папки для временных файлов: {e}")
            logger.error(f"❌ Текущая директория: {os.getcwd()}")
            raise

    @property
    def youtube_api_key(self) -> str:
        """Возвращает YouTube API ключ для использования в BatchVideoProcessor"""
        return YOUTUBE_DATA_API_KEY

    def _is_group_chat(self, update: Update) -> bool:
        """Проверяет, является ли чат группой или супергруппой"""
        return update.effective_chat.type in [ChatType.GROUP, ChatType.SUPERGROUP]

    async def handle_video_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """
        Обработчик видео сообщений для групповых чатов

        Этапы 1-4: Полная обработка видео файлов через Gemini API с интегрированным логированием и метриками
        """
        correlation_id = str(uuid.uuid4())
        start_time = time.time()

        try:
            # Проверяем, что это групповой чат
            if not self._is_group_chat(update):
                video_logger.debug(
                    EventType.VIDEO_PROCESSING_START,
                    "Видео сообщение получено в личном чате - игнорируем",
                    correlation_id=correlation_id
                )
                return

            # Получаем информацию о видео
            video = update.message.video
            user_id = update.effective_user.id
            chat_id = update.effective_chat.id

            # Структурированное логирование начала обработки
            video_logger.info(
                EventType.VIDEO_PROCESSING_START,
                "Начало обработки видео файла в группе",
                context={
                    "chat_id": chat_id,
                    "user_id": user_id,
                    "file_size": video.file_size,
                    "duration": video.duration,
                    "mime_type": video.mime_type
                },
                correlation_id=correlation_id
            )

            # Обновляем метрики
            metrics_collector.increment_counter("video_files_received")
            metrics_collector.set_gauge("video_file_size_bytes", video.file_size)
            metrics_collector.set_gauge("video_duration_seconds", video.duration)

            # Проверяем ограничения размера и длительности
            if video.file_size > MAX_VIDEO_FILE_SIZE:
                video_logger.warning(
                    EventType.VIDEO_PROCESSING_ERROR,
                    "Видео файл превышает максимальный размер",
                    context={
                        "file_size": video.file_size,
                        "max_size": MAX_VIDEO_FILE_SIZE,
                        "chat_id": chat_id,
                        "reason": "file_too_large"
                    },
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("video_files_rejected", labels={"reason": "file_too_large"})
                return

            if video.duration > MAX_VIDEO_DURATION:
                video_logger.warning(
                    EventType.VIDEO_PROCESSING_ERROR,
                    "Видео файл превышает максимальную длительность",
                    context={
                        "duration": video.duration,
                        "max_duration": MAX_VIDEO_DURATION,
                        "chat_id": chat_id,
                        "reason": "duration_too_long"
                    },
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("video_files_rejected", labels={"reason": "duration_too_long"})
                return

            # Проверяем поддерживаемые форматы видео
            if video.mime_type and video.mime_type not in SUPPORTED_VIDEO_MIME_TYPES:
                video_logger.warning(
                    EventType.VIDEO_PROCESSING_ERROR,
                    "Неподдерживаемый формат видео файла",
                    context={
                        "mime_type": video.mime_type,
                        "supported_types": SUPPORTED_VIDEO_MIME_TYPES,
                        "chat_id": chat_id,
                        "reason": "unsupported_format"
                    },
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("video_files_rejected", labels={"reason": "unsupported_format"})
                return

            # Этап 2: Скачивание и загрузка видео в Gemini API
            download_start_time = time.time()
            local_file_path = await self.download_video_file(video, context)
            if not local_file_path:
                video_logger.error(
                    EventType.VIDEO_PROCESSING_ERROR,
                    "Не удалось скачать видео файл",
                    context={"chat_id": chat_id, "stage": "download_failed"},
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("video_download_failures")
                return

            download_time = time.time() - download_start_time
            metrics_collector.set_gauge("video_download_time_seconds", download_time)
            metrics_collector.increment_counter("video_downloads_success")

            upload_start_time = time.time()
            gemini_file_uri, gemini_api_key = await self.upload_video_to_gemini(local_file_path)
            if not gemini_file_uri:
                video_logger.error(
                    EventType.VIDEO_PROCESSING_ERROR,
                    "Не удалось загрузить видео в Gemini API",
                    context={"chat_id": chat_id, "stage": "upload_failed"},
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("gemini_upload_failures")
                # Удаляем локальный файл при ошибке
                await self.cleanup_temp_file(local_file_path)
                return

            upload_time = time.time() - upload_start_time
            metrics_collector.set_gauge("gemini_upload_time_seconds", upload_time)
            metrics_collector.increment_counter("gemini_uploads_success")

            # Этап 3: Обработка видео через Gemini и отправка ответа
            logger.info(f"📹 Видео успешно загружено в Gemini: {gemini_file_uri} (API ключ: {gemini_api_key[:10]}...)")

            # Обрабатываем видео через Gemini API с тем же API ключом
            processing_start_time = time.time()
            video_description = await self.process_video_with_gemini(gemini_file_uri, gemini_api_key)
            processing_time = time.time() - processing_start_time
            metrics_collector.set_gauge("gemini_processing_time_seconds", processing_time)

            if video_description:
                # Отправляем описание как ответ на оригинальное сообщение
                await update.message.reply_text(video_description)

                video_logger.info(
                    EventType.VIDEO_PROCESSING_END,
                    "Описание видео успешно отправлено",
                    context={
                        "chat_id": chat_id,
                        "description_length": len(video_description),
                        "total_processing_time": time.time() - start_time
                    },
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("video_processing_success")
                logger.info(f"✅ Описание видео отправлено в группу {chat_id}")
            else:
                video_logger.error(
                    EventType.VIDEO_PROCESSING_ERROR,
                    "Не удалось получить описание видео от Gemini",
                    context={"chat_id": chat_id, "stage": "gemini_processing_failed"},
                    correlation_id=correlation_id
                )
                metrics_collector.increment_counter("gemini_processing_failures")
                logger.error("❌ Не удалось получить описание видео от Gemini")

            # Очистка файлов после обработки
            await self.cleanup_temp_file(local_file_path)
            await self.delete_gemini_file(gemini_file_uri, gemini_api_key)

        except Exception as e:
            # Детализированная обработка ошибок с метриками и логированием
            processing_time = time.time() - start_time

            # Определяем тип ошибки для метрик
            error_type = type(e).__name__

            # Структурированное логирование ошибки
            video_logger.error(
                EventType.VIDEO_PROCESSING_ERROR,
                "Критическая ошибка при обработке видео файла",
                context={
                    "chat_id": update.effective_chat.id if update.effective_chat else None,
                    "user_id": update.effective_user.id if update.effective_user else None,
                    "error_type": error_type,
                    "error_message": str(e),
                    "processing_time_seconds": processing_time,
                    "stage": "video_file_processing"
                },
                correlation_id=correlation_id,
                error_details={"exception": str(e)}
            )

            # Обновляем метрики ошибок
            metrics_collector.increment_counter("video_processing_errors", labels={"error_type": error_type})
            metrics_collector.increment_counter("video_files_failed")
            metrics_collector.set_gauge("video_processing_time_seconds", processing_time)

            # Тихая обработка - не отправляем сообщения в чат
            logger.error(f"❌ Ошибка при обработке видео сообщения: {e}")

        finally:
            # Финальные метрики времени обработки
            total_processing_time = time.time() - start_time
            metrics_collector.set_gauge("video_processing_total_time_seconds", total_processing_time)

            video_logger.debug(
                EventType.VIDEO_PROCESSING_END,
                "Завершение обработки видео файла",
                context={
                    "total_processing_time_seconds": total_processing_time,
                    "chat_id": update.effective_chat.id if update.effective_chat else None
                },
                correlation_id=correlation_id
            )

    async def download_video_file(self, video, context: ContextTypes.DEFAULT_TYPE) -> Optional[str]:
        """
        Скачивает видео файл из Telegram во временную папку

        Args:
            video: Объект Video из Telegram
            context: Контекст бота

        Returns:
            Путь к скачанному файлу или None при ошибке
        """
        try:
            # Создаем папку temp если она не существует
            temp_dir = "temp"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir, exist_ok=True)
                logger.info(f"📁 Создана папка для временных файлов: {temp_dir}")

            # Генерируем уникальное имя файла
            file_id = video.file_id
            file_extension = ".mp4"  # По умолчанию mp4

            # Пытаемся определить расширение по MIME типу
            if video.mime_type:
                extension = mimetypes.guess_extension(video.mime_type)
                if extension:
                    file_extension = extension

            unique_filename = f"video_{uuid.uuid4().hex}{file_extension}"
            local_file_path = os.path.join(temp_dir, unique_filename)

            logger.info(f"📥 Скачивание видео файла: {file_id} -> {local_file_path}")

            # Получаем файл через Telegram API
            file = await context.bot.get_file(file_id)

            # Скачиваем файл
            await file.download_to_drive(local_file_path)

            # Проверяем, что файл действительно скачался
            if os.path.exists(local_file_path):
                file_size = os.path.getsize(local_file_path)
                logger.info(f"✅ Видео файл скачан: {local_file_path} ({file_size} байт)")
                return local_file_path
            else:
                logger.error(f"❌ Файл не найден после скачивания: {local_file_path}")
                return None

        except Exception as e:
            # Детализированное логирование ошибки скачивания
            error_type = type(e).__name__

            # Дополнительная информация о состоянии файловой системы
            temp_dir_exists = os.path.exists("temp")
            temp_dir_writable = os.access("temp", os.W_OK) if temp_dir_exists else False
            current_dir = os.getcwd()

            video_logger.error(
                EventType.VIDEO_PROCESSING_ERROR,
                "Ошибка скачивания видео файла из Telegram",
                context={
                    "file_id": video.file_id if hasattr(video, 'file_id') else None,
                    "file_size": video.file_size if hasattr(video, 'file_size') else None,
                    "mime_type": video.mime_type if hasattr(video, 'mime_type') else None,
                    "error_type": error_type,
                    "error_message": str(e),
                    "stage": "download",
                    "temp_dir_exists": temp_dir_exists,
                    "temp_dir_writable": temp_dir_writable,
                    "current_directory": current_dir,
                    "local_file_path": locals().get('local_file_path', 'not_set')
                },
                error_details={"exception": str(e)}
            )

            # Метрики ошибок скачивания
            metrics_collector.increment_counter("video_download_errors", labels={"error_type": error_type})

            logger.error(f"❌ Ошибка скачивания видео файла: {e}")
            logger.error(f"❌ Дополнительная информация: temp_dir_exists={temp_dir_exists}, temp_dir_writable={temp_dir_writable}, current_dir={current_dir}")
            return None

    async def upload_video_to_gemini(self, file_path: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Загружает видео файл в Gemini Files API

        Args:
            file_path: Путь к локальному видео файлу

        Returns:
            Кортеж (file_uri, api_key) при успешной загрузке или (None, None) при ошибке
        """
        try:
            # Определяем MIME тип файла
            mime_type, _ = mimetypes.guess_type(file_path)
            if not mime_type or mime_type not in SUPPORTED_VIDEO_MIME_TYPES:
                # Используем mp4 по умолчанию для видео
                mime_type = "video/mp4"

            logger.info(f"📤 Загрузка видео в Gemini Files API: {file_path} (MIME: {mime_type})")

            # Получаем размер файла
            file_size = os.path.getsize(file_path)

            # Пробуем разные API ключи
            for attempt, api_key in enumerate(GEMINI_API_KEYS):
                try:
                    # Создаем клиент для текущего API ключа по требованию
                    try:
                        client = genai.Client(api_key=api_key)
                    except Exception as e:
                        logger.warning(f"⚠️ Ошибка создания клиента для ключа {api_key[:10]}...: {e}")
                        continue

                    # Загружаем файл в Gemini с использованием нового SDK
                    logger.info(f"📤 Загрузка файла через SDK: {file_path}")

                    # Выполняем загрузку в отдельном потоке, так как SDK синхронный
                    import asyncio
                    import functools

                    upload_func = functools.partial(
                        client.files.upload,
                        file=file_path
                    )

                    # Запускаем в executor для асинхронности
                    loop = asyncio.get_event_loop()
                    file_obj = await loop.run_in_executor(None, upload_func)

                    if file_obj and file_obj.uri:
                        logger.info(f"✅ Видео успешно загружено в Gemini: {file_obj.uri} (API ключ: {api_key[:10]}...)")
                        return file_obj.uri, api_key
                    else:
                        logger.warning(f"⚠️ Не удалось получить URI файла")
                        continue

                except Exception as e:
                    error_type = type(e).__name__
                    video_logger.warning(
                        EventType.API_REQUEST_ERROR,
                        f"Попытка {attempt + 1} загрузки в Gemini неудачна",
                        context={
                            "attempt": attempt + 1,
                            "total_attempts": len(GEMINI_API_KEYS),
                            "error_type": error_type,
                            "error_message": str(e),
                            "stage": "gemini_upload"
                        }
                    )
                    metrics_collector.increment_counter("gemini_upload_retry_attempts", labels={"error_type": error_type})
                    continue

            # Все попытки провалились
            video_logger.error(
                EventType.VIDEO_PROCESSING_ERROR,
                "Все попытки загрузки видео в Gemini провалились",
                context={
                    "file_path": file_path,
                    "mime_type": mime_type,
                    "file_size": file_size,
                    "total_attempts": len(GEMINI_API_KEYS),
                    "stage": "gemini_upload_failed"
                }
            )
            metrics_collector.increment_counter("gemini_upload_total_failures")
            logger.error("❌ Все попытки загрузки видео в Gemini провалились")
            return None, None

        except Exception as e:
            error_type = type(e).__name__
            video_logger.error(
                EventType.VIDEO_PROCESSING_ERROR,
                "Критическая ошибка загрузки видео в Gemini",
                context={
                    "file_path": file_path,
                    "error_type": error_type,
                    "error_message": str(e),
                    "stage": "gemini_upload_critical"
                },
                error_details={"exception": str(e)}
            )
            metrics_collector.increment_counter("gemini_upload_critical_errors", labels={"error_type": error_type})
            logger.error(f"❌ Ошибка загрузки видео в Gemini: {e}")
            return None, None

    async def _upload_file_to_gemini_with_key(self, file_path: str, mime_type: str, file_size: int, api_key: str) -> Optional[str]:
        """
        Загружает файл в Gemini с конкретным API ключом

        Args:
            file_path: Путь к файлу
            mime_type: MIME тип файла
            file_size: Размер файла в байтах
            api_key: API ключ Gemini

        Returns:
            URI файла в Gemini или None при ошибке
        """
        try:
            session = await self.get_session()

            # Создаем метаданные файла
            metadata = {
                "file": {
                    "display_name": os.path.basename(file_path),
                    "mime_type": mime_type
                }
            }

            # Первый запрос - создание resumable upload session
            headers = {
                "X-Goog-Upload-Protocol": "resumable",
                "X-Goog-Upload-Command": "start",
                "X-Goog-Upload-Header-Content-Length": str(file_size),
                "X-Goog-Upload-Header-Content-Type": mime_type,
                "Content-Type": "application/json"
            }

            url = f"{GEMINI_FILES_API_URL}?key={api_key}"

            async with session.post(url, headers=headers, json=metadata) as response:
                if response.status != 200:
                    logger.error(f"❌ Ошибка создания upload session: {response.status}")
                    return None

                upload_url = response.headers.get("X-Goog-Upload-URL")
                if not upload_url:
                    logger.error("❌ Не получен upload URL")
                    return None

            # Второй запрос - загрузка файла
            with open(file_path, 'rb') as f:
                file_data = f.read()

            upload_headers = {
                "X-Goog-Upload-Offset": "0",
                "X-Goog-Upload-Command": "upload, finalize",
                "Content-Length": str(file_size)
            }

            async with session.put(upload_url, headers=upload_headers, data=file_data) as response:
                if response.status != 200:
                    logger.error(f"❌ Ошибка загрузки файла: {response.status}")
                    return None

                result = await response.json()
                file_uri = result.get("file", {}).get("uri")

                if file_uri:
                    logger.info(f"✅ Файл загружен в Gemini: {file_uri}")
                    return file_uri
                else:
                    logger.error("❌ Не получен URI файла")
                    return None

        except Exception as e:
            logger.error(f"❌ Ошибка загрузки файла в Gemini: {e}")
            return None

    async def _wait_for_file_active(self, file_uri: str, api_key: str, max_wait_time: int = 300) -> bool:
        """
        Ожидает, пока файл станет активным в Gemini

        Args:
            file_uri: URI файла в Gemini
            api_key: API ключ
            max_wait_time: Максимальное время ожидания в секундах

        Returns:
            True если файл стал активным, False при таймауте или ошибке
        """
        try:
            # Создаем клиент для данного API ключа по требованию
            try:
                client = genai.Client(api_key=api_key)
            except Exception as e:
                logger.error(f"❌ Ошибка создания клиента для ключа {api_key[:10]}...: {e}")
                return False

            # Извлекаем имя файла из URI
            file_name = file_uri.split("/")[-1]

            start_time = time.time()
            while time.time() - start_time < max_wait_time:
                try:
                    # Получаем информацию о файле через SDK
                    file_info = client.files.get(name=f"files/{file_name}")

                    if file_info.state == "ACTIVE":
                        logger.info(f"✅ Файл стал активным: {file_uri}")
                        return True
                    elif file_info.state == "FAILED":
                        logger.error(f"❌ Файл не удалось обработать: {file_uri}")
                        return False
                    else:
                        logger.info(f"⏳ Файл в состоянии {file_info.state}, ожидаем...")
                        await asyncio.sleep(5)

                except Exception as e:
                    logger.warning(f"⚠️ Ошибка проверки статуса файла: {e}")
                    await asyncio.sleep(5)

            logger.warning(f"⏰ Таймаут ожидания активации файла: {file_uri}")
            return False

        except Exception as e:
            logger.error(f"❌ Ошибка ожидания активации файла: {e}")
            return False

    def get_next_api_key(self) -> str:
        """Получает следующий API ключ из списка"""
        api_key = GEMINI_API_KEYS[self.current_api_key_index]
        self.current_api_key_index = (self.current_api_key_index + 1) % len(GEMINI_API_KEYS)
        return api_key

    async def cleanup_temp_file(self, file_path: str) -> None:
        """
        Удаляет временный файл

        Args:
            file_path: Путь к файлу для удаления
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"🗑️ Временный файл удален: {file_path}")
            else:
                logger.debug(f"🗑️ Файл уже не существует: {file_path}")
        except Exception as e:
            logger.error(f"❌ Ошибка удаления временного файла {file_path}: {e}")

    async def delete_gemini_file(self, file_uri: str, api_key: Optional[str] = None) -> bool:
        """
        Удаляет файл из Gemini Files API

        Args:
            file_uri: URI файла в Gemini
            api_key: Опциональный API ключ для использования конкретного ключа.
                    Если не передан, используется ротация ключей (обратная совместимость)

        Returns:
            True если файл удален успешно, False при ошибке
        """
        try:
            # Извлекаем имя файла из URI
            file_name = file_uri.split("/")[-1]

            # Если передан конкретный API ключ, используем только его
            if api_key:
                logger.info(f"🔑 Удаляем файл с переданным API ключом: {api_key[:10]}...")
                return await self._delete_gemini_file_with_key(file_name, api_key)
            else:
                # Иначе пробуем разные API ключи (обратная совместимость)
                logger.info("🔄 Удаляем файл с ротацией API ключей")
                for current_api_key in GEMINI_API_KEYS:
                    if await self._delete_gemini_file_with_key(file_name, current_api_key):
                        return True

                logger.error(f"❌ Не удалось удалить файл из Gemini: {file_uri}")
                return False

        except Exception as e:
            logger.error(f"❌ Ошибка удаления файла из Gemini: {e}")
            return False

    async def _delete_gemini_file_with_key(self, file_name: str, api_key: str) -> bool:
        """
        Удаляет файл из Gemini с конкретным API ключом

        Args:
            file_name: Имя файла в Gemini (например, 'files/abc123')
            api_key: API ключ

        Returns:
            True если файл удален успешно, False при ошибке
        """
        try:
            # Создаем клиент для API ключа по требованию
            try:
                client = genai.Client(api_key=api_key)
            except Exception as e:
                logger.warning(f"⚠️ Ошибка создания клиента для ключа {api_key[:10]}...: {e}")
                return False

            # Выполняем удаление в отдельном потоке, так как SDK синхронный
            import asyncio
            import functools

            delete_func = functools.partial(client.files.delete, name=file_name)

            # Запускаем в executor для асинхронности
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, delete_func)

            logger.info(f"🗑️ Файл удален из Gemini: {file_name}")
            return True

        except Exception as e:
            logger.error(f"❌ Ошибка удаления файла из Gemini: {e}")
            return False

    async def process_video_with_gemini(self, file_uri: str, api_key: Optional[str] = None) -> Optional[str]:
        """
        Обрабатывает видео через Gemini API и получает краткое описание

        Args:
            file_uri: URI файла в Gemini Files API
            api_key: Опциональный API ключ для использования конкретного ключа.
                    Если не передан, используется ротация ключей (обратная совместимость)

        Returns:
            Краткое описание видео или None при ошибке
        """
        try:
            logger.info(f"🤖 Отправка видео в Gemini для анализа: {file_uri}")

            # Системный промпт для краткого описания видео
            prompt = "Создайте лаконичное описание содержания видео для быстрого ознакомления. Начните с релевантного эмодзи. Изложите ключевые моменты в деловом стиле без излишних подробностей, при этом в неформальном стиле общения и с матом, остроумной шуткой черного юмора. Максимум 2 предложения на русском языке без форматирования."

            # Если передан конкретный API ключ, используем только его
            if api_key:
                api_keys_to_try = [api_key]
                logger.info(f"🔑 Используем переданный API ключ: {api_key[:10]}...")
            else:
                # Иначе пробуем разные API ключи в случае ошибки (обратная совместимость)
                api_keys_to_try = GEMINI_API_KEYS
                logger.info("🔄 Используем ротацию API ключей")

            for attempt, current_api_key in enumerate(api_keys_to_try):
                try:
                    # Создаем клиент для текущего API ключа по требованию
                    try:
                        client = genai.Client(api_key=current_api_key)
                    except Exception as e:
                        logger.warning(f"⚠️ Ошибка создания клиента для ключа {current_api_key[:10]}...: {e}")
                        continue

                    # ВАЖНО: Ждем пока файл станет активным перед использованием
                    logger.info(f"⏳ Ожидание активации файла перед обработкой: {file_uri}")
                    file_is_active = await self._wait_for_file_active(file_uri, current_api_key, max_wait_time=120)
                    if not file_is_active:
                        logger.error(f"❌ Файл не стал активным за отведенное время: {file_uri}")
                        continue

                    # Создаем объект файла из URI
                    from google.genai import types
                    file_part = types.Part.from_uri(file_uri=file_uri, mime_type="video/mp4")

                    # Генерируем контент с использованием нового SDK
                    response = client.models.generate_content(
                        model="gemini-2.5-flash-lite",
                        contents=[prompt, file_part],
                        config=types.GenerateContentConfig(
                            temperature=0.7,
                            max_output_tokens=64000,  # Увеличиваем лимит токенов
                            thinking_config=types.ThinkingConfig(
                                thinking_budget=24576,
                                include_thoughts=False
                            )
                        )
                    )

                    # Проверяем ответ
                    if response.text:
                        logger.info(f"✅ Получено описание видео от Gemini")
                        return response.text.strip()
                    else:
                        logger.warning(f"⚠️ Пустой ответ от Gemini")
                        continue

                except Exception as e:
                    error_msg = str(e)
                    if "429" in error_msg or "quota" in error_msg.lower():
                        # Превышен лимит запросов, пробуем следующий ключ
                        logger.warning(f"⚠️ API ключ {current_api_key[:10]}... превысил лимит, пробуем следующий")
                        continue
                    else:
                        logger.error(f"❌ Ошибка при запросе к Gemini API: {error_msg}")
                        continue

            logger.error("❌ Все попытки обработки видео через Gemini провалились")
            return None

        except Exception as e:
            logger.error(f"❌ Ошибка при обработке видео через Gemini: {e}")
            return None

    # validate_video_id и create_safe_video_url теперь импортированы как standalone функции из config_and_utils

    async def get_session(self) -> aiohttp.ClientSession:
        """Получает или создает HTTP сессию БЕЗ прокси для Gemini API - ПОЛНОСТЬЮ АСИНХРОННО"""
        if self._session_no_proxy is None or self._session_no_proxy.closed:
            # УБИРАЕМ LOCK - создаем сессию БЕЗ таймаутов для полной асинхронности
            # Создаем обычную сессию БЕЗ прокси для Gemini API БЕЗ ТАЙМАУТОВ
            self._session_no_proxy = aiohttp.ClientSession(
                # УБИРАЕМ ВСЕ ТАЙМАУТЫ для полной асинхронности
                timeout=aiohttp.ClientTimeout(total=None, connect=None, sock_read=None, sock_connect=None),
                connector=aiohttp.TCPConnector(limit=100, limit_per_host=50)  # Увеличиваем лимиты для параллельности
            )
            print(f"YouTube Bot: HTTP сессия создана БЕЗ прокси и БЕЗ ТАЙМАУТОВ для Gemini API (ПОЛНАЯ АСИНХРОННОСТЬ)")
        return self._session_no_proxy

    async def close_session(self):
        """Закрывает HTTP сессию"""
        if self._session_no_proxy and not self._session_no_proxy.closed:
            await self._session_no_proxy.close()

    async def get_video_info(self, video_url: str) -> Dict[str, Optional[str]]:
        """Получение информации о видео через YouTube Services"""
        return await self.youtube_services.get_video_info(video_url)

    async def get_video_comments(self, video_url: str, max_comments: int = 30) -> List[str]:
        """Получение комментариев к видео через YouTube Services"""
        return await self.youtube_services.get_video_comments(video_url, max_comments)

    async def extract_channel_id_from_url(self, channel_url: str) -> Optional[str]:
        """Извлечение channel_id из URL через YouTube Services"""
        return await self.youtube_services.extract_channel_id_from_url(channel_url)



    async def init_telegraph(self):
        """Инициализирует Telegraph клиент"""
        try:
            if self.telegraph is None:
                self.telegraph = Telegraph()

                if self.telegraph_token:
                    # Используем существующий токен
                    self.telegraph.access_token = self.telegraph_token
                else:
                    # Создаем новый аккаунт
                    response = self.telegraph.create_account(
                        short_name=TELEGRAPH_AUTHOR_NAME,
                        author_name=TELEGRAPH_AUTHOR_NAME,
                        author_url=TELEGRAPH_AUTHOR_URL
                    )
                    self.telegraph_token = response['access_token']
                    logger.info(f"Создан новый Telegraph аккаунт: {response['auth_url']}")

            return True
        except Exception as e:
            logger.error(f"Ошибка инициализации Telegraph: {str(e)}")
            return False

    async def get_channel_id_from_video_id(self, video_id: str) -> Optional[str]:
        """Получение channel_id из video_id через YouTube Services"""
        return await self.youtube_services.get_channel_id_from_video_id(video_id)

    async def get_channel_info(self, channel_url: str) -> Dict[str, Optional[str]]:
        """Получение информации о канале через YouTube Services"""
        return await self.youtube_services.get_channel_info(channel_url)

    async def get_channel_uploads_playlist(self, channel_id: str) -> Optional[str]:
        """Получение ID плейлиста uploads канала через YouTube Services"""
        return await self.youtube_services.get_channel_uploads_playlist(channel_id)











    def _validate_videos_api_response(self, data: Dict, requested_video_ids: List[str]) -> bool:
        """
        Валидирует структуру ответа YouTube Videos API

        Args:
            data: Ответ от API
            requested_video_ids: Список запрошенных video_id

        Returns:
            True если структура корректна, False иначе
        """
        try:
            if not isinstance(data, dict):
                logger.error("Ответ Videos API не является словарем")
                return False

            if "items" not in data:
                logger.warning(f"Отсутствует поле 'items' в ответе Videos API для {len(requested_video_ids)} видео")
                return True  # Может быть пустой ответ

            if not isinstance(data["items"], list):
                logger.error("Поле 'items' в Videos API не является списком")
                return False

            return True
        except Exception as e:
            logger.error(f"Ошибка валидации ответа Videos API: {e}")
            return False

    def _validate_video_item_structure(self, item: Dict, item_index: int) -> Dict[str, any]:
        """
        Валидирует структуру элемента видео из Videos API

        Args:
            item: Элемент видео
            item_index: Индекс элемента для логирования

        Returns:
            Словарь с результатом валидации: {"valid": bool, "error": str}
        """
        try:
            if not isinstance(item, dict):
                return {"valid": False, "error": "элемент не является словарем"}

            if "id" not in item:
                return {"valid": False, "error": "отсутствует поле 'id'"}

            video_id = item["id"]
            if not isinstance(video_id, str):
                return {"valid": False, "error": f"поле 'id' не является строкой: {type(video_id)}"}

            if not video_id.strip():
                return {"valid": False, "error": "поле 'id' пустое"}

            if "contentDetails" not in item:
                return {"valid": False, "error": "отсутствует поле 'contentDetails'"}

            content_details = item["contentDetails"]
            if not isinstance(content_details, dict):
                return {"valid": False, "error": "поле 'contentDetails' не является словарем"}

            return {"valid": True, "error": ""}

        except Exception as e:
            return {"valid": False, "error": f"неожиданная ошибка валидации: {e}"}

    async def _get_videos_duration(self, video_ids: List[str]) -> Dict[str, str]:
        """
        Получает длительность видео через YouTube Data API v3

        Args:
            video_ids: Список ID видео

        Returns:
            Словарь {video_id: duration_string} в формате ISO 8601
        """
        if not video_ids:
            logger.debug("Пустой список video_ids для получения длительности")
            return {}

        # Валидация входных данных
        valid_video_ids = []
        for video_id in video_ids[:50]:  # API лимит 50
            if validate_video_id(video_id):
                valid_video_ids.append(video_id)
            else:
                logger.warning(f"Пропуск некорректного video_id при получении длительности: {video_id}")

        if not valid_video_ids:
            logger.warning("Нет валидных video_id для получения длительности")
            return {}

        try:
            video_ids_str = ",".join(valid_video_ids)
            logger.debug(f"Запрос длительности для {len(valid_video_ids)} видео: {video_ids_str}")

            params = {
                "part": "contentDetails",
                "id": video_ids_str,
                "key": YOUTUBE_DATA_API_KEY
            }

            # Используем retry механизм для API запроса через youtube_services
            data = await self.youtube_services._retry_api_request(
                self.youtube_services._make_youtube_api_request,
                "https://www.googleapis.com/youtube/v3/videos",
                params
            )

            if data is None:
                logger.error(f"Не удалось получить длительность для {len(valid_video_ids)} видео после всех попыток")
                return {}

            # Валидация структуры ответа
            if not self._validate_videos_api_response(data, valid_video_ids):
                logger.error("Некорректная структура ответа Videos API")
                return {}

            durations = {}
            processed_count = 0
            error_count = 0

            if "items" in data:
                        for item_index, item in enumerate(data["items"]):
                            try:
                                # Детальная валидация элемента
                                validation_result = self._validate_video_item_structure(item, item_index)
                                if not validation_result["valid"]:
                                    logger.warning(f"Пропуск видео #{item_index} в Videos API: {validation_result['error']}")
                                    error_count += 1
                                    continue

                                video_id = item.get("id")
                                content_details = item.get("contentDetails", {})
                                duration = content_details.get("duration")

                                if video_id and duration:
                                    # Дополнительная валидация video_id
                                    if not validate_video_id(video_id):
                                        logger.warning(f"Некорректный video_id в ответе Videos API: {video_id}")
                                        error_count += 1
                                        continue

                                    durations[video_id] = duration
                                    processed_count += 1
                                    logger.debug(f"Получена длительность для {video_id}: {duration}")
                                else:
                                    logger.warning(f"Отсутствуют данные о длительности для видео #{item_index}: video_id={video_id}, duration={duration}")
                                    error_count += 1

                            except Exception as e:
                                logger.error(f"Ошибка обработки видео #{item_index} в Videos API: {e}")
                                error_count += 1
                                continue

            # Проверяем, что получили данные для всех запрошенных видео
            missing_videos = set(valid_video_ids) - set(durations.keys())
            if missing_videos:
                logger.warning(f"Не получена длительность для {len(missing_videos)} видео: {missing_videos}")

            if error_count > 0:
                logger.warning(f"Получена длительность для {processed_count}/{len(valid_video_ids)} видео, ошибок: {error_count}")
            else:
                logger.debug(f"Получена длительность для {processed_count}/{len(valid_video_ids)} видео")
            return durations
        except Exception as e:
            logger.error(f"Неожиданная ошибка получения длительности видео: {e}")
            return {}

    @staticmethod
    def parse_iso8601_duration(duration_str: str) -> Optional[int]:
        """
        Парсит ISO 8601 duration в секунды

        Args:
            duration_str: Строка длительности в формате ISO 8601 (например, PT4M13S)

        Returns:
            Длительность в секундах или None если формат некорректный

        Examples:
            PT4M13S -> 253 секунды
            PT45S -> 45 секунд
            PT1H2M3S -> 3723 секунды
            PT2M -> 120 секунд
            PT1H -> 3600 секунд
        """
        if not duration_str or not duration_str.startswith('PT'):
            return None

        import re
        # Паттерн для парсинга ISO 8601 duration: PT[nH][nM][nS]
        pattern = r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?'
        match = re.match(pattern, duration_str)

        if not match:
            return None

        hours = int(match.group(1) or 0)
        minutes = int(match.group(2) or 0)
        seconds = int(match.group(3) or 0)

        total_seconds = hours * 3600 + minutes * 60 + seconds
        return total_seconds

    def parse_gemini_response(self, response_text: str) -> tuple[str, str, str]:
        """
        Парсит ответ от Gemini и извлекает название, краткую сводку и Telegraph HTML

        Args:
            response_text: Полный ответ от Gemini

        Returns:
            Кортеж (название_статьи, краткая_сводка, telegraph_html)
        """
        try:
            # Ищем блоки [NAME], [SUM] и [TELEGRAPH] с более гибким поиском
            name_start = response_text.find('[NAME]:')
            sum_start = response_text.find('[SUM]:')

            # Ищем [TELEGRAPH] с двоеточием или без него
            telegraph_start = response_text.find('[TELEGRAPH]:')
            if telegraph_start == -1:
                telegraph_start = response_text.find('[TELEGRAPH]')

            # Извлекаем название статьи
            article_title = ""
            if name_start != -1:
                if sum_start != -1:
                    article_title = response_text[name_start + 7:sum_start].strip()
                else:
                    # Если [SUM] не найден, ищем до [TELEGRAPH] или до конца
                    end_pos = telegraph_start if telegraph_start != -1 else len(response_text)
                    article_title = response_text[name_start + 7:end_pos].strip()

            # Очищаем название статьи от HTML тегов (дополнительная защита)
            if article_title:
                import re
                original_title = article_title
                # Удаляем все HTML теги из названия
                article_title = re.sub(r'<[^>]+>', '', article_title).strip()
                # Логируем, если были найдены и удалены HTML теги
                if original_title != article_title:
                    logger.warning(f"Удалены HTML теги из названия статьи: '{original_title}' -> '{article_title}'")

            if sum_start == -1:
                # Если теги не найдены, пытаемся очистить текст от тегов и вернуть как краткую сводку
                cleaned_text = response_text
                # Удаляем теги, если они есть
                import re
                cleaned_text = re.sub(r'\[NAME\]:?[^\n]*\n?', '', cleaned_text)
                cleaned_text = re.sub(r'\[SUM\]:?[^\n]*\n?', '', cleaned_text)
                cleaned_text = re.sub(r'\[TELEGRAPH\]:?[^\n]*\n?', '', cleaned_text)
                cleaned_text = cleaned_text.strip()

                return article_title, cleaned_text, "<p>Telegraph статья недоступна</p>"

            # Извлекаем краткую сводку
            if telegraph_start != -1:
                sum_text = response_text[sum_start + 6:telegraph_start].strip()

                # Извлекаем Telegraph HTML
                if response_text[telegraph_start:telegraph_start+12] == '[TELEGRAPH]:':
                    telegraph_html = response_text[telegraph_start + 12:].strip()
                else:
                    telegraph_html = response_text[telegraph_start + 11:].strip()
            else:
                # Если нет блока [TELEGRAPH], то [SUM] идет до конца
                sum_text = response_text[sum_start + 6:].strip()
                telegraph_html = "<p>Telegraph статья недоступна</p>"

            return article_title, sum_text, telegraph_html

        except Exception as e:
            logger.error(f"Ошибка при парсинге ответа Gemini: {str(e)}")
            # В случае ошибки тоже очищаем от тегов
            import re
            cleaned_text = re.sub(r'\[NAME\]:?[^\n]*\n?', '', response_text)
            cleaned_text = re.sub(r'\[SUM\]:?[^\n]*\n?', '', cleaned_text)
            cleaned_text = re.sub(r'\[TELEGRAPH\]:?[^\n]*\n?', '', cleaned_text)
            cleaned_text = cleaned_text.strip()
            return "", cleaned_text, "<p>Ошибка при извлечении Telegraph статьи</p>"
    
    async def generate_summary_with_gemini(self, video_url: str, video_info: Dict[str, Optional[str]] = None, comments: List[str] = None) -> str:
        """
        Создает сводку видео через Gemini API с прикреплением YouTube видео

        Args:
            video_url: URL YouTube видео
            video_info: Словарь с информацией о видео (title, author, description)
            comments: Список комментариев к видео

        Returns:
            Краткая сводка от Gemini
        """
        # Формируем информацию о видео для передачи в промпт
        video_info_text = ""
        if video_info:
            if video_info.get('title'):
                video_info_text += f"Название видео: {video_info['title']}\n"
            if video_info.get('author'):
                video_info_text += f"Автор: {video_info['author']}\n"
            if video_info.get('description'):
                description = video_info['description']
                video_info_text += f"Описание: {description[:500]}...\n" if len(description) > 500 else f"Описание: {description}\n"

        # Формируем текст комментариев для передачи в промпт
        comments_text = ""
        if comments and len(comments) > 0:
            comments_text = "КОММЕНТАРИИ ЗРИТЕЛЕЙ:\n"
            for i, comment in enumerate(comments[:30], 1):  # Ограничиваем до 30 комментариев
                comments_text += f"{i}. {comment}\n"
            comments_text += "\n"

        prompt = f"""Ты — элитный ИИ-редактор, который превращает видео в безупречно структурированные и визуально приятные сводки. Твоя главная задача — читаемость и аккуратность.
Твой ответ должен иметь АБСОЛЮТНО СТРОГИЙ формат и содержать ТОЛЬКО ТРИ обязательных блока. Никаких вступлений, комментариев или текста вне тегов.
ФОРМАТ ОТВЕТА:
[NAME]: Название для статьи со сводкой (кратко, 5-6 слов, ТОЛЬКО ТЕКСТ БЕЗ HTML ТЕГОВ!)
ВАЖНО: В названии статьи ЗАПРЕЩЕНО использовать любые HTML теги (<b>, <i>, <strong>, <em> и др.)
Название должно быть простым текстом без форматирования.
Правильно: "Советы по программированию"
НЕПРАВИЛЬНО: "<b>Советы</b> по программированию" или "Советы по <i>программированию</i>"
[SUM]: Текст краткой сводки
[TELEGRAPH]: HTML-статья для Telegraph
Между блоками не должно быть пустых строк.

ВАЖНО! ФОРМАТИРОВАНИЕ ТЕКСТА:
- Для выделения жирным текстом используй ТОЛЬКО HTML-теги <b>текст</b>
- Для выделения курсивом используй ТОЛЬКО HTML-теги <i>текст</i>
- ЗАПРЕЩЕНО использовать любые другие HTML-теги (кроме <b> и <i>)
- ЗАПРЕЩЕНО использовать Markdown форматирование (**текст** или *текст*)

ТРЕБОВАНИЯ К СВОДКАМ
1. Краткая сводка [SUM]:
Задача: Суть видео в одном твите.
Формат: 1-2 предложения. Максимально сжато и по делу.
ОБЯЗАТЕЛЬНО: Краткая сводка ВСЕГДА должна начинаться с подходящего эмодзи, который отражает суть или тематику видео. Эмодзи должен быть ПЕРВЫМ символом, затем пробел, затем текст сводки.
Примеры начала: "💡 В видео объясняется...", "⚠️ Важные ошибки..."
ЗАПРЕЩЕНО использовать эмодзи: 🎯 (попадание в цель) и 🚀 (ракета) - никогда не используй их.

2. Telegraph статья [TELEGRAPH]:
СТРОГИЕ ТРЕБОВАНИЯ К ФОРМАТУ:
Твой ответ должен содержать ТОЛЬКО HTML-контент без каких-либо вступлений, комментариев или текста вне HTML-тегов.

РАЗРЕШЕННЫЕ HTML-ТЕГИ (используй ТОЛЬКО эти теги):
- <h3>, <h4> — заголовки разделов (ТОЛЬКО h3 и h4, НЕ h1, h2, h5, h6!)
- <p> — абзацы
- <strong> — жирный текст (вместо <b>)
- <em> — курсив (вместо <i>)
- <ul>, <ol>, <li> — списки
- <blockquote> — важные цитаты и выводы
- <hr> — разделители между разделами
- <code> — код или технические термины
- <br> — переносы строк при необходимости

СТРОГО ЗАПРЕЩЕННЫЕ ТЕГИ: h1, h2, h5, h6, div, span, table, img, video, iframe, a и любые другие теги кроме перечисленных выше!

ТРЕБОВАНИЯ К СОДЕРЖАНИЮ СТАТЬИ:
- Статья должна быть ПОДРОБНОЙ (минимум 500-800 слов)
- КОНКРЕТНО анализируй содержание видео, а не пиши общие фразы
- Используй КОНКРЕТНЫЕ факты, цифры, примеры из видео
- Анализируй КОНКРЕТНЫЕ комментарии зрителей и их мнения
- Избегай общих фраз типа "в видео рассказывается" - будь КОНКРЕТНЫМ
- Каждый раздел должен содержать КОНКРЕТНУЮ информацию из видео

СТРУКТУРА СТАТЬИ:
1. Заголовок раздела с эмодзи
2. Краткое введение
3. Разделитель
4. Основные разделы с подзаголовками
5. Заключение

ПРАВИЛА ФОРМАТИРОВАНИЯ:
- Используй ТОЛЬКО <h3> для главных разделов, ТОЛЬКО <h4> для подразделов (НЕ h1, h2, h5, h6!)
- Активно используй <strong> для ключевых терминов, цифр, названий
- Используй <em> для акцентов и важных моментов
- Создавай <ul>/<li> списки вместо текстовых маркеров
- Помещай важные выводы в <blockquote>
- Разделяй крупные разделы тегом <hr>
- ПРОВЕРЬ: используешь ли ты только разрешенные теги из списка выше!

ПРИМЕР СТРУКТУРЫ:
<h3>🎥 Анализ видео</h3>
<p><em>Подробный разбор содержания YouTube видео</em></p>
<hr>
<h4>👋 Введение</h4>
<p>Текст введения с <strong>ключевыми терминами</strong>...</p>
<h4>📝 Основные моменты</h4>
<ul>
<li><strong>Первый пункт</strong>: Описание с <em>важными акцентами</em></li>
<li><strong>Второй пункт</strong>: Описание</li>
</ul>
<blockquote>
<p><strong>Важный вывод:</strong> Ключевая мысль из видео</p>
</blockquote>
<hr>
<h4>💡 Заключение</h4>
<p>Финальные мысли и рекомендации...</p>

ИНФОРМАЦИЯ О ВИДЕО:
{video_info_text}

КОММЕНТАРИИ ПОД ВИДЕО:
{comments_text}

Создай сводку на основе прикрепленного видео."""

        # Используем новый Google GenAI SDK
        from google.genai import types
        
        # Счетчик ошибок для fallback на gemini-2.0-flash
        error_count = 0
        max_errors_before_fallback = 3
        
        # Пробуем разные API ключи в случае ошибки
        for attempt in range(len(GEMINI_API_KEYS)):
            api_key = self.get_next_api_key()
            
            try:
                # Создаем клиент Gemini
                client = genai.Client(api_key=api_key)
                
                # Выбираем модель в зависимости от количества ошибок
                model = "gemini-2.0-flash" if error_count >= max_errors_before_fallback else "gemini-2.5-flash-lite"
                
                # Формируем контент с прикрепленным видео
                contents = [
                    types.Content(
                        role="user",
                        parts=[
                            types.Part(
                                file_data=types.FileData(
                                    file_uri=video_url,
                                    mime_type="video/*",
                                ),
                                video_metadata=types.VideoMetadata(
                                    fps=0.01,
                                ),
                            ),
                            types.Part.from_text(text=prompt),
                        ],
                    ),
                ]
                
                # Конфигурация генерации (только для gemini-2.5-flash-lite)
                if model == "gemini-2.5-flash-lite":
                    generate_content_config = types.GenerateContentConfig(
                        thinking_config=types.ThinkingConfig(
                            thinking_budget=128,
                        ),
                    )
                else:
                    generate_content_config = types.GenerateContentConfig()
                
                # Выполняем запрос к Gemini
                response = await asyncio.to_thread(
                    client.models.generate_content,
                    model=model,
                    contents=contents,
                    config=generate_content_config
                )
                
                if response and response.text:
                    logger.info(f"✅ Сводка создана через {model}")
                    return response.text.strip()
                else:
                    logger.warning(f"Пустой ответ от {model}")
                    error_count += 1
                    continue
                    
            except Exception as e:
                error_count += 1
                logger.error(f"Ошибка при запросе к Gemini API (попытка {attempt + 1}, ошибка {error_count}): {str(e)}")
                
                # Если достигли лимита ошибок, переключаемся на gemini-2.0-flash
                if error_count >= max_errors_before_fallback:
                    logger.warning(f"Переключение на gemini-2.0-flash после {max_errors_before_fallback} ошибок")
                
                continue

        return "Не удалось получить сводку. Все API ключи недоступны или превысили лимит."



    def clean_html_for_telegraph(self, html_content: str) -> str:
        """
        Очищает HTML контент от тегов, не поддерживаемых Telegraph

        Поддерживаемые Telegraph теги:
        a, aside, b, blockquote, br, code, em, figcaption, figure,
        h3, h4, hr, i, iframe, img, li, ol, p, pre, s, strong, u, ul, video

        Args:
            html_content: Исходный HTML контент

        Returns:
            Очищенный HTML контент
        """
        # Список поддерживаемых Telegraph тегов
        supported_tags = {
            'a', 'aside', 'b', 'blockquote', 'br', 'code', 'em', 'figcaption',
            'figure', 'h3', 'h4', 'hr', 'i', 'iframe', 'img', 'li', 'ol',
            'p', 'pre', 's', 'strong', 'u', 'ul', 'video'
        }

        # Паттерн для поиска всех HTML тегов
        tag_pattern = r'<(/?)([a-zA-Z][a-zA-Z0-9]*)[^>]*>'

        def replace_tag(match):
            is_closing = match.group(1)  # '/' для закрывающих тегов
            tag_name = match.group(2).lower()
            full_tag = match.group(0)

            if tag_name in supported_tags:
                # Тег поддерживается - оставляем как есть
                return full_tag
            else:
                # Тег не поддерживается - заменяем на поддерживаемый аналог или удаляем
                if tag_name in ['h1', 'h2']:
                    # Заменяем h1, h2 на h3
                    return full_tag.replace(f'<{is_closing}{tag_name}', f'<{is_closing}h3')
                elif tag_name in ['h5', 'h6']:
                    # Заменяем h5, h6 на h4
                    return full_tag.replace(f'<{is_closing}{tag_name}', f'<{is_closing}h4')
                elif tag_name == 'div':
                    # Заменяем div на p
                    return full_tag.replace(f'<{is_closing}div', f'<{is_closing}p')
                elif tag_name == 'span':
                    # Удаляем span теги, оставляя содержимое
                    return ''
                elif tag_name in ['del', 'strike']:
                    # Заменяем del, strike на s
                    return full_tag.replace(f'<{is_closing}{tag_name}', f'<{is_closing}s')
                elif tag_name in ['mark', 'highlight']:
                    # Заменяем mark, highlight на strong
                    return full_tag.replace(f'<{is_closing}{tag_name}', f'<{is_closing}strong')
                else:
                    # Для всех остальных неподдерживаемых тегов - удаляем
                    logger.warning(f"Удален неподдерживаемый Telegraph тег: {tag_name}")
                    return ''

        # Применяем замены
        cleaned_html = re.sub(tag_pattern, replace_tag, html_content)

        # Удаляем пустые строки и лишние пробелы
        cleaned_html = re.sub(r'\n\s*\n', '\n', cleaned_html)
        cleaned_html = cleaned_html.strip()

        logger.info("HTML контент очищен для Telegraph")
        return cleaned_html

    async def publish_to_telegraph(self, title: str, html_content: str) -> Optional[str]:
        """
        Публикует контент в Telegraph

        Args:
            title: Заголовок статьи
            html_content: HTML контент статьи

        Returns:
            URL опубликованной статьи или None в случае ошибки
        """
        try:
            # Инициализируем Telegraph если нужно
            if not await self.init_telegraph():
                return None

            # Очищаем HTML от неподдерживаемых Telegraph тегов
            cleaned_html = self.clean_html_for_telegraph(html_content)

            # Создаем страницу с очищенным HTML
            response = self.telegraph.create_page(
                title=title,
                html_content=cleaned_html,
                author_name=TELEGRAPH_AUTHOR_NAME,
                author_url=TELEGRAPH_AUTHOR_URL
            )

            if response and 'url' in response:
                logger.info(f"Статья опубликована в Telegraph: {response['url']}")
                return response['url']
            else:
                logger.error(f"Неожиданный ответ от Telegraph: {response}")
                return None

        except Exception as e:
            logger.error(f"Ошибка публикации в Telegraph: {str(e)}")
            return None

    async def create_summary_with_ai(self, video_url: str, video_info: Dict, comments: List[str]) -> Optional[Dict]:
        """
        Создает сводку видео с помощью AI и сохраняет в базу данных

        Args:
            video_url: URL видео
            video_info: Информация о видео
            comments: Комментарии к видео

        Returns:
            Словарь с результатами или None в случае ошибки
        """
        try:
            # Извлекаем video_id
            video_id = self.transcriber.extract_video_id(video_url)
            if not video_id:
                logger.error(f"Не удалось извлечь video_id из URL: {video_url}")
                return None

            # Генерируем сводку с помощью Gemini
            summary_content = await self.generate_summary_with_gemini(video_url, video_info, comments)
            if not summary_content:
                logger.error(f"Не удалось создать сводку для видео {video_id}")
                return None

            # Парсим ответ от Gemini
            article_title, short_summary, telegraph_html = self.parse_gemini_response(summary_content)
            if not short_summary:
                logger.error(f"Не удалось распарсить ответ Gemini для видео {video_id}")
                return None

            # detailed_summary не используется в новом формате, используем telegraph_html
            detailed_summary = telegraph_html

            # Публикуем в Telegraph
            telegraph_url = None
            if telegraph_html:
                # Используем название из тега [NAME] или создаем из информации о видео
                if article_title:
                    telegraph_title = article_title.strip()
                else:
                    telegraph_title = f"Сводка: {video_info.get('title', 'YouTube видео')}"
                telegraph_url = await self.publish_to_telegraph(telegraph_title, telegraph_html)

            # Сохраняем в базу данных (без transcript_text)
            save_success = await self.db.save_video_summary(
                video_id=video_id,
                video_url=video_url,
                title=video_info.get('title'),
                author=video_info.get('author'),
                description=video_info.get('description'),
                transcript_text="",  # Транскрипция больше не используется
                short_summary=short_summary,
                detailed_summary=detailed_summary,
                telegraph_html=telegraph_html,
                telegraph_url=telegraph_url,
                article_title=telegraph_title if telegraph_html else None
            )

            if not save_success:
                logger.warning(f"Не удалось сохранить сводку в БД для видео {video_id}")

            return {
                'video_id': video_id,
                'short_summary': short_summary,
                'detailed_summary': detailed_summary,
                'telegraph_url': telegraph_url,
                'transcript_text': "",  # Транскрипция больше не используется
                'title': video_info.get('title'),
                'author': video_info.get('author'),
                'description': video_info.get('description')
            }

        except Exception as e:
            logger.error(f"Ошибка создания сводки для видео {video_url}: {e}")
            return None

    async def answer_question_with_gemini(self, user_id: int, transcript_text: str, question: str, video_info: Dict[str, Optional[str]] = None, comments: List[str] = None) -> str:
        """
        Отвечает на вопрос пользователя на основе транскрипции видео с учетом контекста переписки

        Args:
            user_id: ID пользователя для получения контекста переписки
            transcript_text: Текст транскрипции (используется только для первого вопроса)
            question: Вопрос пользователя
            video_info: Словарь с информацией о видео (title, author, description)
            comments: Список комментариев к видео

        Returns:
            Ответ от Gemini
        """
        # Получаем историю диалога пользователя
        conversation_history = self.user_conversations.get(user_id, [])

        # Формируем информацию о видео для передачи в промпт
        video_info_text = ""
        if video_info:
            if video_info.get('title'):
                video_info_text += f"Название видео: {video_info['title']}\n"
            if video_info.get('author'):
                video_info_text += f"Автор: {video_info['author']}\n"
            if video_info.get('description'):
                description = video_info['description']
                video_info_text += f"Описание: {description[:500]}...\n" if len(description) > 500 else f"Описание: {description}\n"

        # Формируем текст комментариев для передачи в промпт
        comments_text = ""
        if comments and len(comments) > 0:
            comments_text = "КОММЕНТАРИИ ЗРИТЕЛЕЙ:\n"
            for i, comment in enumerate(comments[:30], 1):  # Ограничиваем до 30 комментариев
                comments_text += f"{i}. {comment}\n"
            comments_text += "\n"

        # Проверяем, есть ли транскрипция
        has_transcript = not transcript_text.startswith("Транскрипция недоступна")

        # Системный промпт с правилами форматирования (всегда актуальный)
        system_prompt = f"""Ты — эксперт-аналитик, который отвечает на вопросы по содержанию YouTube видео.

КРИТИЧЕСКИ ВАЖНЫЕ ПРАВИЛА ФОРМАТИРОВАНИЯ:
1. Используй ТОЛЬКО HTML-теги: <b>текст</b> для жирного, <i>текст</i> для курсива
2. НИКОГДА НЕ ИСПОЛЬЗУЙ MARKDOWN теги (**текст** или *текст*)
3. Отвечай КРАТКО и по существу (1-3 предложения максимум)
4. СРАЗУ ОТВЕЧАЙ НА ВОПРОС с конкретными выводами - без вступлений и общих фраз
5. Первое предложение должно содержать ПРЯМОЙ ОТВЕТ на вопрос
6. Используй КОНКРЕТНЫЕ факты и детали из видео в ответе
7. Если есть цифры, даты, имена - обязательно включай их в ответ
8. Отвечай ТОЛЬКО на основе информации из видео
9. Если нет информации для ответа, честно скажи об этом
10. Отвечай на русском языке
{"11. ВАЖНО: Транскрипция для данного видео недоступна. Отвечай на основе названия, описания видео и комментариев зрителей. Если информации недостаточно для ответа, честно скажи об этом." if not has_transcript else ""}

ИНФОРМАЦИЯ О ВИДЕО:
{video_info_text}
{comments_text}ТРАНСКРИПЦИЯ ВИДЕО:
{transcript_text}"""

        # Если это первый вопрос, создаем новую историю
        if not conversation_history:
            conversation_history = [
                {"role": "system", "content": system_prompt}
            ]
        else:
            # Для последующих вопросов обновляем системный промпт, чтобы напомнить о форматировании
            conversation_history[0] = {"role": "system", "content": system_prompt}

        # Добавляем текущий вопрос пользователя
        conversation_history.append({"role": "user", "content": question})

        # Обрезаем историю диалога, чтобы не превысить лимиты токенов
        conversation_history = self.trim_conversation_history(conversation_history, max_messages=10)

        # Формируем промпт для Gemini API
        prompt_parts = []
        for message in conversation_history:
            if message["role"] == "system":
                prompt_parts.append(f"СИСТЕМНАЯ ИНСТРУКЦИЯ:\n{message['content']}\n")
            elif message["role"] == "user":
                prompt_parts.append(f"ПОЛЬЗОВАТЕЛЬ: {message['content']}\n")
            elif message["role"] == "assistant":
                prompt_parts.append(f"АССИСТЕНТ: {message['content']}\n")

        prompt_parts.append("АССИСТЕНТ:")
        prompt = "\n".join(prompt_parts)

        payload = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.3,  # Более низкая температура для точных ответов
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 64000,  # Ограничиваем токены для коротких ответов
                "thinkingConfig": {
                    "thinkingBudget": 8192,  # Увеличиваем бюджет размышления для лучших ответов
                    "includeThoughts": False
                }
            }
        }

        # Используем переиспользуемую сессию для лучшей производительности
        session = await self.get_session()

        # Пробуем разные API ключи в случае ошибки
        for attempt in range(len(GEMINI_API_KEYS)):
            api_key = self.get_next_api_key()
            url = f"{GEMINI_API_URL}?key={api_key}"

            try:
                # УБИРАЕМ ВСЕ ТАЙМАУТЫ - полностью асинхронный запрос
                async with session.post(
                    url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                    # НЕТ ТАЙМАУТОВ - полная асинхронность
                ) as response:

                    if response.status == 200:
                        result = await response.json()

                        if "candidates" in result and len(result["candidates"]) > 0:
                            content = result["candidates"][0]["content"]["parts"][0]["text"]
                            answer = content.strip()

                            # Сохраняем ответ в историю диалога
                            conversation_history.append({"role": "assistant", "content": answer})
                            self.user_conversations[user_id] = conversation_history

                            return answer
                        else:
                            logger.warning(f"Неожиданный формат ответа от Gemini: {result}")
                            return "Не удалось получить ответ от Gemini"

                    elif response.status == 429:
                        # Превышен лимит запросов, пробуем следующий ключ
                        logger.warning(f"API ключ {api_key[:10]}... превысил лимит, пробуем следующий")
                        continue

                    else:
                        error_text = await response.text()
                        logger.error(f"Ошибка Gemini API: {response.status} - {error_text}")
                        continue

            # УБИРАЕМ ОБРАБОТКУ ТАЙМАУТОВ - их больше нет
            except Exception as e:
                logger.error(f"Ошибка при запросе к Gemini API: {str(e)}")
                continue

        return "Не удалось получить ответ. Все API ключи недоступны или превысили лимит."

    def trim_conversation_history(self, conversation_history: list, max_messages: int = 10) -> list:
        """
        Обрезает историю диалога, оставляя системное сообщение и последние max_messages сообщений

        Args:
            conversation_history: История диалога
            max_messages: Максимальное количество сообщений пользователя/ассистента (не считая системное)

        Returns:
            Обрезанная история диалога
        """
        if len(conversation_history) <= max_messages + 1:  # +1 для системного сообщения
            return conversation_history

        # Сохраняем системное сообщение (первое) и последние max_messages сообщений
        system_message = conversation_history[0] if conversation_history[0]["role"] == "system" else None
        user_assistant_messages = [msg for msg in conversation_history if msg["role"] in ["user", "assistant"]]

        # Берем последние max_messages сообщений
        recent_messages = user_assistant_messages[-max_messages:]

        # Формируем новую историю
        trimmed_history = []
        if system_message:
            trimmed_history.append(system_message)
        trimmed_history.extend(recent_messages)

        return trimmed_history

    def split_long_message(self, text: str, max_length: int = 4000) -> list[str]:
        """
        Разделяет длинное сообщение на части, не превышающие max_length символов

        Args:
            text: Текст для разделения
            max_length: Максимальная длина одной части

        Returns:
            Список частей сообщения
        """
        if len(text) <= max_length:
            return [text]

        parts = []
        current_part = ""

        # Разделяем по абзацам
        paragraphs = text.split('\n\n')

        for paragraph in paragraphs:
            # Если добавление этого абзаца превысит лимит
            if len(current_part) + len(paragraph) + 2 > max_length:
                if current_part:
                    parts.append(current_part.strip())
                    current_part = ""

                # Если сам абзац слишком длинный, разделяем по предложениям
                if len(paragraph) > max_length:
                    sentences = paragraph.split('. ')
                    for sentence in sentences:
                        if len(current_part) + len(sentence) + 2 > max_length:
                            if current_part:
                                parts.append(current_part.strip())
                                current_part = ""

                        if current_part:
                            current_part += ". " + sentence
                        else:
                            current_part = sentence
                else:
                    current_part = paragraph
            else:
                if current_part:
                    current_part += "\n\n" + paragraph
                else:
                    current_part = paragraph

        if current_part:
            parts.append(current_part.strip())

        return parts

    async def _send_existing_summary(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                   summary_data: Dict[str, Any], user_id: int) -> None:
        """
        Отправляет существующую сводку из базы данных

        Args:
            update: Update объект Telegram
            context: Context объект Telegram
            summary_data: Данные сводки из базы данных
            user_id: ID пользователя
        """
        try:
            # Форматируем краткую сводку
            short_summary = summary_data.get('short_summary', 'Краткая сводка недоступна')
            formatted_short_summary = f"<b>Краткая сводка:</b>\n\n{short_summary}"

            # Проверяем наличие Telegraph URL
            telegraph_url = summary_data.get('telegraph_url')

            # Создаем кнопку подписки на автора (только для личных чатов)
            reply_markup = None
            if not self._is_group_chat(update):
                # Получаем информацию о видео для создания кнопки подписки
                video_url = summary_data.get('video_url')
                if video_url:
                    # Получаем полную информацию о видео включая channel_id
                    video_info = await self.get_video_info(video_url)
                    reply_markup = await self._create_subscription_button(user_id, video_info)

            if telegraph_url:
                # Отправляем с ссылкой на Telegraph
                message_with_link = f"{formatted_short_summary}\n\n<a href='{telegraph_url}'><b>📖 Подробная сводка</b></a>"

                await update.message.reply_text(
                    message_with_link,
                    parse_mode='HTML',
                    disable_web_page_preview=False,
                    reply_markup=reply_markup
                )
            else:
                # Fallback: отправляем только краткую сводку если Telegraph не работает
                await update.message.reply_text(
                    formatted_short_summary,
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )

            # Сохраняем транскрипцию и информацию о видео для пользователя
            self.user_transcripts[user_id] = summary_data.get('transcript_text', '')
            self.user_video_info[user_id] = {
                'title': summary_data.get('title'),
                'author': summary_data.get('author'),
                'description': summary_data.get('description')
            }

            # Очищаем предыдущий контекст переписки для нового видео
            if user_id in self.user_conversations:
                del self.user_conversations[user_id]

            # Отправляем сообщение о возможности задавать вопросы только в личных чатах
            if not self._is_group_chat(update):
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="😌 <b>Если есть какие-то вопросы по видео - задавайте!</b> Отвечу на основе видео.",
                    parse_mode='HTML'
                )

            logger.info(f"Отправлена существующая сводка для пользователя {user_id}")

        except Exception as e:
            logger.error(f"Ошибка отправки существующей сводки для пользователя {user_id}: {e}")
            await update.message.reply_text("Произошла ошибка при загрузке сводки из базы данных.")

    async def _send_demo_summary(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int,
                               summary_data: Dict[str, Any], reply_to_message_id: int) -> None:
        """
        Отправляет демо-сводку из базы данных с reply на сообщение с ссылкой

        Args:
            context: Context объект Telegram
            chat_id: ID чата
            summary_data: Данные сводки из базы данных
            reply_to_message_id: ID сообщения с ссылкой для reply
        """
        try:
            # Форматируем краткую сводку
            short_summary = summary_data.get('short_summary', 'Краткая сводка недоступна')
            formatted_short_summary = f"<b>Краткая сводка:</b>\n\n{short_summary}"

            # Проверяем наличие Telegraph URL
            telegraph_url = summary_data.get('telegraph_url')

            if telegraph_url:
                # Отправляем с ссылкой на Telegraph
                message_with_link = f"{formatted_short_summary}\n\n<a href='{telegraph_url}'><b>📖 Подробная сводка</b></a>"

                await context.bot.send_message(
                    chat_id=chat_id,
                    text=message_with_link,
                    parse_mode='HTML',
                    disable_web_page_preview=False,
                    reply_parameters=ReplyParameters(message_id=reply_to_message_id)
                )
            else:
                # Fallback: отправляем только краткую сводку если Telegraph не работает
                await context.bot.send_message(
                    chat_id=chat_id,
                    text=formatted_short_summary,
                    parse_mode='HTML',
                    reply_parameters=ReplyParameters(message_id=reply_to_message_id)
                )

            logger.info(f"Отправлена демо-сводка в чат {chat_id}")

        except Exception as e:
            logger.error(f"Ошибка отправки демо-сводки в чат {chat_id}: {e}")
            await context.bot.send_message(
                chat_id=chat_id,
                text="Произошла ошибка при загрузке демо-сводки.",
                reply_parameters=ReplyParameters(message_id=reply_to_message_id)
            )

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик команды /start с демонстрационной последовательностью"""
        user = update.effective_user
        chat_id = update.effective_chat.id
        user_name = user.first_name or user.last_name or user.username or "пользователь"

        logger.info(f"User {user.id} started the bot")

        # Убираем клавиатуру
        reply_kb_markup = None

        try:
            # --- Welcome Sequence ---
            safe_user_name = html.escape(user_name)

            # 1. Waving hand emoji
            await update.message.reply_text("👋", reply_markup=reply_kb_markup)
            await asyncio.sleep(0.5)

            # 2. Combined greeting and info
            info_text = f"""Привет, <b>{safe_user_name}</b>! Я бот для YouTube сводок.

Отправь мне ссылку на видео — сделаю краткую и подробную сводку 📝

<b>Если есть вопросы по видео</b> — на основе видео сделаю подробный ответ! ✨"""

            markup_info = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton("Автор", url="http://t.me/kirilldzf"),
                    InlineKeyboardButton("Поддержать автора", url="https://pay.cloudtips.ru/p/469fba34")
                ]
            ])

            await context.bot.send_message(
                chat_id,
                info_text,
                reply_markup=markup_info,
                parse_mode="HTML"
            )

            logger.info(f"Sent welcome message to user {user.id}")

            # --- Демонстрационная последовательность ---
            await asyncio.sleep(2)

            # Первое сообщение с цитатой
            demo_message1 = "<blockquote>😇 Смотри, как это работает:</blockquote>"
            first_message = await context.bot.send_message(chat_id, demo_message1, parse_mode="HTML")

            await asyncio.sleep(2)

            # Редактируем сообщение, добавляя вторую часть
            demo_message_edited = "<blockquote>😇 Смотри как это работает\n\nТы скидываешь ссылку в чат. Например:</blockquote>"
            await context.bot.edit_message_text(
                chat_id=chat_id,
                message_id=first_message.message_id,
                text=demo_message_edited,
                parse_mode="HTML"
            )

            await asyncio.sleep(2)

            # Отправляем ссылку с превью (НЕ в цитате)
            demo_url = "https://youtu.be/iiP4kQyzcm8"
            url_message = await context.bot.send_message(
                chat_id,
                demo_url,
                disable_web_page_preview=True
            )

            await asyncio.sleep(2)

            # Сообщение с цитатой
            demo_message3 = "<blockquote>😌 А я начинаю обработку этого видео, смотрю его за тебя и создаю краткую выжимку...</blockquote>"
            await context.bot.send_message(chat_id, demo_message3, parse_mode="HTML")

            await asyncio.sleep(2)

            # Отправляем стикер реплаем на сообщение с ссылкой (НЕ в цитате)
            sticker_message = await context.bot.send_sticker(
                chat_id=chat_id,
                sticker="CAACAgIAAxkBAAEBZ09oan7CQtvYITGhYo6Y5He9WN12RQACSAIAAladvQoc9XL43CkU0DYE",
                reply_parameters=ReplyParameters(message_id=url_message.message_id)
            )

            await asyncio.sleep(6)

            # Удаляем стикер перед отправкой ответа
            await context.bot.delete_message(chat_id=chat_id, message_id=sticker_message.message_id)

            # Проверяем, есть ли сводка для демо-видео в базе данных
            demo_video_id = "iiP4kQyzcm8"  # ID из ссылки
            existing_summary = await self.db.get_video_summary(demo_video_id)

            if existing_summary:
                # Отправляем существующую сводку
                await self._send_demo_summary(context, chat_id, existing_summary, url_message.message_id)
            else:
                # Отправляем заглушку, если сводки нет
                demo_summary = """<b>Краткая сводка:</b>

🎥 В этом видео объясняется, почему кошки ложатся на спину при виде хозяина и что означает такое поведение.

📖 <a href="https://telegra.ph/Demo-Summary-01-01"><b>Подробная сводка</b></a>"""

                await context.bot.send_message(
                    chat_id=chat_id,
                    text=demo_summary,
                    parse_mode='HTML',
                    disable_web_page_preview=False,
                    reply_parameters=ReplyParameters(message_id=url_message.message_id)
                )

            await asyncio.sleep(6)

            # Сообщение с цитатой
            demo_message4 = "<blockquote>😉 Если хочешь, можешь задать вопросы на основе содержания видео! Например...</blockquote>"
            await context.bot.send_message(chat_id, demo_message4, parse_mode="HTML")

            await asyncio.sleep(2)

            # Пример вопроса в моноширинном формате (имитация сообщения пользователя)
            demo_question = "<code>Почему кошки ложатся на спину?</code>"
            question_message = await context.bot.send_message(chat_id, demo_question, parse_mode='HTML')

            # Имитируем ответ ИИ
            await asyncio.sleep(3)
            demo_answer = """В видео объясняется, что кошки ложатся на спину по нескольким причинам:

😸 Это проявление доверия - кошка показывает самую уязвимую часть тела
🎮 Приглашение к игре - особенно у молодых кошек
😌 Демонстрация расслабленности и комфорта
🤗 Желание получить внимание и ласку от хозяина

Такое поведение говорит о том, что кошка чувствует себя в безопасности рядом с вами."""

            await context.bot.send_message(
                chat_id=chat_id,
                text=demo_answer,
                reply_parameters=ReplyParameters(message_id=question_message.message_id)
            )

            await asyncio.sleep(2)

            # Финальное сообщение с цитатой
            final_message = """<blockquote>☺️ Ты получаешь ответ на основе содержания видео.
Пробуй! Присылай ссылку на видео прямо сейчас!</blockquote>"""
            await context.bot.send_message(chat_id, final_message, parse_mode="HTML")

            # Добавляем кнопку подписок только для личных чатов
            if not self._is_group_chat(update):
                await asyncio.sleep(2)
                keyboard = [[InlineKeyboardButton("👤 Подписки", callback_data="subscriptions_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await context.bot.send_message(
                    chat_id=chat_id,
                    text="📺 <b>Хотите получать сводки новых видео автоматически?</b>\n\nИспользуйте кнопку ниже для управления подписками на каналы:",
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error sending multi-part welcome to {user.id}: {e}")
            # Fallback to a single simple message
            try:
                fallback_welcome = f"Привет, <b>{html.escape(user_name)}</b>! Я бот для создания сводок YouTube видео. Отправьте ссылку на видео для получения сводки."
                await update.message.reply_text(fallback_welcome, reply_markup=reply_kb_markup, parse_mode="HTML")
            except Exception as fallback_e:
                logger.error(f"Critical error sending fallback welcome to {user.id}: {fallback_e}")
    
    async def info_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик команды /info"""
        info_message = """Я — <b>sh: YouTube</b>. Моя главная задача — экономить ваше время и помогать извлекать самую суть из YouTube видео.

Вот как я это делаю:

📝 <b>Краткая выжимка</b>
Я мгновенно создаю краткое саммари, чтобы вы за пару секунд поняли, о чем видео и стоит ли тратить на него время.

📖 <b>Подробная статья</b>
Для глубокого погружения я готовлю красивую и структурированную статью с основными тезисами, списками и выводами. Идеально, чтобы сохранить важное.

🤔 <b>Интерактивный диалог</b>
После получения сводки вы можете задать мне любой вопрос по видео, например:
- <i>«Какие три совета он дал в конце?»</i>
- <i>«Что автор думает о этом?»</i>

Я найду точный ответ прямо из видео.

<b>Как начать?</b>
Просто отправьте мне ссылку на видео с YouTube, и я приступлю к работе!"""
        await update.message.reply_text(info_message, parse_mode='HTML')

    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик команды /stats - показывает статистику базы данных"""
        try:
            stats = await self.db.get_database_stats()

            stats_message = f"""📊 <b>Статистика базы данных:</b>

🎥 <b>Всего видео:</b> {stats['total_videos']}
📖 <b>С Telegraph статьями:</b> {stats['with_telegraph']}
🕐 <b>За последние 24 часа:</b> {stats['recent_24h']}

💾 <b>База данных:</b> video_summaries.db
📍 <b>Расположение:</b> папка бота"""

            await update.message.reply_text(stats_message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            await update.message.reply_text("Ошибка получения статистики базы данных.")

    async def settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик команды /settings для групп"""
        # Проверяем, что это группа
        if not self._is_group_chat(update):
            return  # Игнорируем в личных чатах

        chat_id = update.effective_chat.id

        # Проверяем, что команда адресована нашему боту
        if not self._is_settings_for_our_bot(update, context):
            return  # Игнорируем если команда для другого бота

        try:
            # Получаем текущую настройку отправки стикера
            send_sticker = await self.db.get_group_setting(chat_id, 'send_sticker')

            # Создаем инлайн клавиатуру
            keyboard = [[
                InlineKeyboardButton(
                    f"{'✅' if send_sticker else '❌'} Отправлять уточку",
                    callback_data=f"toggle_sticker_{chat_id}"
                )
            ]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Отправляем сообщение с настройками
            settings_text = f"⚙️ <b>Настройки группы</b>\n\n" \
                          f"🦆 Отправка стикера: {'включена' if send_sticker else 'выключена'}"

            await update.message.reply_text(
                settings_text,
                parse_mode='HTML',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Ошибка в команде settings для группы {chat_id}: {e}")
            await update.message.reply_text("Произошла ошибка при загрузке настроек.")

    def _is_settings_for_our_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Проверяет, адресована ли команда settings нашему боту"""
        if not update.message or not update.message.text:
            return False
        message_text = update.message.text.lower().strip()

        # Если просто /settings или settings - откликаемся
        if message_text in ['/settings', 'settings']:
            return True

        # Если есть упоминание бота, проверяем что это наш бот
        if '@' in message_text:
            try:
                # Получаем username нашего бота
                our_bot_username = context.bot.username
                if our_bot_username and f"@{our_bot_username.lower()}" in message_text:
                    return True
                else:
                    return False  # Упомянут другой бот
            except:
                return True  # В случае ошибки откликаемся

        return True  # Если нет упоминания, откликаемся

    async def subscription_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик команды /subscription"""
        try:
            # Проверяем, что команда вызвана в личном чате
            if self._is_group_chat(update):
                await update.message.reply_text(
                    "⚠️ Команда /subscription доступна только в личных чатах.",
                    parse_mode='HTML'
                )
                return

            user_id = update.effective_user.id

            # Получаем количество подписок пользователя
            subscription_count = await self.db.get_subscription_count(user_id)

            # Формируем текст сообщения
            message_text = SUBSCRIPTION_MESSAGES["welcome"].format(
                count=subscription_count,
                max_count=MAX_SUBSCRIPTIONS_PER_USER
            )

            # Создаем клавиатуру
            keyboard = []

            if subscription_count > 0:
                keyboard.append([InlineKeyboardButton("📋 Мои подписки", callback_data="list_subscriptions")])

            if subscription_count < MAX_SUBSCRIPTIONS_PER_USER:
                keyboard.append([InlineKeyboardButton("➕ Добавить канал", callback_data="add_subscription")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                text=message_text,
                parse_mode='HTML',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Ошибка в subscription_command: {e}")
            await update.message.reply_text(
                "❌ Произошла ошибка при обработке команды.",
                parse_mode='HTML'
            )

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик callback query от инлайн кнопок"""
        query = update.callback_query
        await query.answer()  # Подтверждаем получение callback

        try:
            callback_data = query.data

            if callback_data.startswith('toggle_sticker_'):
                # Извлекаем chat_id из callback_data
                chat_id = int(callback_data.replace('toggle_sticker_', ''))

                # Проверяем, что пользователь имеет права администратора
                if not await self._is_user_admin(update, context, chat_id):
                    await query.edit_message_text(
                        "❌ Только администраторы могут изменять настройки группы.",
                        parse_mode='HTML'
                    )
                    return

                # Получаем текущую настройку и переключаем её
                current_setting = await self.db.get_group_setting(chat_id, 'send_sticker')
                new_setting = not current_setting

                # Сохраняем новую настройку
                success = await self.db.set_group_setting(chat_id, 'send_sticker', new_setting)

                if success:
                    # Обновляем клавиатуру
                    keyboard = [[
                        InlineKeyboardButton(
                            f"{'✅' if new_setting else '❌'} Отправлять уточку",
                            callback_data=f"toggle_sticker_{chat_id}"
                        )
                    ]]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Обновляем сообщение
                    settings_text = f"⚙️ <b>Настройки группы</b>\n\n" \
                                  f"🦆 Отправка стикера: {'включена' if new_setting else 'выключена'}"

                    await query.edit_message_text(
                        settings_text,
                        parse_mode='HTML',
                        reply_markup=reply_markup
                    )
                else:
                    await query.edit_message_text(
                        "❌ Ошибка при сохранении настройки.",
                        parse_mode='HTML'
                    )

            # Обработчики для подписок
            elif callback_data == "subscriptions_menu":
                await self.show_subscriptions_menu(query, context)
            elif callback_data == "add_subscription":
                await self.start_add_subscription(query, context)
            elif callback_data == "list_subscriptions":
                await self.show_subscriptions_list(query, context)
            elif callback_data.startswith("remove_sub_"):
                channel_id = callback_data.replace("remove_sub_", "")
                await self.remove_subscription(query, context, channel_id)
            elif callback_data == "back_to_subscriptions":
                await self.show_subscriptions_menu(query, context)
            elif callback_data.startswith("subscribe_to_author_"):
                channel_id = callback_data.replace("subscribe_to_author_", "")
                await self.handle_subscribe_to_author(query, context, channel_id)

        except Exception as e:
            logger.error(f"Ошибка в обработчике callback query: {e}")
            await query.edit_message_text(
                "❌ Произошла ошибка при обработке запроса.",
                parse_mode='HTML'
            )

    async def _is_user_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE, chat_id: int) -> bool:
        """Проверяет, является ли пользователь администратором группы"""
        try:
            if not update.effective_user:
                logger.warning("Нет информации о пользователе в _is_user_admin")
                return False
            user_id = update.effective_user.id
            chat_member = await context.bot.get_chat_member(chat_id, user_id)
            return chat_member.status in ['administrator', 'creator']
        except Exception as e:
            logger.error(f"Ошибка проверки прав администратора: {e}")
            return False

    async def handle_youtube_url(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик YouTube ссылок с публикацией в Telegraph - ПОЛНОСТЬЮ АСИНХРОННЫЙ"""
        if not update.message or not update.message.text:
            logger.warning("Получено сообщение без текста в handle_youtube_url")
            return

        url = update.message.text.strip()
        user_id = update.effective_user.id

        # Увеличиваем счетчик активных запросов
        self.active_requests += 1
        logger.info(f"Пользователь {user_id} отправил URL: {url} - ПОЛНАЯ АСИНХРОННОСТЬ (активных запросов: {self.active_requests})")

        # Проверяем, не обрабатывается ли уже видео для этого пользователя
        is_processing = await self.db.check_user_processing_status(user_id)
        if is_processing:
            await update.message.reply_text(
                "⏳ <b>Дождитесь создания сводки предыдущего видео</b>\n\n"
                "Одновременно можно обрабатывать только одно видео. "
                "Как только текущая сводка будет готова, сможете отправить новое видео! 😊",
                parse_mode='HTML'
            )
            self.active_requests -= 1
            return

        # Проверяем дневной лимит (50 сводок в день)
        can_create, current_count = await self.db.check_daily_limit(user_id, 50)
        if not can_create:
            await update.message.reply_text(
                f"📊 <b>Достигнут дневной лимит сводок</b>\n\n"
                f"Использовано: <b>{current_count}/50</b> сводок за сегодня\n"
                f"Попробуйте завтра! Лимит обновляется в 00:00 🌙",
                parse_mode='HTML'
            )
            self.active_requests -= 1
            return

        # Извлекаем video_id для проверки в базе данных
        video_id = self.transcriber.extract_video_id(url)
        if not video_id:
            await update.message.reply_text("Не удалось извлечь ID видео из ссылки. Проверьте корректность ссылки.")
            self.active_requests -= 1
            return

        # Проверяем, есть ли уже сводка в базе данных
        existing_summary = await self.db.get_video_summary(video_id)
        if existing_summary:
            logger.info(f"Найдена существующая сводка для видео {video_id} в базе данных")

            # Отправляем сводку из базы данных
            await self._send_existing_summary(update, context, existing_summary, user_id)
            self.active_requests -= 1
            return

        # Устанавливаем статус обработки для пользователя
        await self.db.set_user_processing_status(user_id, True)

        # Проверяем настройки группы для отправки стикера
        should_send_sticker = True
        if self._is_group_chat(update):
            should_send_sticker = await self.db.get_group_setting(update.effective_chat.id, 'send_sticker')

        # Отправляем стикер во время обработки - асинхронно (только если включено)
        sticker_task = None
        if should_send_sticker:
            sticker_task = asyncio.create_task(
                update.message.reply_sticker(
                    sticker="CAACAgIAAxkBAAEBZ09oan7CQtvYITGhYo6Y5He9WN12RQACSAIAAladvQoc9XL43CkU0DYE"
                )
            )

        try:
            # Получаем информацию о видео и комментарии асинхронно параллельно (без транскрипции)
            video_info_task = asyncio.create_task(
                self.get_video_info(url)
            )
            comments_task = asyncio.create_task(
                self.get_video_comments(url, max_comments=30)
            )

            # Ждем стикер (если отправляется), информацию о видео и комментарии параллельно
            if sticker_task:
                sticker_message, video_info, comments = await asyncio.gather(
                    sticker_task, video_info_task, comments_task, return_exceptions=True
                )
            else:
                # Если стикер не отправляется, ждем только информацию о видео и комментарии
                video_info, comments = await asyncio.gather(
                    video_info_task, comments_task, return_exceptions=True
                )
                sticker_message = None

            # Обрабатываем результат получения информации о видео
            if isinstance(video_info, Exception):
                logger.error(f"Ошибка получения информации о видео: {video_info}")
                video_info = {"title": None, "author": None, "description": None}

            # Обрабатываем результат получения комментариев
            if isinstance(comments, Exception):
                logger.error(f"Ошибка получения комментариев: {comments}")
                comments = []

            # Логируем полученную информацию о видео (кратко)
            if video_info.get("title"):
                logger.debug(f"Видео: {video_info['title'][:50]}... | Автор: {video_info['author']} | Комментариев: {len(comments) if comments else 0}")
            else:
                logger.warning("Не удалось получить информацию о видео")

            # Создаем задачу для генерации всех сводок в одном запросе с прикрепленным видео
            summary_task = asyncio.create_task(
                self.generate_summary_with_gemini(url, video_info, comments)
            )

            # Ждем завершения задачи - БЕЗ ТАЙМАУТОВ
            full_response = await summary_task

            # Обрабатываем результат
            if isinstance(full_response, Exception):
                logger.error(f"Ошибка генерации сводки: {full_response}")
                full_response = "Не удалось создать сводку"

            # Парсим ответ на название статьи, краткую сводку и Telegraph HTML
            article_title, short_summary, telegraph_html = self.parse_gemini_response(full_response)

            # Используем название из тега [NAME] или создаем из краткой сводки
            if article_title:
                telegraph_title = article_title.strip()
            else:
                # Fallback: создаем заголовок из краткой сводки
                telegraph_title = short_summary.strip()
                if telegraph_title.startswith(('💡', '⚠️', '🎯', '🚀', '📚', '🔍', '💰', '🎥', '📝', '🌟')):
                    telegraph_title = telegraph_title[2:].strip()  # Убираем эмодзи и пробел
                telegraph_title = telegraph_title[:100] + "..." if len(telegraph_title) > 100 else telegraph_title

            # Создаем задачи для параллельной обработки Telegraph и отправки сообщений
            telegraph_task = asyncio.create_task(
                self.publish_to_telegraph(telegraph_title, telegraph_html)
            )

            # Удаляем стикер асинхронно (только если он был отправлен)
            delete_sticker_task = None
            if sticker_message and not isinstance(sticker_message, Exception):
                delete_sticker_task = asyncio.create_task(
                    self._safe_delete_message(context, update.effective_chat.id, sticker_message.message_id)
                )

            # Ждем Telegraph URL
            telegraph_url = await telegraph_task

            # Форматируем краткую сводку с заголовком
            formatted_short_summary = f"<b>Краткая сводка:</b>\n\n{short_summary}"

            # Создаем сообщение с краткой сводкой и ссылкой на Telegraph
            if telegraph_url:
                # Если Telegraph статья создана успешно
                message_with_link = f"{formatted_short_summary}\n\n<a href='{telegraph_url}'><b>📖 Подробная сводка</b></a>"

                # Проверяем, нужно ли добавить кнопку подписки на автора (только для личных чатов)
                reply_markup = None
                if not self._is_group_chat(update):
                    reply_markup = await self._create_subscription_button(user_id, video_info)

                # Отправляем сообщение асинхронно
                reply_task = asyncio.create_task(
                    update.message.reply_text(
                        message_with_link,
                        parse_mode='HTML',
                        disable_web_page_preview=False,
                        reply_markup=reply_markup
                    )
                )
            else:
                # Fallback: отправляем только краткую сводку если Telegraph не работает
                # Проверяем, нужно ли добавить кнопку подписки на автора (только для личных чатов)
                reply_markup = None
                if not self._is_group_chat(update):
                    reply_markup = await self._create_subscription_button(user_id, video_info)

                reply_task = asyncio.create_task(
                    update.message.reply_text(
                        formatted_short_summary,
                        parse_mode='HTML',
                        reply_markup=reply_markup
                    )
                )

                # Ждем отправки сообщения
                await reply_task

            # Ждем завершения удаления стикера (если он был отправлен)
            if delete_sticker_task:
                await delete_sticker_task

            # Сохраняем сводку в базу данных асинхронно (только ссылку на Telegraph, не HTML)
            save_db_task = asyncio.create_task(
                self.db.save_video_summary(
                    video_id=video_id,
                    video_url=url,
                    title=video_info.get('title'),
                    author=video_info.get('author'),
                    description=video_info.get('description'),
                    transcript_text=transcript_text,
                    short_summary=short_summary,
                    detailed_summary=None,  # Больше не используем подробную сводку
                    telegraph_html=None,  # Не сохраняем HTML для экономии места
                    telegraph_url=telegraph_url,  # Сохраняем только ссылку
                    article_title=article_title
                )
            )

            # Сохраняем транскрипцию, информацию о видео и комментарии для пользователя
            self.user_transcripts[user_id] = transcript_text
            self.user_video_info[user_id] = video_info
            self.user_comments[user_id] = comments

            # Очищаем предыдущий контекст переписки для нового видео
            if user_id in self.user_conversations:
                del self.user_conversations[user_id]

            # Отправляем сообщение о возможности задавать вопросы асинхронно только в личных чатах
            final_message_task = None
            if not self._is_group_chat(update):
                final_message_task = asyncio.create_task(
                    context.bot.send_message(
                        chat_id=update.effective_chat.id,
                        text="😌 <b>Если есть какие-то вопросы по видео - задавайте!</b> Отвечу на основе видео.",
                        parse_mode='HTML'
                    )
                )

            # Ждем отправки финального сообщения и сохранения в базу данных
            if telegraph_url:
                if final_message_task:
                    results = await asyncio.gather(reply_task, final_message_task, save_db_task, return_exceptions=True)
                    save_result = results[2]  # результат save_db_task
                else:
                    results = await asyncio.gather(reply_task, save_db_task, return_exceptions=True)
                    save_result = results[1]  # результат save_db_task
            else:
                if final_message_task:
                    results = await asyncio.gather(final_message_task, save_db_task, return_exceptions=True)
                    save_result = results[1]  # результат save_db_task
                else:
                    results = await asyncio.gather(save_db_task, return_exceptions=True)
                    save_result = results[0]  # результат save_db_task

            # Проверяем результат сохранения в базу данных
            if isinstance(save_result, Exception):
                logger.error(f"❌ Исключение при сохранении в БД для видео {video_id}: {save_result}")
            elif save_result is False:
                logger.error(f"❌ Не удалось сохранить сводку в БД для видео {video_id}")
            elif save_result is True:
                logger.info(f"✅ Сводка для видео {video_id} успешно сохранена в БД")
            else:
                logger.warning(f"⚠️ Неожиданный результат сохранения в БД: {save_result}")

            # Увеличиваем счетчик дневных сводок при успешном завершении
            await self.db.increment_daily_count(user_id)

        except YouTubeTranscriberError as e:
            # Удаляем стикер в случае ошибки асинхронно (только если он был отправлен)
            delete_task = None
            if sticker_message and not isinstance(sticker_message, Exception):
                delete_task = asyncio.create_task(
                    self._safe_delete_message(context, update.effective_chat.id, sticker_message.message_id)
                )

            error_message = f"Ошибка при получении транскрипции:\n{str(e)}"
            reply_task = asyncio.create_task(update.message.reply_text(error_message))

            # Выполняем операции параллельно - ПОЛНАЯ АСИНХРОННОСТЬ
            if delete_task:
                await asyncio.gather(delete_task, reply_task, return_exceptions=True)
            else:
                await reply_task
            logger.error(f"Ошибка транскрипции для пользователя {user_id}: {str(e)}")

        except Exception as e:
            # Удаляем стикер в случае ошибки асинхронно (только если он был отправлен)
            delete_task = None
            if sticker_message and not isinstance(sticker_message, Exception):
                delete_task = asyncio.create_task(
                    self._safe_delete_message(context, update.effective_chat.id, sticker_message.message_id)
                )

            error_message = f"Произошла ошибка:\n{str(e)}"
            reply_task = asyncio.create_task(update.message.reply_text(error_message))

            # Выполняем операции параллельно - ПОЛНАЯ АСИНХРОННОСТЬ
            if delete_task:
                await asyncio.gather(delete_task, reply_task, return_exceptions=True)
            else:
                await reply_task
            logger.error(f"Неожиданная ошибка для пользователя {user_id}: {str(e)}")
        finally:
            # Сбрасываем статус обработки для пользователя
            await self.db.set_user_processing_status(user_id, False)
            # Уменьшаем счетчик активных запросов
            self.active_requests -= 1
            logger.debug(f"Завершена обработка URL для пользователя {user_id} (активных: {self.active_requests})")

    async def _create_subscription_button(self, user_id: int, video_info: Dict) -> Optional[InlineKeyboardMarkup]:
        """
        Создает кнопку подписки на автора видео (только для личных чатов)

        Args:
            user_id: ID пользователя
            video_info: Информация о видео с channel_id, channel_url и author

        Returns:
            InlineKeyboardMarkup с кнопкой подписки или None если кнопка не нужна
        """
        try:
            # Проверяем, есть ли информация о канале
            channel_id = video_info.get('channel_id')
            author = video_info.get('author')

            if not channel_id or not author:
                logger.debug("Нет информации о канале для создания кнопки подписки")
                return None

            # Проверяем лимит подписок пользователя
            subscription_count = await self.db.get_subscription_count(user_id)
            if subscription_count >= MAX_SUBSCRIPTIONS_PER_USER:
                logger.debug(f"Пользователь {user_id} достиг лимита подписок ({subscription_count}/{MAX_SUBSCRIPTIONS_PER_USER})")
                return None

            # Проверяем, не подписан ли уже пользователь на этот канал
            user_subscriptions = await self.db.get_user_subscriptions(user_id)
            for sub in user_subscriptions:
                if sub["channel_id"] == channel_id:
                    logger.debug(f"Пользователь {user_id} уже подписан на канал {channel_id}")
                    return None

            # Создаем кнопку подписки
            # Обрезаем имя автора если оно слишком длинное
            author_name = author[:20] + "..." if len(author) > 20 else author
            button_text = SUBSCRIPTION_MESSAGES["subscription_button_text"].format(channel_name=author_name)

            keyboard = [[InlineKeyboardButton(
                button_text,
                callback_data=f"subscribe_to_author_{channel_id}"
            )]]

            logger.info(f"Создана кнопка подписки на канал {channel_id} для пользователя {user_id}")
            return InlineKeyboardMarkup(keyboard)

        except Exception as e:
            logger.error(f"Ошибка создания кнопки подписки для пользователя {user_id}: {e}")
            return None

    async def _safe_delete_message(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: Optional[int]) -> None:
        """Безопасное удаление сообщения с обработкой ошибок - ПОЛНОСТЬЮ АСИНХРОННО"""
        try:
            if message_id is not None:
                await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        except Exception:
            pass  # Игнорируем ошибки удаления стикера

    async def _keep_typing(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, stop_event: asyncio.Event) -> None:
        """Периодически отправляет статус 'печатает' пока не получен сигнал остановки"""
        try:
            while not stop_event.is_set():
                await context.bot.send_chat_action(
                    chat_id=chat_id,
                    action=ChatAction.TYPING
                )
                # Ждем 4 секунды или пока не получим сигнал остановки
                try:
                    await asyncio.wait_for(stop_event.wait(), timeout=4.0)
                    break  # Если получили сигнал остановки, выходим
                except asyncio.TimeoutError:
                    continue  # Продолжаем цикл
        except Exception as e:
            logger.warning(f"Ошибка при отправке статуса 'печатает': {str(e)}")

    async def handle_question(self, update: Update, context: ContextTypes.DEFAULT_TYPE, question: str) -> None:
        """Обработчик вопросов по видео - ПОЛНОСТЬЮ АСИНХРОННЫЙ"""
        if not update.message:
            logger.warning("Получено обновление без сообщения в handle_question")
            return

        user_id = update.effective_user.id
        message_id = update.message.message_id

        # Увеличиваем счетчик активных запросов
        self.active_requests += 1
        logger.info(f"Пользователь {user_id} задал вопрос: {question} - ПОЛНАЯ АСИНХРОННОСТЬ (активных запросов: {self.active_requests})")

        # Ставим реакцию "🤔" на сообщение с вопросом асинхронно
        reaction_task = asyncio.create_task(
            self._safe_set_reaction(context, update.effective_chat.id, message_id, "🤔")
        )

        # Получаем сохраненную транскрипцию, информацию о видео и комментарии
        transcript_text = self.user_transcripts.get(user_id)
        video_info = self.user_video_info.get(user_id)
        comments = self.user_comments.get(user_id, [])

        if not transcript_text:
            # Убираем реакцию перед отправкой ответа асинхронно
            remove_reaction_task = asyncio.create_task(
                self._safe_set_reaction(context, update.effective_chat.id, message_id, None)
            )

            reply_task = asyncio.create_task(
                update.message.reply_text(
                    "У меня нет сохраненной транскрипции видео. Отправьте сначала ссылку на YouTube видео."
                )
            )

            # Выполняем параллельно
            await asyncio.gather(remove_reaction_task, reply_task, return_exceptions=True)
            return

        try:
            # Создаем событие для остановки статуса "печатает"
            stop_typing_event = asyncio.Event()

            # Запускаем задачу для периодического показа статуса "печатает" асинхронно
            typing_task = asyncio.create_task(
                self._keep_typing(context, update.effective_chat.id, stop_typing_event)
            )

            try:
                # Получаем ответ от Gemini с учетом контекста переписки - БЕЗ ТАЙМАУТОВ
                answer_task = asyncio.create_task(
                    self.answer_question_with_gemini(user_id, transcript_text, question, video_info, comments)
                )

                # Ждем реакцию и ответ параллельно
                await reaction_task  # Ждем установки реакции
                answer = await answer_task  # Ждем ответ от Gemini

            finally:
                # Останавливаем статус "печатает"
                stop_typing_event.set()
                await typing_task

            # Убираем реакцию перед отправкой ответа асинхронно
            remove_reaction_task = asyncio.create_task(
                self._safe_set_reaction(context, update.effective_chat.id, message_id, None)
            )

            # Разделяем длинное сообщение на части, если необходимо
            message_parts = self.split_long_message(answer)

            # Создаем задачи для отправки ответа в реплай (возможно, в нескольких частях)
            reply_tasks = []
            for i, part in enumerate(message_parts):
                if i == 0:
                    # Первая часть - отвечаем в реплай на исходное сообщение
                    reply_tasks.append(asyncio.create_task(
                        context.bot.send_message(
                            chat_id=update.effective_chat.id,
                            text=part,
                            parse_mode='HTML',
                            reply_parameters=ReplyParameters(message_id=message_id)
                        )
                    ))
                else:
                    # Последующие части - обычные сообщения
                    reply_tasks.append(asyncio.create_task(
                        context.bot.send_message(
                            chat_id=update.effective_chat.id,
                            text=f"<b>Продолжение ответа:</b>\n\n{part}",
                            parse_mode='HTML'
                        )
                    ))

            # Ждем отправки всех сообщений и удаления реакции параллельно
            await asyncio.gather(remove_reaction_task, *reply_tasks, return_exceptions=True)

        except Exception as e:
            # Убираем реакцию в случае ошибки асинхронно
            remove_reaction_task = asyncio.create_task(
                self._safe_set_reaction(context, update.effective_chat.id, message_id, None)
            )

            error_message = f"Произошла ошибка при обработке вопроса:\n{str(e)}"
            error_reply_task = asyncio.create_task(
                context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=error_message,
                    reply_parameters=ReplyParameters(message_id=message_id)
                )
            )

            # Выполняем параллельно - ПОЛНАЯ АСИНХРОННОСТЬ
            await asyncio.gather(remove_reaction_task, error_reply_task, return_exceptions=True)
            logger.error(f"Ошибка при обработке вопроса пользователя {user_id}: {str(e)}")
        finally:
            # Уменьшаем счетчик активных запросов
            self.active_requests -= 1
            logger.info(f"Завершена обработка вопроса для пользователя {user_id} (активных запросов: {self.active_requests})")

    async def _safe_set_reaction(self, context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int, emoji: Optional[str]) -> None:
        """Безопасная установка/удаление реакции с обработкой ошибок"""
        try:
            if emoji:
                await context.bot.set_message_reaction(
                    chat_id=chat_id,
                    message_id=message_id,
                    reaction=[ReactionTypeEmoji(emoji=emoji)]
                )
            else:
                await context.bot.set_message_reaction(
                    chat_id=chat_id,
                    message_id=message_id,
                    reaction=[]
                )
        except Exception as e:
            logger.warning(f"Не удалось установить/убрать реакцию: {str(e)}")
    
    async def handle_text_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик текстовых сообщений - ПОЛНОСТЬЮ АСИНХРОННЫЙ"""
        # Проверяем, что сообщение содержит текст
        if not update.message or not update.message.text:
            logger.warning("Получено сообщение без текста в handle_text_message")
            return

        text = update.message.text.strip()

        # Проверяем, что текст не пустой после strip()
        if not text:
            logger.warning("Получено пустое сообщение в handle_text_message")
            return

        user_id = update.effective_user.id

        # Проверяем состояние пользователя
        user_state = self.user_states.get(user_id)
        if user_state == USER_STATE_WAITING_CHANNEL_URL:
            await self.handle_channel_url_input(update, context, text)
            return

        # Проверяем, является ли сообщение YouTube ссылкой асинхронно
        url_check_task = asyncio.create_task(
            asyncio.to_thread(self._is_youtube_url, text)
        )

        is_youtube_url = await url_check_task

        if is_youtube_url:
            # Обрабатываем YouTube URL - полностью асинхронно
            await self.handle_youtube_url(update, context)
        else:
            # В группах не обрабатываем текстовые сообщения как вопросы
            if self._is_group_chat(update):
                return  # Игнорируем текстовые сообщения в группах

            # В личных чатах обрабатываем вопросы
            if user_id in self.user_transcripts:
                # Это вопрос по видео - обрабатываем асинхронно
                await self.handle_question(update, context, text)
            else:
                # Отправляем сообщение асинхронно
                await update.message.reply_text(
                    "Отправьте ссылку на YouTube видео для создания сводки."
                )

    def _is_youtube_url(self, text: str) -> bool:
        """
        Проверяет, является ли текст YouTube URL
        Поддерживает максимальное количество форматов YouTube ссылок
        """
        import re

        # Нормализуем текст - убираем лишние пробелы
        text = text.strip()

        # Если это прямой video ID (11 символов)
        if re.match(r'^[a-zA-Z0-9_-]{11}$', text):
            return True

        # Добавляем протокол если его нет
        normalized_text = text
        if not text.startswith(('http://', 'https://')):
            # Проверяем, начинается ли с домена YouTube
            if text.startswith(('youtube.com', 'youtu.be', 'www.youtube.com', 'www.youtu.be', 'm.youtube.com')):
                normalized_text = 'https://' + text

        # Расширенные паттерны для всех возможных форматов YouTube URL
        youtube_patterns = [
            # Стандартные форматы
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtube\.com/v/([a-zA-Z0-9_-]{11})',

            # Мобильные форматы
            r'(?:https?://)?(?:m\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:m\.)?youtu\.be/([a-zA-Z0-9_-]{11})',

            # Форматы с дополнительными параметрами
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?.*[&?]v=([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11}).*',

            # Плейлисты с видео
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?.*[&?]v=([a-zA-Z0-9_-]{11}).*[&?]list=',
            r'(?:https?://)?(?:www\.)?youtube\.com/playlist\?.*[&?]v=([a-zA-Z0-9_-]{11})',

            # Shorts
            r'(?:https?://)?(?:www\.)?youtube\.com/shorts/([a-zA-Z0-9_-]{11})',

            # Gaming
            r'(?:https?://)?(?:www\.)?youtube\.com/gaming/watch\?v=([a-zA-Z0-9_-]{11})',

            # Старые форматы
            r'(?:https?://)?(?:www\.)?youtube\.com/watch#!v=([a-zA-Z0-9_-]{11})',

            # Без протокола - только домены
            r'^(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
            r'^(?:www\.)?youtu\.be/([a-zA-Z0-9_-]{11})',
            r'^(?:m\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
        ]

        # Проверяем оригинальный текст и нормализованный
        for text_to_check in [text, normalized_text]:
            for pattern in youtube_patterns:
                if re.search(pattern, text_to_check, re.IGNORECASE):
                    return True

        return False

    async def handle_my_chat_member(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик события добавления бота в чат"""
        try:
            # Получаем информацию об изменении статуса бота
            my_chat_member = update.my_chat_member
            if not my_chat_member:
                return

            old_status = my_chat_member.old_chat_member.status
            new_status = my_chat_member.new_chat_member.status
            chat = my_chat_member.chat

            # Проверяем, что бота добавили в группу или супергруппу
            if (chat.type in [ChatType.GROUP, ChatType.SUPERGROUP] and
                old_status in ['left', 'kicked'] and
                new_status == 'member'):

                logger.info(f"Бот добавлен в чат {chat.id} ({chat.title})")

                # Отправляем приветственное сообщение
                welcome_message = """👋 <b>Привет!</b>

Теперь я буду делать краткую сводку всех YouTube видео, что скидывают в чат.

📝 <b>Как это работает:</b>
• Просто отправьте ссылку на YouTube видео
• Я автоматически создам краткую сводку
• Сводка будет отправлена как ответ на ваше сообщение

⚡ <b>Просьба:</b> Сделайте меня администратором, чтобы я работал без перебоев и мог отвечать на все сообщения!"""

                await context.bot.send_message(
                    chat_id=chat.id,
                    text=welcome_message,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Ошибка в обработчике my_chat_member: {e}")

    # ========== МЕТОДЫ ДЛЯ РАБОТЫ С ПОДПИСКАМИ ==========

    async def show_subscriptions_menu(self, query, context):
        """Показывает главное меню подписок"""
        try:
            user_id = query.from_user.id

            # Получаем количество подписок пользователя
            subscription_count = await self.db.get_subscription_count(user_id)

            # Формируем текст сообщения
            message_text = SUBSCRIPTION_MESSAGES["welcome"].format(
                count=subscription_count,
                max_count=MAX_SUBSCRIPTIONS_PER_USER
            )

            # Создаем клавиатуру
            keyboard = []

            if subscription_count > 0:
                keyboard.append([InlineKeyboardButton("📋 Мои подписки", callback_data="list_subscriptions")])

            if subscription_count < MAX_SUBSCRIPTIONS_PER_USER:
                keyboard.append([InlineKeyboardButton("➕ Добавить канал", callback_data="add_subscription")])

            reply_markup = InlineKeyboardMarkup(keyboard) if keyboard else None

            await query.edit_message_text(
                text=message_text,
                parse_mode='HTML',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Ошибка в show_subscriptions_menu: {e}")
            await query.edit_message_text(
                "❌ Произошла ошибка при загрузке меню подписок.",
                parse_mode='HTML'
            )

    async def start_add_subscription(self, query, context):
        """Начинает процесс добавления подписки"""
        try:
            user_id = query.from_user.id

            # Проверяем лимит подписок
            subscription_count = await self.db.get_subscription_count(user_id)
            if subscription_count >= MAX_SUBSCRIPTIONS_PER_USER:
                await query.edit_message_text(
                    SUBSCRIPTION_MESSAGES["limit_reached"].format(max_count=MAX_SUBSCRIPTIONS_PER_USER),
                    parse_mode='HTML'
                )
                return

            # Устанавливаем состояние ожидания URL канала
            self.user_states[user_id] = USER_STATE_WAITING_CHANNEL_URL

            # Создаем кнопку "Назад"
            keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="back_to_subscriptions")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                SUBSCRIPTION_MESSAGES["add_channel"],
                parse_mode='HTML',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Ошибка в start_add_subscription: {e}")
            await query.edit_message_text(
                "❌ Произошла ошибка при добавлении подписки.",
                parse_mode='HTML'
            )

    async def handle_channel_url_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE, channel_url: str):
        """Обрабатывает ввод URL канала пользователем"""
        try:
            user_id = update.effective_user.id

            # Убираем состояние ожидания
            if user_id in self.user_states:
                del self.user_states[user_id]

            # Получаем информацию о канале
            channel_info = await self.get_channel_info(channel_url)

            if channel_info.get("error"):
                await update.message.reply_text(
                    SUBSCRIPTION_MESSAGES["invalid_channel"],
                    parse_mode='HTML'
                )
                return

            channel_id = channel_info["channel_id"]
            channel_name = channel_info["channel_name"]
            uploads_playlist_id = channel_info["uploads_playlist_id"]

            # Проверяем, не подписан ли уже пользователь на этот канал
            user_subscriptions = await self.db.get_user_subscriptions(user_id)
            for sub in user_subscriptions:
                if sub["channel_id"] == channel_id:
                    await update.message.reply_text(
                        SUBSCRIPTION_MESSAGES["channel_exists"],
                        parse_mode='HTML'
                    )
                    return

            # Получаем ID последнего видео канала для установки last_video_id
            last_video_id = None
            if uploads_playlist_id:
                latest_videos = await self.youtube_services.get_latest_videos_from_playlist(uploads_playlist_id, max_results=1)
                if latest_videos:
                    last_video_id = latest_videos[0].get('video_id')
                    logger.info(f"Получен last_video_id: {last_video_id} для канала {channel_id}")

            # Добавляем подписку с last_video_id
            success = await self.db.add_channel_subscription(
                user_id, channel_id, channel_name, channel_url, last_video_id
            )

            if success:
                await update.message.reply_text(
                    SUBSCRIPTION_MESSAGES["channel_added"].format(channel_name=html.escape(channel_name)),
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    SUBSCRIPTION_MESSAGES["processing_error"],
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Ошибка в handle_channel_url_input: {e}")
            await update.message.reply_text(
                SUBSCRIPTION_MESSAGES["processing_error"],
                parse_mode='HTML'
            )

    async def show_subscriptions_list(self, query, context):
        """Показывает список подписок пользователя"""
        try:
            user_id = query.from_user.id

            # Получаем подписки пользователя
            subscriptions = await self.db.get_user_subscriptions(user_id)

            if not subscriptions:
                keyboard = [[InlineKeyboardButton("🔙 Назад", callback_data="back_to_subscriptions")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    SUBSCRIPTION_MESSAGES["no_subscriptions"],
                    parse_mode='HTML',
                    reply_markup=reply_markup
                )
                return

            # Формируем текст со списком подписок
            message_text = f"📋 <b>Ваши подписки ({len(subscriptions)}/{MAX_SUBSCRIPTIONS_PER_USER}):</b>\n\n"

            keyboard = []
            for i, sub in enumerate(subscriptions, 1):
                channel_name = sub["channel_name"]
                message_text += f"{i}. <b>{html.escape(channel_name)}</b>\n"

                # Добавляем кнопку удаления для каждого канала
                keyboard.append([
                    InlineKeyboardButton(
                        f"🗑️ Удалить {channel_name[:20]}{'...' if len(channel_name) > 20 else ''}",
                        callback_data=f"remove_sub_{sub['channel_id']}"
                    )
                ])

            # Добавляем кнопки управления
            keyboard.append([InlineKeyboardButton("➕ Добавить канал", callback_data="add_subscription")])
            keyboard.append([InlineKeyboardButton("🔙 Назад", callback_data="back_to_subscriptions")])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                text=message_text,
                parse_mode='HTML',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Ошибка в show_subscriptions_list: {e}")
            await query.edit_message_text(
                "❌ Произошла ошибка при загрузке списка подписок.",
                parse_mode='HTML'
            )

    async def remove_subscription(self, query, context, channel_id: str):
        """Удаляет подписку пользователя"""
        try:
            user_id = query.from_user.id

            # Получаем информацию о канале перед удалением
            user_subscriptions = await self.db.get_user_subscriptions(user_id)
            channel_name = None
            for sub in user_subscriptions:
                if sub["channel_id"] == channel_id:
                    channel_name = sub["channel_name"]
                    break

            # Удаляем подписку
            success = await self.db.remove_channel_subscription(user_id, channel_id)

            if success and channel_name:
                await query.edit_message_text(
                    SUBSCRIPTION_MESSAGES["channel_removed"].format(channel_name=html.escape(channel_name)),
                    parse_mode='HTML'
                )
            else:
                await query.edit_message_text(
                    SUBSCRIPTION_MESSAGES["processing_error"],
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Ошибка в remove_subscription: {e}")
            await query.edit_message_text(
                SUBSCRIPTION_MESSAGES["processing_error"],
                parse_mode='HTML'
            )

    async def handle_subscribe_to_author(self, query, context, channel_id: str):
        """Обрабатывает подписку на автора видео"""
        try:
            user_id = query.from_user.id

            # Проверяем лимит подписок пользователя
            subscription_count = await self.db.get_subscription_count(user_id)
            if subscription_count >= MAX_SUBSCRIPTIONS_PER_USER:
                await query.answer(
                    SUBSCRIPTION_MESSAGES["author_limit_reached"].format(max_count=MAX_SUBSCRIPTIONS_PER_USER),
                    show_alert=True
                )
                return

            # Проверяем, не подписан ли уже пользователь на этот канал
            user_subscriptions = await self.db.get_user_subscriptions(user_id)
            for sub in user_subscriptions:
                if sub["channel_id"] == channel_id:
                    # Обновляем кнопку на "✅ Подписан"
                    channel_name = sub["channel_name"]
                    button_text = SUBSCRIPTION_MESSAGES["subscription_button_subscribed"].format(channel_name=channel_name[:15] + "..." if len(channel_name) > 15 else channel_name)
                    keyboard = [[InlineKeyboardButton(button_text, callback_data="already_subscribed")]]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    await query.edit_message_reply_markup(reply_markup=reply_markup)
                    await query.answer(
                        SUBSCRIPTION_MESSAGES["author_already_subscribed"].format(channel_name=channel_name),
                        show_alert=True
                    )
                    return

            # Показываем индикатор обработки
            await query.answer(SUBSCRIPTION_MESSAGES["author_subscription_processing"])

            # Получаем информацию о канале
            channel_url = f"https://www.youtube.com/channel/{channel_id}"
            channel_info = await self.get_channel_info(channel_url)

            if channel_info.get("error"):
                await query.answer(SUBSCRIPTION_MESSAGES["author_subscription_error"], show_alert=True)
                return

            channel_name = channel_info["channel_name"]
            uploads_playlist_id = channel_info["uploads_playlist_id"]

            # ВАЖНО: Получаем ID последнего видео канала для установки last_video_id
            last_video_id = None
            if uploads_playlist_id:
                latest_videos = await self.youtube_services.get_latest_videos_from_playlist(uploads_playlist_id, max_results=1)
                if latest_videos:
                    last_video_id = latest_videos[0].get('video_id')
                    logger.info(f"Получен last_video_id: {last_video_id} для канала {channel_id}")

            # Добавляем подписку с last_video_id
            success = await self.db.add_channel_subscription(
                user_id, channel_id, channel_name, channel_url, last_video_id
            )

            if success:
                # Обновляем кнопку на "✅ Подписан"
                button_text = SUBSCRIPTION_MESSAGES["subscription_button_subscribed"].format(channel_name=channel_name[:15] + "..." if len(channel_name) > 15 else channel_name)
                keyboard = [[InlineKeyboardButton(button_text, callback_data="already_subscribed")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_reply_markup(reply_markup=reply_markup)
                await query.answer(
                    SUBSCRIPTION_MESSAGES["author_subscribed"].format(channel_name=channel_name),
                    show_alert=True
                )

                logger.info(f"Пользователь {user_id} подписался на канал {channel_name} ({channel_id}) через кнопку автора")
            else:
                await query.answer(SUBSCRIPTION_MESSAGES["author_subscription_error"], show_alert=True)

        except Exception as e:
            logger.error(f"Ошибка в handle_subscribe_to_author: {e}")
            await query.answer(SUBSCRIPTION_MESSAGES["author_subscription_error"], show_alert=True)

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Обработчик ошибок для Telegram Bot"""
        from telegram.error import NetworkError, TimedOut, RetryAfter

        error = context.error
        logger.error(f"Ошибка в боте: {error}")

        # Обработка сетевых ошибок
        if isinstance(error, (NetworkError, TimedOut)):
            logger.warning(f"Сетевая ошибка: {error}. Бот продолжит работу.")
            return

        # Обработка ошибки превышения лимита запросов
        if isinstance(error, RetryAfter):
            logger.warning(f"Превышен лимит запросов. Ожидание {error.retry_after} секунд.")
            return

        # Для других ошибок логируем подробную информацию
        import traceback
        logger.error(f"Необработанная ошибка: {error}")
        logger.error(f"Traceback: {traceback.format_exc()}")

    def _setup_monitoring_system(self):
        """Настраивает систему мониторинга"""
        try:
            # Добавляем проверки здоровья компонентов
            youtube_checker = YouTubeAPIHealthChecker(YOUTUBE_DATA_API_KEY)
            self.health_check_system.add_checker(youtube_checker)

            database_checker = DatabaseHealthChecker(self.db)
            self.health_check_system.add_checker(database_checker)

            telegram_checker = TelegramBotHealthChecker(TELEGRAM_BOT_TOKEN)
            self.health_check_system.add_checker(telegram_checker)

            # Настраиваем алерты
            alerting_system.add_telegram_alerter(
                TELEGRAM_BOT_TOKEN,
                [ADMIN_CHAT_ID] if 'ADMIN_CHAT_ID' in globals() else []
            )

            # Настраиваем пороги алертов для видео обработки
            alerting_system.set_suppression_rule("video_processing_error", timedelta(minutes=15))
            alerting_system.set_suppression_rule("api_quota_exhausted", timedelta(hours=1))

            monitoring_logger.info(
                EventType.HEALTH_CHECK,
                "Система мониторинга настроена успешно",
                context={
                    "health_checkers": len(self.health_check_system.checkers),
                    "alerting_configured": True
                }
            )

        except Exception as e:
            monitoring_logger.error(
                EventType.HEALTH_CHECK,
                "Ошибка настройки системы мониторинга",
                error_details={"exception": str(e)}
            )

    async def log_api_metrics_periodically(self):
        """Периодически логирует метрики API"""
        while True:
            try:
                await asyncio.sleep(3600)  # Каждый час
                self.api_metrics.log_metrics_summary()

                # Также запускаем проверку здоровья системы
                await self.health_check_system.run_all_checks()

                # Проверяем алерты
                await alert_manager.check_alerts()

            except asyncio.CancelledError:
                logger.info("📊 Остановка периодического логирования метрик")
                break
            except Exception as e:
                logger.error(f"Ошибка при логировании метрик: {e}")


async def start_bot():
    """Асинхронная функция запуска бота для использования в ma2.py"""
    application = None
    bot = None
    try:
        # Создаем экземпляр бота
        bot = YouTubeSummaryBot()

        # ИНИЦИАЛИЗИРУЕМ БАЗУ ДАННЫХ ПЕРЕД ЗАПУСКОМ БОТА
        logger.info("🗄️ Инициализация базы данных YouTube бота...")
        try:
            await bot.db.init_database()
            logger.info("✅ База данных YouTube бота инициализирована успешно")
        except Exception as e:
            logger.error(f"❌ Ошибка инициализации базы данных: {e}")
            raise

        # ИНИЦИАЛИЗИРУЕМ МОНИТОРИНГ КАНАЛОВ
        logger.info("🔄 Инициализация мониторинга каналов...")
        bot.channel_monitor = ChannelMonitor(bot)

        # Создаем Telegram Bot API БЕЗ ПРОКСИ для избежания ошибок "Network unreachable"
        from telegram.ext import ApplicationBuilder
        from telegram.request import HTTPXRequest

        # Создаем кастомный request объект БЕЗ ПРОКСИ - УСТАНАВЛИВАЕМ БОЛЬШИЕ ТАЙМАУТЫ
        request = HTTPXRequest(
            connection_pool_size=50,  # Увеличиваем пул соединений для параллельности
            read_timeout=300,         # Таймаут чтения 5 минут (было 60 сек)
            write_timeout=300,        # Таймаут записи 5 минут (было 60 сек)
            connect_timeout=120,      # Таймаут подключения 2 минуты (было 30 сек)
            pool_timeout=60,          # Таймаут получения соединения из пула 1 минута (было 10 сек)
            # proxy=proxy_url  # УБИРАЕМ ПРОКСИ ДЛЯ TELEGRAM BOT API
        )

        # Создаем приложение БЕЗ ПРОКСИ и с ПОЛНОЙ КОНКУРЕНТНОСТЬЮ
        application = (
            ApplicationBuilder()
            .token(TELEGRAM_BOT_TOKEN)
            .request(request)
            .concurrent_updates(True)  # ВКЛЮЧАЕМ КОНКУРЕНТНУЮ ОБРАБОТКУ СООБЩЕНИЙ
            .build()
        )

        # Настройки для более стабильной работы (bootstrap_retries удален в новых версиях)
        # application.updater.bootstrap_retries = 10
        # application.updater.bootstrap_retry_delay = 15

        print("YouTube Bot: Telegram Bot API настроен БЕЗ ПРОКСИ для избежания ошибок 'Network unreachable'")
        print("YouTube Bot: ПРОКСИ ИСПОЛЬЗУЕТСЯ ТОЛЬКО ДЛЯ ТРАНСКРИПЦИИ В youtube_transcriber.py")
        print("YouTube Bot: ВКЛЮЧЕНА ПОЛНАЯ КОНКУРЕНТНАЯ ОБРАБОТКА СООБЩЕНИЙ - множественные пользователи могут работать одновременно!")

        # Добавляем обработчики
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("info", bot.info_command))
        application.add_handler(CommandHandler("settings", bot.settings_command))
        application.add_handler(CommandHandler("subscription", bot.subscription_command))
        application.add_handler(CallbackQueryHandler(bot.handle_callback_query))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_text_message))

        # Добавляем обработчик для события добавления бота в чат
        from telegram.ext import ChatMemberHandler
        application.add_handler(ChatMemberHandler(bot.handle_my_chat_member, ChatMemberHandler.MY_CHAT_MEMBER))

        # Добавляем обработчик ошибок
        application.add_error_handler(bot.error_handler)

        logger.info("🎥 YouTube бот запущен и готов к работе с ПОЛНОЙ КОНКУРЕНТНОСТЬЮ...")

        # Инициализируем приложение
        await application.initialize()
        await application.start()

        # Сохраняем ссылку на application в боте для отправки уведомлений
        bot.application = application

        # ЗАПУСКАЕМ МОНИТОРИНГ КАНАЛОВ
        logger.info("🔄 Запуск мониторинга каналов...")
        await bot.channel_monitor.start_monitoring()
        logger.info("✅ Мониторинг каналов запущен")

        # Запускаем updater с обработкой сетевых ошибок
        max_retries = 10
        retry_delay = 15

        for attempt in range(max_retries):
            try:
                await application.updater.start_polling(allowed_updates=Update.ALL_TYPES)
                break
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Ошибка при запуске polling (попытка {attempt + 1}/{max_retries}): {e}")
                    logger.info(f"Повторная попытка через {retry_delay} секунд...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Экспоненциальная задержка
                else:
                    logger.error(f"Не удалось запустить polling после {max_retries} попыток: {e}")
                    raise

        # Ждем бесконечно (пока не будет отменено)
        try:
            while True:
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            logger.info("🎥 YouTube бот получил сигнал остановки")
            raise

    except asyncio.CancelledError:
        logger.info("🎥 YouTube бот остановлен")
        raise
    except Exception as e:
        logger.error(f"❌ Ошибка при запуске YouTube бота: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Корректно останавливаем приложение
        if application is not None:
            try:
                if application.updater.running:
                    await application.updater.stop()
                await application.stop()
                await application.shutdown()
            except Exception as e:
                logger.error(f"Ошибка при остановке приложения: {e}")

        # Останавливаем мониторинг каналов
        if bot is not None and bot.channel_monitor:
            try:
                logger.info("🛑 Остановка мониторинга каналов...")
                await bot.channel_monitor.stop_monitoring()
            except Exception as e:
                logger.error(f"Ошибка при остановке мониторинга: {e}")

        # Закрываем сессию при завершении
        if bot is not None:
            try:
                await bot.close_session()
            except Exception as e:
                logger.error(f"Ошибка при закрытии сессии: {e}")
        logger.info("🎥 YouTube бот завершил работу")



def main():
    """Основная функция запуска бота (для запуска отдельно)"""
    # Создаем экземпляр бота
    bot = YouTubeSummaryBot()

    # Создаем Telegram Bot API БЕЗ ПРОКСИ для избежания ошибок "Network unreachable"
    from telegram.ext import ApplicationBuilder
    from telegram.request import HTTPXRequest

    # Создаем кастомный request объект БЕЗ ПРОКСИ - УСТАНАВЛИВАЕМ БОЛЬШИЕ ТАЙМАУТЫ
    request = HTTPXRequest(
        connection_pool_size=50,  # Увеличиваем пул соединений для параллельности
        read_timeout=300,         # Таймаут чтения 5 минут (было 60 сек)
        write_timeout=300,        # Таймаут записи 5 минут (было 60 сек)
        connect_timeout=120,      # Таймаут подключения 2 минуты (было 30 сек)
        pool_timeout=60,          # Таймаут получения соединения из пула 1 минута (было 10 сек)
        # proxy=proxy_url  # УБИРАЕМ ПРОКСИ ДЛЯ TELEGRAM BOT API
    )

    # Создаем приложение БЕЗ ПРОКСИ и с ПОЛНОЙ КОНКУРЕНТНОСТЬЮ
    application = (
        ApplicationBuilder()
        .token(TELEGRAM_BOT_TOKEN)
        .request(request)
        .concurrent_updates(True)  # ВКЛЮЧАЕМ КОНКУРЕНТНУЮ ОБРАБОТКУ СООБЩЕНИЙ
        .build()
    )

    # Настраиваем параметры updater для более стабильной работы (bootstrap_retries удален в новых версиях)
    # application.updater.bootstrap_retries = 10
    # application.updater.bootstrap_retry_delay = 15

    print("YouTube Bot: Telegram Bot API настроен БЕЗ ПРОКСИ для избежания ошибок 'Network unreachable'")
    print("YouTube Bot: ПРОКСИ ИСПОЛЬЗУЕТСЯ ТОЛЬКО ДЛЯ ТРАНСКРИПЦИИ В youtube_transcriber.py")
    print("YouTube Bot: ВКЛЮЧЕНА ПОЛНАЯ КОНКУРЕНТНАЯ ОБРАБОТКА СООБЩЕНИЙ - множественные пользователи могут работать одновременно!")

    # Добавляем обработчики
    application.add_handler(CommandHandler("start", bot.start_command))
    application.add_handler(CommandHandler("info", bot.info_command))
    application.add_handler(CommandHandler("stats", bot.stats_command))
    application.add_handler(CommandHandler("settings", bot.settings_command))
    application.add_handler(CommandHandler("subscription", bot.subscription_command))
    application.add_handler(CallbackQueryHandler(bot.handle_callback_query))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_text_message))

    # Добавляем обработчик для события добавления бота в чат
    from telegram.ext import ChatMemberHandler
    application.add_handler(ChatMemberHandler(bot.handle_my_chat_member, ChatMemberHandler.MY_CHAT_MEMBER))

    # Добавляем обработчик ошибок
    application.add_error_handler(bot.error_handler)

    # Обработчик сигналов для корректного завершения
    def signal_handler(signum, frame):
        logger.info(f"Получен сигнал {signum}, завершаем работу...")
        # Закрываем сессию синхронно
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(bot.close_session())
            loop.close()
        except Exception as e:
            logger.error(f"Ошибка при закрытии сессии: {e}")
        sys.exit(0)

    # Регистрируем обработчики сигналов
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    async def init_and_run():
        try:
            # Инициализируем базу данных
            logger.info("Инициализация базы данных...")
            await bot.db.init_database()
            logger.info("База данных инициализирована успешно")

            # Запускаем бота
            logger.info("Запуск бота...")
            await application.initialize()
            await application.start()
            await application.updater.start_polling(allowed_updates=Update.ALL_TYPES)
            
            # Ждем завершения (используем run_polling вместо idle)
            import signal
            import asyncio
            
            # Создаем событие для остановки
            stop_event = asyncio.Event()
            
            def signal_handler(signum, frame):
                logger.info(f"Получен сигнал {signum}, останавливаем бота...")
                stop_event.set()
            
            # Регистрируем обработчики сигналов
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            logger.info("🎉 Бот успешно запущен и готов к работе!")
            logger.info("📱 Отправьте боту ссылку на YouTube видео для создания сводки")
            logger.info("⏹️ Нажмите Ctrl+C для остановки бота")
            
            # Ждем сигнала остановки
            await stop_event.wait()
            
        except Exception as e:
            logger.error(f"Ошибка при запуске бота: {e}")
        finally:
            logger.info("Завершение работы бота...")
            try:
                await application.stop()
                await application.shutdown()
                await bot.close_session()
            except Exception as e:
                logger.error(f"Ошибка при завершении: {e}")

    # Запускаем асинхронную функцию
    try:
        asyncio.run(init_and_run())
    except KeyboardInterrupt:
        logger.info("Получен сигнал прерывания, завершаем работу...")
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")


if __name__ == "__main__":
    main()
