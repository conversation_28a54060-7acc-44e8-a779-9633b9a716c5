#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы нового Gemini API с YouTube видео
"""

import asyncio
import os
from google import genai
from google.genai import types

async def test_gemini_video():
    """Тестирует создание сводки YouTube видео через новый Gemini API"""
    
    # Проверяем наличие API ключа
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Не найден GEMINI_API_KEY в переменных окружения")
        return
    
    # Тестовое видео
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    prompt = """Создай краткую сводку этого YouTube видео на русском языке. 
Начни с подходящего эмодзи и опиши основное содержание в 1-2 предложениях."""
    
    try:
        print(f"🔄 Тестируем Gemini API с видео: {video_url}")
        
        # Создаем клиент Gemini
        client = genai.Client(api_key=api_key)
        
        # Пробуем сначала gemini-2.5-flash-lite
        model = "gemini-2.5-flash-lite"
        
        # Формируем контент с прикрепленным видео
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part(
                        file_data=types.FileData(
                            file_uri=video_url,
                            mime_type="video/*",
                        ),
                        video_metadata=types.VideoMetadata(
                            fps=0.01,
                        ),
                    ),
                    types.Part.from_text(text=prompt),
                ],
            ),
        ]
        
        # Конфигурация для gemini-2.5-flash-lite
        generate_content_config = types.GenerateContentConfig(
            thinking_config=types.ThinkingConfig(
                thinking_budget=128,
            ),
        )
        
        print(f"📡 Отправляем запрос к {model}...")
        
        # Выполняем запрос к Gemini
        response = await asyncio.to_thread(
            client.models.generate_content,
            model=model,
            contents=contents,
            config=generate_content_config
        )
        
        if response and response.text:
            print(f"✅ Успешно получена сводка от {model}:")
            print(f"📝 {response.text}")
        else:
            print(f"❌ Пустой ответ от {model}")
            
            # Пробуем fallback на gemini-2.0-flash
            print("🔄 Пробуем gemini-2.0-flash...")
            model = "gemini-2.0-flash"
            generate_content_config = types.GenerateContentConfig()
            
            response = await asyncio.to_thread(
                client.models.generate_content,
                model=model,
                contents=contents,
                config=generate_content_config
            )
            
            if response and response.text:
                print(f"✅ Успешно получена сводка от {model}:")
                print(f"📝 {response.text}")
            else:
                print(f"❌ Пустой ответ от {model}")
                
    except Exception as e:
        print(f"❌ Ошибка при тестировании Gemini API: {e}")
        
        # Пробуем fallback на gemini-2.0-flash при ошибке
        try:
            print("🔄 Пробуем gemini-2.0-flash после ошибки...")
            model = "gemini-2.0-flash"
            generate_content_config = types.GenerateContentConfig()
            
            response = await asyncio.to_thread(
                client.models.generate_content,
                model=model,
                contents=contents,
                config=generate_content_config
            )
            
            if response and response.text:
                print(f"✅ Успешно получена сводка от {model}:")
                print(f"📝 {response.text}")
            else:
                print(f"❌ Пустой ответ от {model}")
                
        except Exception as e2:
            print(f"❌ Ошибка и с gemini-2.0-flash: {e2}")

if __name__ == "__main__":
    asyncio.run(test_gemini_video())