"""
YouTube Services Module
Содержит класс YouTubeServices для работы с YouTube Data API v3
Извлечен из telegram_bot.py для улучшения модульности кода
"""

import asyncio
import logging
import random
import re
import aiohttp
from typing import Optional, Dict, Any, List

# Импорт конфигурации и утилит
from config_and_utils import (
    YOUTUBE_DATA_API_KEY, YOUTUBE_DATA_API_URL, YOUTUBE_COMMENTS_API_URL,
    YOUTUBE_CHANNELS_API_URL, YOUTUBE_SEARCH_API_URL, YOUTUBE_PLAYLISTS_API_URL,
    MAX_RETRY_ATTEMPTS, RETRY_BASE_DELAY, RETRY_MAX_DELAY, RETRY_EXPONENTIAL_BASE,
    RETRYABLE_HTTP_STATUSES, validate_video_id, APIMetrics, logger
)

# Импорт транскрибера для извлечения video_id
from youtube_transcriber import YouTubeTranscriber


class YouTubeServices:
    """
    Класс для работы с YouTube Data API v3
    Содержит все методы для получения информации о видео, каналах и плейлистах
    """
    
    def __init__(self, api_metrics: APIMetrics):
        """
        Инициализация YouTube сервисов
        
        Args:
            api_metrics: Экземпляр APIMetrics для отслеживания метрик API
        """
        self.api_metrics = api_metrics
        self.transcriber = YouTubeTranscriber(max_workers=50)
        self._session = None
        logger.info("🔧 Инициализирован YouTubeServices")
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Получает или создает HTTP сессию без прокси для YouTube API"""
        if self._session is None or self._session.closed:
            # Создаем сессию БЕЗ таймаутов для полной асинхронности
            timeout = aiohttp.ClientTimeout(total=None, connect=None, sock_read=None, sock_connect=None)
            self._session = aiohttp.ClientSession(timeout=timeout)
            logger.debug("YouTube Services: HTTP сессия создана БЕЗ прокси и БЕЗ ТАЙМАУТОВ")
        return self._session
    
    async def close_session(self):
        """Закрывает HTTP сессию"""
        if self._session and not self._session.closed:
            await self._session.close()
            logger.debug("YouTube Services: HTTP сессия закрыта")
    
    async def get_video_info(self, video_url: str) -> Dict[str, Optional[str]]:
        """
        АСИНХРОННОЕ получение информации о видео через YouTube Data API v3

        Args:
            video_url: YouTube URL или video ID

        Returns:
            Словарь с информацией о видео: title, author, description, channel_id, channel_url
        """
        try:
            # Извлекаем video ID из URL
            video_id = self.transcriber.extract_video_id(video_url)
            if not video_id:
                logger.error(f"Не удалось извлечь video ID из URL: {video_url}")
                return {"video_id": None, "title": None, "author": None, "description": None, "channel_id": None, "channel_url": None}

            logger.info(f"Извлечен video_id: {video_id} из URL: {video_url}")

            # Параметры запроса к YouTube Data API v3
            params = {
                "part": "snippet",
                "id": video_id,
                "key": YOUTUBE_DATA_API_KEY
            }

            # Получаем HTTP сессию без прокси для YouTube Data API
            session = await self.get_session()

            logger.info(f"YouTube Data API: Запрос информации о видео {video_id}")

            # Выполняем асинхронный запрос к YouTube Data API v3
            async with session.get(YOUTUBE_DATA_API_URL, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if "items" in data and len(data["items"]) > 0:
                        snippet = data["items"][0]["snippet"]
                        channel_id = snippet.get("channelId")
                        channel_url = f"https://www.youtube.com/channel/{channel_id}" if channel_id else None

                        video_info = {
                            "video_id": video_id,  # Добавляем video_id в возвращаемый словарь
                            "title": snippet.get("title"),
                            "author": snippet.get("channelTitle"),
                            "description": snippet.get("description"),
                            "channel_id": channel_id,
                            "channel_url": channel_url
                        }

                        logger.info(f"YouTube Data API: ✅ Получена информация о видео {video_id}")
                        return video_info
                    else:
                        logger.error(f"YouTube Data API: Видео {video_id} не найдено")
                        return {"video_id": None, "title": None, "author": None, "description": None, "channel_id": None, "channel_url": None}
                else:
                    logger.error(f"YouTube Data API: Ошибка {response.status} при запросе информации о видео {video_id}")
                    return {"video_id": None, "title": None, "author": None, "description": None, "channel_id": None, "channel_url": None}

        except Exception as e:
            logger.error(f"Ошибка получения информации о видео: {str(e)}")
            return {"video_id": None, "title": None, "author": None, "description": None, "channel_id": None, "channel_url": None}

    async def get_video_comments(self, video_url: str, max_comments: int = 30) -> List[str]:
        """
        Получает первые комментарии к видео через YouTube Data API v3

        Args:
            video_url: URL видео YouTube
            max_comments: Максимальное количество комментариев (по умолчанию 30)

        Returns:
            Список текстов комментариев
        """
        try:
            # Извлекаем video ID из URL
            video_id = self.transcriber.extract_video_id(video_url)
            if not video_id:
                logger.error(f"Не удалось извлечь video ID из URL: {video_url}")
                return []

            # Параметры запроса к YouTube Data API v3 commentThreads
            params = {
                "part": "snippet",
                "videoId": video_id,
                "key": YOUTUBE_DATA_API_KEY,
                "maxResults": min(max_comments, 100),  # API лимит 100
                "order": "relevance",  # Получаем самые релевантные комментарии
                "textFormat": "plainText"  # Получаем комментарии в виде обычного текста
            }

            # Получаем HTTP сессию без прокси для YouTube Data API
            session = await self.get_session()

            logger.info(f"YouTube Comments API: Запрос комментариев для видео {video_id}")

            # Выполняем асинхронный запрос к YouTube Data API v3
            async with session.get(YOUTUBE_COMMENTS_API_URL, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    comments = []
                    if "items" in data and len(data["items"]) > 0:
                        for item in data["items"]:
                            try:
                                comment_text = item["snippet"]["topLevelComment"]["snippet"]["textDisplay"]
                                # Очищаем комментарий от лишних символов и ограничиваем длину
                                comment_text = comment_text.strip()
                                if comment_text and len(comment_text) <= 500:  # Ограничиваем длину комментария
                                    comments.append(comment_text)
                            except KeyError:
                                continue

                        logger.info(f"YouTube Comments API: ✅ Получено {len(comments)} комментариев")
                        return comments[:max_comments]  # Возвращаем не больше запрошенного количества
                    else:
                        logger.info("YouTube Comments API: Комментарии не найдены")
                        return []
                elif response.status == 403:
                    # Комментарии отключены для видео
                    logger.info("YouTube Comments API: Комментарии отключены для этого видео")
                    return []
                else:
                    logger.error(f"YouTube Comments API: Ошибка {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Ошибка получения комментариев: {str(e)}")
            return []

    async def extract_channel_id_from_url(self, channel_url: str) -> Optional[str]:
        """
        Извлекает channel_id из различных форматов URL каналов

        Args:
            channel_url: URL канала в различных форматах

        Returns:
            channel_id или None если не удалось извлечь
        """
        try:
            if not channel_url:
                return None

            # Нормализуем URL
            channel_url = channel_url.strip()
            if not channel_url.startswith('http'):
                channel_url = 'https://' + channel_url

            # Паттерны для извлечения channel_id
            patterns = [
                # Прямой channel ID: https://www.youtube.com/channel/UC...
                r'youtube\.com/channel/([a-zA-Z0-9_-]+)',
                # Handle: https://www.youtube.com/@username
                r'youtube\.com/@([a-zA-Z0-9_.-]+)',
                # Custom URL: https://www.youtube.com/c/channelname
                r'youtube\.com/c/([a-zA-Z0-9_.-]+)',
                # User: https://www.youtube.com/user/username
                r'youtube\.com/user/([a-zA-Z0-9_.-]+)',
            ]

            for pattern in patterns:
                match = re.search(pattern, channel_url, re.IGNORECASE)
                if match:
                    identifier = match.group(1)

                    # Если это уже channel ID (начинается с UC)
                    if identifier.startswith('UC') and len(identifier) == 24:
                        return identifier

                    # Иначе нужно получить channel ID через API
                    return await self._resolve_channel_id(identifier, channel_url)

            logger.error(f"Не удалось извлечь идентификатор канала из URL: {channel_url}")
            return None

        except Exception as e:
            logger.error(f"Ошибка извлечения channel_id: {e}")
            return None

    async def _resolve_channel_id(self, identifier: str, original_url: str) -> Optional[str]:
        """
        Получает channel_id через YouTube Data API для handle/username/custom URL
        """
        try:
            session = await self.get_session()

            # Пробуем разные способы поиска канала
            search_methods = [
                # Поиск по handle (@username)
                {"part": "snippet", "forHandle": identifier, "key": YOUTUBE_DATA_API_KEY},
                # Поиск по username
                {"part": "snippet", "forUsername": identifier, "key": YOUTUBE_DATA_API_KEY},
            ]

            for method_index, params in enumerate(search_methods):
                try:
                    logger.debug(f"Попытка {method_index + 1}: поиск канала с параметрами {list(params.keys())}")
                    async with session.get(YOUTUBE_CHANNELS_API_URL, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if "items" in data and len(data["items"]) > 0:
                                channel_id = data["items"][0]["id"]
                                logger.info(f"Найден channel_id: {channel_id} для {identifier}")
                                return channel_id
                        else:
                            logger.debug(f"Метод {method_index + 1} вернул статус {response.status}")
                except Exception as e:
                    logger.debug(f"Ошибка в методе {method_index + 1}: {e}")
                    continue

            # Если прямые методы не сработали, пробуем поиск
            logger.debug(f"Прямые методы не сработали, пробуем поиск для: {identifier}")
            search_params = {
                "part": "snippet",
                "q": identifier,
                "type": "channel",
                "maxResults": 5,
                "key": YOUTUBE_DATA_API_KEY
            }

            async with session.get(YOUTUBE_SEARCH_API_URL, params=search_params) as response:
                if response.status == 200:
                    data = await response.json()
                    if "items" in data and len(data["items"]) > 0:
                        # Ищем точное совпадение
                        for item in data["items"]:
                            snippet = item["snippet"]
                            channel_title = snippet.get("channelTitle", "").lower()
                            if identifier.lower() in channel_title:
                                channel_id = snippet["channelId"]
                                logger.info(f"Найден channel_id через поиск: {channel_id} для {identifier}")
                                return channel_id

            logger.error(f"Не удалось найти channel_id для {identifier} из URL {original_url}")
            return None

        except Exception as e:
            logger.error(f"Ошибка получения channel_id для {identifier}: {e}")
            return None

    async def get_channel_id_from_video_id(self, video_id: str) -> Optional[str]:
        """
        Получает channel_id из video_id через YouTube Data API

        Args:
            video_id: ID видео

        Returns:
            channel_id или None
        """
        try:
            params = {
                "part": "snippet",
                "id": video_id,
                "key": YOUTUBE_DATA_API_KEY
            }

            session = await self.get_session()
            async with session.get(YOUTUBE_DATA_API_URL, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if "items" in data and len(data["items"]) > 0:
                        channel_id = data["items"][0]["snippet"]["channelId"]
                        logger.info(f"Получен channel_id: {channel_id} для видео {video_id}")
                        return channel_id
                    else:
                        logger.error(f"Видео {video_id} не найдено")
                        return None
                else:
                    logger.error(f"Ошибка API при получении информации о видео {video_id}: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Ошибка получения channel_id из video_id {video_id}: {e}")
            return None

    async def get_channel_info(self, channel_url: str) -> Dict[str, Optional[str]]:
        """
        Получает информацию о канале через YouTube Data API

        Args:
            channel_url: URL канала или видео

        Returns:
            Словарь с информацией о канале
        """
        try:
            # Проверяем, является ли это ссылкой на видео
            video_id = self.transcriber.extract_video_id(channel_url)

            if video_id:
                # Это ссылка на видео - получаем channel_id из video_id
                logger.info(f"Обнаружена ссылка на видео: {video_id}, получаем channel_id")
                channel_id = await self.get_channel_id_from_video_id(video_id)
                if not channel_id:
                    return {"error": "Не удалось получить информацию о канале из видео"}

                # Формируем URL канала для корректного отображения
                channel_url = f"https://www.youtube.com/channel/{channel_id}"
            else:
                # Это прямая ссылка на канал - используем существующую логику
                channel_id = await self.extract_channel_id_from_url(channel_url)
                if not channel_id:
                    return {"error": "Не удалось извлечь ID канала"}

            # Получаем полную информацию о канале
            params = {
                "part": "snippet,contentDetails",
                "id": channel_id,
                "key": YOUTUBE_DATA_API_KEY
            }

            session = await self.get_session()
            async with session.get(YOUTUBE_CHANNELS_API_URL, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if "items" in data and len(data["items"]) > 0:
                        channel_info = data["items"][0]["snippet"]
                        content_details = data["items"][0]["contentDetails"]

                        return {
                            "channel_id": channel_id,
                            "channel_name": channel_info.get("title"),
                            "channel_url": channel_url,
                            "uploads_playlist_id": content_details.get("relatedPlaylists", {}).get("uploads"),
                            "error": None
                        }
                return {"error": "Канал не найден"}
        except Exception as e:
            logger.error(f"Ошибка получения информации о канале: {e}")
            return {"error": "Ошибка API"}

    async def get_channel_uploads_playlist(self, channel_id: str) -> Optional[str]:
        """
        Получает ID плейлиста uploads канала

        Args:
            channel_id: ID канала

        Returns:
            ID плейлиста uploads или None
        """
        try:
            params = {
                "part": "contentDetails",
                "id": channel_id,
                "key": YOUTUBE_DATA_API_KEY
            }

            # Используем retry механизм для API запроса
            data = await self._retry_api_request(
                self._make_youtube_api_request,
                YOUTUBE_CHANNELS_API_URL,
                params
            )

            if data is None:
                logger.error(f"Не удалось получить информацию о канале {channel_id} после всех попыток")
                return None

            if "items" in data and len(data["items"]) > 0:
                content_details = data["items"][0]["contentDetails"]
                uploads_playlist_id = content_details.get("relatedPlaylists", {}).get("uploads")
                return uploads_playlist_id
            return None
        except Exception as e:
            logger.error(f"Ошибка получения плейлиста uploads для канала {channel_id}: {e}")
            return None

    async def _retry_api_request(self, request_func, *args, **kwargs) -> Optional[Dict]:
        """
        Универсальная функция для retry API запросов с экспоненциальной задержкой

        Args:
            request_func: Асинхронная функция для выполнения запроса
            *args, **kwargs: Аргументы для request_func

        Returns:
            Результат запроса или None при неудаче
        """
        # Извлекаем URL для метрик (первый аргумент)
        endpoint = args[0] if args else "unknown_endpoint"
        self.api_metrics.record_request_start(endpoint)

        last_exception = None

        for attempt in range(MAX_RETRY_ATTEMPTS):
            try:
                logger.debug(f"API запрос к {endpoint}, попытка {attempt + 1}/{MAX_RETRY_ATTEMPTS}")

                if attempt > 0:
                    self.api_metrics.record_retry_attempt(endpoint)

                result = await request_func(*args, **kwargs)

                if result is not None:
                    self.api_metrics.record_request_success(endpoint)
                    if attempt > 0:
                        logger.info(f"API запрос к {endpoint} успешен с попытки {attempt + 1}")
                    return result

            except Exception as e:
                last_exception = e
                error_type = type(e).__name__
                logger.warning(f"Попытка {attempt + 1} для {endpoint} неудачна: {error_type}: {e}")

                # Если это последняя попытка, не ждем
                if attempt == MAX_RETRY_ATTEMPTS - 1:
                    break

                # Вычисляем задержку с экспоненциальным backoff
                delay = min(
                    RETRY_BASE_DELAY * (RETRY_EXPONENTIAL_BASE ** attempt),
                    RETRY_MAX_DELAY
                )

                # Добавляем случайный jitter для избежания thundering herd
                jitter = random.uniform(0.1, 0.3) * delay
                total_delay = delay + jitter

                logger.debug(f"Ожидание {total_delay:.2f}с перед следующей попыткой")
                await asyncio.sleep(total_delay)

        # Записываем финальную неудачу
        error_type = type(last_exception).__name__ if last_exception else "UnknownError"
        self.api_metrics.record_request_failure(endpoint, error_type)

        logger.error(f"Все {MAX_RETRY_ATTEMPTS} попыток API запроса к {endpoint} неудачны. Последняя ошибка: {last_exception}")
        return None

    async def _make_youtube_api_request(self, url: str, params: Dict) -> Optional[Dict]:
        """
        Выполняет HTTP запрос к YouTube API с обработкой ошибок

        Args:
            url: URL для запроса
            params: Параметры запроса

        Returns:
            JSON ответ или None при ошибке
        """
        try:
            session = await self.get_session()
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                elif response.status in RETRYABLE_HTTP_STATUSES:
                    logger.warning(f"Получен retryable HTTP статус {response.status} для {url}")
                    # Записываем HTTP статус для метрик
                    self.api_metrics.record_request_failure(url, f"HTTP_{response.status}", response.status)
                    return None  # Будет повторен через retry механизм
                else:
                    logger.error(f"Неретryable HTTP ошибка {response.status} для {url}")
                    # Записываем HTTP статус для метрик
                    self.api_metrics.record_request_failure(url, f"HTTP_{response.status}", response.status)
                    raise Exception(f"HTTP {response.status}")

        except Exception as e:
            error_type = type(e).__name__
            logger.error(f"Ошибка HTTP запроса к {url}: {error_type}: {e}")
            # Не записываем метрики здесь, так как это будет сделано в _retry_api_request
            raise e

    def _validate_playlist_api_response(self, data: Dict) -> bool:
        """
        Валидирует структуру ответа YouTube Playlist API

        Args:
            data: Ответ от API

        Returns:
            True если структура корректна, False иначе
        """
        try:
            if not isinstance(data, dict):
                logger.error("Ответ API не является словарем")
                return False

            if "items" not in data:
                logger.warning("Отсутствует поле 'items' в ответе API")
                return True  # Пустой плейлист - это нормально

            if not isinstance(data["items"], list):
                logger.error("Поле 'items' не является списком")
                return False

            return True
        except Exception as e:
            logger.error(f"Ошибка валидации ответа API: {e}")
            return False

    def _validate_playlist_item_structure(self, item: Dict, item_index: int) -> Dict[str, any]:
        """
        Валидирует структуру элемента плейлиста

        Args:
            item: Элемент плейлиста
            item_index: Индекс элемента для логирования

        Returns:
            Словарь с результатом валидации: {"valid": bool, "error": str}
        """
        try:
            if not isinstance(item, dict):
                return {"valid": False, "error": "элемент не является словарем"}

            if "snippet" not in item:
                return {"valid": False, "error": "отсутствует поле 'snippet'"}

            snippet = item["snippet"]
            if not isinstance(snippet, dict):
                return {"valid": False, "error": "поле 'snippet' не является словарем"}

            if "resourceId" not in snippet:
                return {"valid": False, "error": "отсутствует поле 'snippet.resourceId'"}

            resource_id = snippet["resourceId"]
            if not isinstance(resource_id, dict):
                return {"valid": False, "error": "поле 'snippet.resourceId' не является словарем"}

            if "videoId" not in resource_id:
                return {"valid": False, "error": "отсутствует поле 'snippet.resourceId.videoId'"}

            video_id = resource_id["videoId"]
            if not isinstance(video_id, str):
                return {"valid": False, "error": f"поле 'videoId' не является строкой: {type(video_id)}"}

            if not video_id.strip():
                return {"valid": False, "error": "поле 'videoId' пустое"}

            return {"valid": True, "error": ""}

        except Exception as e:
            return {"valid": False, "error": f"неожиданная ошибка валидации: {e}"}

    async def get_latest_videos_from_playlist(self, playlist_id: str, max_results: int = 5) -> List[Dict]:
        """
        Получает последние видео из плейлиста uploads с информацией о длительности

        Args:
            playlist_id: ID плейлиста
            max_results: Максимальное количество видео

        Returns:
            Список информации о видео с полем duration
        """
        try:
            params = {
                "part": "snippet",
                "playlistId": playlist_id,
                "maxResults": min(max_results, 50),  # API лимит 50
                "order": "date",
                "key": YOUTUBE_DATA_API_KEY
            }

            # Используем retry механизм для API запроса
            data = await self._retry_api_request(
                self._make_youtube_api_request,
                YOUTUBE_PLAYLISTS_API_URL,
                params
            )

            if data is None:
                logger.error(f"Не удалось получить данные плейлиста {playlist_id} после всех попыток")
                return []

            # Валидация структуры ответа API
            if not self._validate_playlist_api_response(data):
                logger.error(f"Некорректная структура ответа API для плейлиста {playlist_id}")
                return []

            videos = []

            if "items" in data:
                # Собираем video_ids для получения длительности
                video_ids = []
                video_snippets = {}

                for item_index, item in enumerate(data["items"]):
                    try:
                        # Детальная валидация структуры элемента
                        validation_result = self._validate_playlist_item_structure(item, item_index)
                        if not validation_result["valid"]:
                            logger.warning(f"Пропуск видео #{item_index}: {validation_result['error']}")
                            continue

                        snippet = item["snippet"]
                        video_id = snippet["resourceId"]["videoId"]

                        # Валидация video_id сразу при извлечении
                        if not validate_video_id(video_id):
                            logger.warning(f"Пропуск видео #{item_index}: некорректный video_id '{video_id}'")
                            continue

                        video_ids.append(video_id)
                        video_snippets[video_id] = snippet
                        logger.debug(f"Обработано видео #{item_index}: {video_id} - {snippet.get('title', 'Без названия')}")

                    except KeyError as e:
                        logger.warning(f"Пропуск видео #{item_index} из-за отсутствующего поля: {e}")
                        continue
                    except Exception as e:
                        logger.error(f"Неожиданная ошибка при обработке видео #{item_index}: {e}")
                        continue

                logger.debug(f"Извлечено {len(video_ids)} валидных video_id из {len(data['items'])} элементов")

                # Получаем длительность видео через videos API
                durations = await self._get_videos_duration(video_ids)

                # Формируем итоговый список видео
                for video_id in video_ids:
                    if video_id in video_snippets:
                        snippet = video_snippets[video_id]
                        video_info = {
                            "video_id": video_id,
                            "title": snippet.get("title", "Без названия"),
                            "published_at": snippet.get("publishedAt"),
                            "description": snippet.get("description", ""),
                            "thumbnail": snippet.get("thumbnails", {}).get("medium", {}).get("url"),
                            "duration": durations.get(video_id)  # Добавляем длительность
                        }
                        videos.append(video_info)

            logger.debug(f"Получено {len(videos)} видео из плейлиста {playlist_id}")
            return videos
        except Exception as e:
            logger.error(f"Ошибка получения видео из плейлиста {playlist_id}: {e}")
            return []

    def _validate_videos_api_response(self, data: Dict, requested_video_ids: List[str]) -> bool:
        """
        Валидирует структуру ответа YouTube Videos API

        Args:
            data: Ответ от API
            requested_video_ids: Список запрошенных video_id

        Returns:
            True если структура корректна, False иначе
        """
        try:
            if not isinstance(data, dict):
                logger.error("Ответ Videos API не является словарем")
                return False

            if "items" not in data:
                logger.warning(f"Отсутствует поле 'items' в ответе Videos API для {len(requested_video_ids)} видео")
                return True  # Может быть пустой ответ

            if not isinstance(data["items"], list):
                logger.error("Поле 'items' в Videos API не является списком")
                return False

            return True
        except Exception as e:
            logger.error(f"Ошибка валидации ответа Videos API: {e}")
            return False

    def _validate_video_item_structure(self, item: Dict, item_index: int) -> Dict[str, any]:
        """
        Валидирует структуру элемента видео из Videos API

        Args:
            item: Элемент видео
            item_index: Индекс элемента для логирования

        Returns:
            Словарь с результатом валидации: {"valid": bool, "error": str}
        """
        try:
            if not isinstance(item, dict):
                return {"valid": False, "error": "элемент не является словарем"}

            if "id" not in item:
                return {"valid": False, "error": "отсутствует поле 'id'"}

            video_id = item["id"]
            if not isinstance(video_id, str):
                return {"valid": False, "error": f"поле 'id' не является строкой: {type(video_id)}"}

            if not video_id.strip():
                return {"valid": False, "error": "поле 'id' пустое"}

            if "contentDetails" not in item:
                return {"valid": False, "error": "отсутствует поле 'contentDetails'"}

            content_details = item["contentDetails"]
            if not isinstance(content_details, dict):
                return {"valid": False, "error": "поле 'contentDetails' не является словарем"}

            return {"valid": True, "error": ""}

        except Exception as e:
            return {"valid": False, "error": f"неожиданная ошибка валидации: {e}"}

    async def _get_videos_duration(self, video_ids: List[str]) -> Dict[str, str]:
        """
        Получает длительность видео через YouTube Data API v3

        Args:
            video_ids: Список ID видео

        Returns:
            Словарь {video_id: duration_string} в формате ISO 8601
        """
        if not video_ids:
            logger.debug("Пустой список video_ids для получения длительности")
            return {}

        # Валидация входных данных
        valid_video_ids = []
        for video_id in video_ids[:50]:  # API лимит 50
            if validate_video_id(video_id):
                valid_video_ids.append(video_id)
            else:
                logger.warning(f"Пропуск некорректного video_id при получении длительности: {video_id}")

        if not valid_video_ids:
            logger.warning("Нет валидных video_id для получения длительности")
            return {}

        try:
            video_ids_str = ",".join(valid_video_ids)
            logger.debug(f"Запрос длительности для {len(valid_video_ids)} видео: {video_ids_str}")

            params = {
                "part": "contentDetails",
                "id": video_ids_str,
                "key": YOUTUBE_DATA_API_KEY
            }

            # Используем retry механизм для API запроса
            data = await self._retry_api_request(
                self._make_youtube_api_request,
                "https://www.googleapis.com/youtube/v3/videos",
                params
            )

            if data is None:
                logger.error(f"Не удалось получить длительность для {len(valid_video_ids)} видео после всех попыток")
                return {}

            # Валидация структуры ответа
            if not self._validate_videos_api_response(data, valid_video_ids):
                logger.error("Некорректная структура ответа Videos API")
                return {}

            durations = {}
            processed_count = 0
            error_count = 0

            if "items" in data:
                for item_index, item in enumerate(data["items"]):
                    try:
                        # Детальная валидация элемента
                        validation_result = self._validate_video_item_structure(item, item_index)
                        if not validation_result["valid"]:
                            logger.warning(f"Пропуск видео #{item_index} в Videos API: {validation_result['error']}")
                            error_count += 1
                            continue

                        video_id = item.get("id")
                        content_details = item.get("contentDetails", {})
                        duration = content_details.get("duration")

                        if video_id and duration:
                            # Дополнительная валидация video_id
                            if not validate_video_id(video_id):
                                logger.warning(f"Некорректный video_id в ответе Videos API: {video_id}")
                                error_count += 1
                                continue

                            durations[video_id] = duration
                            processed_count += 1
                            logger.debug(f"Получена длительность для {video_id}: {duration}")
                        else:
                            logger.warning(f"Отсутствуют данные о длительности для видео #{item_index}: video_id={video_id}, duration={duration}")
                            error_count += 1

                    except Exception as e:
                        logger.error(f"Ошибка обработки видео #{item_index} в Videos API: {e}")
                        error_count += 1
                        continue

            # Проверяем, что получили данные для всех запрошенных видео
            missing_videos = set(valid_video_ids) - set(durations.keys())
            if missing_videos:
                logger.warning(f"Не получена длительность для {len(missing_videos)} видео: {missing_videos}")

            if error_count > 0:
                logger.warning(f"Получена длительность для {processed_count}/{len(valid_video_ids)} видео, ошибок: {error_count}")
            else:
                logger.debug(f"Получена длительность для {processed_count}/{len(valid_video_ids)} видео")
            return durations
        except Exception as e:
            logger.error(f"Неожиданная ошибка получения длительности видео: {e}")
            return {}

    @staticmethod
    def parse_iso8601_duration(duration_str: str) -> Optional[int]:
        """
        Парсит ISO 8601 duration в секунды

        Args:
            duration_str: Строка длительности в формате ISO 8601 (например, PT4M13S)

        Returns:
            Длительность в секундах или None если формат некорректный

        Examples:
            PT4M13S -> 253 секунды
            PT45S -> 45 секунд
            PT1H2M3S -> 3723 секунды
            PT2M -> 120 секунд
            PT1H -> 3600 секунд
        """
        if not duration_str or not duration_str.startswith('PT'):
            return None

        # Паттерн для парсинга ISO 8601 duration: PT[nH][nM][nS]
        pattern = r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?'
        match = re.match(pattern, duration_str)

        if not match:
            return None

        hours = int(match.group(1) or 0)
        minutes = int(match.group(2) or 0)
        seconds = int(match.group(3) or 0)

        total_seconds = hours * 3600 + minutes * 60 + seconds
        return total_seconds
