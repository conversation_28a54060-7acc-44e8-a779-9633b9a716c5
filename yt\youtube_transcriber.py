"""
YouTube Transcriber Module - ПОЛНОСТЬЮ АСИНХРОННАЯ ВЕРСИЯ
Модуль для получения субтитров с YouTube видео через youtube-transcript-api с прокси
ПОДДЕРЖИВАЕТ МНОЖЕСТВЕННЫЕ ОДНОВРЕМЕННЫЕ ЗАПРОСЫ БЕЗ БЛОКИРОВОК

Использование:
    from youtube_transcriber import YouTubeTranscriber

    transcriber = YouTubeTranscriber()
    # Можно запускать множественные запросы одновременно
    tasks = [
        transcriber.get_transcript("https://youtube.com/watch?v=..."),
        transcriber.get_transcript("https://youtube.com/watch?v=..."),
        transcriber.get_transcript("https://youtube.com/watch?v=...")
    ]
    results = await asyncio.gather(*tasks, return_exceptions=True)
"""

import re
import asyncio
import json
import os
from typing import List, Dict, Optional
from datetime import timedelta
import concurrent.futures
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import TranscriptsDisabled, NoTranscriptFound, VideoUnavailable
from youtube_transcript_api.proxies import GenericProxyConfig

# Загружаем конфигурацию прокси из JSON файла
def load_proxy_config():
    """Загружает конфигурацию прокси из proxy_config.json в родительской папке"""
    try:
        # Получаем путь к текущей папке (где находится этот файл)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Поднимаемся на уровень выше (родительская папка)
        parent_dir = os.path.dirname(current_dir)
        config_path = os.path.join(parent_dir, 'proxy_config.json')

        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        proxy_config = config.get('proxy', {})

        if not proxy_config.get('enabled', False):
            print("YouTube Transcriber: Прокси отключен в конфигурации")
            return None

        # Формируем URL прокси
        proxy_url = f"http://{proxy_config['username']}:{proxy_config['password']}@{proxy_config['host']}:{proxy_config['http_port']}"

        print(f"YouTube Transcriber: Загружена конфигурация прокси из {config_path}")
        print(f"YouTube Transcriber: Прокси {proxy_config['host']}:{proxy_config['http_port']}")

        return {
            'url': proxy_url,
            'host': proxy_config['host'],
            'port': proxy_config['http_port'],
            'username': proxy_config['username'],
            'password': proxy_config['password']
        }

    except FileNotFoundError:
        print("YouTube Transcriber: ОШИБКА - Файл proxy_config.json не найден в родительской папке!")
        print(f"YouTube Transcriber: Ожидаемый путь: {config_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"YouTube Transcriber: ОШИБКА - Неверный формат JSON в proxy_config.json: {e}")
        return None
    except Exception as e:
        print(f"YouTube Transcriber: ОШИБКА при загрузке конфигурации прокси: {e}")
        return None

# Загружаем конфигурацию прокси
PROXY_CONFIG = load_proxy_config()
PROXY_URL = PROXY_CONFIG['url'] if PROXY_CONFIG else None


class YouTubeTranscriberError(Exception):
    """Базовое исключение для YouTubeTranscriber"""
    pass


class VideoNotFoundError(YouTubeTranscriberError):
    """Видео не найдено или недоступно"""
    pass


class TranscriptNotFoundError(YouTubeTranscriberError):
    """Субтитры не найдены"""
    pass


class YouTubeTranscriber:
    """
    ПОЛНОСТЬЮ АСИНХРОННЫЙ класс для получения субтитров с YouTube видео
    Поддерживает множественные одновременные запросы через прокси БЕЗ БЛОКИРОВОК
    """

    def __init__(self, default_language: str = 'ru', max_workers: int = 20):
        """
        Инициализация транскрибера

        Args:
            default_language: Язык субтитров по умолчанию
            max_workers: Максимальное количество воркеров для параллельных запросов
        """
        self.default_language = default_language
        self.fallback_languages = ['en', 'ru']
        # Создаем пул потоков для полной асинхронности
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)

        print(f"YouTube Transcriber: ПОЛНОСТЬЮ АСИНХРОННЫЙ режим с {max_workers} воркерами")
        if PROXY_CONFIG:
            print(f"YouTube Transcriber: Настроен IPv4 HTTP прокси {PROXY_CONFIG['host']}:{PROXY_CONFIG['port']}")
        else:
            print("YouTube Transcriber: ВНИМАНИЕ - Прокси не настроен! Работа может быть нестабильной")
        print(f"YouTube Transcriber: Поддержка множественных одновременных запросов БЕЗ БЛОКИРОВОК")

    def extract_video_id(self, url: str) -> Optional[str]:
        """
        Извлекает video ID из YouTube URL
        Поддерживает максимальное количество форматов YouTube ссылок

        Args:
            url: YouTube URL или video ID

        Returns:
            Video ID или None если не удалось извлечь
        """
        # Нормализуем URL - убираем лишние пробелы
        url = url.strip()

        # Если это уже video ID (11 символов)
        if re.match(r'^[a-zA-Z0-9_-]{11}$', url):
            return url

        # Добавляем протокол если его нет
        normalized_url = url
        if not url.startswith(('http://', 'https://')):
            # Проверяем, начинается ли с домена YouTube
            if url.startswith(('youtube.com', 'youtu.be', 'www.youtube.com', 'www.youtu.be', 'm.youtube.com')):
                normalized_url = 'https://' + url

        # Расширенные паттерны для извлечения video ID из всех возможных форматов
        patterns = [
            # Стандартные форматы
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtu\.be/([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtube\.com/v/([a-zA-Z0-9_-]{11})',

            # Мобильные форматы
            r'(?:https?://)?(?:m\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:m\.)?youtu\.be/([a-zA-Z0-9_-]{11})',

            # Форматы с дополнительными параметрами
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?.*[&?]v=([a-zA-Z0-9_-]{11})',
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11}).*',

            # Плейлисты с видео
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?.*[&?]v=([a-zA-Z0-9_-]{11}).*[&?]list=',
            r'(?:https?://)?(?:www\.)?youtube\.com/playlist\?.*[&?]v=([a-zA-Z0-9_-]{11})',

            # Shorts
            r'(?:https?://)?(?:www\.)?youtube\.com/shorts/([a-zA-Z0-9_-]{11})',

            # Gaming
            r'(?:https?://)?(?:www\.)?youtube\.com/gaming/watch\?v=([a-zA-Z0-9_-]{11})',

            # Старые форматы
            r'(?:https?://)?(?:www\.)?youtube\.com/watch#!v=([a-zA-Z0-9_-]{11})',

            # Без протокола - только домены
            r'^(?:www\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',
            r'^(?:www\.)?youtu\.be/([a-zA-Z0-9_-]{11})',
            r'^(?:m\.)?youtube\.com/watch\?v=([a-zA-Z0-9_-]{11})',

            # Общие паттерны для извлечения ID
            r'[&?]v=([a-zA-Z0-9_-]{11})',
            r'/([a-zA-Z0-9_-]{11})(?:[&?#]|$)',
        ]

        # Проверяем оригинальный URL и нормализованный
        for url_to_check in [url, normalized_url]:
            for pattern in patterns:
                match = re.search(pattern, url_to_check, re.IGNORECASE)
                if match:
                    video_id = match.group(1)
                    # Дополнительная проверка что это действительно video ID
                    if re.match(r'^[a-zA-Z0-9_-]{11}$', video_id):
                        return video_id

        return None

    async def _get_transcript_with_api(self, video_id: str, language: Optional[str] = None) -> Optional[List[Dict]]:
        """
        ПОЛНОСТЬЮ АСИНХРОННОЕ получение субтитров через youtube-transcript-api с прокси

        Args:
            video_id: ID видео
            language: Язык субтитров

        Returns:
            Список сегментов субтитров или None если не удалось получить
        """
        try:
            print(f"YouTube Transcriber: АСИНХРОННОЕ получение субтитров для {video_id}")
            result = await self._try_get_transcript_with_api(video_id, language)
            if result:
                return result
        except Exception as e:
            print(f"YouTube Transcriber: Ошибка при получении субтитров: {e}")

        return None

    async def _try_get_transcript_with_api(self, video_id: str, language: Optional[str] = None) -> Optional[List[Dict]]:
        """
        ПОЛНОСТЬЮ АСИНХРОННЫЙ внутренний метод для получения субтитров
        Использует пул потоков для неблокирующего выполнения

        Args:
            video_id: ID видео
            language: Язык субтитров

        Returns:
            Список сегментов субтитров или None если не удалось получить
        """
        try:
            # Определяем языки для поиска
            target_language = language or self.default_language
            languages_to_try = [target_language] + [lang for lang in self.fallback_languages if lang != target_language]

            print(f"YouTube Transcriber: АСИНХРОННАЯ попытка для видео {video_id}")
            print(f"YouTube Transcriber: Языки для поиска: {languages_to_try}")

            def get_transcript_sync():
                """Синхронная функция для выполнения в пуле потоков"""
                try:
                    # Создаем API с прокси конфигурацией (если прокси доступен)
                    if PROXY_CONFIG and PROXY_URL:
                        proxy_config = GenericProxyConfig(
                            http_url=PROXY_URL,
                            https_url=PROXY_URL
                        )
                        # Используем официальный API с прокси
                        ytt_api = YouTubeTranscriptApi(proxy_config=proxy_config)
                        print(f"YouTube Transcriber: Используется прокси {PROXY_CONFIG['host']}:{PROXY_CONFIG['port']}")
                    else:
                        # Используем API без прокси
                        ytt_api = YouTubeTranscriptApi()
                        print("YouTube Transcriber: Используется прямое подключение (без прокси)")

                    # Пробуем получить субтитры для каждого языка
                    for lang in languages_to_try:
                        try:
                            print(f"YouTube Transcriber: Попытка языка: {lang}")
                            transcript_data = ytt_api.fetch(video_id, languages=[lang])
                            if transcript_data:
                                print(f"YouTube Transcriber: ✅ Субтитры найдены на языке: {lang}")
                                return transcript_data.to_raw_data()
                        except NoTranscriptFound:
                            print(f"YouTube Transcriber: Субтитры на языке {lang} не найдены")
                            continue
                        except Exception as e:
                            print(f"YouTube Transcriber: Ошибка для языка {lang}: {e}")
                            continue

                    # Если точные языки не найдены, пробуем получить любые доступные
                    try:
                        print("YouTube Transcriber: Попытка получения любых доступных субтитров")
                        transcript_list = ytt_api.list(video_id)

                        # Ищем подходящие субтитры
                        for transcript in transcript_list:
                            if transcript.language_code in languages_to_try:
                                print(f"YouTube Transcriber: Найдены субтитры на языке: {transcript.language_code}")
                                return transcript.fetch()

                        # Если нет подходящих, берем первые доступные
                        for transcript in transcript_list:
                            if transcript.is_generated or not transcript.is_translatable:
                                print(f"YouTube Transcriber: Используем автогенерированные субтитры на языке: {transcript.language_code}")
                                return transcript.fetch()

                        # В крайнем случае берем любые первые
                        first_transcript = next(iter(transcript_list), None)
                        if first_transcript:
                            print(f"YouTube Transcriber: Используем первые доступные субтитры на языке: {first_transcript.language_code}")
                            return first_transcript.fetch()

                    except Exception as e:
                        print(f"YouTube Transcriber: Ошибка при получении списка субтитров: {e}")

                    return None
                except Exception as e:
                    if PROXY_CONFIG:
                        print(f"YouTube Transcriber: Ошибка при создании API с прокси: {e}")
                    else:
                        print(f"YouTube Transcriber: Ошибка при создании API: {e}")
                    return None

            # ПОЛНОСТЬЮ АСИНХРОННОЕ выполнение через пул потоков
            loop = asyncio.get_event_loop()
            transcript_data = await loop.run_in_executor(self.executor, get_transcript_sync)

            if not transcript_data:
                print("YouTube Transcriber: Субтитры не найдены")
                return None

            # Конвертируем в наш формат
            result = []
            for entry in transcript_data:
                result.append({
                    'start': entry['start'],
                    'duration': entry['duration'],
                    'end': entry['start'] + entry['duration'],
                    'text': entry['text'].strip()
                })

            print(f"YouTube Transcriber: ✅ АСИНХРОННО обработано сегментов: {len(result)}")
            return result

        except Exception as e:
            print(f"YouTube Transcriber: Ошибка при асинхронном получении субтитров: {e}")
            return None


    async def get_transcript(self,
                            video_url: str,
                            language: Optional[str] = None,
                            prefer_generated: bool = True) -> List[Dict]:
        """
        ПОЛНОСТЬЮ АСИНХРОННОЕ получение субтитров для видео
        Поддерживает множественные одновременные запросы БЕЗ БЛОКИРОВОК

        Args:
            video_url: YouTube URL или video ID
            language: Язык субтитров (если None, используется default_language)
            prefer_generated: Предпочитать автогенерированные субтитры (не используется)

        Returns:
            Список сегментов субтитров

        Raises:
            VideoNotFoundError: Если видео не найдено
            TranscriptNotFoundError: Если субтитры не найдены
        """
        video_id = self.extract_video_id(video_url)
        if not video_id:
            raise VideoNotFoundError(f"Не удалось извлечь video ID из: {video_url}")

        target_language = language or self.default_language

        # ПОЛНОСТЬЮ АСИНХРОННОЕ получение субтитров через прокси
        try:
            print(f"YouTube Transcriber: АСИНХРОННЫЙ запрос субтитров для видео {video_id}")
            result = await self._get_transcript_with_api(video_id, target_language)
            if result:
                if PROXY_CONFIG:
                    print(f"YouTube Transcriber: ✅ АСИНХРОННО получены субтитры через HTTP прокси")
                else:
                    print(f"YouTube Transcriber: ✅ АСИНХРОННО получены субтитры через прямое подключение")
                return result
            else:
                raise TranscriptNotFoundError(f"Субтитры не найдены для языков: {[target_language] + self.fallback_languages}")
        except (TranscriptsDisabled, NoTranscriptFound, VideoUnavailable) as e:
            print(f"YouTube Transcriber: ❌ Ошибка youtube-transcript-api: {e}")
            raise TranscriptNotFoundError(f"Субтитры недоступны: {str(e)}")
        except Exception as e:
            if isinstance(e, TranscriptNotFoundError):
                raise e
            print(f"YouTube Transcriber: ❌ Ошибка при асинхронном получении: {e}")
            raise YouTubeTranscriberError(f"Ошибка при получении субтитров: {str(e)}")

    async def close(self):
        """Закрывает пул потоков при завершении работы"""
        if hasattr(self, 'executor') and self.executor:
            print("YouTube Transcriber: Закрытие пула потоков...")
            self.executor.shutdown(wait=True)
            print("YouTube Transcriber: Пул потоков закрыт")
    
    def format_timestamp(self, seconds: float, format_type: str = 'simple') -> str:
        """
        Форматирует временную метку

        Args:
            seconds: Время в секундах
            format_type: Тип формата ('simple', 'srt', 'vtt')

        Returns:
            Отформатированная временная метка
        """
        if format_type == 'srt':
            # Формат SRT: 00:00:00,000
            td = timedelta(seconds=seconds)
            hours, remainder = divmod(td.total_seconds(), 3600)
            minutes, secs = divmod(remainder, 60)
            milliseconds = int((secs % 1) * 1000)
            return f"{int(hours):02d}:{int(minutes):02d}:{int(secs):02d},{milliseconds:03d}"

        elif format_type == 'vtt':
            # Формат VTT: 00:00:00.000
            td = timedelta(seconds=seconds)
            hours, remainder = divmod(td.total_seconds(), 3600)
            minutes, secs = divmod(remainder, 60)
            milliseconds = int((secs % 1) * 1000)
            return f"{int(hours):02d}:{int(minutes):02d}:{int(secs):02d}.{milliseconds:03d}"

        else:
            # Простой формат: MM:SS или HH:MM:SS
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)

            if hours > 0:
                return f"{hours:02d}:{minutes:02d}:{secs:02d}"
            else:
                return f"{minutes:02d}:{secs:02d}"

    def format_as_text(self, transcript: List[Dict], include_timestamps: bool = True) -> str:
        """
        Форматирует субтитры как простой текст

        Args:
            transcript: Список сегментов субтитров
            include_timestamps: Включать ли временные метки

        Returns:
            Отформатированный текст
        """
        if include_timestamps:
            lines = []
            for entry in transcript:
                start_time = self.format_timestamp(entry['start'])
                end_time = self.format_timestamp(entry['end'])
                lines.append(f"[{start_time} - {end_time}] {entry['text']}")
            return '\n'.join(lines)
        else:
            return ' '.join(entry['text'] for entry in transcript)
