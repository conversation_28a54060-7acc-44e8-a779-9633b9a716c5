"""
YouTube Video Database - База данных SQLite для хранения сводок YouTube видео
Хранит информацию о видео, транскрипции и сводки для быстрого доступа
Переименован в yt_database.py чтобы избежать конфликтов с другими database.py файлами
"""

import sqlite3
import json
import logging
from typing import Optional, Dict, Any, Tuple, List
from datetime import datetime
import asyncio
import aiosqlite
from pathlib import Path

logger = logging.getLogger(__name__)

class VideoDatabase:
    """Класс для работы с базой данных видео"""
    
    def __init__(self, db_path: str = "video_summaries.db"):
        """
        Инициализация базы данных
        
        Args:
            db_path: Путь к файлу базы данных (в папке с ботом)
        """
        # Убеждаемся, что путь относительный к папке с ботом
        self.db_path = Path(__file__).parent / db_path
        logger.info(f"Инициализация базы данных: {self.db_path}")
    
    async def init_database(self):
        """Создает таблицы базы данных если они не существуют"""
        try:
            logger.info(f"🗄️ Инициализация базы данных по пути: {self.db_path}")
            logger.info(f"🗄️ Абсолютный путь: {self.db_path.absolute()}")

            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS video_summaries (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id TEXT UNIQUE NOT NULL,
                        video_url TEXT NOT NULL,
                        title TEXT,
                        author TEXT,
                        description TEXT,
                        transcript_text TEXT NOT NULL,
                        short_summary TEXT NOT NULL,
                        detailed_summary TEXT,
                        telegraph_html TEXT,
                        telegraph_url TEXT,
                        article_title TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Создаем таблицу для отслеживания пользователей
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS user_activity (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        date TEXT NOT NULL,
                        summary_count INTEGER DEFAULT 0,
                        is_processing INTEGER DEFAULT 0,
                        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(user_id, date)
                    )
                """)

                # Создаем индекс для быстрого поиска по video_id
                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_video_id ON video_summaries(video_id)
                """)

                # Создаем триггер для автоматического обновления updated_at
                await db.execute("""
                    CREATE TRIGGER IF NOT EXISTS update_video_summaries_updated_at
                    AFTER UPDATE ON video_summaries
                    FOR EACH ROW
                    BEGIN
                        UPDATE video_summaries SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
                    END
                """)

                # Создаем индекс для быстрого поиска по пользователю и дате
                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_user_date ON user_activity(user_id, date)
                """)

                # Создаем таблицу для настроек групп
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS group_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        chat_id INTEGER UNIQUE NOT NULL,
                        send_sticker INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Создаем индекс для быстрого поиска по chat_id
                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_chat_id ON group_settings(chat_id)
                """)

                # Создаем таблицу подписок пользователей на каналы
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS channel_subscriptions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        channel_id TEXT NOT NULL,
                        channel_name TEXT NOT NULL,
                        channel_url TEXT NOT NULL,
                        last_video_id TEXT,
                        last_check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_active INTEGER DEFAULT 1,
                        UNIQUE(user_id, channel_id)
                    )
                """)

                # Создаем таблицу для отслеживания обработанных видео
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS channel_videos (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        channel_id TEXT NOT NULL,
                        video_id TEXT NOT NULL,
                        video_title TEXT,
                        published_at TIMESTAMP,
                        processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(channel_id, video_id)
                    )
                """)

                # Создаем индексы для оптимизации подписок
                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_user_subscriptions ON channel_subscriptions(user_id)
                """)

                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_channel_id ON channel_subscriptions(channel_id)
                """)

                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_channel_videos ON channel_videos(channel_id, video_id)
                """)

                # Создаем таблицу для очереди ожидания субтитров
                await db.execute("""
                    CREATE TABLE IF NOT EXISTS pending_videos (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id TEXT NOT NULL,
                        channel_id TEXT NOT NULL,
                        video_info TEXT NOT NULL,        -- JSON с данными видео (title, author, etc.)
                        subscribers TEXT NOT NULL,       -- JSON массив с user_id подписчиков
                        attempt_count INTEGER DEFAULT 0,
                        next_attempt_time TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        max_attempts INTEGER DEFAULT 7,
                        UNIQUE(video_id)
                    )
                """)

                # Создаем индексы для оптимизации запросов очереди ожидания
                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_pending_video_id ON pending_videos(video_id)
                """)

                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_pending_next_attempt ON pending_videos(next_attempt_time)
                """)

                await db.execute("""
                    CREATE INDEX IF NOT EXISTS idx_pending_channel ON pending_videos(channel_id)
                """)

                await db.commit()
                logger.info(f"✅ База данных инициализирована успешно: {self.db_path}")

                # Проверяем, что файл действительно создался
                if self.db_path.exists():
                    file_size = self.db_path.stat().st_size
                    logger.info(f"✅ Файл базы данных создан, размер: {file_size} байт")
                else:
                    logger.error("❌ Файл базы данных не был создан!")

        except Exception as e:
            logger.error(f"❌ Ошибка инициализации базы данных: {e}")
            logger.error(f"❌ Путь к БД: {self.db_path}")
            raise
    
    async def get_video_summary(self, video_id: str) -> Optional[Dict[str, Any]]:
        """
        Получает сводку видео из базы данных

        Args:
            video_id: ID видео YouTube

        Returns:
            Словарь с данными видео или None если не найдено
        """
        try:
            logger.info(f"🔍 Поиск сводки для видео {video_id} в БД: {self.db_path}")

            # Проверяем, существует ли файл базы данных
            if not self.db_path.exists():
                logger.warning(f"⚠️ Файл базы данных не существует: {self.db_path}")
                return None

            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row

                cursor = await db.execute("""
                    SELECT * FROM video_summaries WHERE video_id = ?
                """, (video_id,))

                row = await cursor.fetchone()

                if row:
                    # Конвертируем Row в словарь
                    result = dict(row)
                    logger.info(f"✅ Найдена сводка для видео {video_id} в базе данных")
                    return result
                else:
                    logger.info(f"ℹ️ Сводка для видео {video_id} не найдена в базе данных")
                    return None

        except Exception as e:
            logger.error(f"❌ Ошибка получения сводки для видео {video_id}: {e}")
            logger.error(f"❌ Путь к БД: {self.db_path}")
            return None
    
    async def save_video_summary(self, 
                                video_id: str,
                                video_url: str,
                                title: Optional[str],
                                author: Optional[str], 
                                description: Optional[str],
                                transcript_text: str,
                                short_summary: str,
                                detailed_summary: Optional[str],
                                telegraph_html: Optional[str] = None,
                                telegraph_url: Optional[str] = None,
                                article_title: Optional[str] = None) -> bool:
        """
        Сохраняет сводку видео в базу данных

        Args:
            video_id: ID видео YouTube
            video_url: URL видео
            title: Название видео
            author: Автор видео
            description: Описание видео
            transcript_text: Текст транскрипции
            short_summary: Краткая сводка
            detailed_summary: Подробная сводка (может быть None, так как больше не используется)
            telegraph_html: HTML для Telegraph (обычно None для экономии места)
            telegraph_url: URL Telegraph статьи (основной способ доступа к подробной сводке)
            article_title: Название статьи

        Returns:
            True если сохранено успешно, False в случае ошибки
        """
        try:
            logger.info(f"💾 Начинаем сохранение сводки для видео {video_id}")
            logger.info(f"💾 Путь к БД: {self.db_path}")
            detailed_len = len(detailed_summary) if detailed_summary is not None else 0
            logger.info(f"💾 Размеры данных: transcript={len(transcript_text)}, short={len(short_summary)}, detailed={detailed_len}")

            # Проверяем, что путь к базе данных существует
            if not self.db_path.parent.exists():
                logger.error(f"❌ Директория для БД не существует: {self.db_path.parent}")
                return False

            async with aiosqlite.connect(self.db_path) as db:
                logger.info(f"💾 Подключение к БД установлено")

                await db.execute("""
                    INSERT OR REPLACE INTO video_summaries
                    (video_id, video_url, title, author, description, transcript_text,
                     short_summary, detailed_summary, telegraph_html, telegraph_url,
                     article_title)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    video_id, video_url, title, author, description, transcript_text,
                    short_summary, detailed_summary, telegraph_html, telegraph_url,
                    article_title
                ))

                logger.info(f"💾 SQL запрос выполнен")
                await db.commit()
                logger.info(f"💾 Транзакция зафиксирована")

                # Проверяем, что данные действительно сохранились
                cursor = await db.execute("SELECT COUNT(*) FROM video_summaries WHERE video_id = ?", (video_id,))
                count = await cursor.fetchone()
                logger.info(f"💾 Проверка: найдено записей для видео {video_id}: {count[0]}")

                # Проверяем размер файла базы данных
                if self.db_path.exists():
                    file_size = self.db_path.stat().st_size
                    logger.info(f"💾 Размер файла БД после сохранения: {file_size} байт")

                logger.info(f"✅ Сводка для видео {video_id} успешно сохранена в базу данных")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка сохранения сводки для видео {video_id}: {e}")
            logger.error(f"❌ Путь к БД: {self.db_path}")
            logger.error(f"❌ Тип ошибки: {type(e).__name__}")
            import traceback
            logger.error(f"❌ Трассировка: {traceback.format_exc()}")
            return False
    
    async def update_telegraph_info(self, video_id: str, telegraph_html: str, telegraph_url: str) -> bool:
        """
        Обновляет информацию о Telegraph статье для существующей записи
        
        Args:
            video_id: ID видео YouTube
            telegraph_html: HTML для Telegraph
            telegraph_url: URL Telegraph статьи
            
        Returns:
            True если обновлено успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    UPDATE video_summaries 
                    SET telegraph_html = ?, telegraph_url = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE video_id = ?
                """, (telegraph_html, telegraph_url, video_id))
                
                await db.commit()
                logger.info(f"Telegraph информация обновлена для видео {video_id}")
                return True
                
        except Exception as e:
            logger.error(f"Ошибка обновления Telegraph информации для видео {video_id}: {e}")
            return False
    

    async def cleanup_old_records(self, days_old: int = 30) -> int:
        """
        Удаляет старые записи из базы данных

        Args:
            days_old: Количество дней, после которых записи считаются старыми

        Returns:
            Количество удаленных записей
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    DELETE FROM video_summaries
                    WHERE created_at < datetime('now', '-{} days')
                """.format(days_old))

                deleted_count = cursor.rowcount
                await db.commit()

                logger.info(f"Удалено {deleted_count} старых записей из базы данных")
                return deleted_count

        except Exception as e:
            logger.error(f"Ошибка очистки старых записей: {e}")
            return 0

    async def check_user_processing_status(self, user_id: int) -> bool:
        """
        Проверяет, обрабатывается ли сейчас видео для пользователя

        Args:
            user_id: ID пользователя

        Returns:
            True если пользователь уже обрабатывает видео, False если нет
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    SELECT is_processing FROM user_activity
                    WHERE user_id = ? AND date = date('now')
                """, (user_id,))

                row = await cursor.fetchone()

                if row and row[0] == 1:
                    return True
                return False

        except Exception as e:
            logger.error(f"Ошибка проверки статуса обработки для пользователя {user_id}: {e}")
            return False

    async def set_user_processing_status(self, user_id: int, is_processing: bool) -> bool:
        """
        Устанавливает статус обработки для пользователя

        Args:
            user_id: ID пользователя
            is_processing: True если начинаем обработку, False если заканчиваем

        Returns:
            True если статус установлен успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                today = datetime.now().strftime('%Y-%m-%d')

                await db.execute("""
                    INSERT OR REPLACE INTO user_activity
                    (user_id, date, summary_count, is_processing, last_activity)
                    VALUES (
                        ?, ?,
                        COALESCE((SELECT summary_count FROM user_activity WHERE user_id = ? AND date = ?), 0),
                        ?,
                        CURRENT_TIMESTAMP
                    )
                """, (user_id, today, user_id, today, 1 if is_processing else 0))

                await db.commit()
                logger.info(f"Статус обработки для пользователя {user_id} установлен: {is_processing}")
                return True

        except Exception as e:
            logger.error(f"Ошибка установки статуса обработки для пользователя {user_id}: {e}")
            return False

    async def check_daily_limit(self, user_id: int, limit: int = 50) -> Tuple[bool, int]:
        """
        Проверяет дневной лимит сводок для пользователя

        Args:
            user_id: ID пользователя
            limit: Дневной лимит (по умолчанию 50)

        Returns:
            Кортеж (можно_создать_сводку, количество_использованных_сводок)
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                today = datetime.now().strftime('%Y-%m-%d')

                cursor = await db.execute("""
                    SELECT summary_count FROM user_activity
                    WHERE user_id = ? AND date = ?
                """, (user_id, today))

                row = await cursor.fetchone()
                current_count = row[0] if row else 0

                can_create = current_count < limit
                return can_create, current_count

        except Exception as e:
            logger.error(f"Ошибка проверки дневного лимита для пользователя {user_id}: {e}")
            return True, 0  # В случае ошибки разрешаем создание

    async def increment_daily_count(self, user_id: int) -> bool:
        """
        Увеличивает счетчик дневных сводок для пользователя

        Args:
            user_id: ID пользователя

        Returns:
            True если счетчик увеличен успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                today = datetime.now().strftime('%Y-%m-%d')

                await db.execute("""
                    INSERT OR REPLACE INTO user_activity
                    (user_id, date, summary_count, is_processing, last_activity)
                    VALUES (
                        ?, ?,
                        COALESCE((SELECT summary_count FROM user_activity WHERE user_id = ? AND date = ?), 0) + 1,
                        COALESCE((SELECT is_processing FROM user_activity WHERE user_id = ? AND date = ?), 0),
                        CURRENT_TIMESTAMP
                    )
                """, (user_id, today, user_id, today, user_id, today))

                await db.commit()
                logger.info(f"Счетчик дневных сводок увеличен для пользователя {user_id}")
                return True

        except Exception as e:
            logger.error(f"Ошибка увеличения счетчика для пользователя {user_id}: {e}")
            return False

    async def get_group_setting(self, chat_id: int, setting_name: str) -> bool:
        """
        Получает настройку группы

        Args:
            chat_id: ID чата группы
            setting_name: Название настройки (например, 'send_sticker')

        Returns:
            Значение настройки (True/False), по умолчанию True
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(
                    f"SELECT {setting_name} FROM group_settings WHERE chat_id = ?",
                    (chat_id,)
                )
                result = await cursor.fetchone()

                if result:
                    return bool(result[0])
                else:
                    # Если настройки нет, возвращаем значение по умолчанию (True)
                    return True

        except Exception as e:
            logger.error(f"❌ Ошибка получения настройки {setting_name} для группы {chat_id}: {e}")
            return True  # По умолчанию возвращаем True

    async def set_group_setting(self, chat_id: int, setting_name: str, value: bool) -> bool:
        """
        Устанавливает настройку группы

        Args:
            chat_id: ID чата группы
            setting_name: Название настройки (например, 'send_sticker')
            value: Значение настройки (True/False)

        Returns:
            True если успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Используем INSERT OR REPLACE для обновления или создания записи
                await db.execute(f"""
                    INSERT OR REPLACE INTO group_settings
                    (chat_id, {setting_name}, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (chat_id, int(value)))

                await db.commit()
                logger.info(f"✅ Настройка {setting_name} = {value} установлена для группы {chat_id}")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка установки настройки {setting_name} для группы {chat_id}: {e}")
            return False

    # ========== МЕТОДЫ ДЛЯ РАБОТЫ С ПОДПИСКАМИ НА КАНАЛЫ ==========

    async def add_channel_subscription(self, user_id: int, channel_id: str, channel_name: str, channel_url: str, last_video_id: str = None) -> bool:
        """
        Добавляет подписку пользователя на канал

        Args:
            user_id: ID пользователя
            channel_id: ID канала YouTube
            channel_name: Название канала
            channel_url: URL канала
            last_video_id: ID последнего видео канала (для предотвращения отправки старых видео)

        Returns:
            True если подписка добавлена успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO channel_subscriptions
                    (user_id, channel_id, channel_name, channel_url, last_video_id, created_at, is_active)
                    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 1)
                """, (user_id, channel_id, channel_name, channel_url, last_video_id))

                await db.commit()
                logger.info(f"✅ Подписка добавлена: пользователь {user_id} на канал {channel_name} ({channel_id}) с last_video_id: {last_video_id}")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка добавления подписки для пользователя {user_id} на канал {channel_id}: {e}")
            return False

    async def remove_channel_subscription(self, user_id: int, channel_id: str) -> bool:
        """
        Удаляет подписку пользователя на канал

        Args:
            user_id: ID пользователя
            channel_id: ID канала YouTube

        Returns:
            True если подписка удалена успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    DELETE FROM channel_subscriptions
                    WHERE user_id = ? AND channel_id = ?
                """, (user_id, channel_id))

                await db.commit()

                if cursor.rowcount > 0:
                    logger.info(f"✅ Подписка удалена: пользователь {user_id} отписался от канала {channel_id}")
                    return True
                else:
                    logger.warning(f"⚠️ Подписка не найдена: пользователь {user_id}, канал {channel_id}")
                    return False

        except Exception as e:
            logger.error(f"❌ Ошибка удаления подписки для пользователя {user_id} на канал {channel_id}: {e}")
            return False

    async def get_user_subscriptions(self, user_id: int) -> List[Dict[str, Any]]:
        """
        Получает список подписок пользователя

        Args:
            user_id: ID пользователя

        Returns:
            Список словарей с информацией о подписках
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row

                cursor = await db.execute("""
                    SELECT * FROM channel_subscriptions
                    WHERE user_id = ? AND is_active = 1
                    ORDER BY created_at DESC
                """, (user_id,))

                rows = await cursor.fetchall()

                # Конвертируем Row объекты в словари
                subscriptions = [dict(row) for row in rows]

                logger.info(f"📊 Найдено {len(subscriptions)} подписок для пользователя {user_id}")
                return subscriptions

        except Exception as e:
            logger.error(f"❌ Ошибка получения подписок для пользователя {user_id}: {e}")
            return []

    async def get_all_active_subscriptions(self) -> List[Dict[str, Any]]:
        """
        Получает все активные подписки для мониторинга

        Returns:
            Список словарей с информацией о всех активных подписках
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row

                cursor = await db.execute("""
                    SELECT * FROM channel_subscriptions
                    WHERE is_active = 1
                    ORDER BY last_check_time ASC
                """)

                rows = await cursor.fetchall()

                # Конвертируем Row объекты в словари
                subscriptions = [dict(row) for row in rows]

                logger.info(f"📊 Найдено {len(subscriptions)} активных подписок для мониторинга")
                return subscriptions

        except Exception as e:
            logger.error(f"❌ Ошибка получения активных подписок: {e}")
            return []

    async def update_last_video_check(self, channel_id: str, last_video_id: str) -> bool:
        """
        Обновляет информацию о последней проверке канала

        Args:
            channel_id: ID канала YouTube
            last_video_id: ID последнего обработанного видео

        Returns:
            True если обновлено успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    UPDATE channel_subscriptions
                    SET last_video_id = ?, last_check_time = CURRENT_TIMESTAMP
                    WHERE channel_id = ?
                """, (last_video_id, channel_id))

                await db.commit()
                logger.info(f"✅ Обновлена информация о последней проверке канала {channel_id}")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка обновления последней проверки канала {channel_id}: {e}")
            return False

    async def is_video_processed(self, channel_id: str, video_id: str) -> bool:
        """
        Проверяет, было ли видео уже обработано

        Args:
            channel_id: ID канала YouTube
            video_id: ID видео YouTube

        Returns:
            True если видео уже обработано, False если нет
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    SELECT COUNT(*) FROM channel_videos
                    WHERE channel_id = ? AND video_id = ?
                """, (channel_id, video_id))

                count = await cursor.fetchone()
                is_processed = count[0] > 0

                logger.debug(f"🔍 Видео {video_id} канала {channel_id} {'уже обработано' if is_processed else 'не обработано'}")
                return is_processed

        except Exception as e:
            logger.error(f"❌ Ошибка проверки обработки видео {video_id} канала {channel_id}: {e}")
            return False

    async def mark_video_processed(self, channel_id: str, video_id: str, video_title: str, published_at: str) -> bool:
        """
        Отмечает видео как обработанное

        Args:
            channel_id: ID канала YouTube
            video_id: ID видео YouTube
            video_title: Название видео
            published_at: Дата публикации видео

        Returns:
            True если отмечено успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO channel_videos
                    (channel_id, video_id, video_title, published_at, processed_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (channel_id, video_id, video_title, published_at))

                await db.commit()
                logger.info(f"✅ Видео {video_id} канала {channel_id} отмечено как обработанное")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка отметки видео {video_id} канала {channel_id} как обработанного: {e}")
            return False

    async def get_subscription_count(self, user_id: int) -> int:
        """
        Получает количество активных подписок пользователя

        Args:
            user_id: ID пользователя

        Returns:
            Количество активных подписок
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    SELECT COUNT(*) FROM channel_subscriptions
                    WHERE user_id = ? AND is_active = 1
                """, (user_id,))

                count = await cursor.fetchone()
                subscription_count = count[0] if count else 0

                logger.debug(f"📊 Пользователь {user_id} имеет {subscription_count} активных подписок")
                return subscription_count

        except Exception as e:
            logger.error(f"❌ Ошибка получения количества подписок для пользователя {user_id}: {e}")
            return 0

    # ===== МЕТОДЫ ДЛЯ РАБОТЫ С ОЧЕРЕДЬЮ ОЖИДАНИЯ СУБТИТРОВ =====

    async def add_video_to_pending_queue(self, video_id: str, channel_id: str, video_info: Dict, subscribers: List[int]) -> bool:
        """
        Добавляет видео в очередь ожидания субтитров

        Args:
            video_id: ID видео YouTube
            channel_id: ID канала YouTube
            video_info: Словарь с информацией о видео (title, author, etc.)
            subscribers: Список ID подписчиков канала

        Returns:
            True если добавлено успешно, False в случае ошибки
        """
        try:
            from datetime import datetime, timedelta

            # Первая попытка будет через 5 минут
            next_attempt_time = datetime.now() + timedelta(minutes=5)

            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT OR REPLACE INTO pending_videos
                    (video_id, channel_id, video_info, subscribers, attempt_count, next_attempt_time)
                    VALUES (?, ?, ?, ?, 0, ?)
                """, (
                    video_id,
                    channel_id,
                    json.dumps(video_info, ensure_ascii=False),
                    json.dumps(subscribers),
                    next_attempt_time.isoformat()
                ))

                await db.commit()
                logger.info(f"📝 Видео {video_id} добавлено в очередь ожидания субтитров")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка добавления видео {video_id} в очередь ожидания: {e}")
            return False

    async def get_videos_ready_for_retry(self) -> List[Dict[str, Any]]:
        """
        Получает видео, готовые для повторной попытки получения субтитров

        Returns:
            Список словарей с информацией о видео
        """
        try:
            from datetime import datetime

            current_time = datetime.now().isoformat()

            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    SELECT id, video_id, channel_id, video_info, subscribers, attempt_count, max_attempts
                    FROM pending_videos
                    WHERE next_attempt_time <= ? AND attempt_count < max_attempts
                    ORDER BY next_attempt_time ASC
                """, (current_time,))

                rows = await cursor.fetchall()

                videos = []
                for row in rows:
                    video_data = {
                        'id': row[0],
                        'video_id': row[1],
                        'channel_id': row[2],
                        'video_info': json.loads(row[3]),
                        'subscribers': json.loads(row[4]),
                        'attempt_count': row[5],
                        'max_attempts': row[6]
                    }
                    videos.append(video_data)

                if videos:
                    logger.info(f"🔄 Найдено {len(videos)} видео готовых для повторной попытки")

                return videos

        except Exception as e:
            logger.error(f"❌ Ошибка получения видео для повторной попытки: {e}")
            return []

    async def update_video_attempt(self, video_id: str, attempt_count: int) -> bool:
        """
        Обновляет счетчик попыток и время следующей попытки для видео

        Args:
            video_id: ID видео YouTube
            attempt_count: Новый счетчик попыток

        Returns:
            True если обновлено успешно, False в случае ошибки
        """
        try:
            from datetime import datetime, timedelta

            # Расписание задержек в минутах: [5, 15, 30, 60, 120, 240]
            delay_schedule = [5, 15, 30, 60, 120, 240]

            # Вычисляем время следующей попытки
            if attempt_count < len(delay_schedule):
                delay_minutes = delay_schedule[attempt_count]
                next_attempt_time = datetime.now() + timedelta(minutes=delay_minutes)
            else:
                # Если превысили расписание, ставим большую задержку (4 часа)
                next_attempt_time = datetime.now() + timedelta(hours=4)

            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    UPDATE pending_videos
                    SET attempt_count = ?, next_attempt_time = ?
                    WHERE video_id = ?
                """, (attempt_count, next_attempt_time.isoformat(), video_id))

                await db.commit()
                logger.info(f"🔄 Обновлен счетчик попыток для видео {video_id}: {attempt_count}")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка обновления попытки для видео {video_id}: {e}")
            return False

    async def remove_video_from_queue(self, video_id: str) -> bool:
        """
        Удаляет видео из очереди ожидания

        Args:
            video_id: ID видео YouTube

        Returns:
            True если удалено успешно, False в случае ошибки
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    DELETE FROM pending_videos WHERE video_id = ?
                """, (video_id,))

                await db.commit()
                logger.info(f"✅ Видео {video_id} удалено из очереди ожидания")
                return True

        except Exception as e:
            logger.error(f"❌ Ошибка удаления видео {video_id} из очереди: {e}")
            return False

    async def get_pending_videos_count(self) -> int:
        """
        Получает количество видео в очереди ожидания

        Returns:
            Количество видео в очереди
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    SELECT COUNT(*) FROM pending_videos
                """)

                count = await cursor.fetchone()
                pending_count = count[0] if count else 0

                logger.debug(f"📊 В очереди ожидания {pending_count} видео")
                return pending_count

        except Exception as e:
            logger.error(f"❌ Ошибка получения количества видео в очереди: {e}")
            return 0

    async def cleanup_old_pending_videos(self, hours_old: int = 48) -> int:
        """
        Удаляет старые записи из очереди ожидания

        Args:
            hours_old: Возраст записей в часах для удаления (по умолчанию 48 часов)

        Returns:
            Количество удаленных записей
        """
        try:
            from datetime import datetime, timedelta

            cutoff_time = datetime.now() - timedelta(hours=hours_old)

            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute("""
                    DELETE FROM pending_videos
                    WHERE created_at < ?
                """, (cutoff_time.isoformat(),))

                await db.commit()
                deleted_count = cursor.rowcount

                if deleted_count > 0:
                    logger.info(f"🧹 Удалено {deleted_count} старых записей из очереди ожидания")

                return deleted_count

        except Exception as e:
            logger.error(f"❌ Ошибка очистки старых записей из очереди: {e}")
            return 0

    async def get_pending_queue_stats(self) -> Dict[str, Any]:
        """
        Получает статистику очереди ожидания

        Returns:
            Словарь со статистикой очереди
        """
        try:
            from datetime import datetime, timedelta

            async with aiosqlite.connect(self.db_path) as db:
                # Общее количество видео в очереди
                cursor = await db.execute("SELECT COUNT(*) FROM pending_videos")
                total_count = (await cursor.fetchone())[0]

                # Количество по попыткам
                cursor = await db.execute("""
                    SELECT attempt_count, COUNT(*)
                    FROM pending_videos
                    GROUP BY attempt_count
                    ORDER BY attempt_count
                """)
                attempts_stats = dict(await cursor.fetchall())

                # Средний возраст записей в очереди
                cursor = await db.execute("""
                    SELECT AVG(julianday('now') - julianday(created_at)) * 24 as avg_age_hours
                    FROM pending_videos
                """)
                avg_age_result = await cursor.fetchone()
                avg_age_hours = avg_age_result[0] if avg_age_result[0] else 0

                # Количество видео готовых для повторной попытки
                current_time = datetime.now().isoformat()
                cursor = await db.execute("""
                    SELECT COUNT(*) FROM pending_videos
                    WHERE next_attempt_time <= ? AND attempt_count < max_attempts
                """, (current_time,))
                ready_for_retry = (await cursor.fetchone())[0]

                # Количество видео, достигших максимального количества попыток
                cursor = await db.execute("""
                    SELECT COUNT(*) FROM pending_videos
                    WHERE attempt_count >= max_attempts
                """)
                max_attempts_reached = (await cursor.fetchone())[0]

                stats = {
                    'total_count': total_count,
                    'attempts_stats': attempts_stats,
                    'avg_age_hours': round(avg_age_hours, 2),
                    'ready_for_retry': ready_for_retry,
                    'max_attempts_reached': max_attempts_reached
                }

                return stats

        except Exception as e:
            logger.error(f"❌ Ошибка получения статистики очереди: {e}")
            return {
                'total_count': 0,
                'attempts_stats': {},
                'avg_age_hours': 0,
                'ready_for_retry': 0,
                'max_attempts_reached': 0
            }
