import requests
import base64
import json
import re
import time
import traceback
import random
import aiohttp
import asyncio



from telebot import types  # Для кнопок
from config import (
    GEMINI_API_URL_TEMPLATE,
    VOIDAI_API_KEYS, VOIDAI_API_HOST,
    MODEL_LITE_CLASSIFY_TRANSCRIBE,
    MODEL_FLASH, MODEL_MID, MODEL_PRO, MODEL_GEMINI_2_5_PRO,

    OFFICIAL_GEMINI_API_KEYS,
    PODCAST_WOMAN_VOICE, PODCAST_MAN_VOICE,
    PODCAST_ANNA_VOICE, PODCAST_MIKHAIL_VOICE,
)
import config # Added import config for GEMINI_API_URL_TEMPLATE

from bot_globals import log_admin
from database import log_api_request
from retry_utils import retry_on_censorship

# Import new genai functions
from genai_client import (
    call_gemini_2_5_flash_api_genai,
    call_official_gemini_api_genai,
    call_gemma_3_1b_api_genai,
    call_gemini_2_5_flash_lite_for_shortening_genai,
    # Streaming functions
    call_official_gemini_api_genai_stream,
    call_gemini_2_5_flash_api_genai_stream
)
from genai_tts import (
    call_gemini_tts_api_genai
)

# --- Navy API Configuration for Grok-4 ---
NAVY_API_URL = "https://api.navy/v1/chat/completions"
NAVY_API_KEY = "sk-55a958382b1eb58ae5729b5b164f0babd4c8014ec89ed32a"


# --- VoidAI API Call Function ---
def call_voidai_api_sync(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", message_to_edit_id=None, chat_id_for_edit=None, bot_instance=None):
    """
    Calls the VoidAI API with key rotation logic.
    """
    log_prefix = f"[{call_type.capitalize()}/VoidAI] "

    if not VOIDAI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: VOIDAI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})

    # Process history
    if history:
        for turn in history:
            role_for_api = "assistant" if turn.get("role") == "model" else turn.get("role", "user")
            parts_from_history = turn.get("parts", [])
            if not parts_from_history: continue

            current_turn_content_blocks = []
            for part in parts_from_history:
                if "text" in part and part["text"] is not None:
                    if part["text"].strip():
                        current_turn_content_blocks.append({"type": "text", "text": part["text"]})
                elif "inline_data" in part:
                    inline_data = part["inline_data"]
                    if "mime_type" in inline_data and "data" in inline_data:
                        current_turn_content_blocks.append({
                            "type": "image_url",
                            "image_url": {"url": f"data:{inline_data['mime_type']};base64,{inline_data['data']}"}
                        })

            if current_turn_content_blocks:
                if len(current_turn_content_blocks) == 1 and current_turn_content_blocks[0]["type"] == "text":
                    messages.append({"role": role_for_api, "content": current_turn_content_blocks[0]["text"]})
                else:
                    messages.append({"role": role_for_api, "content": current_turn_content_blocks})

    # Process current user input
    current_user_content = []
    if user_text:
        sanitized_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)
        current_user_content.append({"type": "text", "text": sanitized_text})

    if input_data:
        items_to_iterate_through = input_data if isinstance(input_data, list) else [input_data]
        images_added = 0
        for element_from_input in items_to_iterate_through:
            if images_added >= 10: break
            potential_image_dicts = []
            if isinstance(element_from_input, list):
                potential_image_dicts.extend(element_from_input)
            elif isinstance(element_from_input, dict):
                potential_image_dicts.append(element_from_input)

            for image_dict_item in potential_image_dicts:
                if images_added >= 10: break
                if isinstance(image_dict_item, dict) and "mime_type" in image_dict_item and "data" in image_dict_item:
                    image_url = f"data:{image_dict_item['mime_type']};base64,{image_dict_item['data']}"
                    current_user_content.append({"type": "image_url", "image_url": {"url": image_url}})
                    images_added += 1

    if current_user_content:
        messages.append({"role": "user", "content": current_user_content})

    if not messages:
         log_admin(f"{log_prefix}API call aborted: no messages constructed.")
         return "Извините, нет данных для обработки запроса."

    # Prepare payload
    payload = {
        "model": model_name,
        "messages": messages,
        "max_tokens": 8000,
        "temperature": 0.7,
        "stream": False  # Simplified: no streaming for now
    }

    # Special parameters for HTML generation
    if call_type == "html_generation":
        payload["temperature"] = 1  # Set temperature to 1 for HTML generation
        payload["max_tokens"] = 64000  # Set max_tokens to 64000 for Claude Sonnet 4 HTML generation


        # Убираем temperature для sonar-deep-research, используем значение по умолчанию
        if "temperature" in payload:
            del payload["temperature"]

    import time

    def try_api_call(api_key, attempt_info=""):
        """Helper function to make API call with given key"""
        headers["Authorization"] = f"Bearer {api_key}"
        log_admin(f"{log_prefix}Attempting API call to {model_name} {attempt_info}")

        try:
            # Устанавливаем общий таймаут в 20 минут (1200 секунд)
            response = requests.post(VOIDAI_API_HOST, headers=headers, json=payload, timeout=1200)

            if response.status_code == 429:
                log_admin(f"{log_prefix}API key returned 429 (rate limit)")
                return None, "429"

            response.raise_for_status()

            # Handle response
            response_text = response.text
            try:
                json_response = json.loads(response_text)



                if json_response.get("choices") and len(json_response["choices"]) > 0:
                    choice = json_response["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"].get("content")
                        if content:
                            log_admin(f"{log_prefix}API call successful. Text length: {len(content)}")
                            # Log successful API request
                            log_api_request(user_id=None, model_name=model_name, request_type=call_type, api_key_identifier=api_key[-4:])
                            return content.strip(), "success"
                    elif "delta" in choice and "content" in choice["delta"]:
                        content = choice["delta"].get("content")
                        if content:
                            return content.strip(), "success"
                        else:
                            return "", "success"
                log_admin(f"{log_prefix}Response structure not as expected: {response_text[:300]}", level="error")
                return None, "invalid_response"
            except json.JSONDecodeError:
                log_admin(f"{log_prefix}JSONDecodeError: {response_text[:300]}", level="error")
                return None, "json_error"

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            log_admin(f"{log_prefix}HTTPError. Status: {status_code}")
            if status_code == 429:
                return None, "429"
            elif status_code == 401:
                return None, "401"
            else:
                return None, f"http_error_{status_code}"
        except Exception as e:
            log_admin(f"{log_prefix}Exception: {e}")
            return None, "exception"

    # Phase 1: Try first key
    if len(VOIDAI_API_KEYS) > 0:
        result, error_type = try_api_call(VOIDAI_API_KEYS[0], "with first key")
        if result is not None:
            return result

        # Phase 2: If first key failed with 429, try second key
        if error_type == "429" and len(VOIDAI_API_KEYS) > 1:
            log_admin(f"{log_prefix}First key failed with 429, trying second key")
            result, error_type = try_api_call(VOIDAI_API_KEYS[1], "with second key")
            if result is not None:
                return result

            # Phase 3: If second key also failed, retry first key with delays (up to 10 times)
            if error_type == "429":
                log_admin(f"{log_prefix}Second key also failed with 429, retrying first key with delays")
                for retry_attempt in range(10):
                    time.sleep(3)  # 3 second delay
                    result, error_type = try_api_call(VOIDAI_API_KEYS[0], f"with first key (retry {retry_attempt + 1}/10)")
                    if result is not None:
                        return result
                    if error_type != "429":
                        break  # If it's not 429, no point in retrying

    # --- Все ключи VoidAI не сработали: пробуем Gemini Flash ---
    log_admin(f"{log_prefix}Both VoidAI keys failed, switching to Gemini 2.5-Flash-05-20")

    try:
        fallback_response = call_official_gemini_api(
            model_name="gemini-2.5-flash",
            history=history,
            user_text=user_text,
            input_data=input_data,
            system_prompt=system_prompt,
            call_type=f"{call_type}_fallback_gemini_flash"
        )
        # Если Gemini вернул валидный текст – отдаём его наверх,
        # пользователь даже не узнает о переключении
        if isinstance(fallback_response, str) and not fallback_response.startswith("Извините"):
            log_admin(f"{log_prefix}Gemini fallback successful, response length: {len(fallback_response)}")
            return fallback_response.strip()
        else:
            log_admin(f"{log_prefix}Gemini fallback also failed: {str(fallback_response)[:200]}")
    except Exception as e:
        log_admin(f"{log_prefix}Exception during Gemini fallback: {e}")

    # Если и Gemini не помог – показываем старое сообщение-ошибку
    return "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."



# --- Gemini API Call Function ---
@retry_on_censorship(max_attempts=5)
def call_gemini_api(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", user_id=None):
    log_prefix = f"[{call_type.capitalize()}/GeminiViaVoid] " # Changed log_prefix for clarity
    if not VOIDAI_API_KEYS: # Using VOIDAI_API_KEYS for VoidAI endpoint
        log_admin(f"{log_prefix}API call skipped: API_KEYS list for VoidAI is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    headers = { "Content-Type": "application/json", "Accept": "application/json" }
    # VoidAI uses a "messages" list with OpenAI-like structure
    messages_for_void_ai = []
    if system_prompt: messages_for_void_ai.append({"role": "system", "content": system_prompt})

    use_history = (
        call_type.startswith("general")      # «general», «general_primary», «general_fallback…»
        or call_type in ["media_group", "file_query"]
    )
    if use_history and history:
        # Ограничиваем длину истории, чтобы не превысить лимит токенов
        MAX_HISTORY_TURNS = 20
        limited_history = history[-MAX_HISTORY_TURNS:] if len(history) > MAX_HISTORY_TURNS else history
        for i, turn in enumerate(limited_history):
            role_for_api = "assistant" if turn.get("role") == "model" else turn.get("role", "user")
            parts_from_history = turn.get("parts", [])
            if not parts_from_history: continue

            current_turn_content_blocks = []
            has_image_in_turn = False
            for j, part in enumerate(parts_from_history):
                if "text" in part and part["text"] is not None:
                    if part["text"].strip(): current_turn_content_blocks.append({"type": "text", "text": part["text"]})
                elif "inline_data" in part: # This is how images are stored in history
                    inline_data = part["inline_data"]
                    if "mime_type" in inline_data and "data" in inline_data:
                        # VoidAI expects "image_url" format for images in messages
                        current_turn_content_blocks.append({"type": "image_url", "image_url": {"url": f"data:{inline_data['mime_type']};base64,{inline_data['data']}"}})
                        has_image_in_turn = True

            if not current_turn_content_blocks: continue

            # Construct message for VoidAI
            if has_image_in_turn or len(current_turn_content_blocks) > 1:
                 messages_for_void_ai.append({"role": role_for_api, "content": current_turn_content_blocks})
            elif len(current_turn_content_blocks) == 1 and current_turn_content_blocks[0]["type"] == "text":
                 messages_for_void_ai.append({"role": role_for_api, "content": current_turn_content_blocks[0]["text"]})
            else: # Should not happen if current_turn_content_blocks is not empty
                 messages_for_void_ai.append({"role": role_for_api, "content": current_turn_content_blocks})

    current_user_content_for_void_ai = []
    if user_text:
        current_user_content_for_void_ai.append({"type": "text", "text": re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)})

    if input_data: # input_data is already a list of dicts with mime_type and data
        items_to_iterate_through = input_data if isinstance(input_data, list) else [input_data]
        images_added = 0
        for element in items_to_iterate_through:
            if images_added >=10: break
            potential_images = []
            if isinstance(element, list): potential_images.extend(element)
            elif isinstance(element, dict): potential_images.append(element)
            for item in potential_images:
                if images_added >= 10: break
                if isinstance(item, dict) and "mime_type" in item and "data" in item:
                    # VoidAI expects "image_url" format
                    current_user_content_for_void_ai.append({"type": "image_url", "image_url": {"url": f"data:{item['mime_type']};base64,{item['data']}"}})
                    images_added += 1

    if current_user_content_for_void_ai:
        messages_for_void_ai.append({"role": "user", "content": current_user_content_for_void_ai})

    if not messages_for_void_ai: return "Извините, нет данных для обработки запроса."
    
    # Set temperature and max_tokens based on call type
    if call_type == "html_generation":
        temperature = 1  # Set temperature to 1 for HTML generation
        max_tokens = 65000  # Set max_tokens to 65000 for Gemini 2.5 Pro HTML generation
    else:
        temperature = 0.75 if use_history else 0.4
        max_tokens = 8192
    # Payload for VoidAI
    payload = { "model": model_name, "messages": messages_for_void_ai, "max_tokens": max_tokens, "temperature": temperature, "stream": False }

    available_keys = list(VOIDAI_API_KEYS)

    # For HTML generation, prioritize the "basic" key first, then "pro" key
    if call_type == "html_generation":
        basic_keys = [key for key in available_keys if key.endswith("-basic")]
        pro_keys = [key for key in available_keys if key.endswith("-pro")]
        other_keys = [key for key in available_keys if not key.endswith("-basic") and not key.endswith("-pro")]
        # Arrange keys: basic first, then pro, then others
        available_keys = basic_keys + pro_keys + other_keys
        log_admin(f"{log_prefix}HTML generation: Using prioritized key order (basic -> pro -> others)")
    else:
        random.shuffle(available_keys)

    last_error = None
    
    for attempt in range(len(available_keys)): # Iterate only once per available key
        api_key_to_try = available_keys[attempt]
        headers["Authorization"] = f"Bearer {api_key_to_try}"
        log_admin(f"{log_prefix}Attempt to {model_name} via VoidAI with key ending ...{api_key_to_try[-4:]}. Attempt {attempt+1}/{len(available_keys)}")
        try:
            # Устанавливаем общий таймаут в 20 минут (1200 секунд) для всех запросов
            response = requests.post(VOIDAI_API_HOST, headers=headers, json=payload, timeout=1200)

            if response.status_code == 429:
                log_admin(f"{log_prefix}Key ending ...{api_key_to_try[-4:]} returned 429. Trying next key.")
                last_error = "Ошибка: Слишком много запросов к Gemini (429, через VoidAI). Попробуйте позже."
                continue # Try next key

            response.raise_for_status() # Raises HTTPError for bad responses (4xx or 5xx)

            response_data = response.json()
            if response_data.get("choices") and len(response_data["choices"]) > 0:
                choice = response_data["choices"][0]
                if "message" in choice and "content" in choice["message"]:
                    content = choice["message"].get("content")
                    if content: 
                        log_api_request(user_id=None, model_name=model_name, request_type=call_type, api_key_identifier=api_key_to_try[-4:])
                        return content.strip()
                # VoidAI might use "delta" even for non-stream, or if stream was intended but failed
                elif "delta" in choice and "content" in choice["delta"]:
                    content = choice["delta"].get("content")
                    if content: return content.strip()
                    else: return "" # Empty content is a valid response

            log_admin(f"{log_prefix}Response structure not as expected from VoidAI: {response.text[:300]}", level="error")
            last_error = "Извините, получен неожиданный ответ от сервиса."
            break

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            error_text = e.response.text[:200] if e.response else "N/A"
            log_admin(f"{log_prefix}HTTPError Status: {status_code}. Error: {error_text}", level="error")
            if status_code == 400: last_error = "Извините, ваш запрос не может быть обработан (ошибка 400). Пожалуйста, проверьте его или попробуйте изменить."
            elif status_code == 401:
                last_error = "Извините, проблема с конфигурацией API. Пожалуйста, сообщите администратору."
            elif status_code == 403: last_error = "Извините, доступ к сервису ограничен (ошибка 403)."
            elif status_code == 429:
                last_error = "Извините, сейчас слишком много запросов к сервису. Пожалуйста, попробуйте через несколько минут."
            elif status_code is not None and status_code >= 500:
                last_error = f"Извините, на сервере произошла внутренняя ошибка ({status_code}). Пожалуйста, попробуйте позже."
            else: last_error = f"Извините, произошла ошибка при обработке вашего запроса (код: {status_code})."

            if status_code in [400, 403]: break
            continue

        except requests.exceptions.RequestException as e:
            log_admin(f"{log_prefix}Network/request error: {e}", level="error")
            last_error = "Извините, проблема с сетевым подключением к сервису. Проверьте ваше интернет-соединение."
            time.sleep(0.5)
            continue
        except json.JSONDecodeError:
            log_admin(f"{log_prefix}JSONDecodeError. Response: {response.text[:300]}", level="error")
            last_error = "Извините, получен неожиданный или некорректный ответ от сервиса."
            break
        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error: {e}\n{traceback.format_exc()}", level="error")
            last_error = "Извините, произошла непредвиденная ошибка. Пожалуйста, попробуйте позже."
            time.sleep(0.5)
            continue

    if last_error: return last_error
    return "Извините, не удалось связаться с сервисом после нескольких попыток. Пожалуйста, попробуйте позже."


# --- Gemini TTS API Functions for Podcast ---
# Note: split_dialogue_by_speakers function removed - using native multi-speaker mode instead


def call_gemini_tts_api(dialogue_text, call_type="podcast_tts", podcast_host_type="diana_sasha"):
    """
    Обертка для новой функции genai. Сохраняет совместимость с существующим кодом.
    """
    return call_gemini_tts_api_genai(dialogue_text, call_type, podcast_host_type)


def call_gemini_tts_api_old(dialogue_text, call_type="podcast_tts", podcast_host_type="diana_sasha"):
    """
    Calls Gemini 2.5 Flash TTS API to generate podcast audio using native multi-speaker mode.
    Returns audio data as bytes or None on error.
    """
    log_prefix = f"[{call_type.capitalize()}/GeminiTTS] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API key not found")
        return None

    if not dialogue_text or not isinstance(dialogue_text, str):
        log_admin(f"{log_prefix}Invalid dialogue text provided")
        return None

    # Validate dialogue format
    if "Woman:" not in dialogue_text or "Man:" not in dialogue_text:
        log_admin(f"{log_prefix}Dialogue text doesn't contain required speaker markers (Woman:/Man:)")
        return None

    # Select voices based on podcast host type
    if podcast_host_type == "anna_mikhail":
        woman_voice = config.PODCAST_ANNA_VOICE
        man_voice = config.PODCAST_MIKHAIL_VOICE
        log_admin(f"{log_prefix}Using Anna & Mikhail voices: {woman_voice}, {man_voice}")
    else:
        woman_voice = config.PODCAST_WOMAN_VOICE
        man_voice = config.PODCAST_MAN_VOICE
        log_admin(f"{log_prefix}Using Diana & Sasha voices: {woman_voice}, {man_voice}")

    log_admin(f"{log_prefix}Generating multi-speaker audio for dialogue ({len(dialogue_text)} characters)")

    # Shuffle keys for random selection
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    tts_model = "gemini-2.5-flash-preview-tts"
    headers = {
        "Content-Type": "application/json"
    }

    # Multi-speaker payload structure
    payload = {
        "model": tts_model,
        "contents": [{
            "parts": [{
                "text": dialogue_text  # Весь диалог сразу
            }]
        }],
        "generationConfig": {
            "responseModalities": ["AUDIO"],
            "speechConfig": {
                "multiSpeakerVoiceConfig": {
                    "speakerVoiceConfigs": [
                        {
                            "speaker": "Woman",
                            "voiceConfig": {
                                "prebuiltVoiceConfig": {
                                    "voiceName": woman_voice 
                                }
                            }
                        },
                        {
                            "speaker": "Man",
                            "voiceConfig": {
                                "prebuiltVoiceConfig": {
                                    "voiceName": man_voice 
                                }
                            }
                        }
                    ]
                }
            }
        }
    }

    # Try each key until success or all keys exhausted
    for attempt, api_key in enumerate(available_keys):
        log_admin(f"{log_prefix}Attempting TTS with key ending ...{api_key[-4:]} (attempt {attempt + 1}/{len(available_keys)})")

        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{tts_model}:generateContent?key={api_key}"

        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=600)
            response.raise_for_status()

            response_data = response.json()
            if "candidates" in response_data and len(response_data["candidates"]) > 0:
                candidate = response_data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "inlineData" in parts[0] and "data" in parts[0]["inlineData"]:
                        audio_base64 = parts[0]["inlineData"]["data"]
                        audio_data = base64.b64decode(audio_base64)
                        log_admin(f"{log_prefix}Successfully generated multi-speaker audio ({len(audio_data)} bytes) with key ...{api_key[-4:]}")
                        log_api_request(user_id=None, model_name=tts_model, request_type=call_type, api_key_identifier=api_key[-4:])
                        return audio_data
                    else:
                        log_admin(f"{log_prefix}No audio data in response with key ...{api_key[-4:]}")
                        continue  # Try next key
                else:
                    log_admin(f"{log_prefix}Invalid response structure with key ...{api_key[-4:]}")
                    continue  # Try next key
            else:
                log_admin(f"{log_prefix}No candidates in response with key ...{api_key[-4:]}")
                continue  # Try next key

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            error_text = e.response.text[:200] if e.response else "No response text"
            log_admin(f"{log_prefix}HTTPError with key ...{api_key[-4:]}. Status: {status_code}, Error: {error_text}")

            # For auth errors (401, 403), try next key immediately
            if status_code in [401, 403]:
                log_admin(f"{log_prefix}Authentication error with key ...{api_key[-4:]}, trying next key")
                continue
            # For rate limit (429), try next key
            elif status_code == 429:
                log_admin(f"{log_prefix}Rate limit with key ...{api_key[-4:]}, trying next key")
                continue
            # For server errors (500+), try next key
            elif status_code and status_code >= 500:
                log_admin(f"{log_prefix}Server error with key ...{api_key[-4:]}, trying next key")
                continue
            else:
                # For other HTTP errors, try next key
                continue

        except requests.exceptions.RequestException as e:
            log_admin(f"{log_prefix}RequestException with key ...{api_key[-4:]}: {e}")
            continue  # Try next key

        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error with key ...{api_key[-4:]}: {e}")
            continue  # Try next key

    # All keys failed - try fallback to VoidAI TTS
    log_admin(f"{log_prefix}All {len(available_keys)} API keys failed for multi-speaker TTS")
    log_admin(f"{log_prefix}Attempting fallback to VoidAI TTS for content that may be rejected by Gemini")

    # Try VoidAI TTS as fallback for potentially sensitive content
    fallback_audio = call_gemini_single_tts_api_via_voidai(dialogue_text, call_type, "alloy")
    if fallback_audio:
        log_admin(f"{log_prefix}Fallback to VoidAI TTS successful")
        return fallback_audio
    else:
        log_admin(f"{log_prefix}Fallback to VoidAI TTS also failed")
        return None


# --- Gemini 2.5 Flash API Call Function for Private Chats ---
def call_gemini_2_5_flash_api(history, user_text, input_data=None, system_prompt=None, call_type="general"):
    """
    Обертка для новой функции genai. Сохраняет совместимость с существующим кодом.
    """
    return call_gemini_2_5_flash_api_genai(history, user_text, input_data, system_prompt, call_type)


def call_gemini_2_5_flash_api_old(history, user_text, input_data=None, system_prompt=None, call_type="general"):
    """
    Специальная функция для вызова Gemini 2.5 Flash через официальное API для личных чатов.
    Поддерживает 65k токенов, context7, Code Thinking и веб-поиск.
    Размышление отключено.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemini2.5Flash] "
    model_name = "gemini-2.5-flash"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    # Shuffle keys for random selection and fallback
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    headers = { "Content-Type": "application/json" }

    # Constructing the 'contents' for Gemini API
    gemini_contents = []

    # Add system instruction if provided
    system_instruction = None
    if system_prompt:
        system_instruction = {"parts": [{"text": system_prompt}]}

    # Process conversation history
    if history:
        for entry in history:
            role = entry.get("role", "user")
            parts = entry.get("parts", [])

            if role == "user":
                gemini_role = "user"
            elif role == "model":
                gemini_role = "model"
            else:
                continue  # Skip unknown roles

            gemini_parts = []
            for part in parts:
                if "text" in part:
                    gemini_parts.append({"text": part["text"]})
                # Add support for other part types if needed

            if gemini_parts:
                gemini_contents.append({"role": gemini_role, "parts": gemini_parts})

    # Add current user message
    current_user_parts = []
    if user_text:
        current_user_parts.append({"text": user_text})

    # Add media data (image/video) if provided
    if input_data:
        for media_data in input_data:
            # Support both formats: "data" (from handlers.py) and "base64_data" (legacy)
            data = media_data.get("data") or media_data.get("base64_data")
            mime_type = media_data.get("mime_type")

            if data and mime_type:
                # Use correct format for Gemini API - both image and video use inline_data
                current_user_parts.append({
                    "inline_data": {
                        "mime_type": mime_type,
                        "data": data
                    }
                })

    if current_user_parts:
        gemini_contents.append({"role": "user", "parts": current_user_parts})

    if not gemini_contents:
        return "Извините, нет данных для обработки запроса."

    # Check if this is a video request to adjust config
    has_video = False
    if input_data:
        for media_data in input_data:
            mime_type = media_data.get("mime_type")
            if mime_type and mime_type.startswith("video/"):
                has_video = True
                break

    # Prepare payload with high token limit for private chats
    generation_config = {
        "maxOutputTokens": 65000,  # 65k tokens as requested
        "temperature": 0.7,
        "topP": 0.9,
        "topK": 40
    }

    # Only add thinkingConfig for non-video requests
    # Используем максимальный thinking budget для Gemini 2.5 Flash (1000 токенов)
    if not has_video:
        generation_config["thinkingConfig"] = {
            "thinkingBudget": config.THINKING_BUDGET_GEMINI_2_5_FLASH_MAX  # 1000 токенов для размышления
        }

    payload = {
        "contents": gemini_contents,
        "generationConfig": generation_config,
        "safetySettings": [
            {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"},
            {"category": "HARM_CATEGORY_CIVIC_INTEGRITY", "threshold": "BLOCK_NONE"}
        ]
    }

    # Add system instruction if provided
    if system_instruction:
        payload["systemInstruction"] = system_instruction

    # Try each API key
    for attempt, api_key in enumerate(available_keys):
        try:
            url = config.GEMINI_API_URL_TEMPLATE.format(model=model_name, key=api_key)
            log_admin(f"{log_prefix}Attempt {attempt+1}/{len(available_keys)} with key ending ...{api_key[-4:]}")

            response = requests.post(url, headers=headers, json=payload, timeout=1200)

            if response.status_code == 429:
                log_admin(f"{log_prefix}API key returned 429 (rate limit)")
                continue

            response.raise_for_status()

            # Parse response
            response_data = response.json()

            if "candidates" in response_data and response_data["candidates"]:
                candidate = response_data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    response_text = ""
                    for part in parts:
                        if "text" in part:
                            response_text += part["text"]

                    if response_text.strip():
                        log_admin(f"{log_prefix}Successful response received (length: {len(response_text)})")
                        return response_text.strip()

            log_admin(f"{log_prefix}Empty or invalid response structure")
            continue

        except requests.exceptions.Timeout:
            log_admin(f"{log_prefix}Timeout with key ...{api_key[-4:]}")
            continue
        except requests.exceptions.RequestException as e:
            log_admin(f"{log_prefix}Request error with key ...{api_key[-4:]}: {e}")
            continue
        except json.JSONDecodeError as e:
            log_admin(f"{log_prefix}JSON decode error with key ...{api_key[-4:]}: {e}")
            continue
        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error with key ...{api_key[-4:]}: {e}")
            continue

    # All keys failed
    log_admin(f"{log_prefix}All {len(available_keys)} API keys failed")
    return "Извините, не удалось связаться с сервером ИИ после нескольких попыток. Пожалуйста, попробуйте позже."


# --- Official Gemini API Call Function ---
def call_official_gemini_api(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", user_id=None):
    """
    Обертка для новой функции genai. Сохраняет совместимость с существующим кодом.
    """
    return call_official_gemini_api_genai(model_name, history, user_text, input_data, system_prompt, call_type, user_id)


def call_official_gemini_api_old(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general"):
    log_prefix = f"[{call_type.capitalize()}/GeminiOfficial] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    # Shuffle keys for random selection and fallback
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    headers = { "Content-Type": "application/json" }

    # Constructing the 'contents' for Gemini API
    gemini_contents = []

    # Process history
    use_history = (
        call_type.startswith("general")      # «general», «general_primary», «general_fallback…»
        or call_type in ["media_group", "file_query"]
    )
    if use_history and history:
        # Ограничиваем длину истории, чтобы не превысить лимит токенов
        MAX_HISTORY_TURNS = 20
        limited_history = history[-MAX_HISTORY_TURNS:] if len(history) > MAX_HISTORY_TURNS else history
        for turn in limited_history:
            role = "model" if turn.get("role") == "assistant" or turn.get("role") == "model" else "user"

            current_turn_parts = []
            parts_from_history = turn.get("parts", [])
            if not parts_from_history: continue

            for part_data in parts_from_history:
                if "text" in part_data and part_data["text"] is not None:
                    if part_data["text"].strip():
                        current_turn_parts.append({"text": part_data["text"]})
                elif "inline_data" in part_data: # Handling images from history
                    inline_data = part_data["inline_data"]
                    if "mime_type" in inline_data and "data" in inline_data:
                        current_turn_parts.append({
                            "inlineData": {
                                "mimeType": inline_data["mime_type"],
                                "data": inline_data["data"]
                            }
                        })
            if current_turn_parts:
                gemini_contents.append({"role": role, "parts": current_turn_parts})

    # Process current user input (text and images)
    current_user_parts = []
    if user_text:
        # Sanitize user text (though Gemini might handle it, good practice)
        sanitized_user_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)
        if sanitized_user_text.strip():
            current_user_parts.append({"text": sanitized_user_text})

    if input_data: # input_data is expected to be a list of dicts with mime_type and data
        images_added_to_current_turn = 0
        items_to_iterate = input_data if isinstance(input_data, list) else [input_data]
        for item_dict in items_to_iterate:
            if images_added_to_current_turn >= 16: # Gemini Vision limits (check official docs for exact limits)
                log_admin(f"{log_prefix}Reached image limit for current turn.", level="warning")
                break
            if isinstance(item_dict, dict) and "mime_type" in item_dict and "data" in item_dict:
                current_user_parts.append({
                    "inlineData": {
                        "mimeType": item_dict["mime_type"],
                        "data": item_dict["data"]
                    }
                })
                images_added_to_current_turn +=1
            else: # If input_data contains non-dict items or malformed dicts
                log_admin(f"{log_prefix}Skipping malformed item in input_data: {type(item_dict)}", level="debug")


    if current_user_parts:
        gemini_contents.append({"role": "user", "parts": current_user_parts})

    if not gemini_contents:
        log_admin(f"{log_prefix}No content to send to Gemini API.")
        return "Извините, нет данных для обработки запроса."

    payload = { "contents": gemini_contents }

    # Add generationConfig
    # Default temperature similar to existing, maxOutputTokens as per model capabilities
    # Special handling for Gemini 2.5 Pro and Claude Sonnet 4 formatting with increased token limit
    if call_type == "html_generation":
        max_tokens = 65000  # Gemini 2.5 Pro max output tokens for HTML generation
    elif model_name == "gemini-2.5-pro-preview-06-05":
        max_tokens = 65000
    elif call_type in ["summarize_text_L1", "summarize_text_L2"]:
        max_tokens = 65000  # Максимальные токены для сокращения текста
    else:
        max_tokens = 8192

    # Set temperature based on call type
    if call_type == "html_generation":
        temperature = 1  # Set temperature to 1 for HTML generation
    else:
        temperature = 0.75 if use_history else 0.4  # Consistent with call_gemini_api logic

    payload["generationConfig"] = {
        "temperature": temperature,
        "maxOutputTokens": max_tokens
    }

    # Add systemInstruction if system_prompt is provided
    if system_prompt and system_prompt.strip():
        payload["systemInstruction"] = {"parts": [{"text": system_prompt}]}

    # Try each key until success or all keys exhausted
    for key_attempt, api_key in enumerate(available_keys):
        log_admin(f"{log_prefix}Attempting API call to {model_name} with key ending ...{api_key[-4:]} (attempt {key_attempt + 1}/{len(available_keys)})")

        api_url = config.GEMINI_API_URL_TEMPLATE.format(model=model_name, key=api_key)

        try:
            # Log payload (cautiously, to avoid very large data like base64 images in logs)
            try:
                debug_payload = json.loads(json.dumps(payload)) # Deep copy for logging
                for content_item in debug_payload.get("contents", []):
                    for part_item in content_item.get("parts", []):
                        if "inlineData" in part_item and "data" in part_item["inlineData"]:
                            part_item["inlineData"]["data"] = part_item["inlineData"]["data"][:100] + "... [TRUNCATED_FOR_LOG]"
                log_admin(f"{log_prefix}Payload for {model_name}: {json.dumps(debug_payload, ensure_ascii=False, indent=2)}", level="debug")
            except Exception as e_log:
                log_admin(f"{log_prefix}Error preparing payload for logging: {e_log}", level="warning")

            # Устанавливаем общий таймаут в 20 минут (1200 секунд)
            response = requests.post(api_url, headers=headers, json=payload, timeout=1200)
            response.raise_for_status() # Check for HTTP errors

            response_data = response.json()

            # Parse response
            if response_data.get("candidates") and len(response_data["candidates"]) > 0:
                first_candidate = response_data["candidates"][0]
                if first_candidate.get("content") and first_candidate["content"].get("parts") and len(first_candidate["content"]["parts"]) > 0:
                    text_response = first_candidate["content"]["parts"][0].get("text")
                    if text_response is not None: # Can be empty string which is valid
                        log_admin(f"{log_prefix}Successfully received response from {model_name} with key ...{api_key[-4:]}. Text length: {len(text_response)}")
                        log_api_request(user_id=None, model_name=model_name, request_type=call_type, api_key_identifier=api_key[-4:])
                        return text_response.strip()

            # Handle cases like safety blocks or empty/unexpected responses
            if response_data.get("promptFeedback"):
                log_admin(f"{log_prefix}Prompt feedback received with key ...{api_key[-4:]}: {response_data['promptFeedback']}", level="warning")
                # Check for specific block reasons
                block_reason = response_data['promptFeedback'].get('blockReason')
                if block_reason:
                    # Provide a more specific user-facing error based on the reason
                    if block_reason == "SAFETY":
                        safety_ratings_info = response_data['promptFeedback'].get('safetyRatings', [])
                        categories_blocked = [sr['category'] for sr in safety_ratings_info if sr.get('probability') == 'HIGH'] # Example: check for high probability blocks
                        if categories_blocked:
                             return f"Извините, ваш запрос заблокирован из-за настроек безопасности (категории: {', '.join(categories_blocked)}). Пожалуйста, измените ваш запрос."
                        return "Извините, ваш запрос заблокирован из-за настроек безопасности. Пожалуйста, измените ваш запрос."
                    return f"Извините, запрос не выполнен (причина: {block_reason})."

            log_admin(f"{log_prefix}Unexpected response structure from {model_name} with key ...{api_key[-4:]}: {response.text[:500]}", level="error")
            continue  # Try next key

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            error_text = e.response.text[:200] if e.response else "N/A"
            log_admin(f"{log_prefix}HTTPError for {model_name} with key ...{api_key[-4:]}. Status: {status_code}. Error: {error_text}", level="error")

            # For auth errors (401, 403), try next key immediately
            if status_code in [401, 403]:
                log_admin(f"{log_prefix}Authentication error with key ...{api_key[-4:]}, trying next key")
                continue
            # For rate limit (429), try next key
            elif status_code == 429:
                log_admin(f"{log_prefix}Rate limit with key ...{api_key[-4:]}, trying next key")
                continue
            # For server errors (500+), try next key
            elif status_code and status_code >= 500:
                log_admin(f"{log_prefix}Server error with key ...{api_key[-4:]}, trying next key")
                continue
            # For bad request (400), this is likely a content issue, not key issue
            elif status_code == 400:
                return "Извините, ваш запрос не может быть обработан (ошибка 400). Пожалуйста, проверьте его или попробуйте изменить."
            else:
                # For other HTTP errors, try next key
                continue
        except requests.exceptions.RequestException as e:
            log_admin(f"{log_prefix}RequestException for {model_name} with key ...{api_key[-4:]}: {e}", level="error")
            continue  # Try next key

        except json.JSONDecodeError:
            log_admin(f"{log_prefix}JSONDecodeError from {model_name} with key ...{api_key[-4:]}. Response: {response.text[:300]}", level="error")
            continue  # Try next key

        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error during {model_name} API call with key ...{api_key[-4:]}: {e}\n{traceback.format_exc()}", level="error")
            continue  # Try next key

    # All keys failed
    log_admin(f"{log_prefix}All {len(available_keys)} API keys failed for {model_name}")
    return f"Извините, не удалось получить ответ от сервиса ({model_name}) после попыток со всеми доступными ключами. Пожалуйста, попробуйте позже."


# --- Gemma-3-1b-it API Call Function (Async) ---
async def call_gemma_3_1b_api(history, user_text, input_data=None, call_type="general"):
    """
    Обертка для новой функции genai. Сохраняет совместимость с существующим кодом.
    """
    return await call_gemma_3_1b_api_genai(history, user_text, input_data, call_type)


async def call_gemma_3_1b_api_old(history, user_text, input_data=None, call_type="general"):
    """
    Асинхронная функция для вызова Gemma-3-1b-it через официальный Gemini API.
    ТОЛЬКО для групповых чатов. БЕЗ системного промпта. Температура = 1.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemma3-1b] "
    model_name = "gemma-3-1b-it"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return "Извините, сервис временно недоступен из-за конфигурации. Пожалуйста, сообщите администратору."

    # Shuffle keys for random selection and fallback
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    headers = {"Content-Type": "application/json"}

    # Constructing the 'contents' for Gemini API
    gemini_contents = []

    # Process history
    use_history = (
        call_type.startswith("general")      # «general», «general_primary», «general_fallback…»
        or call_type in ["media_group", "file_query"]
    )
    if use_history and history:
        # Ограничиваем длину истории, чтобы не превысить лимит токенов
        MAX_HISTORY_TURNS = 20
        limited_history = history[-MAX_HISTORY_TURNS:] if len(history) > MAX_HISTORY_TURNS else history
        for turn in limited_history:
            role = "model" if turn.get("role") == "assistant" or turn.get("role") == "model" else "user"

            current_turn_parts = []
            parts_from_history = turn.get("parts", [])
            if not parts_from_history:
                continue

            for part_data in parts_from_history:
                if "text" in part_data and part_data["text"] is not None:
                    if part_data["text"].strip():
                        current_turn_parts.append({"text": part_data["text"]})
                elif "inline_data" in part_data:  # Handling images from history
                    inline_data = part_data["inline_data"]
                    if "mime_type" in inline_data and "data" in inline_data:
                        current_turn_parts.append({
                            "inlineData": {
                                "mimeType": inline_data["mime_type"],
                                "data": inline_data["data"]
                            }
                        })
            if current_turn_parts:
                gemini_contents.append({"role": role, "parts": current_turn_parts})

    # Process current user input (text and images)
    current_user_parts = []
    if user_text:
        # Sanitize user text
        sanitized_user_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', user_text)
        if sanitized_user_text.strip():
            current_user_parts.append({"text": sanitized_user_text})

    if input_data:  # input_data is expected to be a list of dicts with mime_type and data
        images_added_to_current_turn = 0
        items_to_iterate = input_data if isinstance(input_data, list) else [input_data]
        for item_dict in items_to_iterate:
            if images_added_to_current_turn >= 16:  # Gemini Vision limits
                log_admin(f"{log_prefix}Reached image limit for current turn.", level="warning")
                break
            if isinstance(item_dict, dict) and "mime_type" in item_dict and "data" in item_dict:
                current_user_parts.append({
                    "inlineData": {
                        "mimeType": item_dict["mime_type"],
                        "data": item_dict["data"]
                    }
                })
                images_added_to_current_turn += 1
            else:
                log_admin(f"{log_prefix}Skipping malformed item in input_data: {type(item_dict)}", level="debug")

    if current_user_parts:
        gemini_contents.append({"role": "user", "parts": current_user_parts})

    if not gemini_contents:
        log_admin(f"{log_prefix}No content to send to Gemini API.")
        return "Извините, нет данных для обработки запроса."

    payload = {"contents": gemini_contents}

    # Add generationConfig with temperature = 1 (as requested)
    payload["generationConfig"] = {
        "temperature": 1.0,  # Температура = 1 как требовал пользователь
        "maxOutputTokens": 8192
    }

    # НЕ добавляем systemInstruction - пользователь просил БЕЗ системного промпта

    # Try each key until success or all keys exhausted
    async with aiohttp.ClientSession() as session:
        for key_attempt, api_key in enumerate(available_keys):
            log_admin(f"{log_prefix}Attempting API call to {model_name} with key ending ...{api_key[-4:]} (attempt {key_attempt + 1}/{len(available_keys)})")

            api_url = config.GEMINI_API_URL_TEMPLATE.format(model=model_name, key=api_key)

            try:
                # Log payload (cautiously, to avoid very large data like base64 images in logs)
                try:
                    debug_payload = json.loads(json.dumps(payload))  # Deep copy for logging
                    for content_item in debug_payload.get("contents", []):
                        for part_item in content_item.get("parts", []):
                            if "inlineData" in part_item and "data" in part_item["inlineData"]:
                                part_item["inlineData"]["data"] = part_item["inlineData"]["data"][:100] + "... [TRUNCATED_FOR_LOG]"
                    log_admin(f"{log_prefix}Payload for {model_name}: {json.dumps(debug_payload, ensure_ascii=False, indent=2)}", level="debug")
                except Exception as e_log:
                    log_admin(f"{log_prefix}Error preparing payload for logging: {e_log}", level="warning")

                timeout = aiohttp.ClientTimeout(total=600)  # 10 minutes timeout
                async with session.post(api_url, headers=headers, json=payload, timeout=timeout) as response:
                    if response.status == 200:
                        response_data = await response.json()

                        # Parse response
                        if response_data.get("candidates") and len(response_data["candidates"]) > 0:
                            first_candidate = response_data["candidates"][0]
                            if first_candidate.get("content") and first_candidate["content"].get("parts") and len(first_candidate["content"]["parts"]) > 0:
                                text_response = first_candidate["content"]["parts"][0].get("text")
                                if text_response is not None:  # Can be empty string which is valid
                                    log_admin(f"{log_prefix}Successfully received response from {model_name} with key ...{api_key[-4:]}. Text length: {len(text_response)}")
                                    log_api_request(user_id=None, model_name=model_name, request_type=call_type, api_key_identifier=api_key[-4:])
                                    return text_response.strip()

                        # Handle cases like safety blocks or empty/unexpected responses
                        if response_data.get("promptFeedback"):
                            log_admin(f"{log_prefix}Prompt feedback received with key ...{api_key[-4:]}: {response_data['promptFeedback']}", level="warning")
                            # Check for specific block reasons
                            block_reason = response_data['promptFeedback'].get('blockReason')
                            if block_reason:
                                if block_reason == "SAFETY":
                                    safety_ratings_info = response_data['promptFeedback'].get('safetyRatings', [])
                                    categories_blocked = [sr['category'] for sr in safety_ratings_info if sr.get('probability') == 'HIGH']
                                    if categories_blocked:
                                        return f"Извините, ваш запрос заблокирован из-за настроек безопасности (категории: {', '.join(categories_blocked)}). Пожалуйста, измените ваш запрос."
                                    return "Извините, ваш запрос заблокирован из-за настроек безопасности. Пожалуйста, измените ваш запрос."
                                return f"Извините, запрос не выполнен (причина: {block_reason})."

                        log_admin(f"{log_prefix}Unexpected response structure from {model_name} with key ...{api_key[-4:]}: {str(response_data)[:500]}", level="error")
                        continue  # Try next key

                    else:
                        # Handle HTTP errors
                        error_text = await response.text()
                        log_admin(f"{log_prefix}HTTPError for {model_name} with key ...{api_key[-4:]}. Status: {response.status}. Error: {error_text[:200]}", level="error")

                        # For auth errors (401, 403), try next key immediately
                        if response.status in [401, 403]:
                            log_admin(f"{log_prefix}Authentication error with key ...{api_key[-4:]}, trying next key")
                            continue
                        # For rate limit (429), try next key
                        elif response.status == 429:
                            log_admin(f"{log_prefix}Rate limit with key ...{api_key[-4:]}, trying next key")
                            continue
                        # For server errors (500+), try next key
                        elif response.status >= 500:
                            log_admin(f"{log_prefix}Server error with key ...{api_key[-4:]}, trying next key")
                            continue
                        # For bad request (400), this is likely a content issue, not key issue
                        elif response.status == 400:
                            return "Извините, ваш запрос не может быть обработан (ошибка 400). Пожалуйста, проверьте его или попробуйте изменить."
                        else:
                            # For other HTTP errors, try next key
                            continue

            except asyncio.TimeoutError:
                log_admin(f"{log_prefix}Timeout for {model_name} with key ...{api_key[-4:]}", level="error")
                continue  # Try next key

            except aiohttp.ClientError as e:
                log_admin(f"{log_prefix}ClientError for {model_name} with key ...{api_key[-4:]}: {e}", level="error")
                continue  # Try next key

            except Exception as e:
                log_admin(f"{log_prefix}Unexpected error during {model_name} API call with key ...{api_key[-4:]}: {e}\n{traceback.format_exc()}", level="error")
                continue  # Try next key

    # All keys failed
    log_admin(f"{log_prefix}All {len(available_keys)} API keys failed for {model_name}")
    return f"Извините, не удалось получить ответ от сервиса ({model_name}) после попыток со всеми доступными ключами. Пожалуйста, попробуйте позже."


# --- Groq API Call Function (Async) ---
async def call_groq_api(history, user_text, input_data=None, call_type="general"):
    """
    DEPRECATED: Заменено на call_gemma_3_1b_api для групповых чатов.
    Эта функция больше не используется.
    """
    log_prefix = f"[{call_type.capitalize()}/Groq-Deprecated] "
    log_admin(f"{log_prefix}Groq API deprecated, redirecting to Gemma 3 1B API")

    # Перенаправляем на новую функцию Gemma 3 1B
    return await call_gemma_3_1b_api(history, user_text, input_data, call_type)




# --- Gemini Router Function ---
def call_gemini(model_name: str, history, user_text, input_data=None, system_prompt=None, call_type="general", user_id=None):
    log_admin(f"[GeminiRouter] Routing model: {model_name}, call_type: {call_type}")

    # Ensure MODEL_GEMINI_2_5_PRO is available, if not, treat it as a string for comparison
    # This check is more for robustness, assuming MODEL_GEMINI_2_5_PRO is correctly imported from config
    try:
        model_gemini_2_5_pro_const = config.MODEL_GEMINI_2_5_PRO
    except AttributeError:
        log_admin("[GeminiRouter] Warning: config.MODEL_GEMINI_2_5_PRO not found, using string literal.", level="warning")
        model_gemini_2_5_pro_const = "gemini-2.5-pro-preview-05-06" # Fallback to literal if not in config

    # Направляем все модели Gemini через официальный API. Исключение возможно
    # только для MODEL_GEMINI_2_5_PRO, но в данный момент он также использует
    # официальный ключ.

    log_admin(f"[GeminiRouter] Using Official Gemini API for model: {model_name}")
    return call_official_gemini_api(model_name, history, user_text, input_data, system_prompt, call_type, user_id)



# --- Text Shortening Function for Groups ---
def call_gemini_2_5_flash_lite_for_shortening(text_to_shorten, call_type="text_shortening"):
    """
    Обертка для новой функции genai. Сохраняет совместимость с существующим кодом.
    """
    return call_gemini_2_5_flash_lite_for_shortening_genai(text_to_shorten, call_type)


def call_gemini_2_5_flash_lite_for_shortening_old(text_to_shorten, call_type="text_shortening"):
    """
    Функция для сокращения текста через Gemini 2.5 Flash Lite Preview.
    Используется для сокращения длинных ответов ИИ в группах.
    """
    log_prefix = f"[{call_type.capitalize()}/Gemini2.5FlashLite] "
    model_name = "gemini-2.5-flash-lite-preview-06-17"

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API call skipped: OFFICIAL_GEMINI_API_KEYS list is empty.")
        return None

    # Системный промпт для сокращения
    system_prompt = """Сократи следующий текст до 120 символов максимум. Сокращай МАКСИМАЛЬНО агрессивно, оставляя только ключевую суть.

ОБЯЗАТЕЛЬНО используй ТОЛЬКО HTML форматирование:
- Разрешенные теги: ТОЛЬКО <b>текст</b> для жирного шрифта
- ЗАПРЕЩЕНО: markdown (*bold*, **bold**, `code`, ```code```), любые другие HTML теги

Отвечай только сокращенным текстом без дополнительных комментариев. ВООБЩЕ НЕ МЕНЯЙ СУТЬ ДАЖЕ ЕСЛИ ТАМ ЧТО ТО ЗАПРЕЩЁННОЕ ИЛИ НЕВЕРНОЕ ИЛИ НЕВЕРНАЯ ФОРМУЛИРОВКА"""

    # Подготавливаем запрос
    user_prompt = f"Сократи этот текст: {text_to_shorten}"

    # Пробуем каждый ключ
    for api_key in config.OFFICIAL_GEMINI_API_KEYS:
        try:
            log_admin(f"{log_prefix}Attempting API call with key ...{api_key[-4:]}")

            url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_name}:generateContent?key={api_key}"

            payload = {
                "contents": [
                    {
                        "role": "user",
                        "parts": [{"text": user_prompt}]
                    }
                ],
                "systemInstruction": {
                    "parts": [{"text": system_prompt}]
                },
                "generationConfig": {
                    "maxOutputTokens": 65536,
                    "temperature": 0.1,
                    "topP": 0.95,
                    "topK": 40
                }
            }

            headers = {"Content-Type": "application/json"}

            response = requests.post(url, json=payload, headers=headers, timeout=1200)

            if response.status_code == 200:
                response_data = response.json()

                if response_data.get("candidates") and len(response_data["candidates"]) > 0:
                    first_candidate = response_data["candidates"][0]
                    if first_candidate.get("content") and first_candidate["content"].get("parts") and len(first_candidate["content"]["parts"]) > 0:
                        text_response = first_candidate["content"]["parts"][0].get("text")
                        if text_response is not None:
                            log_admin(f"{log_prefix}Successfully shortened text from {len(text_to_shorten)} to {len(text_response)} characters")
                            return text_response.strip()

                log_admin(f"{log_prefix}Unexpected response structure: {response_data}")
                continue
            else:
                log_admin(f"{log_prefix}API error {response.status_code}: {response.text}")
                continue

        except Exception as e:
            log_admin(f"{log_prefix}Error with key ...{api_key[-4:]}: {e}")
            continue

    log_admin(f"{log_prefix}All API keys failed for text shortening")
    return None


# --- LLM Router Function with Priority ---
def call_llm_prioritized(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", message_to_edit_id=None, chat_id_for_edit=None, bot_instance=None, is_private_chat=False, is_podcast=False, user_id=None):
    """Routes the request to the appropriate LLM API call with priority semaphores."""
    from bot_globals import LLM_PRIVATE_SEM, LLM_PODCAST_SEM

    sem = LLM_PODCAST_SEM if is_podcast else LLM_PRIVATE_SEM
    with sem:
        return call_llm_original(model_name, history, user_text, input_data, system_prompt, call_type, message_to_edit_id, chat_id_for_edit, bot_instance, is_private_chat, user_id)

# --- LLM Router Function ---
# (This function remains unchanged as per this subtask's focus on API client error messages)
def call_llm_original(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", message_to_edit_id=None, chat_id_for_edit=None, bot_instance=None, is_private_chat=False, user_id=None):
    """Routes the request to the appropriate LLM API call."""
    log_admin(f"Router: model_name='{model_name}', call_type='{call_type}', is_private_chat={is_private_chat}")

    # Unified routing: use passed model_name if specified, otherwise use MODEL_MAIN
    if call_type in ["general_primary", "file_query", "general"]:
        chat_type = "private" if is_private_chat else "group"
        # Используем переданный model_name, если он указан, иначе дефолтный MODEL_MAIN
        effective_model = model_name if model_name else config.MODEL_MAIN
        log_admin(f"Routing to {effective_model} for {chat_type} chat: call_type={call_type}")

        # Обычный вызов для всех чатов (стриминг отключен)
        return call_official_gemini_api(
            effective_model,
            history,
            user_text,
            input_data,
            system_prompt,
            call_type,
            user_id
        )
    elif call_type in ["transcribe", "format_transcript", "classify"] or model_name == MODEL_LITE_CLASSIFY_TRANSCRIBE:
        log_admin(f"Routing to Gemini (via new router) for call_type: {call_type} or model: {model_name}")
        # Determine the correct model name for the call_gemini router
        # If model_name is already specific (like MODEL_LITE_CLASSIFY_TRANSCRIBE), use it.
        # Otherwise, default to MODEL_LITE_CLASSIFY_TRANSCRIBE for these call_types.
        effective_model_name = model_name if model_name == MODEL_LITE_CLASSIFY_TRANSCRIBE else MODEL_LITE_CLASSIFY_TRANSCRIBE
        return call_gemini(
            effective_model_name, # Pass the determined model name
            history,
            user_text,
            input_data,
            system_prompt,
            call_type
        )
    elif call_type in ["summarize_text_L1", "summarize_text_L2"]:
        log_admin(f"Routing to Gemini 2.5 Flash Lite for text summarization: {call_type}")
        # Используем специальную модель для сокращения текста
        response_text = call_official_gemini_api(
            "gemini-2.5-flash-lite-preview-06-17",
            history,
            user_text,
            input_data,
            system_prompt,
            call_type,
            user_id
        )
        return response_text
    elif call_type == "audio_summary_gemini":
        # Специальная маршрутизация для audio_summary_gemini - используем переданный model_name (обычно gemini-2.5-flash)
        log_admin(f"Routing to {model_name} for call_type: {call_type}")
        response_text = call_official_gemini_api(
            model_name,  # Используем переданный model_name вместо config.MODEL_MAIN
            history,
            user_text,
            input_data,
            system_prompt,
            call_type,
            user_id
        )
        return response_text
    elif call_type in ["summarize", "summarize_audio", "media_group"]:
        log_admin(f"Routing to {config.MODEL_MAIN} for call_type: {call_type}")
        response_text = call_official_gemini_api(
            config.MODEL_MAIN,
            history,
            user_text,
            input_data,
            system_prompt,
            call_type,
            user_id
        )
        return response_text

    elif model_name == "o4-mini-high":
        log_admin(f"Routing to {config.MODEL_MAIN} for formatting: {call_type}")
        response_text = call_official_gemini_api(
            config.MODEL_MAIN,
            history,
            user_text,
            input_data,
            system_prompt,
            call_type,
            user_id
        )
        return response_text

    else:
        log_admin(f"Warning: Unknown model_name '{model_name}' or call_type '{call_type}' in router. Defaulting to {config.MODEL_MAIN}.")
        response_text = call_official_gemini_api(
            config.MODEL_MAIN,
            history,
            user_text,
            input_data,
            system_prompt,
            call_type,
            user_id
        )
        return response_text


# Алиас для обратной совместимости
def call_llm(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", message_to_edit_id=None, chat_id_for_edit=None, bot_instance=None, is_private_chat=False, user_id=None):
    """Обертка для call_llm_prioritized с автоопределением типа запроса."""
    # Определяем, является ли это подкаст-запросом
    is_podcast = call_type in ["podcast_generation", "thematic_podcast_generation"]
    return call_llm_prioritized(model_name, history, user_text, input_data, system_prompt, call_type, message_to_edit_id, chat_id_for_edit, bot_instance, is_private_chat, is_podcast, user_id)


def call_llm_stream(model_name, history, user_text, input_data=None, system_prompt=None, call_type="general", user_id=None):
    """
    Стриминговая версия call_llm для личных чатов.
    Возвращает генератор, который выдает чанки текста по мере их получения от API.
    """
    log_admin(f"StreamRouter: model_name='{model_name}', call_type='{call_type}'")

    # Стриминг поддерживается только для основных типов запросов
    if call_type in ["general_primary", "file_query", "general"]:
        # Используем переданный model_name, если он указан, иначе дефолтный MODEL_MAIN
        effective_model = model_name if model_name else config.MODEL_MAIN
        log_admin(f"Streaming routing to {effective_model} for private chat: call_type={call_type}")

        # Выбираем подходящую стриминговую функцию
        if effective_model == "gemini-2.5-flash":
            # Для личных чатов с полным функционалом
            return call_gemini_2_5_flash_api_genai_stream(
                history, user_text, input_data, system_prompt, call_type
            )
        else:
            # Для других моделей используем общую стриминговую функцию
            return call_official_gemini_api_genai_stream(
                effective_model, history, user_text, input_data, system_prompt, call_type, user_id
            )
    else:
        # Для неподдерживаемых типов запросов возвращаем обычный результат как генератор
        log_admin(f"Streaming not supported for call_type '{call_type}', falling back to regular call")
        result = call_llm(model_name, history, user_text, input_data, system_prompt, call_type, None, None, None, True, user_id)
        yield result


def call_gemini_single_tts_api(text, call_type="simple_tts", voice_name="Zephyr"):
    """
    Обертка для новой функции genai. Сохраняет совместимость с существующим кодом.
    """
    return call_gemini_single_tts_api_genai(text, call_type, voice_name)


def call_gemini_single_tts_api_old(text, call_type="simple_tts", voice_name="Zephyr"):
    """
    Calls Gemini TTS API for single speaker text-to-speech.
    Returns audio data as bytes or None on error.
    """
    log_prefix = f"[{call_type.capitalize()}/GeminiTTS] "

    if not config.OFFICIAL_GEMINI_API_KEYS:
        log_admin(f"{log_prefix}API key not found")
        return None

    if not text or not isinstance(text, str):
        log_admin(f"{log_prefix}Invalid text provided")
        return None

    log_admin(f"{log_prefix}Generating single speaker audio for text ({len(text)} characters)")

    # Shuffle keys for random selection
    available_keys = list(config.OFFICIAL_GEMINI_API_KEYS)
    random.shuffle(available_keys)

    tts_model = "gemini-2.5-flash-preview-tts"
    headers = {
        "Content-Type": "application/json"
    }

    # Single speaker payload structure
    payload = {
        "model": tts_model,
        "contents": [{
            "parts": [{
                "text": text
            }]
        }],
        "generationConfig": {
            "responseModalities": ["AUDIO"],
            "speechConfig": {
                "voiceConfig": {
                    "prebuiltVoiceConfig": {
                        "voiceName": voice_name  # Default: Zephyr
                    }
                }
            }
        }
    }

    # Try each key until success or all keys exhausted
    for attempt, api_key in enumerate(available_keys):
        log_admin(f"{log_prefix}Attempting single speaker TTS with key ending ...{api_key[-4:]} (attempt {attempt + 1}/{len(available_keys)})")

        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{tts_model}:generateContent?key={api_key}"

        try:
            log_admin(f"{log_prefix}Making single speaker TTS request with voice: {voice_name}")
            response = requests.post(api_url, headers=headers, json=payload, timeout=600)
            response.raise_for_status()

            response_data = response.json()
            if "candidates" in response_data and len(response_data["candidates"]) > 0:
                candidate = response_data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    parts = candidate["content"]["parts"]
                    if len(parts) > 0 and "inlineData" in parts[0] and "data" in parts[0]["inlineData"]:
                        audio_base64 = parts[0]["inlineData"]["data"]
                        audio_data = base64.b64decode(audio_base64)
                        log_admin(f"{log_prefix}Successfully generated single speaker audio ({len(audio_data)} bytes) with key ...{api_key[-4:]}")
                        return audio_data
                    else:
                        log_admin(f"{log_prefix}No audio data in response with key ...{api_key[-4:]}")
                        continue  # Try next key
                else:
                    log_admin(f"{log_prefix}Invalid response structure with key ...{api_key[-4:]}")
                    continue  # Try next key
            else:
                log_admin(f"{log_prefix}No candidates in response with key ...{api_key[-4:]}")
                continue  # Try next key

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            log_admin(f"{log_prefix}HTTPError with key ...{api_key[-4:]}: Status {status_code}")
            continue  # Try next key

        except Exception as e:
            log_admin(f"{log_prefix}Unexpected error with key ...{api_key[-4:]}: {e}")
            continue  # Try next key

    # All keys failed
    log_admin(f"{log_prefix}All {len(available_keys)} API keys failed for single speaker TTS")
    return None


def call_gemini_single_tts_api_via_voidai(text, call_type="simple_tts", voice_name="alloy"):
    """
    Calls VoidAI TTS API for text-to-speech conversion using tts-1-hd model.
    Returns audio data as bytes or None on error.
    """
    log_prefix = f"[{call_type.capitalize()}/VoidAITTS] "

    if not VOIDAI_API_KEYS:
        log_admin(f"{log_prefix}VoidAI API key not found")
        return None

    if not text or not isinstance(text, str):
        log_admin(f"{log_prefix}Invalid text provided")
        return None

    log_admin(f"{log_prefix}Generating audio via VoidAI TTS API for text ({len(text)} characters)")

    import time

    def try_tts_call(api_key, attempt_info=""):
        """Helper function to make TTS call with given key"""
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # VoidAI TTS API payload
        payload = {
            "model": "tts-1-hd",
            "voice": voice_name,
            "input": text
        }

        log_admin(f"{log_prefix}Attempting TTS call {attempt_info} with voice: {voice_name}")

        try:
            # Use VoidAI TTS endpoint
            tts_endpoint = "https://api.voidai.app/v1/audio/speech"
            response = requests.post(tts_endpoint, headers=headers, json=payload, timeout=1200)

            if response.status_code == 429:
                log_admin(f"{log_prefix}API key returned 429 (rate limit)")
                return None, "429"

            response.raise_for_status()

            # VoidAI TTS API returns raw audio data directly
            audio_data = response.content
            if audio_data and len(audio_data) > 0:
                log_admin(f"{log_prefix}Successfully generated audio ({len(audio_data)} bytes)")
                return audio_data, "success"
            else:
                log_admin(f"{log_prefix}No audio data in response")
                return None, "no_audio_data"

        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response else None
            error_text = e.response.text if e.response else "No response text"
            log_admin(f"{log_prefix}HTTPError. Status: {status_code}, Error: {error_text[:200]}")
            if status_code == 429:
                return None, "429"
            else:
                return None, f"http_error_{status_code}"
        except requests.exceptions.RequestException as e:
            log_admin(f"{log_prefix}RequestException: {e}")
            return None, "request_exception"
        except Exception as e:
            log_admin(f"{log_prefix}Exception: {e}")
            return None, "exception"

    # Phase 1: Try first key
    if len(VOIDAI_API_KEYS) > 0:
        result, error_type = try_tts_call(VOIDAI_API_KEYS[0], "with first key")
        if result is not None:
            return result

        # Phase 2: If first key failed with 429, try second key
        if error_type == "429" and len(VOIDAI_API_KEYS) > 1:
            log_admin(f"{log_prefix}First key failed with 429, trying second key")
            result, error_type = try_tts_call(VOIDAI_API_KEYS[1], "with second key")
            if result is not None:
                return result

            # Phase 3: If second key also failed, retry first key with delays (up to 10 times)
            if error_type == "429":
                log_admin(f"{log_prefix}Second key also failed with 429, retrying first key with delays")
                for retry_attempt in range(10):
                    time.sleep(3)  # 3 second delay
                    result, error_type = try_tts_call(VOIDAI_API_KEYS[0], f"with first key (retry {retry_attempt + 1}/10)")
                    if result is not None:
                        return result
                    if error_type != "429":
                        break  # If it's not 429, no point in retrying

    log_admin(f"{log_prefix}All TTS attempts failed")
    return None


# --- Navy API Function for Grok-4 ---
@retry_on_censorship(max_attempts=5)
def generate_with_grok(prompt, chat_id, call_type="grok_generation"):
    """
    Generates content using grok-4 model via Navy API.

    Args:
        prompt (str): The text prompt to send to grok-4
        chat_id (int): Chat ID for logging purposes
        call_type (str): Type of call for logging

    Returns:
        str: Generated text or error message
    """
    log_prefix = f"[{call_type.capitalize()}/Navy-Grok4] "

    if not NAVY_API_KEY:
        log_admin(f"{log_prefix}API key not configured")
        return None

    if not prompt or not isinstance(prompt, str):
        log_admin(f"{log_prefix}Invalid prompt provided")
        return None

    # Prepare request data
    request_data = {
        "model": "grok-4",  # Navy API expects "grok-4" for grok-4
        "messages": [
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.7,
        "max_tokens": 16000,
        "stream": False
    }

    headers = {
        "Authorization": f"Bearer {NAVY_API_KEY}",
        "Content-Type": "application/json"
    }

    try:
        log_admin(f"{log_prefix}Making request to Navy API for chat {chat_id}")

        # Make request with timeout
        response = requests.post(
            NAVY_API_URL,
            json=request_data,
            headers=headers,
            timeout=1200  # 20 minute timeout
        )

        # Check HTTP status
        if response.status_code != 200:
            log_admin(f"{log_prefix}HTTP error {response.status_code}: {response.text}")
            return None

        # Parse response
        response_data = response.json()

        # Extract generated text
        if "choices" in response_data and len(response_data["choices"]) > 0:
            generated_text = response_data["choices"][0]["message"]["content"]

            if generated_text and generated_text.strip():
                log_admin(f"{log_prefix}Successfully generated {len(generated_text)} characters")

                # Log API request for statistics
                try:
                    log_api_request(
                        user_id=chat_id,  # Use chat_id as user_id for group requests
                        model_name="grok-4",
                        request_type=call_type,
                        api_key_identifier="Navy-API"
                    )
                except Exception as e:
                    log_admin(f"{log_prefix}Error logging API request: {e}")

                return generated_text.strip()
            else:
                log_admin(f"{log_prefix}Empty response from API")
                return None
        else:
            log_admin(f"{log_prefix}Invalid response format: {response_data}")
            return None

    except requests.exceptions.Timeout:
        log_admin(f"{log_prefix}Request timeout after 600 seconds")
        return None
    except requests.exceptions.ConnectionError:
        log_admin(f"{log_prefix}Connection error to Navy API")
        return None
    except requests.exceptions.RequestException as e:
        log_admin(f"{log_prefix}Request error: {e}")
        return None
    except json.JSONDecodeError as e:
        log_admin(f"{log_prefix}JSON decode error: {e}")
        return None
    except Exception as e:
        log_admin(f"{log_prefix}Unexpected error: {e}\n{traceback.format_exc()}")
        return None


def generate_with_grok_fallback(prompt, chat_id, system_prompt=None, call_type="grok_generation_fallback"):
    """
    Generates content using grok-4 with automatic fallback to Gemini on failure.

    Args:
        prompt (str): The text prompt to send
        chat_id (int): Chat ID for logging purposes
        system_prompt (str): System prompt for Gemini fallback
        call_type (str): Type of call for logging

    Returns:
        str: Generated text from grok-4 or Gemini fallback
    """
    log_prefix = f"[{call_type.capitalize()}/GrokFallback] "

    # First try grok-4
    log_admin(f"{log_prefix}Attempting generation with grok-4")
    result = generate_with_grok(prompt, chat_id, call_type)

    if result is not None:
        log_admin(f"{log_prefix}Successfully generated with grok-4")
        return result

    # Fallback to Gemini
    log_admin(f"{log_prefix}grok-4 failed, falling back to Gemini 2.5 Pro")

    try:
        fallback_result = call_official_gemini_api(
            model_name="gemini-2.5-pro",
            history=[],
            user_text=prompt,
            input_data=None,
            system_prompt=system_prompt,
            call_type=f"{call_type}_gemini_fallback"
        )

        if fallback_result and not fallback_result.startswith("ОШИБКА") and not fallback_result.startswith("Извините"):
            log_admin(f"{log_prefix}Successfully generated with Gemini fallback")
            return fallback_result
        else:
            log_admin(f"{log_prefix}Gemini fallback also failed: {fallback_result[:100] if fallback_result else 'None'}")
            return "Извините, произошла ошибка при генерации контента. Попробуйте позже."

    except Exception as e:
        log_admin(f"{log_prefix}Error in Gemini fallback: {e}")
        return "Извините, произошла ошибка при генерации контента. Попробуйте позже."


# ---------------- Image generation ----------------
def call_navy_image_generate_api_sync(prompt: str,
                                      size: str = "1024x1024",
                                      n: int = 1,
                                      model: str = None) -> str | None:
    """
    Генерирует изображение и возвращает URL первого результата
    (или None в случае ошибки).
    Использует каскад фоллбэков моделей при ошибках 500.
    """
    from config import NAVY_IMAGE_API_URL, NAVY_IMAGE_API_KEY, NAVY_ALLOWED_IMAGE_SIZES, NAVY_IMAGE_MODELS
    import requests, json

    if size not in NAVY_ALLOWED_IMAGE_SIZES:
        # Fallback к ближайшему допустимому (по площади)
        size = min(NAVY_ALLOWED_IMAGE_SIZES,
                   key=lambda s: abs(int(s.split('x')[0]) - int(size.split('x')[0])))

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {NAVY_IMAGE_API_KEY}"
    }

    # Используем список моделей-фоллбэков
    models_to_try = NAVY_IMAGE_MODELS if model is None else [model] + [m for m in NAVY_IMAGE_MODELS if m != model]

    for model_name in models_to_try:
        payload = {
            "model": model_name,
            "prompt": prompt,
            "n": n,
            "size": size
        }

        try:
            r = requests.post(NAVY_IMAGE_API_URL, json=payload, headers=headers, timeout=180)

            # Специальная обработка 500 ошибки - пробуем следующую модель
            if r.status_code == 500:
                log_admin(f"[NavyImage] 500 error from {model_name}, trying next fallback...", level="warning")
                continue

            r.raise_for_status()
            data = r.json()
            url = data.get("data", [{}])[0].get("url")
            if url:
                log_admin(f"[NavyImage] Success via {model_name}", level="info")
                return url

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 500:
                log_admin(f"[NavyImage] 500 error from {model_name}, trying next fallback...", level="warning")
                continue
            else:
                log_admin(f"[NavyImage] HTTP error with {model_name}: {e}", level="error")
                continue
        except Exception as e:
            log_admin(f"[NavyImage] Error with {model_name}: {e}", level="error")
            continue

    # Все модели не сработали
    log_admin(f"[NavyImage] All fallback models failed", level="error")
    return None


