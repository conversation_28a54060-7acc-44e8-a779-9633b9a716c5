"""
Модуль для автоматической очистки временных файлов и управления ресурсами бота.
Включает функции для очистки аудио/видео файлов, логов, кеша и оптимизации базы данных.
"""

import os
import shutil
import tempfile
import time
import glob
import sqlite3
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta

# Импорт функции логирования из bot_globals
try:
    from bot_globals import log_admin
except ImportError:
    # Fallback если bot_globals недоступен
    def log_admin(message, level="info"):
        print(f"[{level.upper()}] {message}")


class CleanupManager:
    """Менеджер для автоматической очистки временных файлов и ресурсов."""
    
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Паттерны файлов для очистки
        self.temp_file_patterns = [
            "tts_wav_*.wav",
            "tts_*.mp3", 
            "podcast_raw_*.bin",
            "podcast_*.mp3",
            "diana_sasha_podcast_combined_*.mp3",
            "*_part_*.mp4",
            "*_combined_*.mp4",
            "podcast_concat_*.txt",
            "*.html"  # Временные HTML файлы
        ]
        
        # Максимальный возраст файлов в часах
        self.max_file_age_hours = 24
        
        # Лимиты размеров (в байтах)
        self.max_log_size = 50 * 1024 * 1024  # 50 MB
        self.max_db_size = 100 * 1024 * 1024  # 100 MB
        self.max_temp_dir_size = 500 * 1024 * 1024  # 500 MB
        
    def cleanup_temp_files(self) -> Dict[str, int]:
        """
        Очищает временные файлы из temp директории.
        
        Returns:
            Dict с информацией об очистке: {'files_removed': int, 'space_freed': int}
        """
        files_removed = 0
        space_freed = 0
        
        log_admin("Начинаю очистку временных файлов...", level="info")
        
        try:
            current_time = time.time()
            cutoff_time = current_time - (self.max_file_age_hours * 3600)
            
            for pattern in self.temp_file_patterns:
                pattern_path = os.path.join(self.temp_dir, pattern)
                matching_files = glob.glob(pattern_path)
                
                for file_path in matching_files:
                    try:
                        # Проверяем возраст файла
                        file_mtime = os.path.getmtime(file_path)
                        if file_mtime < cutoff_time:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            files_removed += 1
                            space_freed += file_size
                            log_admin(f"Удален временный файл: {os.path.basename(file_path)} ({file_size} bytes)", level="debug")
                    except (OSError, IOError) as e:
                        log_admin(f"Ошибка при удалении файла {file_path}: {e}", level="warning")
                        
        except Exception as e:
            log_admin(f"Ошибка при очистке временных файлов: {e}", level="error")
            
        log_admin(f"Очистка временных файлов завершена. Удалено файлов: {files_removed}, освобождено места: {space_freed} bytes", level="info")
        return {'files_removed': files_removed, 'space_freed': space_freed}
    
    def cleanup_python_cache(self) -> Dict[str, int]:
        """
        Очищает Python кеш (__pycache__ папки и .pyc файлы).
        
        Returns:
            Dict с информацией об очистке
        """
        files_removed = 0
        space_freed = 0
        
        log_admin("Начинаю очистку Python кеша...", level="info")
        
        try:
            # Очистка __pycache__ папок
            for root, dirs, files in os.walk(self.script_dir):
                if '__pycache__' in dirs:
                    pycache_path = os.path.join(root, '__pycache__')
                    try:
                        # Подсчитываем размер перед удалением
                        for file in os.listdir(pycache_path):
                            file_path = os.path.join(pycache_path, file)
                            if os.path.isfile(file_path):
                                space_freed += os.path.getsize(file_path)
                                files_removed += 1
                        
                        shutil.rmtree(pycache_path)
                        log_admin(f"Удалена папка кеша: {pycache_path}", level="debug")
                    except (OSError, IOError) as e:
                        log_admin(f"Ошибка при удалении папки кеша {pycache_path}: {e}", level="warning")
                
                # Очистка .pyc файлов
                for file in files:
                    if file.endswith('.pyc'):
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            files_removed += 1
                            space_freed += file_size
                            log_admin(f"Удален .pyc файл: {file_path}", level="debug")
                        except (OSError, IOError) as e:
                            log_admin(f"Ошибка при удалении .pyc файла {file_path}: {e}", level="warning")
                            
        except Exception as e:
            log_admin(f"Ошибка при очистке Python кеша: {e}", level="error")
            
        log_admin(f"Очистка Python кеша завершена. Удалено файлов: {files_removed}, освобождено места: {space_freed} bytes", level="info")
        return {'files_removed': files_removed, 'space_freed': space_freed}
    
    # УДАЛЕНО: rotate_logs() - теперь используется RotatingFileHandler для автоматической ротации
    
    # УДАЛЕНО: _cleanup_old_log_archives() - теперь RotatingFileHandler управляет архивами автоматически
    
    def optimize_database(self) -> Dict[str, any]:
        """
        Оптимизирует базу данных: удаляет старые записи и выполняет VACUUM.
        
        Returns:
            Dict с информацией об оптимизации
        """
        db_path = os.path.join(self.script_dir, "chat_messages.db")
        result = {'optimized': False, 'old_size': 0, 'new_size': 0, 'records_deleted': 0}
        
        if not os.path.exists(db_path):
            return result
            
        try:
            result['old_size'] = os.path.getsize(db_path)
            
            # Подключаемся к базе данных
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Удаляем записи старше 30 дней
            cutoff_date = datetime.now() - timedelta(days=30)
            cutoff_timestamp = int(cutoff_date.timestamp())
            
            cursor.execute("SELECT COUNT(*) FROM messages WHERE timestamp < ?", (cutoff_timestamp,))
            old_records_count = cursor.fetchone()[0]
            
            if old_records_count > 0:
                cursor.execute("DELETE FROM messages WHERE timestamp < ?", (cutoff_timestamp,))
                result['records_deleted'] = old_records_count
                log_admin(f"Удалено старых записей из БД: {old_records_count}", level="info")
            
            # Выполняем VACUUM для оптимизации
            cursor.execute("VACUUM")
            conn.commit()
            conn.close()
            
            result['new_size'] = os.path.getsize(db_path)
            result['optimized'] = True
            
            space_saved = result['old_size'] - result['new_size']
            log_admin(f"База данных оптимизирована. Размер до: {result['old_size']} bytes, после: {result['new_size']} bytes, сэкономлено: {space_saved} bytes", level="info")
            
        except Exception as e:
            log_admin(f"Ошибка при оптимизации базы данных: {e}", level="error")
            
        return result
    
    def get_resource_usage(self) -> Dict[str, int]:
        """
        Получает информацию об использовании ресурсов.
        
        Returns:
            Dict с размерами различных ресурсов
        """
        usage = {
            'temp_dir_size': 0,
            'log_file_size': 0,
            'db_size': 0,
            'cache_size': 0,
            'temp_files_count': 0
        }
        
        try:
            # Размер temp директории (только наши файлы)
            temp_files_count = 0
            for pattern in self.temp_file_patterns:
                pattern_path = os.path.join(self.temp_dir, pattern)
                matching_files = glob.glob(pattern_path)
                temp_files_count += len(matching_files)
                for file_path in matching_files:
                    try:
                        usage['temp_dir_size'] += os.path.getsize(file_path)
                    except OSError:
                        pass
            usage['temp_files_count'] = temp_files_count
            
            # Размер лог файла
            log_file_path = os.path.join(self.script_dir, "bot_activity.log")
            if os.path.exists(log_file_path):
                usage['log_file_size'] = os.path.getsize(log_file_path)
            
            # Размер базы данных
            db_path = os.path.join(self.script_dir, "chat_messages.db")
            if os.path.exists(db_path):
                usage['db_size'] = os.path.getsize(db_path)
            
            # Размер кеша Python
            for root, dirs, files in os.walk(self.script_dir):
                if '__pycache__' in dirs:
                    pycache_path = os.path.join(root, '__pycache__')
                    for file in os.listdir(pycache_path):
                        file_path = os.path.join(pycache_path, file)
                        if os.path.isfile(file_path):
                            usage['cache_size'] += os.path.getsize(file_path)
                
                for file in files:
                    if file.endswith('.pyc'):
                        file_path = os.path.join(root, file)
                        try:
                            usage['cache_size'] += os.path.getsize(file_path)
                        except OSError:
                            pass
                            
        except Exception as e:
            log_admin(f"Ошибка при получении информации об использовании ресурсов: {e}", level="error")
            
        return usage
    
    def full_cleanup(self) -> Dict[str, any]:
        """
        Выполняет полную очистку всех ресурсов.
        
        Returns:
            Dict с общей информацией об очистке
        """
        log_admin("Начинаю полную очистку ресурсов...", level="info")
        
        results = {
            'temp_files': self.cleanup_temp_files(),
            'python_cache': self.cleanup_python_cache(),
            'log_rotation': self.rotate_logs(),
            'database': self.optimize_database()
        }
        
        # Подсчитываем общую статистику
        total_files_removed = (results['temp_files']['files_removed'] + 
                             results['python_cache']['files_removed'])
        total_space_freed = (results['temp_files']['space_freed'] + 
                           results['python_cache']['space_freed'])
        
        results['summary'] = {
            'total_files_removed': total_files_removed,
            'total_space_freed': total_space_freed,
            'database_records_deleted': results['database']['records_deleted']
        }
        
        log_admin(f"Полная очистка завершена. Удалено файлов: {total_files_removed}, освобождено места: {total_space_freed} bytes, удалено записей БД: {results['database']['records_deleted']}", level="info")
        
        return results


# Глобальный экземпляр менеджера очистки
cleanup_manager = CleanupManager()


def format_bytes(bytes_count: int) -> str:
    """Форматирует количество байт в читаемый вид."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_count < 1024.0:
            return f"{bytes_count:.1f} {unit}"
        bytes_count /= 1024.0
    return f"{bytes_count:.1f} TB"


def check_resource_limits() -> List[str]:
    """
    Проверяет превышение лимитов ресурсов.

    Returns:
        Список предупреждений о превышении лимитов
    """
    warnings = []
    usage = cleanup_manager.get_resource_usage()

    if usage['temp_dir_size'] > cleanup_manager.max_temp_dir_size:
        warnings.append(f"Размер временных файлов превышает лимит: {format_bytes(usage['temp_dir_size'])} > {format_bytes(cleanup_manager.max_temp_dir_size)}")

    if usage['log_file_size'] > cleanup_manager.max_log_size:
        warnings.append(f"Размер лог файла превышает лимит: {format_bytes(usage['log_file_size'])} > {format_bytes(cleanup_manager.max_log_size)}")

    if usage['db_size'] > cleanup_manager.max_db_size:
        warnings.append(f"Размер базы данных превышает лимит: {format_bytes(usage['db_size'])} > {format_bytes(cleanup_manager.max_db_size)}")

    return warnings


def send_resource_alert_to_admins(warnings: List[str]):
    """
    Отправляет уведомления о превышении лимитов ресурсов администраторам.

    Args:
        warnings: Список предупреждений
    """
    if not warnings:
        return

    try:
        from bot_globals import bot
        from admin_system import get_admin_list

        alert_text = "🚨 <b>ПРЕДУПРЕЖДЕНИЕ О РЕСУРСАХ</b>\n\n"
        alert_text += "Обнаружены превышения лимитов:\n\n"

        for warning in warnings:
            alert_text += f"⚠️ {warning}\n"

        alert_text += "\n🔧 Автоматическая очистка будет запущена немедленно."

        # Отправляем уведомления всем администраторам
        admin_list = get_admin_list()
        for admin_id in admin_list:
            try:
                bot.send_message(
                    chat_id=admin_id,
                    text=alert_text,
                    parse_mode="HTML"
                )
                log_admin(f"Отправлено уведомление о ресурсах администратору {admin_id}", level="info")
            except Exception as e:
                log_admin(f"Ошибка при отправке уведомления администратору {admin_id}: {e}", level="warning")

    except Exception as e:
        log_admin(f"Ошибка при отправке уведомлений о ресурсах: {e}", level="error")


def get_resource_status_report() -> str:
    """
    Создает отчет о текущем состоянии ресурсов.

    Returns:
        Форматированный текст отчета
    """
    try:
        usage = cleanup_manager.get_resource_usage()

        report = "📊 <b>ОТЧЕТ О РЕСУРСАХ</b>\n\n"

        # Временные файлы
        temp_percent = (usage['temp_dir_size'] / cleanup_manager.max_temp_dir_size) * 100
        temp_status = "🔴" if temp_percent > 80 else "🟡" if temp_percent > 60 else "🟢"
        report += f"{temp_status} <b>Временные файлы:</b>\n"
        report += f"   Размер: {format_bytes(usage['temp_dir_size'])} / {format_bytes(cleanup_manager.max_temp_dir_size)} ({temp_percent:.1f}%)\n"
        report += f"   Количество: {usage['temp_files_count']} файлов\n\n"

        # Лог файл
        log_percent = (usage['log_file_size'] / cleanup_manager.max_log_size) * 100
        log_status = "🔴" if log_percent > 80 else "🟡" if log_percent > 60 else "🟢"
        report += f"{log_status} <b>Лог файл:</b>\n"
        report += f"   Размер: {format_bytes(usage['log_file_size'])} / {format_bytes(cleanup_manager.max_log_size)} ({log_percent:.1f}%)\n\n"

        # База данных
        db_percent = (usage['db_size'] / cleanup_manager.max_db_size) * 100
        db_status = "🔴" if db_percent > 80 else "🟡" if db_percent > 60 else "🟢"
        report += f"{db_status} <b>База данных:</b>\n"
        report += f"   Размер: {format_bytes(usage['db_size'])} / {format_bytes(cleanup_manager.max_db_size)} ({db_percent:.1f}%)\n\n"

        # Python кеш
        report += f"🗂 <b>Python кеш:</b>\n"
        report += f"   Размер: {format_bytes(usage['cache_size'])}\n\n"

        # Общий статус
        max_percent = max(temp_percent, log_percent, db_percent)
        if max_percent > 80:
            report += "🚨 <b>Статус:</b> Критический - требуется очистка\n"
        elif max_percent > 60:
            report += "⚠️ <b>Статус:</b> Предупреждение - рекомендуется очистка\n"
        else:
            report += "✅ <b>Статус:</b> Нормальный\n"

        report += f"\n🕐 <i>Обновлено: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>"

        return report

    except Exception as e:
        log_admin(f"Ошибка при создании отчета о ресурсах: {e}", level="error")
        return "❌ Ошибка при получении информации о ресурсах"
