import os
import sys
import platform

def clear_console():
    """Очищает консоль для экономии памяти"""
    try:
        # Windows
        if platform.system() == "Windows":
            os.system('cls')
        # Linux/Mac
        else:
            os.system('clear')
    except:
        # Если очистка не работает, просто выводим пустые строки
        print('\n' * 50)

def minimize_console_buffer():
    """Минимизирует буфер консоли для экономии памяти"""
    try:
        if platform.system() == "Windows":
            # Устанавливаем минимальный буфер для Windows консоли
            os.system('mode con: lines=50')
    except:
        pass