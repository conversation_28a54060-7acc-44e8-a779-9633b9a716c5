import sqlite3
import threading
import os
from datetime import datetime, timedelta
from bot_globals import log_admin

# Database configuration
# Используем абсолютный путь относительно файла или переменную окружения
BOT_DIR = os.environ.get('PODCAST_BOT_DIR', os.path.dirname(os.path.abspath(__file__)))
DB_PATH = os.path.join(BOT_DIR, "chat_messages.db")
db_lock = threading.Lock()

def init_database():
    """Initialize the database and create tables if they don't exist."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Create messages table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    username TEXT,
                    first_name TEXT,
                    last_name TEXT,
                    message_text TEXT,
                    message_id INTEGER,
                    timestamp INTEGER NOT NULL,
                    date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
                    message_type TEXT DEFAULT 'text',
                    is_forwarded BOOLEAN DEFAULT FALSE,
                    forward_from_username TEXT,
                    reply_to_message_id INTEGER,
                    reply_to_user_id INTEGER,
                    reply_to_username TEXT,
                    message_date TEXT,
                    message_time TEXT,
                    time_period TEXT
                )
            ''')

            # Add new columns if they don't exist (for existing databases)
            try:
                cursor.execute('ALTER TABLE chat_messages ADD COLUMN message_date TEXT')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE chat_messages ADD COLUMN message_time TEXT')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE chat_messages ADD COLUMN time_period TEXT')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE chat_messages ADD COLUMN reply_to_user_id INTEGER')
            except sqlite3.OperationalError:
                pass  # Column already exists

            try:
                cursor.execute('ALTER TABLE chat_messages ADD COLUMN reply_to_username TEXT')
            except sqlite3.OperationalError:
                pass  # Column already exists

            # Create podcast_timestamps table for tracking last podcast times
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS podcast_timestamps (
                    chat_id INTEGER PRIMARY KEY,
                    last_podcast_time INTEGER NOT NULL,
                    updated_at INTEGER NOT NULL,
                    last_podcast_message_id INTEGER
                )
            ''')

            # Add message_id column if it doesn't exist (for existing databases)
            try:
                cursor.execute('ALTER TABLE podcast_timestamps ADD COLUMN last_podcast_message_id INTEGER')
            except sqlite3.OperationalError:
                pass  # Column already exists

            # Create unlocked_groups table for group access control
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS unlocked_groups (
                    chat_id INTEGER PRIMARY KEY,
                    unlocked_by INTEGER NOT NULL,
                    unlocked_at INTEGER NOT NULL,
                    group_title TEXT,
                    group_username TEXT
                )
            ''')

            # Create blocked_podcasts_groups table for podcast blocking per group
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS blocked_podcasts_groups (
                    chat_id INTEGER PRIMARY KEY,
                    blocked_by INTEGER NOT NULL,
                    blocked_at INTEGER NOT NULL,
                    group_title TEXT,
                    group_username TEXT
                )
            ''')

            # HTML sites table removed as requested

            # Create indexes for better performance
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_chat_timestamp
                ON chat_messages(chat_id, timestamp)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_chat_date
                ON chat_messages(chat_id, date_created)
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_unlocked_groups_chat
                ON unlocked_groups(chat_id)
            ''')

            # HTML sites indexes removed as requested



            # --- Create api_requests table ---
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS api_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    model_name TEXT NOT NULL,
                    request_type TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    date_created DATETIME DEFAULT CURRENT_TIMESTAMP,
                    api_key_identifier TEXT
                )
            ''')
            try:
                cursor.execute('ALTER TABLE api_requests ADD COLUMN api_key_identifier TEXT')
            except sqlite3.OperationalError:
                pass # Column already exists

            conn.commit()
            conn.close()
            
        log_admin("Database initialized successfully", level="info")
        return True

    except Exception as e:
        log_admin(f"Error initializing database: {e}", level="error")
        return False


def is_group_unlocked(chat_id):
    """Check if a group is unlocked for bot functionality."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT 1 FROM unlocked_groups WHERE chat_id = ?
            ''', (chat_id,))

            result = cursor.fetchone()
            conn.close()

            return result is not None
    except Exception as e:
        log_admin(f"Error checking if group {chat_id} is unlocked: {e}", level="error")
        return False


def unlock_group(chat_id, admin_id, group_title=None, group_username=None):
    """Unlock a group for bot functionality."""
    try:
        import time
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Check if already unlocked
            cursor.execute('''
                SELECT 1 FROM unlocked_groups WHERE chat_id = ?
            ''', (chat_id,))

            if cursor.fetchone():
                conn.close()
                return False, "Группа уже разблокирована."

            # Insert new unlock record
            cursor.execute('''
                INSERT INTO unlocked_groups
                (chat_id, unlocked_by, unlocked_at, group_title, group_username)
                VALUES (?, ?, ?, ?, ?)
            ''', (chat_id, admin_id, int(time.time()), group_title, group_username))

            conn.commit()
            conn.close()

            log_admin(f"Admin {admin_id} unlocked group {chat_id} ({group_title})")
            return True, "✅ Группа разблокирована! Бот теперь работает в этом чате."

    except Exception as e:
        log_admin(f"Error unlocking group {chat_id}: {e}", level="error")
        return False, "❌ Ошибка при разблокировке группы."


def lock_group(chat_id, admin_id):
    """Lock a group (remove from unlocked groups)."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM unlocked_groups WHERE chat_id = ?
            ''', (chat_id,))

            if cursor.rowcount == 0:
                conn.close()
                return False, "Группа уже заблокирована."

            conn.commit()
            conn.close()

            log_admin(f"Admin {admin_id} locked group {chat_id}")
            return True, "✅ Группа заблокирована. Бот больше не работает в этом чате."

    except Exception as e:
        log_admin(f"Error locking group {chat_id}: {e}", level="error")
        return False, "❌ Ошибка при блокировке группы."


def get_group_status(chat_id):
    """Get the status of a group (unlocked/locked)."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT unlocked_by, unlocked_at, group_title, group_username
                FROM unlocked_groups WHERE chat_id = ?
            ''', (chat_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                unlocked_by, unlocked_at, group_title, group_username = result
                import datetime
                unlock_date = datetime.datetime.fromtimestamp(unlocked_at).strftime('%Y-%m-%d %H:%M')
                return {
                    'unlocked': True,
                    'unlocked_by': unlocked_by,
                    'unlocked_at': unlock_date,
                    'group_title': group_title,
                    'group_username': group_username
                }
            else:
                return {'unlocked': False}

    except Exception as e:
        log_admin(f"Error getting group status for {chat_id}: {e}", level="error")
        return {'unlocked': False}


def save_message(chat_id, user_id, username, first_name, last_name, message_text,
                message_id, timestamp, message_type='text', is_forwarded=False,
                forward_from_username=None, reply_to_message_id=None,
                reply_to_user_id=None, reply_to_username=None):
    """Save a message to the database."""
    try:
        # Convert timestamp to readable date and time
        from datetime import datetime
        dt = datetime.fromtimestamp(timestamp)

        # Format date (YYYY-MM-DD)
        message_date = dt.strftime('%Y-%m-%d')

        # Format time (HH:MM)
        message_time = dt.strftime('%H:%M')

        # Determine time period
        hour = dt.hour
        if 6 <= hour < 12:
            time_period = "утром"
        elif 12 <= hour < 18:
            time_period = "днем"
        elif 18 <= hour < 23:
            time_period = "вечером"
        else:
            time_period = "ночью"

        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO chat_messages
                (chat_id, user_id, username, first_name, last_name, message_text,
                 message_id, timestamp, message_type, is_forwarded,
                 forward_from_username, reply_to_message_id, reply_to_user_id, reply_to_username,
                 message_date, message_time, time_period)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                chat_id, user_id, username, first_name, last_name, message_text,
                message_id, timestamp, message_type, is_forwarded,
                forward_from_username, reply_to_message_id, reply_to_user_id, reply_to_username,
                message_date, message_time, time_period
            ))

            conn.commit()
            conn.close()

        return True

    except Exception as e:
        log_admin(f"Error saving message to database: {e}", level="error")
        return False


def get_chat_messages(chat_id, hours=24, min_length=10):
    """Get messages from a chat for the specified number of hours."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Calculate cutoff timestamp
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_timestamp = int(cutoff_time.timestamp())
            
            # Получаем сообщения с временной информацией и информацией о репликах
            cursor.execute('''
                SELECT first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username,
                       message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username
                FROM chat_messages
                WHERE chat_id = ?
                  AND timestamp >= ?
                  AND message_type = 'text'
                  AND message_text IS NOT NULL
                  AND LENGTH(TRIM(message_text)) >= ?
                ORDER BY timestamp ASC
            ''', (chat_id, cutoff_timestamp, min_length))

            rows = cursor.fetchall()
            conn.close()

            messages = []
            for row in rows:
                first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username, message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username = row

                # Create display name
                if is_forwarded and forward_from_username:
                    display_name = forward_from_username
                elif first_name:
                    display_name = first_name
                    if last_name:
                        display_name += f" {last_name}"
                else:
                    display_name = "Пользователь"

                messages.append({
                    'username': display_name,
                    'text': message_text.strip(),
                    'timestamp': timestamp,
                    'message_date': message_date or '',
                    'message_time': message_time or '',
                    'time_period': time_period or '',
                    'reply_to_message_id': reply_to_message_id,
                    'reply_to_user_id': reply_to_user_id,
                    'reply_to_username': reply_to_username
                })
            
            log_admin(f"Retrieved {len(messages)} messages from chat {chat_id} for last {hours} hours")
            return messages
            
    except Exception as e:
        log_admin(f"Error retrieving messages from database: {e}", level="error")
        return []


def get_chat_stats(chat_id, hours=24):
    """Get statistics about messages in a chat."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_timestamp = int(cutoff_time.timestamp())
            
            # Total messages
            cursor.execute('''
                SELECT COUNT(*) FROM chat_messages 
                WHERE chat_id = ? AND timestamp >= ?
            ''', (chat_id, cutoff_timestamp))
            total_messages = cursor.fetchone()[0]
            
            # Text messages
            cursor.execute('''
                SELECT COUNT(*) FROM chat_messages 
                WHERE chat_id = ? AND timestamp >= ? AND message_type = 'text'
                AND message_text IS NOT NULL AND LENGTH(TRIM(message_text)) >= 10
            ''', (chat_id, cutoff_timestamp))
            text_messages = cursor.fetchone()[0]
            
            # Unique users
            cursor.execute('''
                SELECT COUNT(DISTINCT user_id) FROM chat_messages 
                WHERE chat_id = ? AND timestamp >= ?
            ''', (chat_id, cutoff_timestamp))
            unique_users = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_messages': total_messages,
                'text_messages': text_messages,
                'unique_users': unique_users
            }
            
    except Exception as e:
        log_admin(f"Error getting chat stats: {e}", level="error")
        return {'total_messages': 0, 'text_messages': 0, 'unique_users': 0}


def cleanup_old_messages(days=30):
    """Clean up messages older than specified days."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cutoff_time = datetime.now() - timedelta(days=days)
            cutoff_timestamp = int(cutoff_time.timestamp())

            cursor.execute('''
                DELETE FROM chat_messages WHERE timestamp < ?
            ''', (cutoff_timestamp,))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            log_admin(f"Cleaned up {deleted_count} old messages (older than {days} days)")
            return deleted_count

    except Exception as e:
        log_admin(f"Error cleaning up old messages: {e}", level="error")
        return 0


def get_last_podcast_time(chat_id):
    """Get the timestamp of the last podcast for a chat."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT last_podcast_time FROM podcast_timestamps
                WHERE chat_id = ?
            ''', (chat_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0]
            else:
                return None

    except Exception as e:
        log_admin(f"Error getting last podcast time for chat {chat_id}: {e}", level="error")
        return None


def get_last_podcast_message_id(chat_id):
    """Get the message ID of the last podcast for a chat."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT last_podcast_message_id FROM podcast_timestamps
                WHERE chat_id = ?
            ''', (chat_id,))

            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return None

    except Exception as e:
        log_admin(f"Error getting last podcast message ID for chat {chat_id}: {e}", level="error")
        return None


def set_last_podcast_time(chat_id, timestamp, message_id=None):
    """Set the timestamp and message ID of the last podcast for a chat."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Get current Moscow time timestamp
            import pytz
            moscow_tz = pytz.timezone('Europe/Moscow')
            current_time = int(datetime.now(moscow_tz).timestamp())

            cursor.execute('''
                INSERT OR REPLACE INTO podcast_timestamps
                (chat_id, last_podcast_time, updated_at, last_podcast_message_id)
                VALUES (?, ?, ?, ?)
            ''', (chat_id, timestamp, current_time, message_id))

            conn.commit()
            conn.close()

            log_admin(f"Set last podcast time for chat {chat_id} to {timestamp}, message_id: {message_id}")
            return True

    except Exception as e:
        log_admin(f"Error setting last podcast time for chat {chat_id}: {e}", level="error")
        return False


def get_chat_messages_since_timestamp(chat_id, since_timestamp, min_length=10):
    """Get messages from a chat since a specific timestamp."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Получаем сообщения с временной информацией и информацией о репликах
            cursor.execute('''
                SELECT first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username,
                       message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username
                FROM chat_messages
                WHERE chat_id = ?
                  AND timestamp > ?
                  AND message_type = 'text'
                  AND message_text IS NOT NULL
                  AND LENGTH(TRIM(message_text)) >= ?
                ORDER BY timestamp ASC
            ''', (chat_id, since_timestamp, min_length))

            rows = cursor.fetchall()
            conn.close()

            messages = []
            for row in rows:
                first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username, message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username = row

                # Create display name
                if is_forwarded and forward_from_username:
                    display_name = forward_from_username
                elif first_name:
                    display_name = first_name
                    if last_name:
                        display_name += f" {last_name}"
                else:
                    display_name = "Пользователь"

                messages.append({
                    'username': display_name,
                    'text': message_text.strip(),
                    'timestamp': timestamp,
                    'message_date': message_date or '',
                    'message_time': message_time or '',
                    'time_period': time_period or '',
                    'reply_to_message_id': reply_to_message_id,
                    'reply_to_user_id': reply_to_user_id,
                    'reply_to_username': reply_to_username
                })

            log_admin(f"Retrieved {len(messages)} messages from chat {chat_id} since timestamp {since_timestamp}")
            return messages

    except Exception as e:
        log_admin(f"Error retrieving messages since timestamp from database: {e}", level="error")
        return []


def get_recent_messages_for_chat(chat_id, hours=24, limit=50, min_length=10):
    """Get recent messages from a chat."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Calculate cutoff timestamp
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_timestamp = int(cutoff_time.timestamp())

            # Получаем сообщения с временной информацией и информацией о репликах
            cursor.execute('''
                SELECT first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username,
                       message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username
                FROM chat_messages
                WHERE chat_id = ?
                  AND timestamp >= ?
                  AND message_type = 'text'
                  AND message_text IS NOT NULL
                  AND LENGTH(TRIM(message_text)) >= ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (chat_id, cutoff_timestamp, min_length, limit))

            rows = cursor.fetchall()
            conn.close()

            messages = []
            for row in rows:
                first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username, message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username = row

                # Create display name
                if is_forwarded and forward_from_username:
                    display_name = forward_from_username
                elif first_name:
                    display_name = first_name
                    if last_name:
                        display_name += f" {last_name}"
                else:
                    display_name = "Пользователь"

                messages.append({
                    'user_name': display_name,
                    'message_text': message_text.strip(),
                    'timestamp': timestamp,
                    'message_date': message_date or '',
                    'message_time': message_time or '',
                    'time_period': time_period or '',
                    'reply_to_message_id': reply_to_message_id,
                    'reply_to_user_id': reply_to_user_id,
                    'reply_to_username': reply_to_username
                })

            # Reverse to get chronological order (oldest first)
            messages.reverse()

            log_admin(f"Retrieved {len(messages)} recent messages from chat {chat_id} for last {hours} hours (limit: {limit})")
            return messages

    except Exception as e:
        log_admin(f"Error retrieving recent messages from database: {e}", level="error")
        return []


def get_bot_dialogue_messages(chat_id, hours=2, limit=30, min_length=5):
    """
    Get messages that are part of bot-user dialogue from a chat.
    This includes:
    - Messages that are replies to other messages (likely part of conversation)
    - Messages with commands or mentions
    - Short conversational responses
    """
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Calculate cutoff timestamp
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_timestamp = int(cutoff_time.timestamp())

            # Get messages that are likely part of bot dialogue
            cursor.execute('''
                SELECT first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username,
                       message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username
                FROM chat_messages
                WHERE chat_id = ?
                  AND timestamp >= ?
                  AND message_type = 'text'
                  AND message_text IS NOT NULL
                  AND LENGTH(TRIM(message_text)) >= ?
                  AND (
                    reply_to_message_id IS NOT NULL  -- Messages that are replies
                    OR message_text LIKE '/%'        -- Commands
                    OR message_text LIKE '%@%'       -- Mentions
                    OR LENGTH(TRIM(message_text)) < 100  -- Short messages (likely responses)
                  )
                ORDER BY timestamp ASC
                LIMIT ?
            ''', (chat_id, cutoff_timestamp, min_length, limit))

            rows = cursor.fetchall()
            conn.close()

            messages = []
            for row in rows:
                first_name, last_name, message_text, timestamp, is_forwarded, forward_from_username, message_date, message_time, time_period, reply_to_message_id, reply_to_user_id, reply_to_username = row

                # Create display name
                if is_forwarded and forward_from_username:
                    display_name = forward_from_username
                elif first_name:
                    display_name = first_name
                    if last_name:
                        display_name += f" {last_name}"
                else:
                    display_name = "Пользователь"

                messages.append({
                    'user_name': display_name,
                    'message_text': message_text.strip(),
                    'timestamp': timestamp,
                    'message_date': message_date or '',
                    'message_time': message_time or '',
                    'time_period': time_period or '',
                    'reply_to_message_id': reply_to_message_id,
                    'reply_to_user_id': reply_to_user_id,
                    'reply_to_username': reply_to_username
                })

            log_admin(f"Retrieved {len(messages)} bot-dialogue messages from chat {chat_id} for last {hours} hours")
            return messages

    except Exception as e:
        log_admin(f"Error retrieving bot-dialogue messages from chat {chat_id}: {e}", level="error")
        return []


def get_stats():
    """Get general bot statistics for admin panel."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Get total number of messages
            cursor.execute('SELECT COUNT(*) FROM chat_messages')
            total_messages = cursor.fetchone()[0]

            # Get total number of unique users
            cursor.execute('SELECT COUNT(DISTINCT user_id) FROM chat_messages')
            unique_users = cursor.fetchone()[0]

            # Get total number of unique chats
            cursor.execute('SELECT COUNT(DISTINCT chat_id) FROM chat_messages')
            unique_chats = cursor.fetchone()[0]

            # Get messages from last 24 hours
            cutoff_time = datetime.now() - timedelta(hours=24)
            cutoff_timestamp = int(cutoff_time.timestamp())

            cursor.execute('''
                SELECT COUNT(*) FROM chat_messages
                WHERE timestamp >= ?
            ''', (cutoff_timestamp,))
            messages_24h = cursor.fetchone()[0]

            # Get active users in last 24 hours
            cursor.execute('''
                SELECT COUNT(DISTINCT user_id) FROM chat_messages
                WHERE timestamp >= ?
            ''', (cutoff_timestamp,))
            active_users_24h = cursor.fetchone()[0]

            # Get unlocked groups count
            cursor.execute('SELECT COUNT(*) FROM unlocked_groups')
            unlocked_groups = cursor.fetchone()[0]

            conn.close()

            # Format statistics text
            stats_text = f"""📊 <b>СТАТИСТИКА БОТА</b>

📈 <b>Общая статистика:</b>
• Всего сообщений: <code>{total_messages:,}</code>
• Уникальных пользователей: <code>{unique_users:,}</code>
• Уникальных чатов: <code>{unique_chats:,}</code>
• Разблокированных групп: <code>{unlocked_groups:,}</code>

⏰ <b>За последние 24 часа:</b>
• Сообщений: <code>{messages_24h:,}</code>
• Активных пользователей: <code>{active_users_24h:,}</code>

🕐 <i>Обновлено: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>"""

            return stats_text

    except Exception as e:
        log_admin(f"Error getting bot stats: {e}", level="error")
        return f"""📊 <b>СТАТИСТИКА БОТА</b>

❌ <b>Ошибка получения статистики</b>

Произошла ошибка при получении данных из базы данных.
Проверьте логи для получения подробной информации.

🕐 <i>Время ошибки: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>"""


def is_group_podcast_blocked(chat_id):
    """Check if podcasts are blocked for non-admins in a group."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT 1 FROM blocked_podcasts_groups WHERE chat_id = ?
            ''', (chat_id,))

            result = cursor.fetchone()
            conn.close()

            return result is not None
    except Exception as e:
        log_admin(f"Error checking if group {chat_id} has podcast blocking: {e}", level="error")
        return False


def block_group_podcasts(chat_id, admin_id, group_title=None, group_username=None):
    """Block podcasts for non-admins in a group."""
    try:
        import time
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Check if already blocked
            cursor.execute('''
                SELECT 1 FROM blocked_podcasts_groups WHERE chat_id = ?
            ''', (chat_id,))

            if cursor.fetchone():
                conn.close()
                return False, "Подкасты уже заблокированы для обычных пользователей в этой группе."

            # Insert new block record
            cursor.execute('''
                INSERT INTO blocked_podcasts_groups
                (chat_id, blocked_by, blocked_at, group_title, group_username)
                VALUES (?, ?, ?, ?, ?)
            ''', (chat_id, admin_id, int(time.time()), group_title, group_username))

            conn.commit()
            conn.close()

            log_admin(f"Admin {admin_id} blocked podcasts for non-admins in group {chat_id} ({group_title})")
            return True, "✅ Подкасты заблокированы для обычных пользователей. Только администраторы могут создавать подкасты в этой группе."

    except Exception as e:
        log_admin(f"Error blocking podcasts in group {chat_id}: {e}", level="error")
        return False, "❌ Ошибка при блокировке подкастов."


def unblock_group_podcasts(chat_id, admin_id):
    """Unblock podcasts for non-admins in a group."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM blocked_podcasts_groups WHERE chat_id = ?
            ''', (chat_id,))

            if cursor.rowcount == 0:
                conn.close()
                return False, "Подкасты не заблокированы в этой группе."

            conn.commit()
            conn.close()

            log_admin(f"Admin {admin_id} unblocked podcasts for non-admins in group {chat_id}")
            return True, "✅ Блокировка подкастов снята. Все пользователи могут создавать подкасты в этой группе."

    except Exception as e:
        log_admin(f"Error unblocking podcasts in group {chat_id}: {e}", level="error")
        return False, "❌ Ошибка при снятии блокировки подкастов."


def get_group_podcast_status(chat_id):
    """Get the podcast blocking status of a group."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT blocked_by, blocked_at, group_title, group_username
                FROM blocked_podcasts_groups WHERE chat_id = ?
            ''', (chat_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                blocked_by, blocked_at, group_title, group_username = result
                import datetime
                block_date = datetime.datetime.fromtimestamp(blocked_at).strftime('%Y-%m-%d %H:%M')
                return {
                    'blocked': True,
                    'blocked_by': blocked_by,
                    'blocked_at': block_date,
                    'group_title': group_title,
                    'group_username': group_username
                }
            else:
                return {'blocked': False}

    except Exception as e:
        log_admin(f"Error getting group {chat_id} podcast status: {e}", level="error")
        return {'blocked': False}


def get_all_unlocked_groups():
    """Get all unlocked groups from the database."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT chat_id, group_title, group_username, unlocked_by, unlocked_at
                FROM unlocked_groups
                ORDER BY unlocked_at DESC
            ''')

            results = cursor.fetchall()
            conn.close()

            groups = []
            for result in results:
                chat_id, group_title, group_username, unlocked_by, unlocked_at = result
                groups.append({
                    'chat_id': chat_id,
                    'group_title': group_title,
                    'group_username': group_username,
                    'unlocked_by': unlocked_by,
                    'unlocked_at': unlocked_at
                })

            return groups

    except Exception as e:
        log_admin(f"Error getting all unlocked groups: {e}", level="error")
        return []





# --- API Statistics Functions ---

def log_api_request(user_id, model_name, request_type, api_key_identifier=None, request_time=None):
    """Log API request for statistics."""
    try:
        import time
        if request_time is None:
            request_time = int(time.time())
            
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO api_requests (user_id, model_name, request_type, timestamp, api_key_identifier)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, model_name, request_type, request_time, api_key_identifier))
            
            conn.commit()
            conn.close()
            
            return True
            
    except Exception as e:
        log_admin(f"Error logging API request: {e}", level="error")
        return False


def get_api_statistics(hours=24):
    """Get detailed API statistics for the specified hours."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Calculate cutoff timestamp
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cutoff_timestamp = int(cutoff_time.timestamp())
            
            # Total requests
            cursor.execute('''
                SELECT COUNT(*) FROM api_requests 
                WHERE timestamp >= ?
            ''', (cutoff_timestamp,))
            total_requests = cursor.fetchone()[0]
            
            # Requests by model
            cursor.execute('''
                SELECT model_name, COUNT(*) as count
                FROM api_requests 
                WHERE timestamp >= ?
                GROUP BY model_name
                ORDER BY count DESC
            ''', (cutoff_timestamp,))
            model_stats = cursor.fetchall()
            
            # Requests by type
            cursor.execute('''
                SELECT request_type, COUNT(*) as count
                FROM api_requests 
                WHERE timestamp >= ?
                GROUP BY request_type
                ORDER BY count DESC
            ''', (cutoff_timestamp,))
            type_stats = cursor.fetchall()
            
            # Top users
            cursor.execute('''
                SELECT user_id, COUNT(*) as count
                FROM api_requests 
                WHERE timestamp >= ?
                GROUP BY user_id
                ORDER BY count DESC
                LIMIT 10
            ''', (cutoff_timestamp,))
            user_stats = cursor.fetchall()
            
            # Unique users
            cursor.execute('''
                SELECT COUNT(DISTINCT user_id) FROM api_requests 
                WHERE timestamp >= ?
            ''', (cutoff_timestamp,))
            unique_users = cursor.fetchone()[0]
            
            # Recent requests (last 10)
            cursor.execute('''
                SELECT user_id, model_name, request_type, timestamp
                FROM api_requests 
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
                LIMIT 10
            ''', (cutoff_timestamp,))
            recent_requests = cursor.fetchall()
            
            conn.close()
            
            return {
                'total_requests': total_requests,
                'unique_users': unique_users,
                'model_stats': model_stats,
                'type_stats': type_stats,
                'user_stats': user_stats,
                'recent_requests': recent_requests,
                'hours': hours
            }
            
    except Exception as e:
        log_admin(f"Error getting API statistics: {e}", level="error")
        return None




def get_extended_stats(hours=24):
    """Get extended bot statistics for the admin panel."""
    try:
        with db_lock:
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # --- General Stats ---
            cursor.execute('SELECT COUNT(*) FROM chat_messages')
            total_messages = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(DISTINCT user_id) FROM chat_messages')
            unique_users = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(DISTINCT chat_id) FROM chat_messages')
            unique_chats = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM unlocked_groups')
            unlocked_groups = cursor.fetchone()[0]

            # --- Time-based Stats ---
            cutoff_timestamp = int((datetime.now() - timedelta(hours=hours)).timestamp())
            cursor.execute('SELECT COUNT(*) FROM chat_messages WHERE timestamp >= ?', (cutoff_timestamp,))
            messages_period = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(DISTINCT user_id) FROM chat_messages WHERE timestamp >= ?', (cutoff_timestamp,))
            active_users_period = cursor.fetchone()[0]

            # --- API Stats ---
            cursor.execute('SELECT COUNT(*) FROM api_requests WHERE timestamp >= ?', (cutoff_timestamp,))
            api_requests_period = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(DISTINCT user_id) FROM api_requests WHERE timestamp >= ? AND user_id IS NOT NULL', (cutoff_timestamp,))
            api_users_period = cursor.fetchone()[0]
            cursor.execute('SELECT model_name, COUNT(*) FROM api_requests WHERE timestamp >= ? GROUP BY model_name ORDER BY COUNT(*) DESC', (cutoff_timestamp,))
            top_models = cursor.fetchall()
            cursor.execute('SELECT request_type, COUNT(*) FROM api_requests WHERE timestamp >= ? GROUP BY request_type ORDER BY COUNT(*) DESC', (cutoff_timestamp,))
            top_request_types = cursor.fetchall()

            # --- Key Stats (New) ---
            cursor.execute('SELECT api_key_identifier, COUNT(*) FROM api_requests WHERE timestamp >= ? AND api_key_identifier IS NOT NULL GROUP BY api_key_identifier ORDER BY COUNT(*) DESC', (cutoff_timestamp,))
            key_usage = cursor.fetchall()

            

            conn.close()

        # --- User & System Info (from admin_system and bot_globals) ---
        from admin_system import bot_data, is_veo_enabled
        from bot_globals import bot_start_time
        
        admins = bot_data.get('admin_data', {}).get('admins', [])
        blocked_users = bot_data.get('blocked_users', [])
        pro_users = bot_data.get('pro_users', {})
        
        db_size_bytes = os.path.getsize(DB_PATH)
        db_size_mb = round(db_size_bytes / (1024 * 1024), 2)

        uptime_seconds = int(datetime.now().timestamp() - bot_start_time)
        uptime_str = str(timedelta(seconds=uptime_seconds))


        # --- Formatting the Output ---
        stats_text = f"""
📊 <b>МЕГА-ПОДРОБНАЯ СТАТИСТИКА</b>
<i>Обновлено: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>

---
<b>📈 ОБЩАЯ СТАТИСТИКА</b>
• Всего сообщений: <code>{total_messages:,}</code>
• Уникальных пользователей: <code>{unique_users:,}</code>
• Уникальных чатов: <code>{unique_chats:,}</code>
• Разблокированных групп: <code>{unlocked_groups:,}</code>

---
<b>⏰ ЗА ПОСЛЕДНИЕ {hours} ЧАСОВ</b>
• Новых сообщений: <code>{messages_period:,}</code>
• Активных пользователей: <code>{active_users_period:,}</code>

---
<b>🤖 ИСПОЛЬЗОВАНИЕ API (за {hours}ч)</b>
• Всего запросов к API: <code>{api_requests_period:,}</code>
• Уникальных пользователей API: <code>{api_users_period:,}</code>
• <b>Топ Моделей:</b>
"""
        for model, count in top_models:
            stats_text += f"  - <code>{model}</code>: {count} раз\n"
        stats_text += "• <b>Топ Типов Запросов:</b>\n"
        for req_type, count in top_request_types:
            stats_text += f"  - <code>{req_type}</code>: {count} раз\n"

        if key_usage:
            stats_text += "• <b>Использование Ключей:</b>\n"
            for key_id, count in key_usage:
                stats_text += f"  - <code>...{key_id}</code>: {count} раз\n"

        stats_text += f"""
---
<b>👥 ПОЛЬЗОВАТЕЛИ</b>
• Администраторов: <code>{len(admins)}</code>
• Заблокировано: <code>{len(blocked_users)}</code>
• PRO пользователей: <code>{len(pro_users)}</code>

---
<b>⚙️ СИСТЕМА</b>
• Статус VEO: <code>{'Включена' if is_veo_enabled() else 'Выключена'}</code>
• Размер БД: <code>{db_size_mb} MB</code>
• Аптайм бота: <code>{uptime_str}</code>
"""
        return stats_text

    except Exception as e:
        log_admin(f"Error getting extended bot stats: {e}", level="error")
        return f"""📊 <b>СТАТИСТИКА БОТА</b>

❌ <b>Ошибка получения расширенной статистики</b>
Произошла ошибка: {e}
Проверьте логи для получения подробной информации.

🕐 <i>Время ошибки: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</i>"""

# HTML Sites Functions removed as requested


# Initialize database on import
init_database()
