# -*- coding: utf-8 -*-
"""
Модуль для управления контекстом личных чатов с поддержкой кэширования Gemini 2.5 Flash.
Обеспечивает хранение истории сообщений, автоматическое управление размером контекста
и интеграцию с Context7 для получения актуальной документации.
"""

import threading
import time
import json
import os
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from bot_globals import log_admin
import config

# Константы для управления контекстом
MAX_CONTEXT_TOKENS = 800000  # Максимум токенов в контексте (оставляем запас от 1M)
MAX_MESSAGES_PER_USER = None  # Убираем ограничение на количество сообщений, оставляем только ограничение по токенам
CONTEXT_CLEANUP_INTERVAL = 3600  # Очистка каждый час (секунды)
CONTEXT_SAVE_INTERVAL = 300  # Сохранение каждые 5 минут
CONTEXT_FILE_PATH = "private_chat_contexts.json"

# Глобальные переменные
private_contexts: Dict[int, deque] = defaultdict(lambda: deque())  # Убираем maxlen, контролируем размер только по токенам
context_metadata: Dict[int, Dict] = defaultdict(lambda: {
    "created_at": time.time(),
    "last_updated": time.time(),
    "message_count": 0,
    "estimated_tokens": 0,
    "cache_id": None,
    "cache_expires": None
})
context_lock = threading.Lock()

# Фоновые задачи
cleanup_thread = None
save_thread = None
shutdown_flag = threading.Event()


def estimate_tokens(text: str) -> int:
    """
    Приблизительная оценка количества токенов в тексте.
    Gemini использует примерно 4 символа на токен.
    """
    return max(1, len(text) // 4)


def get_context_size(user_id: int) -> Tuple[int, int]:
    """
    Возвращает размер контекста пользователя (количество сообщений, токенов).
    """
    with context_lock:
        messages = private_contexts[user_id]
        metadata = context_metadata[user_id]
        return len(messages), metadata.get("estimated_tokens", 0)


def add_message_to_context(user_id: int, role: str, content: str, parts: List[Dict] = None):
    """
    Добавляет сообщение в контекст пользователя.
    
    Args:
        user_id: ID пользователя
        role: Роль ("user" или "model")
        content: Текстовое содержимое сообщения
        parts: Список частей сообщения в формате Gemini API
    """
    if not content and not parts:
        return
    
    # Создаем части сообщения
    if not parts:
        parts = [{"text": content}]
    
    message = {
        "role": role,
        "parts": parts,
        "timestamp": time.time(),
        "tokens": sum(estimate_tokens(part.get("text", "")) for part in parts if "text" in part)
    }
    
    with context_lock:
        context = private_contexts[user_id]
        metadata = context_metadata[user_id]
        
        # Добавляем сообщение
        context.append(message)
        
        # Обновляем метаданные
        metadata["last_updated"] = time.time()
        metadata["message_count"] = len(context)
        metadata["estimated_tokens"] = sum(msg.get("tokens", 0) for msg in context)
        
        # Проверяем лимиты и очищаем при необходимости
        _cleanup_context_if_needed(user_id)
        
        log_admin(f"Added {role} message to context for user {user_id}. "
                 f"Context size: {len(context)} messages, ~{metadata['estimated_tokens']} tokens")


def get_context_for_api(user_id: int, max_tokens: int = MAX_CONTEXT_TOKENS) -> List[Dict]:
    """
    Получает контекст пользователя в формате для Gemini API.
    
    Args:
        user_id: ID пользователя
        max_tokens: Максимальное количество токенов в контексте
        
    Returns:
        Список сообщений в формате Gemini API
    """
    with context_lock:
        context = private_contexts[user_id]
        if not context:
            return []
        
        # Конвертируем в формат API, исключая служебные поля
        api_context = []
        total_tokens = 0
        
        # Идем с конца, чтобы включить самые свежие сообщения
        for message in reversed(context):
            msg_tokens = message.get("tokens", 0)
            if total_tokens + msg_tokens > max_tokens and api_context:
                break
                
            api_message = {
                "role": message["role"],
                "parts": message["parts"]
            }
            api_context.insert(0, api_message)  # Вставляем в начало для правильного порядка
            total_tokens += msg_tokens
        
        log_admin(f"Retrieved context for user {user_id}: {len(api_context)} messages, ~{total_tokens} tokens")
        return api_context


def clear_context(user_id: int) -> bool:
    """
    Очищает контекст пользователя.
    
    Returns:
        True если контекст был очищен, False если он уже был пуст
    """
    with context_lock:
        context = private_contexts[user_id]
        metadata = context_metadata[user_id]
        
        if not context:
            return False
        
        context.clear()
        metadata.update({
            "last_updated": time.time(),
            "message_count": 0,
            "estimated_tokens": 0,
            "cache_id": None,
            "cache_expires": None
        })
        
        log_admin(f"Cleared context for user {user_id}")
        return True


def get_context_status(user_id: int) -> Dict[str, Any]:
    """
    Возвращает статус контекста пользователя.
    """
    with context_lock:
        context = private_contexts[user_id]
        metadata = context_metadata[user_id]
        
        if not context:
            return {
                "has_context": False,
                "message_count": 0,
                "estimated_tokens": 0,
                "created_at": None,
                "last_updated": None
            }
        
        return {
            "has_context": True,
            "message_count": len(context),
            "estimated_tokens": metadata.get("estimated_tokens", 0),
            "created_at": datetime.fromtimestamp(metadata.get("created_at", 0)),
            "last_updated": datetime.fromtimestamp(metadata.get("last_updated", 0)),
            "cache_active": metadata.get("cache_id") is not None
        }


def _cleanup_context_if_needed(user_id: int):
    """
    Внутренняя функция для очистки контекста при превышении лимитов.
    Должна вызываться под context_lock.
    """
    context = private_contexts[user_id]
    metadata = context_metadata[user_id]
    
    # Если превышен лимит токенов, удаляем старые сообщения
    while metadata.get("estimated_tokens", 0) > MAX_CONTEXT_TOKENS and len(context) > 1:
        removed_msg = context.popleft()
        metadata["estimated_tokens"] -= removed_msg.get("tokens", 0)
        metadata["message_count"] = len(context)
        
    log_admin(f"Context cleanup for user {user_id}: {len(context)} messages, "
             f"~{metadata['estimated_tokens']} tokens remaining")


def _cleanup_old_contexts():
    """
    Фоновая задача для очистки старых неактивных контекстов.
    """
    while not shutdown_flag.is_set():
        try:
            current_time = time.time()
            cutoff_time = current_time - (24 * 3600)  # 24 часа
            
            with context_lock:
                users_to_remove = []
                for user_id, metadata in context_metadata.items():
                    if metadata.get("last_updated", 0) < cutoff_time:
                        users_to_remove.append(user_id)
                
                for user_id in users_to_remove:
                    if user_id in private_contexts:
                        del private_contexts[user_id]
                    if user_id in context_metadata:
                        del context_metadata[user_id]
                    log_admin(f"Removed inactive context for user {user_id}")
            
            if users_to_remove:
                log_admin(f"Cleaned up {len(users_to_remove)} inactive contexts")
                
        except Exception as e:
            log_admin(f"Error in context cleanup: {e}", level="error")
        
        # Ждем до следующей очистки
        shutdown_flag.wait(CONTEXT_CLEANUP_INTERVAL)


def _save_contexts_periodically():
    """
    Фоновая задача для периодического сохранения контекстов.
    """
    while not shutdown_flag.is_set():
        try:
            save_contexts_to_file()
        except Exception as e:
            log_admin(f"Error saving contexts: {e}", level="error")
        
        # Ждем до следующего сохранения
        shutdown_flag.wait(CONTEXT_SAVE_INTERVAL)


def save_contexts_to_file():
    """
    Сохраняет контексты в файл.
    """
    try:
        with context_lock:
            data = {
                "contexts": {},
                "metadata": dict(context_metadata),
                "saved_at": time.time()
            }
            
            # Конвертируем deque в list для JSON сериализации
            for user_id, context in private_contexts.items():
                data["contexts"][str(user_id)] = list(context)
        
        with open(CONTEXT_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
            
        log_admin(f"Saved {len(data['contexts'])} user contexts to file")
        
    except Exception as e:
        log_admin(f"Error saving contexts to file: {e}", level="error")


def load_contexts_from_file():
    """
    Загружает контексты из файла.
    """
    if not os.path.exists(CONTEXT_FILE_PATH):
        log_admin("No context file found, starting with empty contexts")
        return
    
    try:
        with open(CONTEXT_FILE_PATH, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        with context_lock:
            # Загружаем контексты
            for user_id_str, context_list in data.get("contexts", {}).items():
                user_id = int(user_id_str)
                private_contexts[user_id] = deque(context_list)  # Убираем maxlen
            
            # Загружаем метаданные
            for user_id_str, metadata in data.get("metadata", {}).items():
                user_id = int(user_id_str)
                context_metadata[user_id].update(metadata)
        
        log_admin(f"Loaded {len(data.get('contexts', {}))} user contexts from file")
        
    except Exception as e:
        log_admin(f"Error loading contexts from file: {e}", level="error")


def initialize_context_system():
    """
    Инициализирует систему управления контекстом.
    """
    global cleanup_thread, save_thread
    
    log_admin("Initializing private chat context system...")
    
    # Загружаем существующие контексты
    load_contexts_from_file()
    
    # Запускаем фоновые задачи
    cleanup_thread = threading.Thread(target=_cleanup_old_contexts, daemon=True)
    cleanup_thread.start()
    
    save_thread = threading.Thread(target=_save_contexts_periodically, daemon=True)
    save_thread.start()
    
    log_admin("Private chat context system initialized")


def shutdown_context_system():
    """
    Корректно завершает работу системы контекста.
    """
    global shutdown_flag
    
    log_admin("Shutting down private chat context system...")
    
    # Сигнализируем о завершении
    shutdown_flag.set()
    
    # Сохраняем контексты
    save_contexts_to_file()
    
    # Ждем завершения потоков
    if cleanup_thread and cleanup_thread.is_alive():
        cleanup_thread.join(timeout=5)
    if save_thread and save_thread.is_alive():
        save_thread.join(timeout=5)
    
    log_admin("Private chat context system shut down")
