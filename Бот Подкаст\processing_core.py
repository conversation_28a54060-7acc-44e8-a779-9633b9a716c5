import threading
import time
import telebot
from utils import fix_telegram_markdown_v2
import random
import asyncio
import pytz

import traceback
import uuid
import subprocess
import tempfile
import io
import base64
import re
import html
import os
import shutil  # For rmtree
from concurrent.futures import as_completed
from thread_pool_manager import thread_pool_manager

# Import global state variables and locks

from bot_globals import (
    bot,
    user_conversations,
    user_model_override,
    chat_model_override,
    admin_logging_enabled,
    message_states,
    message_states_lock,
    media_groups,
    media_group_lock,
    audio_video_groups,
    audio_video_group_lock,
    user_forward_batch,
    user_forward_batch_lock,
    user_pending_file,
    user_pending_file_lock,
    user_last_response,
    user_last_response_lock,
    user_request_buffer,
    safe_bot_api_call,
    user_request_buffer_lock,
    user_conversations_lock,
    log_admin,
    send_one_time_notification,
    get_user_setting,
)
import bot_globals

# Import configuration constants
from config import (
    MODEL_LITE_CLASSIFY_TRANSCRIBE,
    STATUS_MESSAGES,
    SYSTEM_PROMPT_MAIN,
    SYSTEM_PROMPT_GROUP,

    SYSTEM_PROMPT_SUMMARIZE,
    SYSTEM_PROMPT_TRANSCRIBE,
    SYSTEM_PROMPT_FORMAT_TRANSCRIPT,
    SUMMARIZE_THRESHOLD,
    TELEGRAPH_THRESHOLD,
    TELEGRAM_MSG_LIMIT,
    TELEGRAPH_SUPPORTED,
    MEDIA_GROUP_DELAY,
    AUDIO_VIDEO_GROUP_DELAY,
    MAX_AUDIO_DURATION,
    FORWARD_BATCH_DELAY,
    MAX_FORWARD_BATCH_SIZE,
    PROCESS_BUFFER_DELAY,
    SUPPORTED_DOC_EXTENSIONS,
    PDF_SUPPORTED,
    MODEL_MID,
    SUMMARIZE_THRESHOLD_TEXT,
    SYSTEM_PROMPT_SUMMARIZE_TEXT_L1,
    SYSTEM_PROMPT_SUMMARIZE_TEXT_L2,
    SYSTEM_PROMPT_AUDIO_VIDEO_SUMMARY_GEMINI,
    MODEL_GEMINI_2_5_PRO,  # Added MODEL_GEMINI_2_5_PRO
    MODEL_GEMINI_2_5_FLASH,  # Added MODEL_GEMINI_2_5_FLASH for direct audio processing
    MODEL_GEMINI_2_5_FLASH_LITE,  # Added MODEL_GEMINI_2_5_FLASH_LITE for intelligent model selection
    DISABLE_STREAMING,  # Added DISABLE_STREAMING flag
    GENAI_REQUEST_TIMEOUT,  # Added for timeout handling
    GENAI_TTS_TIMEOUT,  # Added for TTS timeout handling
)

# Import API clients
from api_clients import (
    call_llm,
    call_gemini_api,

    call_gemma_3_1b_api,
    call_gemini_2_5_flash_api,
    # call_gpt_image_generate_api_sync, call_gpt_image_edit_api_sync
)

# Import thread pool manager for isolating blocking operations
from thread_pool_manager import submit_task

# --- Глобальная очередь подкастов ---
import queue
PODCAST_WORKERS = 8          # Уменьшено с 50 до 8 для предотвращения deadlock
_podcast_q = queue.Queue()
_podcast_workers_shutdown = threading.Event()

def _podcast_worker():
    """Воркер для обработки подкастов из очереди с timeout для предотвращения зависания"""
    import traceback
    worker_id = threading.current_thread().name
    log_admin(f"[PodcastWorker-{worker_id}] Started", level="info")
    
    while not _podcast_workers_shutdown.is_set():
        try:
            # Используем timeout для предотвращения бесконечного ожидания
            try:
                func, args, kwargs = _podcast_q.get(timeout=30)  # 30 секунд timeout
            except queue.Empty:
                # Периодически проверяем флаг shutdown
                continue
                
            try:
                func(*args, **kwargs)
            except Exception as e:
                log_admin(f"[PodcastWorker-{worker_id}] Uncaught exception: {e}\n{traceback.format_exc()}", level="error")
            finally:
                try:
                    _podcast_q.task_done()
                except Exception as e_task:
                    # Даже task_done() может кидать, логируем чтобы не потерять поток
                    log_admin(f"[PodcastWorker-{worker_id}] task_done() failed: {e_task}\n{traceback.format_exc()}", level="error")
        except Exception as e_outer:
            log_admin(f"[PodcastWorker-{worker_id}] Outer exception: {e_outer}\n{traceback.format_exc()}", level="error")
            # Продолжаем работу даже после ошибки
            time.sleep(1)
    
    log_admin(f"[PodcastWorker-{worker_id}] Shutdown", level="info")

# Запускаем воркеры для подкастов
_podcast_worker_threads = []
for i in range(PODCAST_WORKERS):
    thread = threading.Thread(target=_podcast_worker, daemon=True, name=f"PodcastWorker-{i}")
    thread.start()
    _podcast_worker_threads.append(thread)

log_admin(f"Initialized podcast queue with {PODCAST_WORKERS} workers (reduced from 50 for stability)")

def shutdown_podcast_workers():
    """Корректное завершение работы подкастных воркеров"""
    log_admin("Shutting down podcast workers...", level="info")
    _podcast_workers_shutdown.set()
    
    # Ждем завершения всех воркеров
    for thread in _podcast_worker_threads:
        thread.join(timeout=5)  # Максимум 5 секунд на воркер
    
    log_admin("Podcast workers shutdown complete", level="info")

# Import research functions from genai_client (for thematic podcast research)
from genai_client import generate_research_queries, perform_multiple_searches

# Import retry utilities
from retry_utils import is_censorship_error

# --- Функции генерации и редактирования изображений ОТКЛЮЧЕНЫ и УДАЛЕНЫ ---

# Import utilities
from utils import (
    escape_html,
    clean_response_text,
    convert_to_mp3,
    get_target_message_content,
    parse_gemini_audio_summary_output,
    send_long_message,
    send_multiple_messages_with_break,
    set_reaction,
    remove_reaction,
    TypingStatusManager,
    fix_telegram_html,
    clean_first_line_tags,
    remove_duplicate_dialogue_lines,
    safe_delete_message,
    parse_file_tags,
    send_parsed_files,
    fix_image_gen_format,

)

from api_clients import call_official_gemini_api  # Added for transcription
from bot_globals import bot  # Ensure bot is available

# Import Telegraph helpers
from telegraph_helpers import generate_telegraph_title, publish_to_telegraph

# Need telebot.types for InlineKeyboardMarkup
from telebot import types


# types is already imported from telebot


# --- Summarize Button Helper ---
def _prepare_summarize_button_if_needed(
    chat_id, text_content, message_id_for_key, bot_instance, user_id=None
):
    """
    ОТКЛЮЧЕНО: Функция больше не создает кнопку "Сократить".
    Всегда возвращает None.

    Ранее подготавливала InlineKeyboardMarkup с кнопкой "Сократить", если текст длинный.
    Также сохраняла состояние сообщения для обработчика callback'а.

    Args:
        user_id: ID пользователя для определения групповых чатов (если None, кнопка создается всегда)
    """
    # ЭТАП 3: Полностью отключаем кнопку "Сократить" для всех чатов
    log_admin(f"_prepare_summarize_button_if_needed: button creation disabled (chat_id: {chat_id})")
    return None


# --- Media Group Processing (Now uses GPT-4.1 with streaming) ---
def process_media_group(media_group_id):
    with media_group_lock:
        if (
            media_group_id not in media_groups
            or media_groups[media_group_id]["processed"]
        ):
            return
        group_data = media_groups[media_group_id]
        group_data["processed"] = True
        # Sort photos by message.date to maintain order if Telegram delivers them out of order
        photos_with_order = sorted(group_data["photos"], key=lambda x: x[0])
        caption = group_data["caption"]
        chat_id = group_data["chat_id"]
        user_id = group_data["user_id"]
        # thinking_message_id is no longer used here as we send a new "⚡" message
        num_photos = len(photos_with_order)
        all_photo_data = [p[1] for p in photos_with_order]  # Get just the data part
        # Determine the message ID to reply to (e.g., the first message in the group)
        original_message_id = (
            min(group_data["message_ids"]) if group_data["message_ids"] else None
        )

    user_info_log = f"user {user_id}"
    log_admin(
        f"{user_info_log} - processing media group {media_group_id} with {num_photos} photos. caption: '{caption[:50] if caption else None}...'. reply_to: {original_message_id}"
    )

    try:
        bot.send_chat_action(chat_id, "typing")
    except Exception as e_action:
        log_admin(
            f"{user_info_log} - Error sending 'typing' action in process_media_group: {e_action}",
            level="warning",
        )

    # Determine initial_status_message_id_for_ptpr based on new logic
    # It's either the ID of the user's message with '⚡' or a fallback bot message ID
    initial_status_message_id_for_ptpr = group_data.get(
        "message_id_with_lightning_reaction"
    )
    if not initial_status_message_id_for_ptpr:  # If reaction setting failed in handler
        initial_status_message_id_for_ptpr = group_data.get(
            "fallback_streaming_message_id"
        )
        if initial_status_message_id_for_ptpr:
            log_admin(
                f"{user_info_log} - Using fallback_streaming_message_id {initial_status_message_id_for_ptpr} for media group {media_group_id} as reaction might have failed."
            )
    # --- Actual API Call (using GPT-4.1 via call_llm) ---
    try:
        log_admin(
            f"{user_info_log} - mg {media_group_id}: generating single response for {num_photos} photos using GPT-4.1..."
        )

        # Call process_text_or_photo_request for media groups
        # It will handle its own "⏳" message now.
        # The original_message_id for reply should be the first message of the media group.
        # The caption is the user_text.
        # all_photo_data is the single_image_data (though it's a list here).
        process_text_or_photo_request(
            user_id,
            chat_id,
            caption,  # User text / caption
            all_photo_data,  # Image data (list of dicts)
            original_message_id=original_message_id,  # Reply to the first msg of the group
            is_file_query=False,
            file_context=None,
            reply_context=None,
            initial_status_message_id=initial_status_message_id_for_ptpr,  # Pass the "⚡" ID if it existed
        )
        # All further message handling, history, etc., is done within process_text_or_photo_request

    except Exception as e:
        log_admin(
            f"{user_info_log} - mg {media_group_id}: critical error in process_media_group wrapper: {e}\n{traceback.format_exc()}"
        )
        error_msg = "⚠️ Ошибка: внутренняя ошибка при обработке группы изображений."
        if initial_status_message_id_for_ptpr:  # If "⚡" was sent
            try:
                bot.delete_message(
                    chat_id=chat_id, message_id=initial_status_message_id_for_ptpr
                )
            except Exception:
                pass
        try:
            bot.send_message(
                chat_id,
                error_msg,
                parse_mode=None,
                reply_to_message_id=original_message_id,
            )
        except Exception:
            pass
    finally:
        with media_group_lock:
            if media_group_id in media_groups:
                del media_groups[media_group_id]
                log_admin(
                    f"{user_info_log} - cleaned up data for media group {media_group_id}"
                )


# --- Helper Functions for Audio Processing ---


def download_and_convert_file(user_id, index, file_id, message_id, file_type, temp_dir):
    """Downloads, saves, and converts a file to MP3 with memory optimization."""
    user_info_log = f"user {user_id}"
    original_file_path = None
    output_mp3_path = None

    # Ensure temp_dir exists (it should, as it's created by mkdtemp)
    output_mp3_path = os.path.join(temp_dir, f"{uuid.uuid4()}.mp3")
    log_admin(
        f"{user_info_log} - Starting download/convert for file index {index} (type: {file_type})"
    )

    try:
        # Get file info first to check size
        file_info = bot.get_file(file_id)

        # Check file size limit (50MB = 52,428,800 bytes)
        MAX_AUDIO_SIZE = 52428800  # 50MB in bytes
        if file_info.file_size and file_info.file_size > MAX_AUDIO_SIZE:
            size_mb = file_info.file_size / (1024 * 1024)
            error_msg = f"Audio file too large ({size_mb:.1f} MB). Maximum size: 50 MB."
            log_admin(f"{user_info_log} - {error_msg}")
            return None

        # Download file with memory optimization
        downloaded_file = bot.download_file(file_info.file_path)

        # Prepare file paths
        original_file_suffix = (
            os.path.splitext(file_info.file_path)[1] or f".{file_type}"
        )  # Basic suffix
        original_file_path = os.path.join(
            temp_dir, f"{uuid.uuid4()}_orig{original_file_suffix}"
        )

        # Write file to disk and immediately clear from memory
        with open(original_file_path, "wb") as f:
            f.write(downloaded_file)

        # Explicitly delete the downloaded_file from memory
        del downloaded_file
        import gc
        gc.collect()

        log_admin(
            f"{user_info_log} - Downloaded file {index+1} to '{original_file_path}' ({file_info.file_size} bytes)"
        )

        if convert_to_mp3(original_file_path, output_mp3_path):
            return output_mp3_path
        else:
            log_admin(f"{user_info_log} - Conversion failed for file {index+1}")
            return None
    except Exception as e:
        log_admin(
            f"{user_info_log} - Error during download/convert for file {index+1}: {e}"
        )
        return None
    finally:
        # Enhanced cleanup of temporary files
        if original_file_path and os.path.exists(original_file_path):
            try:
                os.remove(original_file_path)
                log_admin(f"{user_info_log} - Cleaned up original temp file: {original_file_path}", level="debug")
            except OSError as e_rem:
                log_admin(
                    f"{user_info_log} - WARNING: Failed to remove original temp file '{original_file_path}': {e_rem}",
                    level="warning",
                )

        # Clean up output file if conversion failed
        if output_mp3_path and os.path.exists(output_mp3_path):
            try:
                # Check if file is empty or very small (likely failed conversion)
                if os.path.getsize(output_mp3_path) < 1024:  # Less than 1KB
                    os.remove(output_mp3_path)
                    log_admin(f"{user_info_log} - Cleaned up failed conversion file: {output_mp3_path}", level="debug")
            except OSError:
                pass  # Ignore cleanup errors for output file


def transcribe_audio_file(user_id, index, mp3_path):
    """Transcribes a single MP3 file using Gemini with memory optimization."""
    user_info_log = f"user {user_id}"
    log_admin(
        f"{user_info_log} - Starting transcription for file index {index} ('{mp3_path}') using Gemini"
    )

    audio_bytes = None
    base64_audio = None

    try:
        # Check file size before processing
        file_size = os.path.getsize(mp3_path)
        MAX_AUDIO_SIZE = 52428800  # 50MB in bytes

        if file_size > MAX_AUDIO_SIZE:
            size_mb = file_size / (1024 * 1024)
            error_msg = f"Audio file too large for transcription ({size_mb:.1f} MB). Maximum size: 50 MB."
            log_admin(f"{user_info_log} - {error_msg}")
            return f"[Ошибка: файл {index+1} слишком большой для расшифровки]"

        log_admin(f"{user_info_log} - Processing audio file {index+1} ({file_size} bytes)")

        # Read file in chunks to optimize memory usage
        with open(mp3_path, "rb") as audio_file:
            audio_bytes = audio_file.read()

        # Optimize base64 encoding with memory management
        try:
            base64_audio = base64.b64encode(audio_bytes).decode("utf-8")

            # Immediately clear audio_bytes from memory
            del audio_bytes
            audio_bytes = None
            import gc
            gc.collect()

            audio_data = {"mime_type": "audio/mp3", "data": base64_audio}

            # Clear base64_audio from local scope before API call
            base64_audio_size = len(base64_audio)
            log_admin(f"{user_info_log} - Base64 encoded audio size: {base64_audio_size} characters")

        except MemoryError as mem_err:
            log_admin(f"{user_info_log} - Memory error during base64 encoding for file {index+1}: {mem_err}")
            return f"[Ошибка: недостаточно памяти для обработки файла {index+1}]"

        transcript = call_official_gemini_api(
            model_name="gemini-2.5-flash",  # Используем актуальную стабильную модель
            history=[],
            user_text=None,
            input_data=audio_data,
            system_prompt=SYSTEM_PROMPT_TRANSCRIBE,  # System prompt remains
            call_type="transcribe_official_gemini_flash",  # Updated call_type
            user_id=None,  # Transcription doesn't need user context
        )

        # Clear audio_data from memory after API call
        del audio_data
        if base64_audio:
            del base64_audio
            base64_audio = None
        gc.collect()

        if (
            isinstance(transcript, str)
            and not transcript.startswith("Ошибка API")
            and not transcript.startswith("Извините, сервис временно недоступен")
        ):  # Check for API errors
            return transcript.strip()
        else:
            error_msg = (
                transcript
                if isinstance(transcript, str)
                else "Unknown transcription error"
            )
            log_admin(
                f"{user_info_log} - Transcription API returned error for file {index+1}: {error_msg}",
                level="warning",
            )
            return f"[Ошибка расшифровки файла {index+1}: {error_msg}]"
    except Exception as e:
        log_admin(
            f"{user_info_log} - Critical error during transcription task for file {index+1}: {e}\n{traceback.format_exc()}",
            level="error",
        )
        return f"[Ошибка обработки файла {index+1} при расшифровке: {e}]"
    finally:
        # Ensure memory cleanup in all cases
        if audio_bytes:
            del audio_bytes
        if base64_audio:
            del base64_audio
        import gc
        gc.collect()


# Эта функция ДОЛЖНА обрабатывать ПРЯМЫЕ (НЕ ПЕРЕСЛАННЫЕ) аудио/видео
# И передавать объединенный транскрипт в process_text_or_photo_request
def process_audio_video_group(user_id):
    with audio_video_group_lock:
        if (
            user_id not in audio_video_groups
            or audio_video_groups[user_id]["processed"]
        ):
            return
        group_data = audio_video_groups[user_id]
        group_data["processed"] = True
        files_to_process = list(
            group_data["files"]
        )  # Contains tuples: (file_id, duration, message_id, file_type)
        chat_id = group_data["chat_id"]
        initial_status_message_id = group_data.get("status_message_id")
        num_files = len(files_to_process)

    user_info_log = f"user {user_id}"
    log_admin(
        f"{user_info_log} - DIRECT audio/video group: {num_files} files. Initial bot status_id from handler: {initial_status_message_id}"
    )

    try:
        bot.send_chat_action(chat_id, "typing")
    except Exception as e_action:
        log_admin(
            f"{user_info_log} - Error sending 'typing' action in process_audio_video_group: {e_action}",
            level="warning",
        )

    temp_dir = tempfile.mkdtemp()
    mp3_paths_to_clean = []
    all_raw_transcripts_map = {}  # Maps original index to transcript/error
    an_error_occurred_before_ptpr = False  # Flag to track errors before passing to PTPR

    bot_instance = bot
    user_message_id_for_reaction = (
        None  # This will be the ID of the user's message where '⚡' is set
    )
    reaction_set_on_user_message = False
    reaction_successfully_passed_to_ptpr = False

    try:
        if not files_to_process:
            log_admin(
                f"{user_info_log} - No files to process in audio/video group. Aborting.",
                level="warning",
            )
            if initial_status_message_id:
                try:
                    bot.delete_message(
                        chat_id=chat_id, message_id=initial_status_message_id
                    )
                except:
                    pass
            return

        user_message_id_for_reaction = files_to_process[0][
            2
        ]  # message_id of the first file in the group

        # Delete the initial status message sent by the handler (e.g., "ANALYZING_DIRECT")
        # as we will now use a reaction on the user's message.
        if (
            initial_status_message_id
            and initial_status_message_id != user_message_id_for_reaction
        ):  # Check it's not the user's message ID
            log_admin(
                f"{user_info_log} - Deleting initial status message {initial_status_message_id} from handler."
            )
            try:
                bot.delete_message(
                    chat_id=chat_id, message_id=initial_status_message_id
                )
            except Exception as e_del_initial_handler_msg:
                log_admin(
                    f"{user_info_log} - Failed to delete initial status message {initial_status_message_id} from handler: {e_del_initial_handler_msg}",
                    level="warning",
                )

        emoji_for_reaction = "⚡"  # New standard reaction

        # Check if instant reaction was already set for private chats
        is_private_chat = (chat_id == user_id)
        instant_reaction_already_set = False

        if is_private_chat:
            # For private chats, reaction should already be set instantly
            instant_reaction_already_set = True
            log_admin(f"{user_info_log} - Skipping reaction setting - instant reaction already set for private chat")

        if not instant_reaction_already_set:
            log_admin(
                f"{user_info_log} - Setting '⚡' reaction on user message {user_message_id_for_reaction}."
            )
            try:
                set_reaction(bot, chat_id, user_message_id_for_reaction, emoji_for_reaction)
                reaction_set_on_user_message = True
            except Exception as e_set_reaction:
                log_admin(
                    f"{user_info_log} - Failed to set '⚡' reaction on user message {user_message_id_for_reaction}: {e_set_reaction}. Processing will continue without reaction.",
                    level="error",
                )
                # No fallback message sending here, PTPR will handle its own if needed.
        else:
            # Reaction already set instantly
            reaction_set_on_user_message = True

        # 1. Download and Convert All Files (in parallel)
        # _update_direct_audio_status_text and its calls are removed. The reaction serves as indicator.
        log_admin(
            f"{user_info_log} - Starting download & conversion for {num_files} files."
        )
        converted_mp3s_map = {}  # Maps original_idx to mp3_path

        # Используем глобальный пул потоков с глобальным ограничением на 8 одновременных задач
        log_admin(f"{user_info_log} - Using global thread pool for downloads (global limit: 8 concurrent tasks)")

        # Create futures for download_and_convert_file
        # The 'idx' passed to download_and_convert_file is the original index from files_to_process
        futures_conv = {}
        for idx, file_data in enumerate(files_to_process):
            # file_data is (file_id, duration, message_id, file_type)
            future = thread_pool_manager.submit(
                download_and_convert_file,
                user_id,
                idx,
                file_data[0],
                file_data[2],
                file_data[3],
                temp_dir,
            )
            futures_conv[future] = idx

        # Process all download futures
        for future in as_completed(futures_conv):
            original_idx = futures_conv[future]
            try:
                mp3_path = future.result()
                if mp3_path:
                    converted_mp3s_map[original_idx] = mp3_path
                    mp3_paths_to_clean.append(mp3_path)
                    log_admin(
                        f"{user_info_log} - Converted file {original_idx+1}/{num_files}. Path: {mp3_path}",
                        level="debug",
                    )
                    # Removed status update: _update_direct_audio_status_text
                else:
                    all_raw_transcripts_map[original_idx] = (
                        f"[Ошибка конвертации файла {original_idx+1}]"
                    )
            except Exception as e_conv:
                all_raw_transcripts_map[original_idx] = (
                    f"[Ошибка обработки файла {original_idx+1} при конвертации: {e_conv}]"
                )

        if not converted_mp3s_map:
            all_errors = all(
                i in all_raw_transcripts_map
                and all_raw_transcripts_map[i].startswith("[Ошибка")
                for i in range(num_files)
            )
            if all_errors:
                error_detail_parts = [
                    f"Файл {i+1}: {all_raw_transcripts_map.get(i, '[неизвестная ошибка конвертации]')}"
                    for i in range(num_files)
                ]
                error_summary_text = (
                    "Не удалось сконвертировать ни один файл для обработки:\n"
                    + "\n".join(error_detail_parts)
                )
                if reaction_set_on_user_message:
                    remove_reaction(bot, chat_id, user_message_id_for_reaction)
                    reaction_set_on_user_message = False
                bot.send_message(
                    chat_id,
                    error_summary_text,
                    reply_to_message_id=user_message_id_for_reaction,
                    parse_mode=None,
                )
                return
            raise Exception(
                "Не удалось сконвертировать ни один файл для прямой обработки (не все файлы вызвали ошибку)."
            )

        # 2. Transcribe All Converted Files (in parallel)
        # current_processing_status_msg_id = _update_direct_audio_status_text("TRANSCRIBING", current_processing_status_msg_id) # Removed
        log_admin(
            f"{user_info_log} - Starting transcription for {len(converted_mp3s_map)} converted files."
        )
        transcribed_count = 0

        # Используем глобальный пул потоков с глобальным ограничением на 8 одновременных задач
        log_admin(f"{user_info_log} - Using global thread pool for transcriptions (global limit: 8 concurrent tasks)")

        # Create futures for transcription
        futures_trans = {}
        for original_idx, mp3_path_val in converted_mp3s_map.items():
            future = thread_pool_manager.submit(
                transcribe_audio_file, user_id, original_idx, mp3_path_val
            )
            futures_trans[future] = original_idx

        # Process all transcription futures
        for future in as_completed(futures_trans):
            original_idx = futures_trans[future]
            try:
                transcript = future.result()
                all_raw_transcripts_map[original_idx] = transcript
                if transcript and not transcript.startswith("[Ошибка"):
                    transcribed_count += 1
                log_admin(
                    f"{user_info_log} - Transcribed file {original_idx+1} (overall progress: {transcribed_count}/{len(converted_mp3s_map)} successful).",
                    level="debug",
                )
                # Removed status update: _update_direct_audio_status_text
            except Exception as e_trans:
                all_raw_transcripts_map[original_idx] = (
                    f"[Критическая ошибка расшифровки файла {original_idx+1}: {e_trans}]"
                )

        # 3. Combine Transcripts (in original order)
        combined_transcript_parts = []
        any_valid_transcript = False
        for i in range(num_files):
            transcript_text = all_raw_transcripts_map.get(
                i,
                f"[Транскрипт для файла {i+1} отсутствует или ошибка на предыдущем этапе]",
            )
            if num_files > 1:
                combined_transcript_parts.append(
                    f"--- Голосовое/видео сообщение {i+1} ---\n{transcript_text}"
                )
            else:
                combined_transcript_parts.append(transcript_text)
            if transcript_text and not transcript_text.startswith("[Ошибка"):
                any_valid_transcript = True
        final_combined_transcript = "\n\n".join(combined_transcript_parts).strip()

        if not any_valid_transcript:
            error_detail_parts = [
                f"Файл {i+1}: {all_raw_transcripts_map.get(i, '[неизвестная ошибка]')}"
                for i in range(num_files)
            ]
            error_summary_text = (
                "Не удалось расшифровать аудио/видео для дальнейшей обработки:\n"
                + "\n".join(error_detail_parts)
            )
            if reaction_set_on_user_message:
                remove_reaction(bot, chat_id, user_message_id_for_reaction)
                reaction_set_on_user_message = False
            bot.send_message(
                chat_id,
                error_summary_text,
                reply_to_message_id=user_message_id_for_reaction,
                parse_mode=None,
            )
            return

        # 4. Pass to process_text_or_photo_request
        log_admin(
            f"{user_info_log} - Direct audio group transcribed. Passing to process_text_or_photo_request. Combined length: {len(final_combined_transcript)}"
        )
        # The user_message_id_for_reaction (user's message) is passed as initial_status_message_id.
        # process_text_or_photo_request will handle changing this reaction to '⚡' and then removing it.
        reaction_successfully_passed_to_ptpr = True
        process_text_or_photo_request(
            user_id,
            chat_id,
            final_combined_transcript,
            single_image_data=None,
            original_message_id=user_message_id_for_reaction,  # Reply to the user's message
            initial_status_message_id=user_message_id_for_reaction,  # Pass user's message ID for reaction handling
        )
        # Reaction management is now passed to process_text_or_photo_request.
        reaction_set_on_user_message = False  # Mark as handled

        send_one_time_notification(user_id, chat_id, "voice_answer")

    except Exception as e_group_outer:
        log_admin(
            f"{user_info_log} - Outer error in process_audio_video_group (direct): {e_group_outer}\n{traceback.format_exc()}"
        )
        error_text_outer = (
            f"❌ Ошибка обработки группы аудио/видео: {str(e_group_outer)[:100]}"
        )
        if reaction_set_on_user_message and not reaction_successfully_passed_to_ptpr:
            remove_reaction(bot, chat_id, user_message_id_for_reaction)
            reaction_set_on_user_message = False
        bot.send_message(
            chat_id,
            error_text_outer,
            parse_mode=None,
            reply_to_message_id=(
                user_message_id_for_reaction
                if user_message_id_for_reaction
                else (files_to_process[0][2] if files_to_process else None)
            ),
        )
    finally:
        if reaction_set_on_user_message and not reaction_successfully_passed_to_ptpr:
            log_admin(
                f"{user_info_log} - Removing reaction in finally block as it was not passed to PTPR or already handled by error block."
            )
            try:
                remove_reaction(bot, chat_id, user_message_id_for_reaction)
            except Exception as e_final_remove:
                log_admin(
                    f"{user_info_log} - Error removing reaction in finally: {e_final_remove}",
                    level="warning",
                )
            reaction_set_on_user_message = False

        for mp3_p in mp3_paths_to_clean:
            if os.path.exists(mp3_p):
                try:
                    os.remove(mp3_p)
                except OSError as e_rem_mp3_loop:
                    log_admin(
                        f"{user_info_log} - WARNING: Failed to remove processed MP3 file '{mp3_p}' in loop: {e_rem_mp3_loop}",
                        level="warning",
                    )
        if os.path.exists(temp_dir):
            try:
                shutil.rmtree(
                    temp_dir, ignore_errors=True
                )  # Keep ignore_errors=True for rmtree's own robustness
            except (
                Exception
            ) as e_clean:  # Log any other exception that rmtree might not ignore or if ignore_errors was false
                log_admin(
                    f"{user_info_log} - WARNING: Failed to clean temp_dir '{temp_dir}': {e_clean}",
                    level="warning",
                )
        with audio_video_group_lock:
            if user_id in audio_video_groups:
                del audio_video_groups[user_id]
                log_admin(
                    f"{user_info_log} - Cleaned up data for direct audio/video group from global state (parallel revert)."
                )


# --- Renamed function for processing a single forwarded audio/video item ---
def process_forwarded_audio_item(
    user_id,
    chat_id,
    file_id,
    duration,
    original_message_id,
    file_type,
    forwarder_name,
    bot_instance,
):

    user_info_log_prefix_for_duration_fix = (
        f"user {user_id} [process_forwarded_audio_item]"  # For specific logging
    )

    if isinstance(duration, str):
        try:
            original_str_duration = duration  # For logging
            duration = int(duration)
            log_admin(
                f"{user_info_log_prefix_for_duration_fix} - Converted string duration '{original_str_duration}' to int: {duration}",
                level="debug",
            )
        except ValueError:
            log_admin(
                f"{user_info_log_prefix_for_duration_fix} - ValueError converting string duration '{original_str_duration}' to int. Defaulting to 0.",
                level="warning",
            )
            duration = 0
    elif not isinstance(duration, int):
        original_val_duration = duration  # For logging
        original_type_duration = type(duration).__name__  # For logging
        log_admin(
            f"{user_info_log_prefix_for_duration_fix} - Duration was not string or int (type: {original_type_duration}, value: '{original_val_duration}'). Attempting conversion.",
            level="info",
        )
        try:
            duration = int(duration)
            log_admin(
                f"{user_info_log_prefix_for_duration_fix} - Successfully converted non-string/non-int duration to int: {duration}",
                level="debug",
            )
        except (ValueError, TypeError):
            log_admin(
                f"{user_info_log_prefix_for_duration_fix} - Failed to convert non-string/non-int duration (type: {original_type_duration}, value: '{original_val_duration}') to int. Defaulting to 0.",
                level="warning",
            )
            duration = 0

    user_info_log = f"user {user_id}"
    func_log_prefix = f"[FWD_AUDIO_ITEM {original_message_id}] {user_info_log}: "  # Log prefix changed
    log_admin(
        f"{func_log_prefix}START processing item: {file_type} (duration: {duration}s) from '{forwarder_name}'"
    )

    try:
        bot.send_chat_action(chat_id, "typing")
    except Exception as e_action:
        log_admin(
            f"{func_log_prefix}Error sending 'typing' action in process_forwarded_audio_item: {e_action}",
            level="warning",
        )

    temp_dir = tempfile.mkdtemp()
    log_admin(f"{func_log_prefix}Created temp_dir: {temp_dir}")
    mp3_path_single = None
    # status_msg_obj and _update_status_emoji_detailed are removed for reaction logic

    reaction_set_on_message = False

    try:
        emoji_for_reaction = "⚡"  # Standard 'thinking' reaction

        log_admin(
            f"{func_log_prefix}Attempting to set reaction '{emoji_for_reaction}' on message {original_message_id}"
        )
        try:
            set_reaction(bot, chat_id, original_message_id, emoji_for_reaction)
            reaction_set_on_message = True
        except Exception as e_set_reaction_fwd:
            log_admin(
                f"{func_log_prefix}Failed to set reaction '{emoji_for_reaction}' on message {original_message_id}: {e_set_reaction_fwd}. Processing continues.",
                level="warning",
            )
            # Optionally send a fallback "⏳" message if reaction fails, though for forwarded items, maybe not critical.

        # 1. DOWNLOAD
        # status_msg_obj = _update_status_emoji_detailed("AUDIO_DOWNLOADING", status_msg_obj, "Download Start") # Removed
        log_admin(f"{func_log_prefix}Starting download for file_id {file_id}")
        original_file_path_temp_single = os.path.join(
            temp_dir, f"{uuid.uuid4()}_orig_s"
        )
        log_admin(
            f"{func_log_prefix}Downloading file_id {file_id} to {original_file_path_temp_single}"
        )
        downloaded_file_info_single = bot_instance.get_file(file_id)

        # Check file size limit before download
        MAX_AUDIO_SIZE = 52428800  # 50MB in bytes
        if downloaded_file_info_single.file_size and downloaded_file_info_single.file_size > MAX_AUDIO_SIZE:
            size_mb = downloaded_file_info_single.file_size / (1024 * 1024)
            error_msg = f"Audio file too large ({size_mb:.1f} MB). Maximum size: 50 MB."
            log_admin(f"{func_log_prefix}{error_msg}")
            raise Exception(f"Audio file too large: {size_mb:.1f} MB")

        downloaded_file_bytes_single = bot_instance.download_file(
            downloaded_file_info_single.file_path
        )

        with open(original_file_path_temp_single, "wb") as f_single:
            f_single.write(downloaded_file_bytes_single)

        # Clear downloaded bytes from memory immediately
        file_size = len(downloaded_file_bytes_single)
        del downloaded_file_bytes_single
        import gc
        gc.collect()

        log_admin(
            f"{func_log_prefix}File downloaded successfully. Size: {file_size} bytes."
        )

        # 2. CONVERT
        # status_msg_obj = _update_status_emoji_detailed("AUDIO_CONVERTING", status_msg_obj, "Convert Start") # Removed
        log_admin(
            f"{func_log_prefix}Starting conversion for {original_file_path_temp_single}"
        )
        mp3_path_single = os.path.join(temp_dir, f"{uuid.uuid4()}_s.mp3")
        log_admin(
            f"{func_log_prefix}Converting {original_file_path_temp_single} to {mp3_path_single}"
        )
        if not convert_to_mp3(original_file_path_temp_single, mp3_path_single):
            log_admin(f"{func_log_prefix}CRITICAL_ERROR - Failed to convert to MP3.")
            raise Exception("Failed to convert single forwarded to MP3")
        log_admin(f"{func_log_prefix}Successfully converted to MP3: {mp3_path_single}")
        if os.path.exists(original_file_path_temp_single):
            try:
                os.remove(original_file_path_temp_single)
                log_admin(
                    f"{func_log_prefix}Removed original temp file: {original_file_path_temp_single}"
                )
            except Exception as e_rem:
                log_admin(
                    f"{func_log_prefix}WARNING - Failed to remove original temp file {original_file_path_temp_single}: {e_rem}"
                )

        # 3. TRANSCRIBE
        # status_msg_obj = _update_status_emoji_detailed("AUDIO_TRANSCRIBING_GEMINI", status_msg_obj, "Transcribe Start") # Removed
        log_admin(f"{func_log_prefix}Starting transcription for MP3: {mp3_path_single}")

        # Check file size before transcription
        mp3_file_size = os.path.getsize(mp3_path_single)
        MAX_AUDIO_SIZE = 52428800  # 50MB in bytes
        if mp3_file_size > MAX_AUDIO_SIZE:
            size_mb = mp3_file_size / (1024 * 1024)
            error_msg = f"MP3 file too large for transcription ({size_mb:.1f} MB). Maximum size: 50 MB."
            log_admin(f"{func_log_prefix}{error_msg}")
            raise Exception(f"MP3 file too large: {size_mb:.1f} MB")

        with open(mp3_path_single, "rb") as audio_file_single:
            audio_bytes_single = audio_file_single.read()

        # 3. PREPARE AUDIO FOR DIRECT SUMMARY
        try:
            base64_audio_single = base64.b64encode(audio_bytes_single).decode("utf-8")

            # Clear audio_bytes from memory immediately
            del audio_bytes_single
            import gc
            gc.collect()

            audio_data_gemini_single = {
                "mime_type": "audio/mp3",
                "data": base64_audio_single,
            }
            log_admin(
                f"{func_log_prefix}Base64 audio prepared ({len(base64_audio_single)} chars). Calling Gemini 2.5 Flash for direct audio summary..."
            )

        except MemoryError as mem_err:
            log_admin(f"{func_log_prefix}Memory error during base64 encoding: {mem_err}")
            raise Exception(f"Memory error during audio preparation: {mem_err}")

        # 4. GEMINI 2.5 FLASH FOR DIRECT AUDIO SUMMARY
        # No status_msg_obj to edit to "⏳". Reaction on user message serves as indicator.
        log_admin(f"{func_log_prefix}Preparing for Gemini 2.5 Flash call for direct audio summary.")
        system_prompt_for_gpt = SYSTEM_PROMPT_AUDIO_VIDEO_SUMMARY_GEMINI.format(
            forwarder_name=html.escape(forwarder_name),
            num_transcripts=1,
            transcripts_block="(используй прикреплённое аудио)",  # замещаем placeholder
        )
        log_admin(
            f"{func_log_prefix}System prompt for Gemini 2.5 Flash (first 300 chars): {system_prompt_for_gpt[:300]}..."
        )
        log_admin(
            f"{func_log_prefix}Calling Gemini 2.5 Flash (call_llm) for direct audio summary. Expecting non-streamed output to this function."
        )

        gemini_raw_output = call_llm(
            MODEL_GEMINI_2_5_FLASH,                # ← нужная модель
            history=[],
            user_text=None,
            input_data=audio_data_gemini_single,
            system_prompt=system_prompt_for_gpt,
            call_type="audio_summary_gemini",
            is_private_chat=False,  # Audio processing not specific to chat type
            user_id=None,  # Audio processing doesn't have user context
        )

        # Clear audio data from memory after API call
        del audio_data_gemini_single
        del base64_audio_single
        gc.collect()
        log_admin(
            f"{func_log_prefix}Gemini 2.5 Flash raw_output (length: {len(str(gemini_raw_output))}): '{str(gemini_raw_output)}'"
        )

        # ДЕБАГ: Детальное логирование ошибок от Gemini API
        if not isinstance(gemini_raw_output, str) or gemini_raw_output.startswith(
            "ОШИБКА"
        ):
            error_detail = (
                gemini_raw_output
                if isinstance(gemini_raw_output, str)
                else "Неизвестная ошибка от Gemini 2.5 Flash"
            )
            log_admin(
                f"{func_log_prefix}CRITICAL_ERROR - Gemini 2.5 Flash call failed. Response: {error_detail}",
                level="error"
            )

            # ДЕБАГ: Дополнительное логирование для отладки
            log_admin(
                f"{func_log_prefix}DEBUG_AUDIO_ERROR - Full error details: "
                f"Type: {type(gemini_raw_output)}, "
                f"Content: '{str(gemini_raw_output)[:1000]}', "
                f"System prompt length: {len(system_prompt_for_gpt) if system_prompt_for_gpt else 0}",
                level="error"
            )

            # Send error as new message, replying to original user message
            bot_instance.send_message(
                chat_id,
                f"❌ Ошибка Gemini при создании сводки: {error_detail}",
                reply_to_message_id=original_message_id,
                parse_mode=None,
            )
            # No `raise Exception` here, because we've informed the user. Reaction will be removed in except/finally.
            # Need to ensure reaction is removed before returning if error occurs here.
            if reaction_set_on_message:
                remove_reaction(bot, chat_id, original_message_id)
                reaction_set_on_message = False
            return  # Terminate processing here

        # 5. PARSE GEMINI 2.5 FLASH OUTPUT
        # status_msg_obj = _update_status_emoji_detailed("AUDIO_PARSING", status_msg_obj, "Parse Gemini Output") # Removed
        log_admin(f"{func_log_prefix}Parsing Gemini 2.5 Flash output. Expecting 1 item.")
        log_admin(f"{func_log_prefix}DEBUG: Raw output to parse: '{gemini_raw_output}'")
        parsed_items = parse_gemini_audio_summary_output(
            gemini_raw_output, num_expected_items=1
        )
        log_admin(f"{func_log_prefix}Parsed items: {parsed_items}")

        # Check if the parser itself signaled a fundamental inability to parse the expected structure.
        # This happens when `parse_gemini_audio_summary_output` returns "[Ошибка: не удалось разобрать ответ модели]"
        # for the short_summary field (which implies it couldn't find any tags).
        # item_data = parsed_items[0] # Это присваивание уже есть выше или будет ниже, если parsed_items не пуст

        # Сначала проверим, есть ли вообще элементы в parsed_items
        if not parsed_items:
            log_admin(
                f"{func_log_prefix}CRITICAL_ERROR - parsed_items is empty after call to parser.",
                level="error",
            )
            error_text_internal = "Внутренняя ошибка: парсер вернул пустой результат."
            # Send error as new message
            bot_instance.send_message(
                chat_id,
                error_text_internal,
                reply_to_message_id=original_message_id,
                parse_mode=None,
            )
            if reaction_set_on_message:
                remove_reaction(bot, chat_id, original_message_id)
                reaction_set_on_message = False
            return  # Terminate

        item_data = parsed_items[0]  # Теперь мы уверены, что parsed_items не пуст

        # Теперь проверяем на специфическую ошибку от парсера
        if item_data["short_summary"] == "[Ошибка: не удалось разобрать ответ модели]":
            parse_error_detail = item_data[
                "short_summary"
            ]  # Используем сообщение от парсера
            log_admin(
                f"{func_log_prefix}CRITICAL_ERROR - Parser indicated failure to parse Gemini 2.5 Flash output structure. Detail: {parse_error_detail}",
                level="error",
            )

            # ДЕБАГ: Детальное логирование ошибки парсера
            log_admin(
                f"{func_log_prefix}DEBUG_PARSER_ERROR - Full parsing failure details: "
                f"Raw Gemini output: '{str(gemini_raw_output)[:2000]}', "
                f"Expected tags: SHORT_SUMMARY_1_START/END, DETAILED_SUMMARY_1_START/END, FORMATTED_TRANSCRIPT_1_START/END, "
                f"Parser result: {parsed_items}",
                level="error"
            )

            final_error_message_to_user = (
                f"❌ Ошибка разбора ответа Gemini для сводки.\n\n"
                f"🔍 ДЕБАГ ИНФОРМАЦИЯ:\n"
                f"Ответ модели: '{str(gemini_raw_output)[:500]}...'\n"
                f"Длина ответа: {len(str(gemini_raw_output))} символов\n"
                f"Ожидаемые теги не найдены в ответе модели."
            )
            # Send error as new message
            bot_instance.send_message(
                chat_id,
                final_error_message_to_user,
                reply_to_message_id=original_message_id,
                parse_mode=None,
            )
            if reaction_set_on_message:
                remove_reaction(bot, chat_id, original_message_id)
                reaction_set_on_message = False
            return  # Terminate, as parsing truly failed

        # Если мы здесь, значит парсер не вернул "[Ошибка: не удалось разобрать ответ модели]",
        # но мог вернуть плейсхолдеры типа "[Краткая сводка не найдена]"
        log_admin(
            f"{func_log_prefix}Parser did not return critical error. Short summary from parser: '{item_data['short_summary'][:100]}...'",
            level="info",
        )

        # 6. PREPARE AND SEND FINAL MESSAGE WITH BUTTONS (ALWAYS SEND NEW)
        # --- Phase 2: Modified final_display_text selection ---
        final_display_text = ""
        current_view_for_state = "error"  # Default to error view

        valid_short_summary = item_data.get("short_summary") and not item_data[
            "short_summary"
        ].startswith("[")
        valid_detailed_summary = item_data.get("detailed_summary") and not item_data[
            "detailed_summary"
        ].startswith("[")
        # item_data['formatted_transcript'] is from Gemini formatting pass
        valid_formatted_transcript = item_data.get(
            "formatted_transcript"
        ) and not item_data["formatted_transcript"].startswith("[")

        # Prefer summaries if available and valid
        if valid_short_summary:
            final_display_text = item_data["short_summary"]
            current_view_for_state = "short"
            log_admin(f"{func_log_prefix}Using valid short_summary.")
        elif valid_detailed_summary:
            final_display_text = item_data["detailed_summary"]
            current_view_for_state = "detailed"
            log_admin(
                f"{func_log_prefix}Using valid detailed_summary as short_summary was invalid/placeholder."
            )
        elif valid_formatted_transcript:  # If summaries failed, but transcript is good
            final_display_text = item_data["formatted_transcript"]
            current_view_for_state = "transcript"
            log_admin(
                f"{func_log_prefix}Using valid formatted_transcript as summaries were invalid/placeholders."
            )
        else:  # All options exhausted
            log_admin(
                f"{func_log_prefix}All content (summaries, formatted_transcript) are invalid or placeholders. Sending generic error."
            )
            final_display_text = "⚠️ Не удалось извлечь полезное содержание из аудио/видео после обработки."
            # This message will be sent, and then the '⚡' reaction will be removed in the success path.

        markup = types.InlineKeyboardMarkup(row_width=2)
        buttons_to_add = []

        # Button logic: only add if the target content is valid AND different from what's being displayed
        if valid_detailed_summary and current_view_for_state != "detailed":
            buttons_to_add.append(
                types.InlineKeyboardButton(
                    text="Подробнее", callback_data=f"audio_detail_TEMPKEY"
                )
            )

        if valid_formatted_transcript and current_view_for_state != "transcript":
            # Also ensure we don't add "Расшифровка" if we're already showing the raw transcript as fallback
            if not (
                current_view_for_state == "transcript_raw"
                and final_display_text == item_data["formatted_transcript"]
            ):
                buttons_to_add.append(
                    types.InlineKeyboardButton(
                        text="Расшифровка", callback_data=f"audio_trans_TEMPKEY"
                    )
                )

        # Add "Кратко" button if we are showing detailed or transcript view and short summary is valid
        if valid_short_summary and current_view_for_state in [
            "detailed",
            "transcript",
            "transcript_raw",
        ]:
            buttons_to_add.append(
                types.InlineKeyboardButton(
                    text="Кратко", callback_data=f"audio_short_TEMPKEY"
                )
            )

        if buttons_to_add:
            markup.add(*buttons_to_add)
        else:
            markup = None
        log_admin(
            f"{func_log_prefix}Prepared markup (new logic): {markup.to_json() if markup else 'None'}"
        )

        sent_final_msg_id = None
        final_message_object = None

        # Always send as a new message because status_msg_obj is removed
        log_admin(f"{func_log_prefix}Attempting to SEND new final summary message.")
        try:
            final_message_object = bot_instance.send_message(
                chat_id,
                final_display_text,
                reply_to_message_id=original_message_id,
                reply_markup=markup,  # markup might have TEMPKEY
                parse_mode="HTML",
            )
            sent_final_msg_id = final_message_object.message_id
            log_admin(
                f"{func_log_prefix}SUCCESS - Sent new final summary message (HTML). Final msg_id: {sent_final_msg_id}"
            )
        except telebot.apihelper.ApiTelegramException as e_send_final_html:
            log_admin(
                f"{func_log_prefix}ERROR - Sending new final summary message (HTML): {e_send_final_html}. Trying plain."
            )
            plain_final_display_text = re.sub(r"<[^>]+>", "", final_display_text)
            try:
                final_message_object = bot_instance.send_message(
                    chat_id,
                    plain_final_display_text,
                    reply_to_message_id=original_message_id,
                    reply_markup=markup,
                    parse_mode=None,
                )
                sent_final_msg_id = final_message_object.message_id
                log_admin(
                    f"{func_log_prefix}SUCCESS - Sent new final summary message (plain). Final msg_id: {sent_final_msg_id}"
                )
            except Exception as e_send_final_plain:
                log_admin(
                    f"{func_log_prefix}CRITICAL_ERROR - Sending new final summary message (plain) also failed: {e_send_final_plain}"
                )
                if (
                    reaction_set_on_message
                ):  # Ensure reaction is removed even if final send fails
                    remove_reaction(bot, chat_id, original_message_id)
                    reaction_set_on_message = False
                return  # Terminate
        except Exception as e_send_general:
            log_admin(
                f"{func_log_prefix}CRITICAL_ERROR - General error sending new final summary: {e_send_general}"
            )
            if reaction_set_on_message:  # Ensure reaction is removed
                remove_reaction(bot, chat_id, original_message_id)
                reaction_set_on_message = False
            return  # Terminate

        # 7. UPDATE CALLBACK DATA AND SAVE STATE
        if (
            sent_final_msg_id and markup and final_message_object
        ):  # If there are buttons to update and we have the message object
            message_key_for_state_machine = f"{chat_id}_{sent_final_msg_id}"
            log_admin(
                f"{func_log_prefix}Updating button callback_data with key: {message_key_for_state_machine}"
            )
            updated_buttons_for_markup = []
            for row in markup.keyboard:
                new_row_buttons = []
                for button in row:
                    updated_callback_data = button.callback_data.replace(
                        "TEMPKEY", message_key_for_state_machine
                    )
                    new_row_buttons.append(
                        types.InlineKeyboardButton(
                            text=button.text, callback_data=updated_callback_data
                        )
                    )
                updated_buttons_for_markup.append(new_row_buttons)

            new_markup_with_final_key = types.InlineKeyboardMarkup(
                keyboard=updated_buttons_for_markup
            )
            try:
                bot_instance.edit_message_reply_markup(
                    chat_id=chat_id,
                    message_id=sent_final_msg_id,
                    reply_markup=new_markup_with_final_key,
                )
                log_admin(
                    f"{func_log_prefix}SUCCESS - Updated reply_markup with final key."
                )
            except Exception as e_murk_final:
                log_admin(
                    f"{func_log_prefix}ERROR - Updating reply_markup with final key: {e_murk_final}"
                )

        if sent_final_msg_id:
            message_key_for_state_machine = (
                f"{chat_id}_{sent_final_msg_id}"  # Ensure it's defined
            )
            with message_states_lock:
                message_states[message_key_for_state_machine] = {
                    "type": "multi_audio_summary",  # Use common type for handler
                    "items": [item_data],
                    "current_view": "short",
                    "forwarder_name": forwarder_name,
                    "original_message_id": original_message_id,
                    "message_object_for_interactions": final_message_object,  # Store for potential future interactions if needed
                }
            log_admin(
                f"{func_log_prefix}SUCCESS - Saved state for single forwarded audio summary. Key: {message_key_for_state_machine}"
            )

        # Reaction '⚡' on user's forwarded message should remain.
        # '🎉' will be added to the bot's response message.
        log_admin(
            f"{func_log_prefix}END processing successfully. '⚡' reaction on {original_message_id} remains."
        )
        send_one_time_notification(user_id, chat_id, "audio_summary")
        # Add '🎉' to the bot's response message or remove reactions if too long
        if sent_final_msg_id and final_message_object:  # Ensure these are set
            if len(final_display_text) > 1000:
                try:
                    # Удаляем все реакции с сообщения бота, если ответ длинный
                    remove_reaction(bot, chat_id, sent_final_msg_id)
                    log_admin(
                        f"{func_log_prefix}Response >1000 chars. Removed reactions from bot message {sent_final_msg_id} (forwarded audio)."
                    )
                except Exception as e_rem_react_fwd_long:
                    log_admin(
                        f"{func_log_prefix}Error removing reactions for long response from bot message {sent_final_msg_id} (forwarded audio): {e_rem_react_fwd_long}"
                    )
            else:
                # try:
                #     log_admin(f"{func_log_prefix}Setting '🎉' reaction on bot message {sent_final_msg_id}.")
                #     set_reaction(bot, chat_id, sent_final_msg_id, '🎉')
                # except Exception as e_set_tada_fwd:
                #     log_admin(f"{func_log_prefix}Error setting '🎉' reaction on bot message {sent_final_msg_id}: {e_set_tada_fwd}")
                pass  # Removed 🎉 reaction setting

        # After successful processing and sending summary, remove '⚡' from user's original message
        if reaction_set_on_message:
            try:
                log_admin(
                    f"{func_log_prefix}Attempting to remove '⚡' reaction from user's message {original_message_id} after successful summary."
                )
                remove_reaction(bot, chat_id, original_message_id)
                log_admin(
                    f"{func_log_prefix}Successfully removed '⚡' reaction from user's message {original_message_id}."
                )
                reaction_set_on_message = False  # Flag that it has been handled
            except Exception as e_rem_final_lightning:
                log_admin(
                    f"{func_log_prefix}Failed to remove '⚡' reaction from user's message {original_message_id}: {e_rem_final_lightning}",
                    level="warning",
                )

        log_admin(f"{func_log_prefix}END processing successfully.")

    except Exception as e_inner_processing:
        log_admin(
            f"{func_log_prefix}CRITICAL_ERROR_INNER - During main processing steps: {e_inner_processing}\n{traceback.format_exc()}"
        )
        error_message_text_single_fw_inner = (
            f"❌ Ошибка обработки аудио/видео: {str(e_inner_processing)[:100]}"
        )
        try:
            bot_instance.send_message(
                chat_id,
                error_message_text_single_fw_inner,
                reply_to_message_id=original_message_id,
                parse_mode=None,
            )
        except Exception as e_send_err:
            log_admin(
                f"{func_log_prefix}Failed to send error message for inner processing error: {e_send_err}"
            )
        # If an error occurred, the '⚡' reaction on the user's message should be removed.
        if reaction_set_on_message:
            log_admin(
                f"{func_log_prefix}Error occurred. Removing '⚡' reaction from message {original_message_id}."
            )
            try:
                remove_reaction(bot, chat_id, original_message_id)
            except Exception as e_rem_react_err:
                log_admin(
                    f"{func_log_prefix}Failed to remove reaction on error: {e_rem_react_err}"
                )
            reaction_set_on_message = False  # Reset flag
    finally:
        log_admin(f"{func_log_prefix}Entering FINALLY block.")
        # If an error occurred and reaction was set but not removed in the except block (e.g. if error was after sending response but before reaction logic)
        # This check is a safeguard. The primary logic for reaction removal on error is now in the except block.
        exception_occurred_in_try = (
            "e_inner_processing" in locals()
            or (
                "gemini_raw_output" in locals()
                and isinstance(gemini_raw_output, str)
                and gemini_raw_output.startswith("ОШИБКА")
            )
            or (
                "raw_transcript_single" in locals()
                and isinstance(raw_transcript_single, str)
                and raw_transcript_single.startswith("Ошибка")
            )
        )
        if reaction_set_on_message and exception_occurred_in_try:
            log_admin(
                f"{func_log_prefix}Safeguard: Removing reaction in finally due to earlier error. Flag 'reaction_set_on_message' is {reaction_set_on_message}."
            )
            try:
                remove_reaction(bot, chat_id, original_message_id)
            except Exception as e_final_react_remove:
                log_admin(
                    f"{func_log_prefix}Error removing reaction in finally (safeguard): {e_final_react_remove}"
                )

        if mp3_path_single and os.path.exists(mp3_path_single):
            log_admin(f"{func_log_prefix}Removing MP3 file: {mp3_path_single}")
            try:
                os.remove(mp3_path_single)
            except OSError as e_rem_mp3:  # More specific exception
                log_admin(
                    f"{func_log_prefix}WARNING - Failed to remove MP3 '{mp3_path_single}': {e_rem_mp3}",
                    level="warning",
                )

        if os.path.exists(temp_dir):
            log_admin(f"{func_log_prefix}Removing temp_dir: {temp_dir}")
            try:
                shutil.rmtree(temp_dir, ignore_errors=True)  # Keep ignore_errors=True
            except Exception as e_clean_single_fw:  # Log any other exception
                log_admin(
                    f"{func_log_prefix}WARNING - Failed to clean temp_dir '{temp_dir}': {e_clean_single_fw}",
                    level="warning",
                )
        log_admin(f"{func_log_prefix}FINISHED finally block.")


# --- Forwarded Audio Queue Worker ---
from bot_globals import (
    forwarded_audio_queue,
    forwarded_audio_queue_lock,
    forwarded_audio_processor_active,
)  # Already imported bot, log_admin


def forwarded_audio_queue_worker():
    log_admin("Forwarded audio queue worker started.")
    while True:
        items_to_process_this_batch = []
        with forwarded_audio_queue_lock:
            for _ in range(2):  # Max 2 items per batch
                if forwarded_audio_queue:
                    items_to_process_this_batch.append(forwarded_audio_queue.popleft())
                else:
                    break

        if not items_to_process_this_batch:
            log_admin("Forwarded audio queue is empty. Worker pausing.")
            forwarded_audio_processor_active.clear()  # Signal that worker is now inactive
            break  # Exit loop if queue is empty

        log_admin(
            f"Forwarded audio worker processing batch of {len(items_to_process_this_batch)} items."
        )

        if len(items_to_process_this_batch) == 2:
            # Используем глобальный пул потоков вместо создания отдельных потоков
            futures = []
            for item_details in items_to_process_this_batch:
                future = thread_pool_manager.submit(
                    process_forwarded_audio_item,
                    item_details["user_id"],
                    item_details["chat_id"],
                    item_details["file_id"],
                    item_details["duration"],
                    item_details["original_message_id"],
                    item_details["file_type"],
                    item_details["forwarder_name"],
                    bot,
                )
                futures.append(future)

            # Ждем завершения всех задач в батче
            for future in futures:
                try:
                    future.result()  # Ждем завершения и получаем результат
                except Exception as e:
                    log_admin(f"Error in forwarded audio processing: {e}", level="error")

            log_admin(
                f"Forwarded audio worker finished processing batch of 2 items using global thread pool."
            )

        elif len(items_to_process_this_batch) == 1:
            item_details = items_to_process_this_batch[0]
            try:
                process_forwarded_audio_item(
                    item_details["user_id"],
                    item_details["chat_id"],
                    item_details["file_id"],
                    item_details["duration"],
                    item_details["original_message_id"],
                    item_details["file_type"],
                    item_details["forwarder_name"],
                    bot,
                )
            except Exception as e:
                log_admin(
                    f"Error processing single item {item_details.get('original_message_id')} from forwarded audio queue: {e}\n{traceback.format_exc()}",
                    level="error",
                )
                try:
                    bot.send_message(
                        item_details["chat_id"],
                        f"⚠️ Не удалось обработать пересланное сообщение (ID: {item_details['original_message_id']}). Ошибка: {str(e)[:100]}",
                        reply_to_message_id=item_details["original_message_id"],
                    )
                except Exception as e_notify:
                    log_admin(
                        f"Failed to notify user about item processing error: {e_notify}",
                        level="warning",
                    )
            log_admin(f"Forwarded audio worker finished processing 1 item.")

    log_admin("Forwarded audio queue worker finished a cycle or exited.")


# --- Forwarded Text Batch Processing ---
def process_forwarded_batch(user_id):
    with user_forward_batch_lock:
        if user_id not in user_forward_batch:
            return
        batch_data = user_forward_batch.pop(user_id)  # Pop to prevent reprocessing
        messages = batch_data["messages"]
        chat_id = batch_data["chat_id"]

    user_info_log = f"user {user_id}"
    log_admin(
        f"{user_info_log} - processing forwarded batch with {len(messages)} messages."
    )
    if not messages:
        log_admin(f"{user_info_log} - batch was empty.")
        return

    try:
        bot.send_chat_action(chat_id, "typing")
    except Exception as e_action:
        log_admin(
            f"{user_info_log} - Error sending 'typing' action in process_forwarded_batch: {e_action}",
            level="warning",
        )

    combined_text = ""
    original_message_ids = []
    # Sort messages by timestamp to ensure correct order
    messages.sort(key=lambda x: x["timestamp"])
    first_forwarder_name = messages[0]["forwarder_name"] if messages else "Пользователь"

    for i, msg_data in enumerate(messages):
        text = msg_data["text"]
        original_message_ids.append(msg_data["message_id"])
        current_forwarder = msg_data["forwarder_name"]  # Already escaped in handler

        # Add context with forwarder name for each message if multiple, or just the first if only one distinct forwarder
        # For simplicity, now always adding the forwarder name.
        combined_text += f"--- Сообщение от {current_forwarder} ---\n{text}\n\n"

    combined_text = combined_text.strip()
    last_message_id_in_batch = (
        original_message_ids[-1] if original_message_ids else None
    )

    if combined_text:
        log_admin(
            f"{user_info_log} - combined forwarded text length: {len(combined_text)}. Processing."
        )
        # Process the combined text as a normal text request
        process_text_or_photo_request(
            user_id,
            chat_id,
            combined_text,
            single_image_data=None,
            original_message_id=last_message_id_in_batch,  # Reply to the last message in the batch
            # No specific file_context or reply_context for batched forwards
        )
    else:
        log_admin(
            f"{user_info_log} - combined forwarded text is empty after processing batch."
        )


# --- Request Buffer Processing (Handles quickly sent text/photos) ---
def process_request_buffer(user_id):
    with user_request_buffer_lock:
        if user_id not in user_request_buffer:
            return
        buffer_data = user_request_buffer.pop(user_id)
        messages_to_process = buffer_data["messages"]
        chat_id = buffer_data["chat_id"]

    user_info_log = f"user {user_id}"
    log_admin(
        f"{user_info_log} - processing request buffer with {len(messages_to_process)} items."
    )
    if not messages_to_process:
        log_admin(f"{user_info_log} - request buffer was empty.")
        return

    messages_to_process.sort(key=lambda x: x["timestamp"])

    combined_text_parts = []
    all_images_from_buffer = []
    last_message_id_for_reply = None
    file_name_for_history = None  # Not currently used here but good for consistency
    overall_thinking_message_id = None

    for msg_data in messages_to_process:
        text_part = msg_data.get("user_text", "")
        image_data_part = msg_data.get("image_data")
        msg_id_part = msg_data.get("message_id")
        thinking_id_part = msg_data.get("thinking_message_id")

        if thinking_id_part:
            overall_thinking_message_id = thinking_id_part
        if text_part:
            combined_text_parts.append(text_part)
        if image_data_part:
            all_images_from_buffer.append(image_data_part)
        if msg_id_part:
            last_message_id_for_reply = msg_id_part

    final_combined_text = "\n\n".join(combined_text_parts).strip()
    MAX_IMAGES_FOR_GPT41_VISION = 10
    images_to_send_to_gpt41 = (
        all_images_from_buffer[:MAX_IMAGES_FOR_GPT41_VISION]
        if all_images_from_buffer
        else None
    )

    log_admin(
        f"{user_info_log} - combined buffer: text length={len(final_combined_text)}, num_images={len(images_to_send_to_gpt41 if images_to_send_to_gpt41 else [])}, reply_to={last_message_id_for_reply}"
    )

    # Остановить typing менеджеры для всех буферизованных сообщений кроме последнего
    if hasattr(bot, '_instant_typing_managers') and len(messages_to_process) > 1:
        from utils import stop_instant_typing_if_any
        for msg in messages_to_process[:-1]:  # Все кроме последнего
            msg_id = msg.get("message_id")
            if msg_id:
                stop_instant_typing_if_any(bot, chat_id, msg_id)

    if final_combined_text or images_to_send_to_gpt41:
        process_text_or_photo_request(
            user_id,
            chat_id,
            final_combined_text,
            images_to_send_to_gpt41,
            original_message_id=last_message_id_for_reply,
            initial_status_message_id=overall_thinking_message_id,
        )
    else:
        log_admin(f"{user_info_log} - buffer resulted in empty text and no image.")


# --- Main Request Processor (Text, Photo, File Query) ---
def process_text_or_photo_request(
    user_id,
    chat_id,
    user_text,
    single_image_data,
    original_message_id=None,
    is_file_query=False,
    file_context=None,
    reply_context=None,
    initial_status_message_id=None,
    file_name_for_history=None,
    user_info=None,
):
    # Check private message limits for AI responses (only in private chats)
    if chat_id == user_id:  # Only check limits in private chats
        from rate_limiter import rate_limiter, format_time_remaining
        from admin_system import is_admin

        if not is_admin(user_id):  # Admins bypass all limits
            allowed, reason, wait_time = rate_limiter.check_private_limit(user_id, 'ai_response', 80)

            if not allowed:
                try:
                    bot.send_message(
                        chat_id,
                        f"🤖 {reason}",
                        reply_to_message_id=original_message_id
                    )
                except Exception as e:
                    log_admin(f"user {user_id} - Error sending AI response limit message: {e}")
                return

            # Record the AI response request
            rate_limiter.record_private_request(user_id, 'ai_response')

    # Ensure all relevant flags are initialized at the beginning
    internet_search_triggered = False
    is_initial_search_intent = False
    action_tag_detected = None
    message_already_handled_by_llm = False

    user_info_log = f"user {user_id}"
    
    # Determine chat type early for reaction logic
    is_group_chat = chat_id != user_id
    
    # SET LIGHTNING REACTION IMMEDIATELY for better UX
    # Check if reaction was already set (e.g., by handlers.py for private chats or /sh commands)
    user_message_already_had_reaction = (
        initial_status_message_id is not None
        and initial_status_message_id == original_message_id
    ) and original_message_id is not None
    
    lightning_reaction_set_by_ptpr = False
    
    # Don't set any reaction immediately - wait for model determination
    log_admin(f"{user_info_log} - Reaction will be set after model determination")

    # Ensure all relevant flags are initialized at the beginning
    action_tag_detected = None  # For _IMG_ or _RED_ tags

    user_info_log = f"user {user_id}"

    # This function now handles both private and group chats.

    # Get current Moscow time for system prompts
    from config import get_moscow_datetime
    current_datetime_msk = get_moscow_datetime()

    # Сохраняем оригинальный текст пользователя для Gemma (без ника)
    original_user_text = user_text



    if is_group_chat:
        # Get user information for group chat system prompt
        try:
            user_info = bot.get_chat(user_id)
            user_first_name = user_info.first_name or ""
            user_last_name = user_info.last_name or ""
            user_username = user_info.username or ""

            # Format user info string for groups
            user_info_parts = []
            if user_first_name or user_last_name:
                full_name = f"{user_first_name} {user_last_name}".strip()
                user_info_parts.append(f"Имя: {full_name}")
            if user_username:
                user_info_parts.append(f"Username: @{user_username}")

            user_info_str = ", ".join(user_info_parts) if user_info_parts else "Информация недоступна"

        except Exception as e:
            log_admin(f"{user_info_log} - Error getting user info for group system prompt: {e}")
            user_info_str = "Информация недоступна"

        # Get user custom rules
        user_rules = get_user_setting(user_id, 'custom_rules')
        user_rules_section = ""
        if user_rules:
            # Format rules for insertion into prompt
            rules_text = "\n".join([f"• {rule}" for rule in user_rules])
            user_rules_section = (
                f"\n\nДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА ОТ ПОЛЬЗОВАТЕЛЯ (ИМЕЮТ ВЫСШИЙ ПРИОРИТЕТ):\n"
                f"{rules_text}\n"
            )
            log_admin(f"{user_info_log} - User has {len(user_rules)} custom rules. Adding to system prompt.")

        system_prompt_to_use = SYSTEM_PROMPT_GROUP.format(
            current_datetime_msk=current_datetime_msk,
            user_info=user_info_str,
            user_rules_section=user_rules_section
        )
        context_key = chat_id  # Use chat_id for group context (общий контекст для всех пользователей группы)
        log_admin(f"{user_info_log} - Group chat detected. Using GROUP prompt with user info and shared group context (context_key: {context_key})", level="info")
    else:
        # Get user information for private chat system prompt
        try:
            user_info = bot.get_chat(user_id)
            user_first_name = user_info.first_name or ""
            user_last_name = user_info.last_name or ""
            user_username = user_info.username or ""

            # Format user info string
            user_info_parts = []
            if user_first_name or user_last_name:
                full_name = f"{user_first_name} {user_last_name}".strip()
                user_info_parts.append(f"Имя: {full_name}")
            if user_username:
                user_info_parts.append(f"Username: @{user_username}")

            user_info_str = ", ".join(user_info_parts) if user_info_parts else "Информация недоступна"

        except Exception as e:
            log_admin(f"{user_info_log} - Error getting user info for system prompt: {e}")
            user_info_str = "Информация недоступна"

        # Get user custom rules
        user_rules = get_user_setting(user_id, 'custom_rules')
        user_rules_section = ""
        if user_rules:
            # Format rules for insertion into prompt
            rules_text = "\n".join([f"• {rule}" for rule in user_rules])
            user_rules_section = (
                f"\n\nДОПОЛНИТЕЛЬНЫЕ ПРАВИЛА ОТ ПОЛЬЗОВАТЕЛЯ (ИМЕЮТ ВЫСШИЙ ПРИОРИТЕТ):\n"
                f"{rules_text}\n"
            )
            log_admin(f"{user_info_log} - User has {len(user_rules)} custom rules. Adding to system prompt.")

        system_prompt_to_use = SYSTEM_PROMPT_MAIN.format(
            current_datetime_msk=current_datetime_msk,
            user_info=user_info_str,
            user_rules_section=user_rules_section
        )
        context_key = user_id  # Use user_id for individual context
        log_admin(f"{user_info_log} - Private chat detected. Using main prompt with user info and individual context (context_key: {context_key})", level="info")

    # Determine if '⚡' reaction is already on the user's message
    # This is true if initial_status_message_id (passed from buffer or direct audio handler)
    # Handle bot status message deletion logic (moved from reaction block)
    bot_status_message_to_delete = None  # Stores ID of an old bot status message if initial_status_message_id was one

    if (
        not user_message_already_had_reaction
        and initial_status_message_id
        and initial_status_message_id != original_message_id
        and not is_file_query
    ):
        # initial_status_message_id was a bot's message from a previous stage (e.g. buffer)
        # and it's not a file query (file queries might have their own specific status messages we want to keep for now)
        bot_status_message_to_delete = initial_status_message_id
        log_admin(
            f"{user_info_log} - Initial status message ID {bot_status_message_to_delete} was a bot message. Will attempt to delete it."
        )

    # Delete old bot status message if needed (moved from reaction block)
    if bot_status_message_to_delete and lightning_reaction_set_by_ptpr:
        try:
            bot.delete_message(
                chat_id=chat_id, message_id=bot_status_message_to_delete
            )
            log_admin(
                f"{user_info_log} - Deleted old bot status message {bot_status_message_to_delete}."
            )
            bot_status_message_to_delete = None  # Clear it as it's handled
        except Exception as e_del_old_status:
            log_admin(
                f"{user_info_log} - Failed to delete old bot status message {bot_status_message_to_delete}: {e_del_old_status}"
            )

    # bot_thinking_message_id is largely deprecated for general status.
    # For file queries, specific status messages are still used for now.
    bot_thinking_message_id = None
    if (
        is_file_query
        and initial_status_message_id
        and initial_status_message_id != original_message_id
    ):
        bot_thinking_message_id = initial_status_message_id  # Preserve specific file processing status message
        log_admin(
            f"{user_info_log} - Preserving specific file query status message ID {bot_thinking_message_id}."
        )
    elif (
        is_file_query and not initial_status_message_id
    ):  # If file query didn't have an initial status (e.g. direct call)
        try:
            temp_status_msg = bot.send_message(
                chat_id,
                STATUS_MESSAGES.get("PROCESSING_FILE", "⏳ Обработка файла..."),
                reply_to_message_id=original_message_id,
                parse_mode="HTML",
            )
            bot_thinking_message_id = temp_status_msg.message_id
            log_admin(
                f"{user_info_log} - Sent new status message for file query. ID: {bot_thinking_message_id}"
            )
        except Exception as e_send_fq_status:
            log_admin(
                f"{user_info_log} - Error sending new status for file query: {e_send_fq_status}"
            )

    # Clear any remaining bot_status_message_to_delete if it wasn't handled (e.g. PTPR didn't set reaction due to file query)
    if (
        bot_status_message_to_delete
        and bot_status_message_to_delete != bot_thinking_message_id
    ):
        try:
            bot.delete_message(chat_id=chat_id, message_id=bot_status_message_to_delete)
            log_admin(
                f"{user_info_log} - Cleaned up unhandled bot_status_message_to_delete: {bot_status_message_to_delete}."
            )
        except Exception as e_del_cleanup:
            log_admin(
                f"{user_info_log} - Failed to clean up bot_status_message_to_delete {bot_status_message_to_delete}: {e_del_cleanup}"
            )

    original_raw_response_text = ""
    final_response_to_send = ""
    history_entry_model_text = ""
    # Default to MODEL_MAIN, will be updated if fallback occurs or override is set
    from config import MODEL_MAIN
    chosen_model_for_call = MODEL_MAIN


    prompt_for_llm_call = user_text
    history_note_for_file = None

    if is_file_query and file_context:
        file_name_display = file_name_for_history if file_name_for_history else "файла"
        prompt_for_llm_call = f"Контекст из файла '{file_name_display}':\n```\n{file_context}\n```\n\nЗапрос пользователя:\n{user_text}"
        history_note_for_file = (
            f"[Контекст из файла '{file_name_display}' был использован]"
        )
        log_admin(
            f"{user_info_log} - constructed prompt for file query on '{file_name_display}'."
        )
    elif reply_context and reply_context.get("is_reply_to_bot") and reply_context.get("reply_context_text"):
        # This is a reply to bot message with context
        reply_text = reply_context["reply_context_text"]
        prompt_for_llm_call = f"Контекст сообщения, на которое отвечает пользователь:\n```\n{reply_text}\n```\n\nОтвет пользователя:\n{user_text}"
        log_level = "debug" if is_group_chat else "info"
        log_admin(
            f"{user_info_log} - constructed prompt for reply to bot message {reply_context.get('replied_to_message_id')}.",
            level=log_level
        )

    typing_manager = None
    try:
        if chat_id and bot:  # bot is imported from bot_globals
            # Check if instant typing manager is already running for private chats
            instant_typing_key = f"{chat_id}_{original_message_id}"
            is_private_chat = (chat_id == user_id)

            if is_private_chat and hasattr(bot, '_instant_typing_managers') and instant_typing_key in bot._instant_typing_managers:
                # Use the existing instant typing manager
                typing_manager = bot._instant_typing_managers[instant_typing_key]
                log_admin(f"{user_info_log} - Using existing instant typing manager for private chat")
            else:
                # Create new typing manager for group chats or if instant manager not found
                typing_manager = TypingStatusManager(bot, chat_id)
                typing_manager.start()
                log_admin(f"{user_info_log} - Started new typing manager")

        # Get conversation history for API call BEFORE adding the current user message
        with user_conversations_lock:
            history_for_api_call = list(user_conversations.get(context_key, []))
            log_admin(f"{user_info_log} - Retrieved conversation history with {len(history_for_api_call)} messages for context_key: {context_key}", level="info")

        # Prepare user message parts for history (this will be added AFTER the API call)
        current_user_parts_for_history = []

        # Sanitize user text (remove control characters)
        sanitized_current_user_text = (
            re.sub(r"[\x00-\x1f\x7f-\x9f]", "", user_text) if user_text else ""
        )

        # Add text content and file context if available
        if is_file_query and history_note_for_file:
            if sanitized_current_user_text:
                current_user_parts_for_history.append({"text": sanitized_current_user_text})
            current_user_parts_for_history.append({"text": history_note_for_file})
        elif sanitized_current_user_text:
            current_user_parts_for_history.append({"text": sanitized_current_user_text})

        # Add image content if available
        if single_image_data:
            images_to_add_to_hist = (
                single_image_data
                if isinstance(single_image_data, list)
                else [single_image_data]
            )
            for img_data_hist in images_to_add_to_hist[:10]:  # Max 10 images in history
                if (
                    isinstance(img_data_hist, dict)
                    and "mime_type" in img_data_hist
                    and "data" in img_data_hist
                ):
                    current_user_parts_for_history.append(
                        {
                            "inline_data": {
                                "mime_type": img_data_hist["mime_type"],
                                "data": img_data_hist["data"],
                            }
                        }
                    )
                else:
                    # Handle simple base64 string format
                    current_user_parts_for_history.append(
                        {
                            "inline_data": {
                                "mime_type": "image/jpeg",
                                "data": img_data_hist
                            }
                        }
                    )

            # Log first few messages for debugging
            if history_for_api_call:
                for i, msg in enumerate(history_for_api_call[-3:]):  # Show last 3 messages
                    role = msg.get("role", "unknown")
                    parts = msg.get("parts", [])
                    text_preview = ""
                    if parts and isinstance(parts, list) and len(parts) > 0:
                        first_part = parts[0]
                        if isinstance(first_part, dict) and "text" in first_part:
                            text_preview = first_part["text"][:100] + "..." if len(first_part["text"]) > 100 else first_part["text"]
                    log_admin(f"{user_info_log} - History msg {i}: role={role}, text_preview='{text_preview}'", level="debug")

        # For group chats, use only conversation history (bot-user interactions only)
        if is_group_chat:
            log_admin(f"{user_info_log} - Using conversation history for group chat (bot-user interactions only)", level="debug")

        # Check for model override - first check chat override (for groups), then user override
        forced_model_override = None
        if is_group_chat:
            # For group chats, check chat-specific model override first
            forced_model_override = chat_model_override.get(chat_id)
            if forced_model_override:
                log_admin(
                    f"{user_info_log} - Using CHAT model override for group {chat_id}: {forced_model_override}."
                )

        # If no chat override, check user override
        if not forced_model_override:
            forced_model_override = user_model_override.get(user_id)
            if forced_model_override:
                log_admin(
                    f"{user_info_log} - Using USER model override: {forced_model_override}."
                )

        if forced_model_override:
            chosen_model_for_call = forced_model_override
        else:
            log_admin(
                f"{user_info_log} - No model override. Defaulting to primary: {chosen_model_for_call}."
            )

        # Calculate initial intent flags
        # {{HARD}} intent removed: if not is_initial_hard_intent: is_initial_hard_intent = "{{HARD}}" in user_text

        # Old general status message logic (⏳, Поиск...) is removed.
        # '⚡' reaction on user's message or specific file query status messages are used instead.
        # The bot_thinking_message_id is now primarily for specific file query status messages.

        # --- INTELLIGENT MODEL SELECTION FOR PRIVATE CHATS ---
        # Определяем модель на основе сложности запроса (только для личных чатов)
        final_model_for_call = chosen_model_for_call
        
        if not is_group_chat and user_text and isinstance(user_text, str):  # Только для личных чатов с текстом
            # Проверяем режимы ultrathink и ultrapro
            try:
                ultrathink_enabled = get_user_setting(user_id, "ultrathink_enabled")
                ultrapro_enabled = get_user_setting(user_id, "ultrapro_enabled")
                
                if ultrathink_enabled:
                    # Если включен ultrathink - всегда используем Gemini 2.5 Pro
                    final_model_for_call = MODEL_GEMINI_2_5_PRO
                    log_admin(f"{user_info_log} - Ultrathink enabled, using Gemini 2.5 Pro directly")
                elif ultrapro_enabled:
                    # Если включен ultrapro - всегда используем Gemini 2.5 Pro
                    final_model_for_call = MODEL_GEMINI_2_5_PRO
                    log_admin(f"{user_info_log} - Ultrapro enabled, using Gemini 2.5 Pro directly")
                else:
                    # Классифицируем сложность запроса через Llama 4 Maverick
                    try:
                        from cerebras_client import classify_query_complexity
                        complexity = classify_query_complexity(user_text)
                        
                        if complexity == "LITE":
                            final_model_for_call = MODEL_GEMINI_2_5_FLASH_LITE
                            log_admin(f"{user_info_log} - Query classified as LITE, using Gemini 2.5 Flash Lite")
                        elif complexity == "ULTRAPRO":
                            final_model_for_call = MODEL_GEMINI_2_5_PRO
                            log_admin(f"{user_info_log} - Query classified as ULTRAPRO, using Gemini 2.5 Pro with enhanced reasoning")
                        else:  # HARD
                            final_model_for_call = MODEL_GEMINI_2_5_PRO
                            log_admin(f"{user_info_log} - Query classified as HARD, using Gemini 2.5 Pro")
                            
                    except Exception as e:
                        log_admin(f"{user_info_log} - Error in complexity classification: {e}, defaulting to Gemini 2.5 Pro", level="error")
                        final_model_for_call = MODEL_GEMINI_2_5_PRO
                        
            except Exception as e:
                log_admin(f"{user_info_log} - Error checking ultrathink/ultrapro settings: {e}, using default model", level="error")
        
        # --- Update Reaction Based on Model Selection (Private Chats Only) ---
        # Only update reaction for text messages with Pro model (after 1 second delay)
        if not is_group_chat and original_message_id and not single_image_data:  # Only for private chats with text (not photos)
            def update_reaction_after_delay():
                try:
                    from utils import set_reaction

                    ultrathink_enabled = get_user_setting(user_id, "ultrathink_enabled")
                    ultrapro_enabled = get_user_setting(user_id, "ultrapro_enabled")
                    
                    # Only update reaction for text messages when Pro model is selected and special modes are disabled
                    if not ultrathink_enabled and not ultrapro_enabled and final_model_for_call == MODEL_GEMINI_2_5_PRO:
                        # Hard model (Pro) - replace ⚡ with 👨‍💻
                        set_reaction(bot, chat_id, original_message_id, "👨‍💻")
                        log_admin(f"{user_info_log} - Updated reaction to 👨‍💻 for Pro model after 1 second")
                    else:
                        # No update needed - reaction is already correct
                        log_admin(f"{user_info_log} - No reaction update needed")
                            
                except Exception as e:
                    log_admin(f"{user_info_log} - Error updating reaction based on model: {e}", level="warning")
            
            # Schedule reaction update after 1 second only if Pro model is selected
            if final_model_for_call == MODEL_GEMINI_2_5_PRO:
                import threading
                timer = threading.Timer(1.0, update_reaction_after_delay)
                timer.start()
                log_admin(f"{user_info_log} - Scheduled reaction update to 👨‍💻 in 1 second for Pro model")
        
        # --- Primary API Call ---
        # Инициализируем переменные для стриминга
        message_already_handled_by_llm = False
        final_bot_response_id = None

        # Используем стриминг для личных чатов, обычный вызов для групп
        if not is_group_chat and not DISABLE_STREAMING:
            log_admin(f"{user_info_log} - Attempting STREAMING API call with {final_model_for_call} for private chat...")
            from api_clients import call_llm_stream
            from utils import StreamingMessageManager

            stream_manager = StreamingMessageManager(
                bot_instance=bot,
                chat_id=chat_id,
                reply_to_message_id=original_message_id
            )

            try:
                response_stream = call_llm_stream(
                    final_model_for_call, history_for_api_call, prompt_for_llm_call,
                    single_image_data, system_prompt_to_use,
                    call_type="file_query" if is_file_query else "general_primary",
                    user_id=user_id
                )

                accumulated_response = ""
                for chunk in response_stream:
                    if chunk:
                        accumulated_response += chunk
                        stream_manager.update_stream(chunk)

                final_bot_response_id = stream_manager.finish_stream(
                    user_id=user_id,
                    enable_shortening=True  # Включаем кнопки сокращения для стриминг ответов
                )

                if final_bot_response_id and accumulated_response.strip():
                    message_already_handled_by_llm = True
                    original_raw_response_text = accumulated_response
                else:
                    # Если стрим был пустой, переходим на обычный вызов
                    raise Exception("Streaming resulted in an empty response.")

            except Exception as e:
                log_admin(f"{user_info_log} - Streaming failed: {e}. Falling back to regular call.", level="warning")
                stream_manager.cancel_stream()
                original_raw_response_text = call_llm(
                    final_model_for_call, history_for_api_call, prompt_for_llm_call,
                    single_image_data, system_prompt_to_use,
                    call_type="file_query" if is_file_query else "general_primary",
                    is_private_chat=True, user_id=user_id,
                )
        else:
            # ОБЫЧНЫЙ ВЫЗОВ ДЛЯ ГРУППОВЫХ ЧАТОВ
            log_admin(
                f"{user_info_log} - Attempting regular API call with {final_model_for_call} for group chat..."
            )
            original_raw_response_text = call_llm(
                final_model_for_call,
                history_for_api_call,
                prompt_for_llm_call,
                single_image_data,
                system_prompt_to_use,
                call_type="file_query" if is_file_query else "general_primary",
                is_private_chat=False,
                user_id=user_id,
            )
        log_admin(
            f"{user_info_log} - Raw response from primary LLM ({final_model_for_call}): '{str(original_raw_response_text)[:200]}...'",
            level="debug",
        )

        # --- Fallback Logic ---
        # Trigger fallback if the chosen model failed AND it was one of the lighter models
        primary_call_failed = not original_raw_response_text or (
            isinstance(original_raw_response_text, str)
            and (
                original_raw_response_text.startswith("ОШИБКА")
                or original_raw_response_text.startswith("Ошибка:")
                or "Ошибка: Не удалось связаться с сервером ИИ"
                in original_raw_response_text
                or "Ошибка: Внутренняя ошибка сервера ИИ" in original_raw_response_text
                or original_raw_response_text == "ОШИБКА_ПРОВАЙДЕРА_GEMINI"
                or is_censorship_error(original_raw_response_text)  # Добавлена проверка на 41 символ
            )
        )

        # Fallback only if we used a lighter model and it failed
        should_fallback = (final_model_for_call in ["gemini-2.5-flash", MODEL_GEMINI_2_5_FLASH_LITE] and primary_call_failed)

        if should_fallback:
            # Специальное логирование для retry случаев
            if isinstance(original_raw_response_text, str) and is_censorship_error(original_raw_response_text):
                log_admin(
                    f"{user_info_log} - Primary call to {final_model_for_call} returned 41-char censorship error after retry attempts: '{original_raw_response_text}'. Attempting fallback.",
                    level="warning",
                )
            else:
                log_admin(
                    f"{user_info_log} - Primary call to {final_model_for_call} failed or returned error: '{str(original_raw_response_text)[:200]}'. Attempting fallback.",
                    level="warning",
                )

            modified_system_prompt_lines = [
                line
                for line in system_prompt_to_use.split("\n")
                if not (
                    "{{HARD}}" in line
                    and (
                        "Если задача расценивается как тяжелая" in line
                        or "Если задача расценивается как тяжёлая" in line
                    )
                )
            ]
            modified_system_prompt_for_fallback = "\n".join(
                modified_system_prompt_lines
            )

            # Using call_gemini_api directly as per subtask step 3.4
            # Fallback to MODEL_MAIN
            fallback_model_name = MODEL_MAIN
            log_admin(
                f"{user_info_log} - Calling fallback model: {fallback_model_name} using call_gemini_api."
            )

            fallback_response_text = call_gemini_api(  # Using call_gemini_api
                model_name=fallback_model_name,
                history=history_for_api_call,  # history_for_api_call already prepared
                user_text=prompt_for_llm_call,  # prompt_for_llm_call is the full user query
                input_data=single_image_data,
                system_prompt=modified_system_prompt_for_fallback,
                call_type="general_fallback_gemini_flash",
                user_id=user_id,  # Pass user_id for ultrathink setting
            )
            log_admin(
                f"{user_info_log} - Raw response from FALLBACK model ({fallback_model_name}): '{str(fallback_response_text)[:200]}...'",
                level="debug",
            )

            if fallback_response_text and not (
                isinstance(fallback_response_text, str)
                and fallback_response_text.startswith("ОШИБКА")
            ):
                original_raw_response_text = fallback_response_text
                chosen_model_for_call = fallback_model_name  # Update chosen_model_for_call to the fallback model name
                log_admin(
                    f"{user_info_log} - Fallback to {fallback_model_name} succeeded. Using its response.",
                    level="info",
                )
            else:
                log_admin(
                    f"{user_info_log} - Fallback to {fallback_model_name} also failed or returned error: '{str(fallback_response_text)[:200]}'. Sticking with original error from gemini-2.5-flash.",
                    level="error",
                )
                # original_raw_response_text remains the error from the primary gemini-2.5-flash call

        if isinstance(original_raw_response_text, str) and not original_raw_response_text.startswith("ОШИБКА"):
            # --- Image‑tool check ---
            from utils import extract_image_tool_call, set_reaction, remove_reaction, fix_image_gen_format

            # Логируем ответ ИИ для отладки генерации изображений
            if "image" in original_raw_response_text.lower() or "картин" in original_raw_response_text.lower() or "рисун" in original_raw_response_text.lower():
                log_admin(f"{user_info_log} - AI response contains image-related content: {original_raw_response_text[:200]}...", level="debug")

            # Исправляем неправильные форматы генерации изображений
            original_raw_response_text = fix_image_gen_format(original_raw_response_text)

            call = extract_image_tool_call(original_raw_response_text)
            if call:
                prompt, size = call

                # 🍌 reaction on user message
                try:
                    set_reaction(bot, chat_id, original_message_id, "🍌")
                except Exception:
                    pass

                # 🔄 show "sending photo" status in Telegram
                try:
                    bot.send_chat_action(chat_id, "upload_photo")
                except Exception as e_chat_action:
                    log_admin(f"{user_info_log} - Could not set chat action 'upload_photo': {e_chat_action}", level="debug")

                from api_clients import call_navy_image_generate_api_sync
                url = call_navy_image_generate_api_sync(prompt, size)
                if url:
                    bot.send_photo(chat_id, url, reply_to_message_id=original_message_id)
                else:
                    bot.send_message(
                        chat_id,
                        "⚠️ Не удалось сгенерировать изображение.",
                        reply_to_message_id=original_message_id,
                    )

                # remove 🎨 reaction
                try:
                    remove_reaction(bot, chat_id, original_message_id)
                except Exception:
                    pass

                # -----------------------------------------------------------
                # 🔗  ADD USER & MODEL MESSAGES (image‑tool call) TO CONTEXT
                # -----------------------------------------------------------
                history_entry_model_text = (
                    original_raw_response_text
                    if isinstance(original_raw_response_text, str)
                    else "[image_gen]"
                )

                if current_user_parts_for_history:
                    with user_conversations_lock:
                        if context_key not in user_conversations:
                            user_conversations[context_key] = []
                        user_conversations[context_key].append(
                            {"role": "user", "parts": current_user_parts_for_history}
                        )

                if history_entry_model_text and str(history_entry_model_text).strip():
                    with user_conversations_lock:
                        user_conversations[context_key].append(
                            {"role": "model", "parts": [{"text": str(history_entry_model_text)}]}
                        )

                with user_last_response_lock:
                    user_last_response[user_id] = history_entry_model_text
                # -----------------------------------------------------------

                return  # изображение уже отправили, текст‑ответ не нужен
            else:
                # Если тег не найден, но в ответе есть упоминания об изображениях
                if ("image" in original_raw_response_text.lower() or
                    "картин" in original_raw_response_text.lower() or
                    "рисун" in original_raw_response_text.lower() or
                    "generate_images" in original_raw_response_text.lower() or
                    "print(" in original_raw_response_text.lower()):
                    log_admin(f"{user_info_log} - AI response mentions images but no valid image_gen tag found. Response: {original_raw_response_text[:300]}...", level="warning")

            # Standard response
            cleaned_response = clean_response_text(original_raw_response_text)

            # Parse FILE tags from the cleaned response
            final_response_to_send, files_to_send = parse_file_tags(cleaned_response)
            history_entry_model_text = final_response_to_send
        else:
            # This case handles non-string responses from call_llm (e.g. direct errors, None)
            # OR if the primary call failed AND the fallback also failed to produce a usable string.
            error_msg_from_llm = (
                str(original_raw_response_text)
                if original_raw_response_text
                else "Неизвестная ошибка от API."
            )
            final_response_to_send = (
                error_msg_from_llm
                if (
                    error_msg_from_llm.startswith("ОШИБКА")
                    or error_msg_from_llm.startswith("Ошибка:")
                )
                else "ОШИБКА: Не удалось получить корректный ответ от модели."
            )
            history_entry_model_text = final_response_to_send  # Log the error

            # If an error occurred, '⚡' reaction on user's message (if any) remains.
            # Error message is edited into bot_thinking_message_id or sent as new by send_long_message.
            # No removal of '⚡' from user's message here.
            if bot_thinking_message_id:
                result = safe_bot_api_call(
                    bot.edit_message_text,
                    final_response_to_send,
                    chat_id=chat_id,
                    message_id=bot_thinking_message_id,
                    parse_mode="HTML" if "<" in final_response_to_send else None,
                )
                if result is not None:
                    message_already_handled_by_llm = (
                        True  # Error was edited into existing bot message
                    )
                else:  # Edit failed
                    log_admin(
                        f"{user_info_log} - Failed to edit bot_thinking_message_id {bot_thinking_message_id} with error. Will be sent as new by send_long_message."
                    )
                    bot_thinking_message_id = (
                        None  # Force send_long_message to send new
                    )
            # If no bot_thinking_message_id, send_long_message will send the error as a new message anyway.

        # If hard_tag_found, ensure it's removed from history entry regardless of other tags - REMOVED
        # if hard_tag_found and history_entry_model_text:
        #      history_entry_model_text = history_entry_model_text.replace("{{HARD}}", "").strip()

        # --- Update History ---
        # Add user message and model response to conversation history AFTER successful API call

        # Check if the final response is a provider error for the chosen model
        is_final_response_provider_error = False
        if (
            chosen_model_for_call == "gemini-2.5-flash"
            and original_raw_response_text == "ОШИБКА_ПРОВАЙДЕРА_GEMINI"
        ):
            is_final_response_provider_error = True
        # Add similar checks if call_gemini_api can return specific provider error strings

        # First, add user message to history
        if current_user_parts_for_history:
            with user_conversations_lock:
                # Ensure context_key exists in user_conversations (defaultdict should handle this)
                if context_key not in user_conversations:
                    user_conversations[context_key] = []
                    log_admin(f"{user_info_log} - Initialized new conversation history for context_key: {context_key}", level="debug")

                user_conversations[context_key].append(
                    {"role": "user", "parts": current_user_parts_for_history}
                )
                log_admin(f"{user_info_log} - Added user message to conversation history (context_key: {context_key})", level="debug")

        # Then, add model response to history if it's valid
        if (
            history_entry_model_text
            and history_entry_model_text.strip()
            and not history_entry_model_text.startswith("ОШИБКА_МОДЕЛИ")
            and not is_final_response_provider_error
            and not "ОШИБКА" in history_entry_model_text  # More general error check
        ):
            with user_conversations_lock:
                user_conversations[context_key].append(
                    {"role": "model", "parts": [{"text": history_entry_model_text}]}
                )
                log_admin(f"{user_info_log} - Added model response to conversation history (context_key: {context_key}, response_length: {len(history_entry_model_text)})", level="debug")
        else:
            log_admin(f"{user_info_log} - Model response NOT added to history (error or empty response)", level="debug")

        with user_last_response_lock:
            user_last_response[user_id] = history_entry_model_text

        # --- Final Message Sending & Button Logic ---
        # final_bot_response_id уже инициализирован выше



        if (
            not message_already_handled_by_llm
            and final_response_to_send
            and isinstance(final_response_to_send, str)
            and final_response_to_send.strip()
        ):
            log_admin(
                f"{user_info_log} - Sending final response via send_long_message (either not an internet search, edit failed, or no status message to edit)."
            )
            
            # Используем разные функции для групп и приватных чатов
            if is_group_chat:
                final_bot_response_id = send_multiple_messages_with_break(
                    bot, chat_id, final_response_to_send, original_message_id, user_id=user_id
                )
            else:
                final_bot_response_id = send_long_message(
                    bot, chat_id, final_response_to_send, original_message_id, user_id=user_id
                )
            if final_bot_response_id:
                log_admin(
                    f"{user_info_log} - {'send_multiple_messages_with_break' if is_group_chat else 'send_long_message'} sent final response. Bot message ID: {final_bot_response_id}."
                )
                message_already_handled_by_llm = True

                # Send parsed files if any were found
                if 'files_to_send' in locals() and files_to_send:
                    try:
                        sent_file_ids = send_parsed_files(
                            bot, chat_id, files_to_send,
                            reply_to_message_id=original_message_id,
                            user_id=user_id
                        )
                        if sent_file_ids:
                            log_admin(f"{user_info_log} - Successfully sent {len(sent_file_ids)} files from FILE tags")
                    except Exception as e_send_files:
                        log_admin(f"{user_info_log} - Error sending parsed files: {e_send_files}", level="error")
            else:
                log_admin(
                    f"{user_info_log} - {'send_multiple_messages_with_break' if is_group_chat else 'send_long_message'} failed to send final response.",
                    level="error",
                )

        elif message_already_handled_by_llm and not final_bot_response_id:
            # If it's not an internet search but message_already_handled_by_llm is true,
            # it might be from a file query error that was edited into bot_thinking_message_id
            if bot_thinking_message_id:
                final_bot_response_id = bot_thinking_message_id
                log_admin(
                    f"{user_info_log} - Message was marked as already_handled_by_llm (non-internet, e.g. file error). Using ID: {final_bot_response_id} for potential buttons/reactions."
                )

        if (
            not internet_search_triggered
            and is_file_query
            and bot_thinking_message_id
            and bot_thinking_message_id != final_bot_response_id
        ):
            try:
                bot.delete_message(chat_id=chat_id, message_id=bot_thinking_message_id)
                log_admin(
                    f"{user_info_log} - Deleted specific file query status message {bot_thinking_message_id} as main response is now sent."
                )
            except Exception as e_del_fqs_final:
                log_admin(
                    f"{user_info_log} - Failed to delete file query status message {bot_thinking_message_id} at final stage: {e_del_fqs_final}"
                )

        if final_bot_response_id:
            # --- Reaction handling for successful responses ---
            if original_message_id:
                is_error_response = isinstance(final_response_to_send, str) and (
                    final_response_to_send.startswith("ОШИБКА")
                    or final_response_to_send.startswith("Ошибка:")
                )

                if not is_error_response:
                    if len(final_response_to_send) > 1000:
                        try:
                            remove_reaction(bot, chat_id, original_message_id)
                            log_admin(
                                f"{user_info_log} - Response >1000 chars. Removed reactions from user message {original_message_id}."
                            )
                        except Exception as e_rem_react_long:
                            log_admin(
                                f"{user_info_log} - Error removing reactions for long response from user message {original_message_id}: {e_rem_react_long}"
                            )
                    else:
                        try:
                            chat = bot.get_chat(chat_id)
                            if chat.type == "private":
                                set_reaction(bot, chat_id, original_message_id, "🎉")
                                log_admin(
                                    f"{user_info_log} - Added '🎉' reaction to USER message {original_message_id} after successful response."
                                )
                        except Exception as e_set_tada:
                            log_admin(
                                f"{user_info_log} - Error setting '🎉' reaction on USER message {original_message_id}: {e_set_tada}"
                            )
                else:
                    log_admin(
                        f"{user_info_log} - Response was an error. Skipping '🎉' reaction."
                    )
            else:
                log_admin(
                    f"{user_info_log} - No original_message_id. Skipping '🎉' reaction logic."
                )

            # --- Add other buttons (Summarize, Hard Resend) ---
            # This block is now conditional on final_bot_response_id being valid
            if final_response_to_send and not is_error_response:
                final_reply_markup = None
                buttons_for_final_markup = []

                summarize_markup_obj = _prepare_summarize_button_if_needed(
                    chat_id, final_response_to_send, final_bot_response_id, bot, user_id
                )
                if summarize_markup_obj and summarize_markup_obj.keyboard:
                    buttons_for_final_markup.extend(summarize_markup_obj.keyboard[0])

                # {{HARD}} tag and button creation logic removed.
                # if hard_tag_found: # This variable is set when "{{HARD}}" is detected in original_raw_response_text
                # unique_key_hard = uuid.uuid4().hex
                # callback_data for hard_resend button
                # hard_resend_callback_data = f"hard_resend_{unique_key_hard}"
                # hard_resend_btn = types.InlineKeyboardButton("🧠 Отправить в ИИ умнее", callback_data=hard_resend_callback_data)
                # log_admin(f"{user_info_log} - Created 'Smart AI' button with unique_key_hard: {unique_key_hard}, callback_data: {hard_resend_btn.callback_data}")

                # with message_states_lock:
                #     message_states[unique_key_hard] = {
                #         'type': 'hard_resend_data',
                #         'original_query': prompt_for_llm_call,
                #         'image_data': single_image_data,
                #         'original_user_message_id': original_message_id,
                #         'chat_id': chat_id
                #     }
                # buttons_for_final_markup.append(hard_resend_btn)

                # Logging for hard_tag_found removed
                # if hard_tag_found: # Logging specifically when hard_tag_found is true
                #     log_admin(f"{user_info_log} - buttons_for_final_markup (before creating InlineKeyboardMarkup): {[(btn.text, btn.callback_data) for btn in buttons_for_final_markup if hasattr(btn, 'text') and hasattr(btn, 'callback_data')]}")

                if (
                    buttons_for_final_markup
                ):  # This will now only be true if summarize_markup_obj exists
                    final_reply_markup = types.InlineKeyboardMarkup()
                    if (
                        len(buttons_for_final_markup) == 2
                    ):  # Handles Summarize + Hard Resend or two other buttons
                        final_reply_markup.row(
                            buttons_for_final_markup[0], buttons_for_final_markup[1]
                        )
                    elif (
                        len(buttons_for_final_markup) == 1
                    ):  # Handles only Summarize or only Hard Resend
                        final_reply_markup.add(buttons_for_final_markup[0])
                    # If more than 2 buttons, this logic might need adjustment, but current max is 2.

                if final_reply_markup:
                    log_admin(
                        f"{user_info_log} - final_reply_markup (JSON): {final_reply_markup.to_json()}"
                    )
                else:
                    log_admin(f"{user_info_log} - final_reply_markup is None.")

                if (
                    final_reply_markup
                ):  # Only attempt to edit if there's actually a markup
                    log_admin(
                        f"{user_info_log} - Attempting to call bot.edit_message_reply_markup for message_id: {final_bot_response_id} with markup: {bool(final_reply_markup)}"
                    )
                    try:
                        bot.edit_message_reply_markup(
                            chat_id=chat_id,
                            message_id=final_bot_response_id,
                            reply_markup=final_reply_markup,
                        )
                        log_admin(
                            f"{user_info_log} - Successfully updated/added reply markup to message {final_bot_response_id}."
                        )
                    except Exception as e_edit_markup_final:
                        log_admin(
                            f"{user_info_log} - Error updating reply markup for message {final_bot_response_id}: {e_edit_markup_final}"
                        )
        # If no final_bot_response_id, no buttons or '🎉' reaction can be added.
        # Logging for this case is handled where final_bot_response_id would have been set (or failed to be set).



    except Exception as e_global:
        log_admin(
            f"{user_info_log} - Global exception in process_text_or_photo_request: {e_global}\n{traceback.format_exc()}"
        )
        error_text_global = "Произошла внутренняя ошибка бота."  # Default error

        # Determine if this error is an "expected" API error text that should be shown to user
        if isinstance(e_global, str) and (
            e_global.startswith("ОШИБКА") or e_global.startswith("Ошибка:")
        ):
            error_text_global = e_global

        # Reaction handling in case of global error
        if (
            lightning_reaction_set_by_ptpr or user_message_already_had_reaction
        ) and original_message_id:
            try:
                remove_reaction(bot, chat_id, original_message_id)
                log_admin(
                    f"{user_info_log} - Removed '⚡' reaction from user message {original_message_id} due to global error."
                )
            except Exception as e_rem_react_global_err:
                log_admin(
                    f"{user_info_log} - Failed to remove '⚡' reaction on global error: {e_rem_react_global_err}"
                )

        # Delete any lingering file-specific status message if an error occurs
        if is_file_query and bot_thinking_message_id:
            try:
                bot.delete_message(chat_id=chat_id, message_id=bot_thinking_message_id)
                log_admin(
                    f"{user_info_log} - Deleted file query status message {bot_thinking_message_id} due to global error."
                )
            except Exception as e_del_fq_status_err:
                log_admin(
                    f"{user_info_log} - Failed to delete file query status message {bot_thinking_message_id} on global error: {e_del_fq_status_err}"
                )

        # Send the error message as a new message
        try:
            send_long_message(
                bot, chat_id, error_text_global, original_message_id, user_id=user_id
            )  # This will send as new
            log_admin(f"{user_info_log} - Sent global error message to user.")
        except Exception as e_send_global_err_msg:
            log_admin(
                f"{user_info_log} - Failed to send global error message to user: {e_send_global_err_msg}"
            )
        # return # Removed explicit return to allow history update even on global error if some text was generated

    # Ensure history is updated even if there was a global error but some text was formed
    # This part is tricky, as history_entry_model_text might not be what we want if a global error occurred mid-way.
    # However, the current structure updates history *before* the final message sending block.
    # If e_global happens after history update but before message sending, history might be inconsistent.
    # For now, the existing history update logic within the try block will be used.
    # If e_global happens before history update, history might not be complete for this interaction.
    finally:
        if typing_manager:
            typing_manager.stop()

        # Очистка глобальных instant typing managers
        if is_private_chat and hasattr(bot, '_instant_typing_managers'):
            instant_typing_key = f"{chat_id}_{original_message_id}"
            if instant_typing_key in bot._instant_typing_managers:
                bot._instant_typing_managers.pop(instant_typing_key, None)
                log_admin(f"{user_info_log} - Cleaned up instant typing manager reference")

        # Clean up instant typing manager for private chats
        if chat_id == user_id and hasattr(bot, '_instant_typing_managers'):
            instant_typing_key = f"{chat_id}_{original_message_id}"
            if instant_typing_key in bot._instant_typing_managers:
                try:
                    del bot._instant_typing_managers[instant_typing_key]
                    log_admin(f"{user_info_log} - Cleaned up instant typing manager for private chat")
                except Exception as e:
                    log_admin(f"{user_info_log} - Error cleaning up instant typing manager: {e}")


# --- Deep Research Feature ---


def pluralize_minutes(minutes):
    """Returns the correct Russian pluralization for 'minute'."""
    if minutes % 10 == 1 and minutes % 100 != 11:
        return "минута"
    elif 2 <= minutes % 10 <= 4 and (minutes % 100 < 10 or minutes % 100 >= 20):
        return "минуты"
    else:
        return "минут"






















# --- Podcast Generation Functions ---
def collect_chat_messages(chat_id, hours):
    """
    Collects chat messages from the last N hours using the database.
    Returns list of message dictionaries with text, username, and timestamp.
    """
    from datetime import datetime, timedelta

    log_admin(f"Collecting messages from chat {chat_id} for last {hours} hours")

    try:
        # Get chat information for logging
        try:
            chat_info = bot.get_chat(chat_id)
            chat_title = getattr(chat_info, 'title', f'Chat {chat_id}')
            log_admin(f"Chat title: {chat_title}")
        except Exception as e:
            log_admin(f"Could not get chat info: {e}")
            chat_title = f"Chat {chat_id}"

        # Get messages from database
        from database import get_chat_messages
        messages = get_chat_messages(chat_id, hours, min_length=10)

        log_admin(f"Retrieved {len(messages)} messages from chat {chat_id} for last {hours} hours")
        return messages

    except Exception as e:
        log_admin(f"Error collecting messages from chat {chat_id}: {e}")
        return []


def get_diana_context_from_db(chat_id):
    """
    Gets recent bot-user interactions from database for the context of Diana in group chats.
    Returns only messages that are part of bot-user dialogue (replies, commands, mentions, short responses).
    """
    from config import (
        DIANA_CONTEXT_HOURS,
        DIANA_CONTEXT_MAX_MESSAGES,
        DIANA_CONTEXT_MIN_MESSAGE_LENGTH
    )

    log_admin(f"Getting Diana bot-user dialogue context from chat {chat_id}")

    try:
        # Use the new specialized function to get bot dialogue messages
        from database import get_bot_dialogue_messages
        bot_dialogue_messages = get_bot_dialogue_messages(
            chat_id,
            DIANA_CONTEXT_HOURS,
            limit=DIANA_CONTEXT_MAX_MESSAGES,
            min_length=DIANA_CONTEXT_MIN_MESSAGE_LENGTH
        )

        log_admin(f"Retrieved {len(bot_dialogue_messages)} bot-dialogue messages for Diana context from chat {chat_id}")
        return bot_dialogue_messages

    except Exception as e:
        log_admin(f"Error getting Diana bot-dialogue context from chat {chat_id}: {e}")
        return []


def format_diana_context(messages):
    """
    Formats bot-user dialogue messages into LLM history format for Diana.
    Each message includes author name and reply context for better understanding.
    """
    from config import DIANA_CONTEXT_MAX_TOTAL_LENGTH

    if not messages:
        return []

    formatted_history = []
    total_length = 0

    try:
        for msg in messages:
            # Get author name from user_name field
            author_name = msg.get('user_name', 'Пользователь')
            if not author_name or author_name.strip() == '':
                author_name = "Пользователь"

            # Get message text
            message_text = msg.get('message_text', '').strip()
            if not message_text:
                continue

            # Format message with context
            formatted_text = f"[{author_name}]: {message_text}"

            # Add reply context if available
            if msg.get('reply_to_username'):
                reply_to = msg.get('reply_to_username', 'кому-то')
                formatted_text = f"[{author_name}] (отвечает {reply_to}): {message_text}"

            # Check length limit
            if total_length + len(formatted_text) > DIANA_CONTEXT_MAX_TOTAL_LENGTH:
                break

            # Add to history as user message for context
            formatted_history.append({
                "role": "user",
                "parts": [{"text": formatted_text}]
            })

            total_length += len(formatted_text)

        log_admin(f"Formatted {len(formatted_history)} bot-dialogue messages for Diana context (total length: {total_length})")
        return formatted_history

    except Exception as e:
        log_admin(f"Error formatting Diana bot-dialogue context: {e}")
        return []


def collect_chat_messages_for_podcast(chat_id, podcast_type="regular"):
    """
    Collects chat messages based on podcast type:
    - "scheduled": Always last 24 hours (for /podcastevery)
    - "regular": Since last regular podcast or 24 hours if no previous podcast
    """
    from datetime import datetime, timedelta
    from database import get_last_podcast_time, get_chat_messages, get_chat_messages_since_timestamp

    log_admin(f"Collecting messages from chat {chat_id} for podcast type: {podcast_type}")

    try:
        # Get chat information for logging
        try:
            chat_info = bot.get_chat(chat_id)
            chat_title = getattr(chat_info, 'title', f'Chat {chat_id}')
            log_admin(f"Chat title: {chat_title}")
        except Exception as e:
            log_admin(f"Could not get chat info: {e}")
            chat_title = f"Chat {chat_id}"

        if podcast_type == "scheduled":
            # For scheduled podcasts (/podcastevery), always use last 24 hours
            log_admin(f"Scheduled podcast: collecting ALL messages from last 24 hours")
            messages = get_chat_messages(chat_id, 24, min_length=10)
            log_admin(f"Retrieved {len(messages)} messages from chat {chat_id} for scheduled podcast (24 hours)")
        else:
            # For regular podcasts (/podcast without theme), use messages since last regular podcast
            last_podcast_time = get_last_podcast_time(chat_id)

            if last_podcast_time:
                # Get messages since last podcast
                log_admin(f"Regular podcast: collecting messages since last podcast at {last_podcast_time}")
                messages = get_chat_messages_since_timestamp(chat_id, last_podcast_time, min_length=10)
                log_admin(f"Retrieved {len(messages)} messages from chat {chat_id} since last podcast")
            else:
                # No previous podcast, use last 24 hours
                log_admin(f"Regular podcast: no previous podcast found, collecting messages from last 24 hours")
                messages = get_chat_messages(chat_id, 24, min_length=10)
                log_admin(f"Retrieved {len(messages)} messages from chat {chat_id} for first regular podcast (24 hours)")

        return messages

    except Exception as e:
        log_admin(f"Error collecting messages from chat {chat_id}: {e}")
        return []





def format_messages_for_gpt(messages):
    """
    Formats collected messages into a structured text for GPT processing.
    """
    if not messages:
        return "Нет сообщений для обработки."

    from datetime import datetime

    formatted_lines = []
    for msg in messages:
        timestamp = msg.get('timestamp', '')
        username = msg.get('username', 'Пользователь')
        text = msg.get('text', '')
        reply_to_username = msg.get('reply_to_username')

        if timestamp:
            # Convert timestamp to Moscow time
            import pytz
            moscow_tz = pytz.timezone('Europe/Moscow')
            moscow_time = datetime.fromtimestamp(timestamp, moscow_tz)
            time_str = moscow_time.strftime('[%H:%M]')
        else:
            time_str = '[--:--]'

        # Add reply context if this message is a reply
        if reply_to_username:
            formatted_lines.append(f"{time_str} {username} (отвечает {reply_to_username}): {text}")
        else:
            formatted_lines.append(f"{time_str} {username}: {text}")

    return '\n'.join(formatted_lines)


# --- Queued Podcast Wrapper Functions REMOVED ---
# Old queued functions have been removed. Use direct_* functions instead.



# --- Direct Podcast Wrapper Functions (No Queue) ---

def direct_process_podcast_request(user_id, chat_id, hours, original_message_id, status_message_id, theme="", custom_title="", podcast_type="regular", hours_specified=False, user_nickname="", process_key=None, duration_minutes=10):
    """
    Direct wrapper function to start regular podcast processing in a separate thread without queue.

    Args:
        user_id: User ID who requested the podcast
        chat_id: Chat ID where the podcast was requested
        hours: Number of hours to collect messages for
        original_message_id: ID of the original message that triggered the request
        status_message_id: ID of the status message to update
        theme: Optional theme for the podcast
        custom_title: Optional custom title for the podcast
        podcast_type: Type of podcast ("regular", "thematic", "scheduled")
        hours_specified: True if user explicitly specified hours
        user_nickname: Nickname of the user for logging
        process_key: Unique identifier for this podcast process
        duration_minutes: Duration of podcast in minutes (3, 5, or 10), defaults to 10
    """
    user_info_log = f"user {user_id} (chat {chat_id}) [process {process_key}]"
    log_admin(f"{user_info_log} - Starting direct podcast processing (no queue) - type: {podcast_type}, hours: {hours}, theme: '{theme}'")

    def podcast_thread():
        try:
            # Note: Signal handlers can only be set in the main thread
            # Removed signal handler setup to avoid "signal only works in main thread" error

            process_podcast_request(
                user_id, chat_id, hours, original_message_id, status_message_id,
                theme, custom_title, podcast_type, hours_specified, user_nickname, process_key, duration_minutes
            )
        except Exception as e:
            log_admin(f"{user_info_log} - Error in direct podcast thread: {e}", level="error")
            log_admin(f"{user_info_log} - Traceback: {traceback.format_exc()}", level="error")

            # Cleanup any hanging processes
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] == 'ffmpeg' and any(f'podcast_raw_{chat_id}' in str(arg) for arg in proc.info['cmdline'] or []):
                            log_admin(f"Cleaning up hanging FFmpeg process PID {proc.info['pid']} after error", level="warning")
                            proc.terminate()
                    except Exception as e_cleanup:
                        log_admin(f"{user_info_log} - Error during FFmpeg cleanup after thread error: {e_cleanup}\n{traceback.format_exc()}",
                                  level="warning")
            except Exception as e_cleanup_outer:
                log_admin(f"{user_info_log} - Error during outer FFmpeg cleanup after thread error: {e_cleanup_outer}\n{traceback.format_exc()}",
                          level="warning")

            # Try to update status message with error using PodcastStatusManager
            try:
                from podcast_status_manager import PodcastStatusManager
                process_params = {'hours': hours, 'podcast_type': podcast_type, 'user_id': user_id, 'theme': theme, 'custom_title': custom_title, 'hours_specified': hours_specified, 'user_nickname': user_nickname}
                error_manager = PodcastStatusManager(bot, chat_id, status_message_id, podcast_type, process_params)
                error_manager.show_error('general', "Произошла ошибка при создании подкаста. Попробуйте еще раз.")
            except Exception:
                # Fallback to direct bot edit
                try:
                    bot.edit_message_text(
                        "🎙️ Произошла ошибка при создании подкаста. Попробуйте еще раз.",
                        chat_id=chat_id,
                        message_id=status_message_id
                    )
                except Exception as e_status_msg:
                    log_admin(f"{user_info_log} - Error updating status message: {e_status_msg}", level="warning")

    # Start podcast processing directly without queue
    import threading
    thread = threading.Thread(target=podcast_thread, daemon=True)
    thread.start()
    log_admin(f"{user_info_log} - Started direct podcast thread {process_key}")


def direct_process_thematic_podcast_request(user_id, chat_id, theme, original_message_id, status_message_id, process_key=None, duration_minutes=10, podcast_host_type='anna_mikhail'):
    """
    Direct wrapper function to start thematic podcast processing in a separate thread without queue.

    Args:
        user_id: User ID who requested the podcast
        chat_id: Chat ID where the podcast was requested
        theme: Theme/topic for the podcast
        original_message_id: ID of the original message that triggered the request
        status_message_id: ID of the status message to update
        process_key: Unique identifier for this podcast process
        duration_minutes: Duration of podcast in minutes (3, 5, or 10), defaults to 10
        podcast_host_type: Type of podcast hosts ('anna_mikhail' or 'diana_sasha'), defaults to 'anna_mikhail'
    """
    user_info_log = f"user {user_id} (chat {chat_id}) [process {process_key}]"
    log_admin(f"{user_info_log} - Starting direct thematic podcast processing (no queue) - theme: '{theme}'")

    def podcast_thread():
        try:
            # Note: Signal handlers can only be set in the main thread
            # Removed signal handler setup to avoid "signal only works in main thread" error

            process_thematic_podcast_request(
                user_id, chat_id, theme, original_message_id, status_message_id, process_key, duration_minutes, podcast_host_type
            )
        except Exception as e:
            log_admin(f"{user_info_log} - Error in direct thematic podcast thread: {e}", level="error")
            log_admin(f"{user_info_log} - Traceback: {traceback.format_exc()}", level="error")

            # Try to update status message with error using PodcastStatusManager
            try:
                from podcast_status_manager import PodcastStatusManager
                process_params = {'theme': theme, 'podcast_type': 'thematic', 'user_id': user_id}
                error_manager = PodcastStatusManager(bot, chat_id, status_message_id, 'thematic', process_params)
                error_manager.show_error('general', "Произошла ошибка при создании тематического подкаста. Попробуйте еще раз.")
            except Exception:
                # Fallback to direct bot edit
                try:
                    bot.edit_message_text(
                        "🎙️ Произошла ошибка при создании тематического подкаста. Попробуйте еще раз.",
                        chat_id=chat_id,
                        message_id=status_message_id
                    )
                except Exception as e_status_msg2:
                    log_admin(f"{user_info_log} - Error updating status message in thematic: {e_status_msg2}", level="warning")

    # Start podcast processing directly without queue
    import threading
    thread = threading.Thread(target=podcast_thread, daemon=True)
    thread.start()
    log_admin(f"{user_info_log} - Started direct thematic podcast thread {process_key}")


def direct_process_research_podcast_request(user_id, chat_id, research_key, original_message_id, status_message_id, user_preferences="", preferences_key=None):
    """
    Direct wrapper function to start research podcast processing in a separate thread without queue.

    Args:
        user_id: User ID who requested the podcast
        chat_id: Chat ID where the podcast was requested
        research_key: Key to retrieve research data
        original_message_id: ID of the original research message
        status_message_id: ID of the status message to update
        user_preferences: Optional user preferences for the podcast
        preferences_key: Key for cleaning up podcast preferences data
    """
    user_info_log = f"user {user_id} (chat {chat_id})"
    log_admin(f"{user_info_log} - Starting direct research podcast processing (no queue) - research_key: {research_key}")

    def podcast_thread():
        try:
            # Note: Signal handlers can only be set in the main thread
            # Removed signal handler setup to avoid "signal only works in main thread" error

            process_research_podcast_request(
                user_id, chat_id, research_key, original_message_id, status_message_id, user_preferences, preferences_key
            )
        except Exception as e:
            log_admin(f"{user_info_log} - Error in direct research podcast thread: {e}", level="error")
            log_admin(f"{user_info_log} - Traceback: {traceback.format_exc()}", level="error")

            # Cleanup any hanging processes
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] == 'ffmpeg' and any(f'podcast_raw_{chat_id}' in str(arg) for arg in proc.info['cmdline'] or []):
                            log_admin(f"Cleaning up hanging FFmpeg process PID {proc.info['pid']} after error", level="warning")
                            proc.terminate()
                    except Exception as e_cleanup_proc:
                        log_admin(f"{user_info_log} - Error during process cleanup: {e_cleanup_proc}", level="warning")
            except Exception as e_cleanup_outer2:
                log_admin(f"{user_info_log} - Error during outer cleanup in research podcast: {e_cleanup_outer2}", level="warning")

            # Try to update status message with error using PodcastStatusManager
            try:
                from podcast_status_manager import PodcastStatusManager
                process_params = {'research_key': research_key, 'user_preferences': user_preferences, 'podcast_type': 'research', 'user_id': user_id}
                error_manager = PodcastStatusManager(bot, chat_id, status_message_id, 'research', process_params)
                error_manager.show_error('general', "Произошла ошибка при создании исследовательского подкаста. Попробуйте еще раз.")
            except Exception:
                # Fallback to direct bot edit
                try:
                    bot.edit_message_text(
                        "🎙️ Произошла ошибка при создании исследовательского подкаста. Попробуйте еще раз.",
                        chat_id=chat_id,
                        message_id=status_message_id
                    )
                except Exception as e_status:
                    # Fallback to direct bot edit
                    log_admin(f"{user_info_log} - Error showing error via PodcastStatusManager: {e_status}", level="warning")

    # Start podcast processing directly without queue
    import threading
    thread = threading.Thread(target=podcast_thread, daemon=True)
    thread.start()
    log_admin(f"{user_info_log} - Started direct research podcast thread")


def direct_process_research_podcast_diana_sasha_request(user_id, chat_id, research_key, original_message_id, status_message_id, preferences_key=None):
    """
    Direct wrapper function to start Diana & Sasha research podcast processing in a separate thread without queue.

    Args:
        user_id: User ID who requested the podcast
        chat_id: Chat ID where the podcast was requested
        research_key: Key to retrieve research data
        original_message_id: ID of the original research message
        status_message_id: ID of the status message to update
        preferences_key: Key for cleaning up podcast preferences data
    """
    user_info_log = f"user {user_id} (chat {chat_id})"
    log_admin(f"{user_info_log} - Starting direct Diana & Sasha research podcast processing (no queue) - research_key: {research_key}")

    def podcast_thread():
        try:
            # Note: Signal handlers can only be set in the main thread
            # Removed signal handler setup to avoid "signal only works in main thread" error

            process_research_podcast_diana_sasha_request(
                user_id, chat_id, research_key, original_message_id, status_message_id, preferences_key
            )
        except Exception as e:
            log_admin(f"{user_info_log} - Error in direct Diana & Sasha podcast thread: {e}", level="error")
            log_admin(f"{user_info_log} - Traceback: {traceback.format_exc()}", level="error")

            # Cleanup any hanging processes
            try:
                import psutil
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'] == 'ffmpeg' and any(f'podcast_raw_{chat_id}' in str(arg) for arg in proc.info['cmdline'] or []):
                            log_admin(f"Cleaning up hanging FFmpeg process PID {proc.info['pid']} after error", level="warning")
                            proc.terminate()
                    except Exception as e_cleanup_proc2:
                        log_admin(f"{user_info_log} - Error during process cleanup in Diana & Sasha: {e_cleanup_proc2}", level="warning")
            except Exception as e_cleanup_outer3:
                log_admin(f"{user_info_log} - Error during outer cleanup in Diana & Sasha: {e_cleanup_outer3}", level="warning")

            # Try to update status message with error using PodcastStatusManager
            try:
                from podcast_status_manager import PodcastStatusManager
                process_params = {'research_key': research_key, 'podcast_type': 'diana_sasha', 'user_id': user_id}
                error_manager = PodcastStatusManager(bot, chat_id, status_message_id, 'diana_sasha', process_params)
                error_manager.show_error('general', "Произошла ошибка при создании подкаста Diana & Sasha. Попробуйте еще раз.")
            except Exception:
                # Fallback to direct bot edit
                try:
                    bot.edit_message_text(
                        "🎙️ Произошла ошибка при создании подкаста Diana & Sasha. Попробуйте еще раз.",
                        chat_id=chat_id,
                        message_id=status_message_id
                    )
                except Exception as e_status2:
                    # Fallback to direct bot edit
                    log_admin(f"{user_info_log} - Error showing error via PodcastStatusManager in Diana & Sasha: {e_status2}", level="warning")

    # Start podcast processing directly without queue
    import threading
    thread = threading.Thread(target=podcast_thread, daemon=True)
    thread.start()
    log_admin(f"{user_info_log} - Started direct Diana & Sasha podcast thread")


def process_podcast_request(user_id, chat_id, hours, original_message_id, status_message_id, theme="", custom_title="", podcast_type="regular", hours_specified=False, user_nickname="", process_key=None, duration_minutes=10):
    """
    Main function to process podcast generation request.

    Args:
        hours_specified: True if user explicitly specified hours (e.g., /podcast 24),
                        False if using default logic (e.g., /podcast without hours)
        process_key: Unique identifier for this podcast process
        duration_minutes: Duration of podcast in minutes (3, 5, or 10), defaults to 10
    """
    user_info_log = f"user {user_id} (chat {chat_id}) [process {process_key}]"
    log_admin(f"{user_info_log} - Starting podcast generation for {hours} hours with theme: '{theme}', custom_title: '{custom_title}', type: '{podcast_type}', hours_specified: {hours_specified}")

    # Данные подкаста уже очищены атомарно в handle_podcast_confirmation
    if process_key:
        log_admin(f"{user_info_log} - Process key: {process_key}")
    else:
        log_admin(f"{user_info_log} - No process_key provided")

    # Initialize status manager with process parameters
    from podcast_status_manager import PodcastStatusManager
    process_params = {
        'user_id': user_id,
        'hours': hours,
        'theme': theme,
        'custom_title': custom_title,
        'podcast_type': podcast_type,
        'hours_specified': hours_specified,
        'process_key': process_key,
        'duration_minutes': duration_minutes
    }
    status_manager = PodcastStatusManager(bot, chat_id, status_message_id, podcast_type, process_params, process_key, duration_minutes)

    # Запускаем новый менеджер статусов
    status_manager.start()

    try:
        # Check if this is a thematic podcast (skip message collection)
        if podcast_type == "thematic":
            log_admin(f"{user_info_log} - Thematic podcast detected - skipping message collection")
            # For thematic podcasts, we don't collect messages from chat

            # Start with research stage for thematic podcasts - этап 0 уже установлен в start()

            # ЭТАП 2: Добавляем ресерч для тематических подкастов
            research_results = None
            queries = None
            try:
                # Шаг 1: Генерируем поисковые запросы
                log_admin(f"{user_info_log} - Starting research for thematic podcast")

                # Синхронная функция обновления статуса
                def update_status_sync(completed, total):
                    try:
                        # Для тематических подкастов остаемся на этапе 0 (исследование)
                        # Текст этапа уже установлен, прогресс-бар заполняется по времени
                        pass
                    except Exception as e:
                        log_admin(f"{user_info_log} - Error updating research status: {e}")

                # Этап исследования уже активен, не нужно дополнительных обновлений

                queries = generate_research_queries(theme)
                if queries:
                    log_admin(f"{user_info_log} - Generated {len(queries)} research queries: {queries}")

                    # Показываем начальный статус поиска
                    update_status_sync(0, len(queries))

                    # Шаг 2: Выполняем множественный поиск асинхронно через asyncio.run
                    async def async_research():
                        async def progress_callback(completed, total):
                            # Синхронный вызов из асинхронного контекста
                            update_status_sync(completed, total)

                        return await perform_multiple_searches(queries, progress_callback)

                    research_results = asyncio.run(async_research())

                    if research_results:
                        log_admin(f"{user_info_log} - Research completed successfully, {len(research_results)} results obtained")
                    else:
                        log_admin(f"{user_info_log} - Research failed, proceeding without additional information")
                else:
                    log_admin(f"{user_info_log} - Failed to generate research queries, proceeding without research")

            except Exception as e:
                log_admin(f"{user_info_log} - Error during research: {e}")
                research_results = None

            # Переходим к генерации диалога
            status_manager.set_stage(1)

            # Skip to dialogue generation for thematic podcasts
            formatted_context = None
        else:
            # Step 1: Collect messages (for regular podcasts) - этап 0 уже установлен в start()

            # Choose message collection strategy based on whether hours were explicitly specified
            if hours_specified:
                # User explicitly specified hours - collect messages for EXACTLY that period
                log_admin(f"{user_info_log} - User specified {hours} hours explicitly - collecting messages for exactly {hours} hours")
                messages = collect_chat_messages(chat_id, hours)
            elif podcast_type == "scheduled":
                # Scheduled podcast (/podcastevery) - always use last 24 hours
                messages = collect_chat_messages_for_podcast(chat_id, "scheduled")
            else:
                # Regular podcast without specified hours - ALWAYS use last 24 hours
                log_admin(f"{user_info_log} - Regular podcast without specified hours - collecting messages for exactly 24 hours")
                messages = collect_chat_messages(chat_id, 24)

            # Check if we have enough messages
            from config import PODCAST_MIN_MESSAGES
            if len(messages) < PODCAST_MIN_MESSAGES:
                # Get chat statistics for better error message
                from database import get_chat_stats
                stats = get_chat_stats(chat_id, hours)

                error_details = (
                    f"📊 Статистика за последние {hours} ч:\n"
                    f"• Всего сообщений: {stats['total_messages']}\n"
                    f"• Текстовых (>10 символов): {stats['text_messages']}\n"
                    f"• Уникальных пользователей: {stats['unique_users']}\n\n"
                    f"Требуется минимум {PODCAST_MIN_MESSAGES} текстовых сообщений."
                )

                status_manager.show_error('insufficient_messages', error_details)
                return

            # Step 2: Format messages for GPT
            status_manager.set_stage(1)

            formatted_context = format_messages_for_gpt(messages)

        # Step 3: Generate podcast dialogue
        if podcast_type == "thematic":
            status_manager.set_stage(1)  # thematic: generate_dialogue stage
        else:
            status_manager.set_stage(2)  # regular: generate_dialogue stage

        from api_clients import call_official_gemini_api, generate_with_grok_fallback
        from admin_system import get_group_model_setting
        from datetime import datetime
        from config import get_moscow_datetime
        current_datetime = get_moscow_datetime()
        is_private_chat = (chat_id == user_id)

        # Choose system prompt and dialogue prompt based on podcast type
        if podcast_type == "thematic":
            # Thematic podcast - use special prompt and no chat messages
            from config import SYSTEM_PROMPT_THEMATIC_PODCAST_GENERATION
            from utils import format_thematic_podcast_system_prompt

            # ЭТАП 2: Подготавливаем информацию о ресерче для интеграции в системный промпт
            research_info = ""
            if research_results and any(result and not result.startswith("Поиск по запросу") for result in research_results):
                research_info = "\n\nВот дополнительная информация по вопросу юзера, найденная в интернете\n(ВЕДУЩИЕ ДОЛЖНЫ ПОЛЬЗОВАТЬСЯ ЕЙ БУДТО ОНИ И ТАК ЭТО ЗНАЮТ,\nОНИ НЕ ДОЛЖНЫ УПОМИНАТЬ ЧТО ЭТО В ИНТЕРНЕТЕ НАЙДЕНО):\n\n"

                for i, (query, result) in enumerate(zip(queries if queries else [], research_results), 1):
                    if result and not result.startswith("Поиск по запросу"):
                        research_info += f"ЗАПРОС {i}: {query}\nОТВЕТ {i}: {result}\n\n"

                log_admin(f"{user_info_log} - Prepared research information for system prompt ({len(research_info)} characters)")
            else:
                log_admin(f"{user_info_log} - No valid research results to add to system prompt")

            formatted_system_prompt = format_thematic_podcast_system_prompt(
                SYSTEM_PROMPT_THEMATIC_PODCAST_GENERATION,
                theme,  # user_request
                user_nickname,
                current_datetime,
                research_info,  # Передаем информацию о ресерче
                duration_minutes
            )

            # Простой dialogue_prompt для тематических подкастов
            dialogue_prompt = f"Создай подкаст-диалог на тему: {theme}"

        else:
            # Regular podcast - use regular prompt with chat messages
            from config import SYSTEM_PROMPT_PODCAST_GENERATION
            from utils import format_podcast_system_prompt

            # Format theme instructions - if no theme, don't include the section at all
            if theme and theme.strip():
                theme_instructions = f"ТЕМАТИЧЕСКИЕ ТРЕБОВАНИЯ:\n• Ведите подкаст в стиле: {theme}\n• Адаптируйте тон и настроение под эту тему\n• Пусть тема влияет на манеру общения ведущих\n"
            else:
                theme_instructions = ""

            formatted_system_prompt = format_podcast_system_prompt(
                SYSTEM_PROMPT_PODCAST_GENERATION,
                is_private_chat,
                current_datetime,
                theme_instructions,
                duration_minutes
            )

            dialogue_prompt = f"Создай подкаст-диалог на основе следующих сообщений из чата:\n\n{formatted_context}"

        # Check which model to use for this group
        group_model = get_group_model_setting(chat_id)
        log_admin(f"{user_info_log} - Using model: {group_model} for podcast generation")

        if group_model == "grok":
            # Use grok-4 via Navy API with fallback to Gemini
            # Combine system prompt and user prompt for grok
            if formatted_system_prompt:
                full_prompt = f"{formatted_system_prompt}\n\n{dialogue_prompt}"
            else:
                full_prompt = dialogue_prompt

            dialogue_text = generate_with_grok_fallback(
                prompt=full_prompt,
                chat_id=chat_id,
                system_prompt=formatted_system_prompt,
                call_type="podcast_generation"
            )
        else:
            # Use Gemini (default behavior) - изолируем в thread pool
            def dialogue_worker():
                return call_official_gemini_api(
                    model_name="gemini-2.5-pro",
                    history=[],
                    user_text=dialogue_prompt,
                    input_data=None,
                    system_prompt=formatted_system_prompt,
                    call_type="podcast_generation",
                    user_id=user_id  # Pass user_id for ultrathink setting
                )

            future = submit_task(dialogue_worker)
            dialogue_text = future.result(timeout=GENAI_REQUEST_TIMEOUT)

        if not dialogue_text or dialogue_text.startswith("ОШИБКА") or dialogue_text.startswith("Извините"):
            try:
                bot.edit_message_text(
                    f"🎙️ Ошибка при генерации диалога: {dialogue_text[:100]}...",
                    chat_id=chat_id,
                    message_id=status_message_id
                )
            except Exception as e:
                if "message is not modified" not in str(e):
                    log_admin(f"{user_info_log} - Error updating error message: {e}")
            return

        # Step 4: Generate audio using TTS
        if podcast_type == "thematic":
            status_manager.set_stage(2)  # thematic: synthesize_speech stage
        else:
            status_manager.set_stage(3)  # regular: synthesize_speech stage

        # Clean first line from tags and remove duplicate lines before TTS
        dialogue_text = clean_first_line_tags(dialogue_text)
        dialogue_text = remove_duplicate_dialogue_lines(dialogue_text)

        # Generate single-part podcast for all chats (no more splitting)
        # All podcasts are now single-part without PODCAST_BREAK logic

        # Generate single-part podcast (new default behavior)
        log_admin(f"{user_info_log} - Generating single-part podcast")

        # TTS generation with retry logic
        MAX_TTS_TOTAL_RETRIES = 3  # суммарно 3 захода
        audio_data = None
        
        for tts_try in range(1, MAX_TTS_TOTAL_RETRIES + 1):
            try:
                from api_clients import call_gemini_tts_api
                audio_data = call_gemini_tts_api(dialogue_text, "podcast_tts", "diana_sasha")
                break  # 🎉 успех – выходим из цикла
            except RuntimeError as e:
                if str(e) == "TTS_FAILED_NO_AUDIO" and tts_try < MAX_TTS_TOTAL_RETRIES:
                    status_manager.set_current_stage_text(
                        "⚠️ Проблемы с озвучкой…\nПробуем другой ключ, подождите ещё немного."
                    )
                    status_manager.extend_total_duration(300)  # +5 мин
                    bot.send_chat_action(chat_id, "record_voice")
                    time.sleep(15)  # маленький back‑off
                    continue
                raise  # другие ошибки — наверх
            except Exception as tts_error:
                log_admin(f"{user_info_log} - Critical error during TTS generation: {tts_error}", level="error")
                status_manager.show_error('general', f"Критическая ошибка при синтезе аудио: {str(tts_error)[:100]}...")
                return

        if not audio_data:
            status_manager.show_error('tts_rejected')
            return

        # Step 5: Save and send audio
        if podcast_type == "thematic":
            status_manager.set_stage(3)  # thematic: prepare_sending stage
        else:
            status_manager.set_stage(4)  # regular: prepare_sending stage

        from utils import save_podcast_audio, generate_podcast_title
        import time

        timestamp = int(time.time())
        audio_filepath = save_podcast_audio(audio_data, chat_id, timestamp, process_key=process_key)

        if not audio_filepath:
            status_manager.show_error('general', "Ошибка при сохранении аудиофайла.")
            return

        # Generate title - use custom title if provided, otherwise generate automatically
        if custom_title:
            title = custom_title
            log_admin(f"{user_info_log} - Using custom title: {title}")
        else:
            title = generate_podcast_title(messages, chat_id)
            log_admin(f"{user_info_log} - Generated automatic title: {title}")

        # Send audio file as new message and clean up
        # Complete the status manager (this will also delete the status message)
        status_manager.complete()

        # Note: Keeping the original command message visible (not deleting it)
        # The original command message will remain in the chat for user reference

        sent_message = None

        # Critical check: verify file exists before attempting to send
        if not os.path.exists(audio_filepath):
            log_admin(f"{user_info_log} - CRITICAL ERROR: Audio file does not exist: {audio_filepath}", level="error")
            status_manager.show_error('general', "Не удалось создать аудиофайл подкаста.")
            return

        # Verify file is not empty
        try:
            file_size = os.path.getsize(audio_filepath)
            if file_size == 0:
                log_admin(f"{user_info_log} - CRITICAL ERROR: Audio file is empty: {audio_filepath}", level="error")
                status_manager.show_error('general', "Создан пустой аудиофайл.")
                return
            log_admin(f"{user_info_log} - Audio file verified: {audio_filepath} (size: {file_size} bytes)")
        except Exception as size_error:
            log_admin(f"{user_info_log} - Error checking file size: {size_error}", level="error")

        try:
            # Import safe caption function
            from utils import safe_caption_for_telegram
            safe_caption = safe_caption_for_telegram(f"🎙️ {title}")

            with open(audio_filepath, "rb") as audio_file:
                try:
                    # Try to send with reply first
                    sent_message = bot.send_voice(
                        chat_id=chat_id,
                        voice=audio_file,
                        caption=safe_caption,
                        reply_to_message_id=original_message_id,
                        timeout=120  # Увеличиваем таймаут для больших файлов
                    )
                    log_admin(f"{user_info_log} - Successfully sent podcast voice with reply")
                except Exception as reply_error:
                    if "message to be replied not found" in str(reply_error):
                        # Original message was deleted, send without reply
                        log_admin(f"{user_info_log} - Original message not found, sending without reply")
                        audio_file.seek(0)  # Reset file pointer
                        try:
                            sent_message = bot.send_voice(
                                chat_id=chat_id,
                                voice=audio_file,
                                caption=safe_caption,
                                timeout=120
                            )
                            log_admin(f"{user_info_log} - Successfully sent podcast voice without reply")
                        except Exception as voice_error:
                            if "restricted receiving of video messages" in str(voice_error):
                                # User has restricted voice messages, fallback to audio
                                log_admin(f"{user_info_log} - Voice messages restricted, sending as audio")
                                audio_file.seek(0)  # Reset file pointer
                                sent_message = bot.send_audio(
                                    chat_id=chat_id,
                                    audio=audio_file,
                                    caption=safe_caption,
                                    timeout=120
                                )
                                log_admin(f"{user_info_log} - Successfully sent podcast as audio")
                            else:
                                raise voice_error
                    elif "restricted receiving of video messages" in str(reply_error):
                        # User has restricted voice messages, fallback to audio with reply
                        log_admin(f"{user_info_log} - Voice messages restricted, sending as audio with reply")
                        audio_file.seek(0)  # Reset file pointer
                        sent_message = bot.send_audio(
                            chat_id=chat_id,
                            audio=audio_file,
                            caption=safe_caption,
                            reply_to_message_id=original_message_id,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent podcast as audio with reply")
                    else:
                        raise reply_error  # Re-raise if it's a different error
        except Exception as send_error:
            log_admin(f"{user_info_log} - Error sending voice: {send_error}")
            # Fallback: send text message about completion
            try:
                from utils import safe_caption_for_telegram
                safe_title = safe_caption_for_telegram(title, max_length=200)  # Shorter limit for message text
                bot.send_message(
                    chat_id=chat_id,
                    text=f"🎙️ Подкаст готов, но не удалось отправить аудиофайл.\nРазмер: {len(audio_data)} байт\nНазвание: {safe_title}",
                    reply_to_message_id=original_message_id
                )
            except Exception as fallback_error:
                if "message to be replied not found" in str(fallback_error):
                    # Send fallback message without reply
                    from utils import safe_caption_for_telegram
                    safe_title = safe_caption_for_telegram(title, max_length=200)  # Shorter limit for message text
                    bot.send_message(
                        chat_id=chat_id,
                        text=f"🎙️ Подкаст готов, но не удалось отправить аудиофайл.\nРазмер: {len(audio_data)} байт\nНазвание: {safe_title}"
                    )
                else:
                    raise fallback_error

        # Clean up temporary file
        try:
            os.remove(audio_filepath)
        except:
            pass

        # Save timestamp of successful podcast for regular podcasts (not theme podcasts)
        if podcast_type == "regular" and sent_message:
            from database import set_last_podcast_time
            import time
            current_timestamp = int(time.time())
            set_last_podcast_time(chat_id, current_timestamp, sent_message.message_id)
            log_admin(f"{user_info_log} - Saved last podcast timestamp: {current_timestamp}, message_id: {sent_message.message_id}")

        # Complete the process and clean up
        status_manager.complete()
        log_admin(f"{user_info_log} - Podcast generation completed successfully")

    except Exception as e:
        log_admin(f"{user_info_log} - Error in podcast generation: {e}")
        status_manager.show_error('general', f"Произошла ошибка при создании подкаста: {str(e)[:100]}...")






def process_tts_request(user_id, chat_id, text_to_speak, original_message_id, status_message_id):
    """
    Processes simple TTS request for single speaker.
    """
    user_info_log = f"user {user_id} (chat {chat_id})"
    log_admin(f"{user_info_log} - Starting TTS generation for text: {text_to_speak[:50]}...")

    try:
        # Update status
        try:
            bot.edit_message_text(
                "🔊 Синтезирую речь...",
                chat_id=chat_id,
                message_id=status_message_id
            )
        except Exception as e:
            if "message is not modified" not in str(e):
                log_admin(f"{user_info_log} - Error updating status: {e}")

        # Call single speaker TTS via Gemini (isolated in thread pool)
        try:
            from api_clients import call_gemini_single_tts_api

            # Изолируем блокирующую TTS операцию в thread pool
            def tts_worker():
                return call_gemini_single_tts_api(text_to_speak, "simple_tts", "Zephyr")

            future = submit_task(tts_worker)
            audio_data = future.result(timeout=GENAI_TTS_TIMEOUT)

            if not audio_data:
                try:
                    bot.edit_message_text(
                        "🔊 Не удалось создать аудио. Попробуйте позже.",
                        chat_id=chat_id,
                        message_id=status_message_id
                    )
                except Exception as e:
                    if "message is not modified" not in str(e):
                        log_admin(f"{user_info_log} - Error updating error message: {e}")
                return
        except Exception as tts_error:
            log_admin(f"{user_info_log} - Critical error during simple TTS generation: {tts_error}", level="error")
            try:
                bot.edit_message_text(
                    f"🔊 Критическая ошибка при создании аудио: {str(tts_error)[:100]}...",
                    chat_id=chat_id,
                    message_id=status_message_id
                )
            except Exception as e:
                if "message is not modified" not in str(e):
                    log_admin(f"{user_info_log} - Error updating error message: {e}")
            return

        # Save audio file
        try:
            bot.edit_message_text(
                "🔊 Подготавливаю аудиофайл...",
                chat_id=chat_id,
                message_id=status_message_id
            )
        except Exception as e:
            if "message is not modified" not in str(e):
                log_admin(f"{user_info_log} - Error updating status: {e}")

        from utils import save_tts_audio
        import time

        timestamp = int(time.time())
        audio_filepath = save_tts_audio(audio_data, chat_id, timestamp, process_key=process_key)

        if not audio_filepath:
            try:
                bot.edit_message_text(
                    "🔊 Ошибка при сохранении аудиофайла.",
                    chat_id=chat_id,
                    message_id=status_message_id
                )
            except Exception as e:
                if "message is not modified" not in str(e):
                    log_admin(f"{user_info_log} - Error updating error message: {e}")
            return

        # Send audio file
        # Complete the status manager (this will also delete the status message)
        status_manager.complete()

        try:
            with open(audio_filepath, "rb") as audio_file:
                try:
                    # Try to send with reply first
                    bot.send_voice(
                        chat_id=chat_id,
                        voice=audio_file,
                        caption="🔊 Озвучка готова",
                        reply_to_message_id=original_message_id,
                        timeout=60
                    )
                    log_admin(f"{user_info_log} - Successfully sent TTS audio with reply")
                except Exception as reply_error:
                    if "message to be replied not found" in str(reply_error):
                        # Original message was deleted, send without reply
                        log_admin(f"{user_info_log} - Original message not found, sending TTS without reply")
                        audio_file.seek(0)  # Reset file pointer
                        bot.send_voice(
                            chat_id=chat_id,
                            voice=audio_file,
                            caption="🔊 Озвучка готова",
                            timeout=60
                        )
                        log_admin(f"{user_info_log} - Successfully sent TTS audio without reply")
                    else:
                        raise reply_error  # Re-raise if it's a different error
        except Exception as send_error:
            log_admin(f"{user_info_log} - Error sending TTS audio: {send_error}")
            # Fallback: send text message
            try:
                bot.send_message(
                    chat_id=chat_id,
                    text=f"🔊 Озвучка готова, но не удалось отправить аудиофайл.\nТекст: {text_to_speak[:100]}...",
                    reply_to_message_id=original_message_id
                )
            except Exception as fallback_error:
                if "message to be replied not found" in str(fallback_error):
                    # Send fallback message without reply
                    bot.send_message(
                        chat_id=chat_id,
                        text=f"🔊 Озвучка готова, но не удалось отправить аудиофайл.\nТекст: {text_to_speak[:100]}..."
                    )
                else:
                    raise fallback_error

        # Clean up temporary file
        try:
            os.remove(audio_filepath)
        except:
            pass

        log_admin(f"{user_info_log} - TTS generation completed successfully")

    except Exception as e:
        log_admin(f"{user_info_log} - Error in TTS generation: {e}")
        try:
            bot.edit_message_text(
                f"🔊 Произошла ошибка при создании озвучки: {str(e)[:100]}...",
                chat_id=chat_id,
                message_id=status_message_id
            )
        except Exception as edit_error:
            if "message is not modified" not in str(edit_error):
                log_admin(f"{user_info_log} - Error updating error message: {edit_error}")







def process_thematic_podcast_request(user_id, chat_id, theme, original_message_id, status_message_id, process_key=None, duration_minutes=10, podcast_host_type='anna_mikhail'):
    """
    Main function to process thematic podcast generation request for private chats.

    Args:
        user_id: User ID who requested the podcast
        chat_id: Chat ID where the podcast was requested (should equal user_id for private chats)
        theme: Theme/topic for the podcast
        original_message_id: ID of the original message that triggered the request
        status_message_id: ID of the status message to update
        process_key: Unique identifier for this podcast process
        duration_minutes: Duration of podcast in minutes (3, 5, or 10), defaults to 10
        podcast_host_type: Type of podcast hosts ('anna_mikhail' or 'diana_sasha'), defaults to 'anna_mikhail'
    """
    user_info_log = f"user {user_id} (chat {chat_id}) [process {process_key}]"
    log_admin(f"{user_info_log} - Starting thematic podcast generation for theme: '{theme}'")

    # Данные подкаста уже очищены атомарно в handle_podcast_confirmation
    if process_key:
        log_admin(f"{user_info_log} - Process key: {process_key}")
    else:
        log_admin(f"{user_info_log} - No process_key provided")

    # Initialize status manager for thematic podcast with process parameters
    from podcast_status_manager import PodcastStatusManager
    process_params = {
        'user_id': user_id,
        'theme': theme,
        'podcast_type': 'thematic',
        'process_key': process_key,
        'duration_minutes': duration_minutes
    }
    status_manager = PodcastStatusManager(bot, chat_id, status_message_id, 'thematic', process_params, process_key, duration_minutes)

    # Запускаем новый менеджер статусов
    status_manager.start()

    # Check private message limits for private podcasts (only in private chats)
    if chat_id == user_id:  # Only check limits in private chats
        from rate_limiter import rate_limiter
        from admin_system import is_admin

        if not is_admin(user_id):  # Admins bypass all limits
            allowed, reason, wait_time = rate_limiter.check_private_limit(user_id, 'private_podcast', 3)

            if not allowed:
                status_manager.show_error('general', reason)
                return

            # Record the private podcast request
            rate_limiter.record_private_request(user_id, 'private_podcast')

    try:
        # Start with research stage - этап 0 уже установлен в start()

        # ЭТАП 1: RESEARCH - Исследование темы
        log_admin(f"{user_info_log} - Starting research stage for thematic podcast")

        research_results = []
        queries = []

        # Функция для синхронного обновления статуса research
        def update_status_sync(completed, total):
            try:
                # Обновляем сообщение о прогрессе поиска
                progress_text = f"🔍 Исследование темы ({completed}/{total} запросов выполнено)"
                status_manager.current_stage_text = progress_text
                status_manager._update_message()
            except Exception as e:
                log_admin(f"{user_info_log} - Error updating research status: {e}")

        try:
            # Шаг 1: Генерируем поисковые запросы
            log_admin(f"{user_info_log} - Generating research queries for theme: {theme}")
            queries = generate_research_queries(theme)
            if queries:
                log_admin(f"{user_info_log} - Generated {len(queries)} research queries: {queries}")

                # Показываем начальный статус поиска
                update_status_sync(0, len(queries))

                # Шаг 2: Выполняем множественный поиск асинхронно через asyncio.run
                async def async_research():
                    async def progress_callback(completed, total):
                        # Синхронный вызов из асинхронного контекста
                        update_status_sync(completed, total)

                    return await perform_multiple_searches(queries, progress_callback)

                # Выполняем асинхронный поиск
                import asyncio
                research_results = asyncio.run(async_research())
                log_admin(f"{user_info_log} - Research completed, got {len(research_results)} results")

            else:
                log_admin(f"{user_info_log} - Failed to generate research queries")

        except Exception as e:
            log_admin(f"{user_info_log} - Error during research: {e}")
            # Продолжаем без research в случае ошибки

        # Get user information for the prompt
        try:
            user_info = bot.get_chat(user_id)
            user_first_name = user_info.first_name or ""
            user_last_name = user_info.last_name or ""
            user_full_name = f"{user_first_name} {user_last_name}".strip()
            if not user_full_name:
                user_full_name = user_info.username or "пользователь"
        except Exception as e:
            log_admin(f"{user_info_log} - Error getting user info: {e}")
            user_full_name = "пользователь"

        # Get current date and time
        from datetime import datetime
        current_datetime = datetime.now().strftime('%d.%m.%Y %H:%M')

        # Format system prompt for thematic podcast based on selected host type
        from config import SYSTEM_PROMPT_THEMATIC_PODCAST_GENERATION, SYSTEM_PROMPT_THEMATIC_PODCAST_GENERATION_ANNA_MIKHAIL
        from utils import format_thematic_podcast_system_prompt

        # Choose the appropriate system prompt based on host type
        if podcast_host_type == 'diana_sasha':
            system_prompt_template = SYSTEM_PROMPT_THEMATIC_PODCAST_GENERATION
            log_admin(f"{user_info_log} - Using Diana & Sasha system prompt for thematic podcast")
        else:
            system_prompt_template = SYSTEM_PROMPT_THEMATIC_PODCAST_GENERATION_ANNA_MIKHAIL
            log_admin(f"{user_info_log} - Using Anna & Mikhail system prompt for thematic podcast")

        # ЭТАП 2: Подготавливаем информацию о ресерче для интеграции в системный промпт
        research_info = ""
        if research_results and any(result and not result.startswith("Поиск по запросу") for result in research_results):
            research_info = "\n\nВот дополнительная информация по вопросу юзера, найденная в интернете\n(ВЕДУЩИЕ ДОЛЖНЫ ПОЛЬЗОВАТЬСЯ ЕЙ БУДТО ОНИ И ТАК ЭТО ЗНАЮТ,\nОНИ НЕ ДОЛЖНЫ УПОМИНАТЬ ЧТО ЭТО В ИНТЕРНЕТЕ НАЙДЕНО):\n\n"

            for i, (query, result) in enumerate(zip(queries if queries else [], research_results), 1):
                if result and not result.startswith("Поиск по запросу"):
                    research_info += f"ЗАПРОС {i}: {query}\nОТВЕТ {i}: {result}\n\n"

            log_admin(f"{user_info_log} - Prepared research information for system prompt ({len(research_info)} characters)")
        else:
            log_admin(f"{user_info_log} - No valid research results to add to system prompt")

        formatted_system_prompt = format_thematic_podcast_system_prompt(
            system_prompt_template,
            theme,  # user_request
            user_full_name,  # user_nickname
            current_datetime,
            research_info,  # Передаем информацию о ресерче
            duration_minutes
        )

        # Move to dialogue generation stage
        status_manager.set_stage(1)

        # Simple dialogue prompt for thematic podcasts
        dialogue_prompt = f"Создай подкаст-диалог на тему: {theme}"

        log_admin(f"{user_info_log} - Generating thematic podcast dialogue")

        # Generate dialogue using Gemini API
        from api_clients import call_official_gemini_api
        dialogue_text = call_official_gemini_api(
            model_name="gemini-2.5-pro",
            history=[],
            user_text=dialogue_prompt,
            input_data=None,
            system_prompt=formatted_system_prompt,
            call_type="thematic_podcast_generation",
            user_id=user_id  # Pass user_id for ultrathink setting
        )

        if not dialogue_text or dialogue_text.strip() == "":
            status_manager.show_error('general', "Не удалось сгенерировать подкаст по данной теме. Попробуйте изменить тему или повторить запрос.")
            return

        log_admin(f"{user_info_log} - Generated thematic dialogue ({len(dialogue_text)} characters)")

        # Move to TTS stage
        status_manager.set_stage(2)

        # Clean first line from tags and remove duplicate lines before TTS
        dialogue_text = clean_first_line_tags(dialogue_text)
        dialogue_text = remove_duplicate_dialogue_lines(dialogue_text)

        # Generate audio using TTS
        log_admin(f"{user_info_log} - Generating thematic podcast audio")

        # TTS generation with retry logic
        MAX_TTS_TOTAL_RETRIES = 3  # суммарно 3 захода
        audio_data = None
        
        for tts_try in range(1, MAX_TTS_TOTAL_RETRIES + 1):
            try:
                from api_clients import call_gemini_tts_api
                audio_data = call_gemini_tts_api(dialogue_text, "thematic_podcast_tts", podcast_host_type)
                break  # 🎉 успех – выходим из цикла
            except RuntimeError as e:
                if str(e) == "TTS_FAILED_NO_AUDIO" and tts_try < MAX_TTS_TOTAL_RETRIES:
                    status_manager.set_current_stage_text(
                        "⚠️ Проблемы с озвучкой…\nПробуем другой ключ, подождите ещё немного."
                    )
                    status_manager.extend_total_duration(300)  # +5 мин
                    bot.send_chat_action(chat_id, "record_voice")
                    time.sleep(15)  # маленький back‑off
                    continue
                raise  # другие ошибки — наверх
            except Exception as tts_error:
                log_admin(f"{user_info_log} - Critical error during thematic TTS generation: {tts_error}", level="error")
                status_manager.show_error('general', f"Критическая ошибка при синтезе аудио: {str(tts_error)[:100]}...")
                return

        if not audio_data:
            status_manager.show_error('tts_rejected')
            return

        # Move to final stage
        status_manager.set_stage(3)

        # Save audio to file
        from utils import save_tts_audio
        import time
        timestamp = int(time.time())
        audio_file_path = save_tts_audio(audio_data, chat_id, timestamp)

        if not audio_file_path:
            status_manager.show_error('general', "Не удалось сохранить аудиофайл.")
            return

        # Generate title for the podcast
        podcast_title = f"Подкаст: {theme}"

        # Send audio file to user as voice message (unified with group behavior)
        try:
            # Complete the process and delete status message
            status_manager.complete()
            try:
                bot.delete_message(chat_id=chat_id, message_id=status_message_id)
            except Exception as e:
                log_admin(f"{user_info_log} - Error deleting status message: {e}")

            # Import safe caption function for proper formatting
            from utils import safe_caption_for_telegram
            safe_caption = safe_caption_for_telegram(f"🎙️ {podcast_title}")

            with open(audio_file_path, "rb") as audio_file:
                try:
                    # Try to send with reply first
                    sent_message = bot.send_voice(
                        chat_id=chat_id,
                        voice=audio_file,
                        caption=safe_caption,
                        reply_to_message_id=original_message_id,
                        timeout=120  # Увеличиваем таймаут для больших файлов
                    )
                except Exception as voice_error:
                    if "restricted receiving of video messages" in str(voice_error):
                        # User has restricted voice messages, fallback to audio
                        log_admin(f"{user_info_log} - Voice messages restricted, sending as audio with reply")
                        audio_file.seek(0)  # Reset file pointer
                        sent_message = bot.send_audio(
                            chat_id=chat_id,
                            audio=audio_file,
                            caption=safe_caption,
                            reply_to_message_id=original_message_id,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent thematic podcast as audio with reply")
                    else:
                        raise voice_error
                    log_admin(f"{user_info_log} - Successfully sent thematic podcast voice with reply")
                except Exception as reply_error:
                    if "message to be replied not found" in str(reply_error):
                        # Original message was deleted, send without reply
                        log_admin(f"{user_info_log} - Original message not found, sending thematic podcast without reply")
                        audio_file.seek(0)  # Reset file pointer
                        sent_message = bot.send_voice(
                            chat_id=chat_id,
                            voice=audio_file,
                            caption=safe_caption,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent thematic podcast voice without reply")
                    else:
                        raise reply_error  # Re-raise if it's a different error

        except Exception as send_error:
            log_admin(f"{user_info_log} - Error sending thematic podcast voice: {send_error}")
            # Fallback: send text message about completion
            try:
                from utils import safe_caption_for_telegram
                safe_title = safe_caption_for_telegram(podcast_title, max_length=200)  # Shorter limit for message text
                bot.send_message(
                    chat_id=chat_id,
                    text=f"🎙️ Подкаст готов, но не удалось отправить аудиофайл.\nНазвание: {safe_title}",
                    reply_to_message_id=original_message_id
                )
                log_admin(f"{user_info_log} - Sent fallback message for thematic podcast")
            except Exception as fallback_error:
                if "message to be replied not found" in str(fallback_error):
                    # Send fallback message without reply
                    bot.send_message(
                        chat_id=chat_id,
                        text=f"🎙️ Подкаст готов, но не удалось отправить аудиофайл.\nНазвание: {safe_title}"
                    )
                    log_admin(f"{user_info_log} - Sent fallback message for thematic podcast without reply")
                else:
                    log_admin(f"{user_info_log} - Error sending fallback message: {fallback_error}")
            status_manager.show_error('general', "Не удалось отправить аудиоподкаст.")

        # Clean up audio file
        try:
            import os
            if os.path.exists(audio_file_path):
                os.remove(audio_file_path)
                log_admin(f"{user_info_log} - Cleaned up audio file: {audio_file_path}")
        except Exception as e:
            log_admin(f"{user_info_log} - Error cleaning up audio file: {e}")

        # Complete the process
        status_manager.complete()
        log_admin(f"{user_info_log} - Thematic podcast generation completed successfully")

    except Exception as e:
        log_admin(f"{user_info_log} - Error in thematic podcast generation: {e}")
        status_manager.show_error('general', f"Произошла ошибка при создании подкаста: {str(e)[:100]}...")


def process_research_podcast_request(user_id, chat_id, research_key, original_message_id, status_message_id, user_preferences="", preferences_key=None):
    """
    Main function to process research podcast generation request.
    """
    user_info_log = f"user {user_id} (chat {chat_id})"
    log_admin(f"{user_info_log} - Starting research podcast generation for key: {research_key}")
    if user_preferences:
        log_admin(f"{user_info_log} - User preferences: {user_preferences[:100]}")

    # Очищаем данные подкаста в начале обработки
    if preferences_key:
        # Очистка данных подкаста напрямую
        log_admin(f"{user_info_log} - Cleaned up podcast preferences for key: {preferences_key}")

    # Initialize status manager for research podcast with process parameters
    from podcast_status_manager import PodcastStatusManager
    process_params = {
        'user_id': user_id,
        'research_key': research_key,
        'user_preferences': user_preferences,
        'podcast_type': 'research'
    }
    status_manager = PodcastStatusManager(bot, chat_id, status_message_id, 'research', process_params)

    # Запускаем новый менеджер статусов
    status_manager.start()

    # Check private message limits for private podcasts (only in private chats)
    if chat_id == user_id:  # Only check limits in private chats
        from rate_limiter import rate_limiter
        from admin_system import is_admin

        if not is_admin(user_id):  # Admins bypass all limits
            allowed, reason, wait_time = rate_limiter.check_private_limit(user_id, 'private_podcast', 3)

            if not allowed:
                status_manager.show_error('general', reason)
                return

            # Record the private podcast request
            rate_limiter.record_private_request(user_id, 'private_podcast')

    try:
        # Get research data
        from bot_globals import research_podcast_data, research_podcast_data_lock
        with research_podcast_data_lock:
            research_data = research_podcast_data.get(research_key)

        if not research_data:
            status_manager.show_error('general', "Данные исследования не найдены. Попробуйте создать подкаст сразу после получения результатов исследования.")
            return

        research_text = research_data['research_text']
        topic = research_data['topic']
        original_query = research_data.get('original_query', topic)  # Get original user query

        # Step 1: Analyze research data - этап 0 уже установлен в start()

        from api_clients import call_official_gemini_api

        # Format user preferences for the prompt
        preferences_text = ""
        if user_preferences.strip():
            preferences_text = f"\n\nПОЖЕЛАНИЯ ПОЛЬЗОВАТЕЛЯ:\n{user_preferences.strip()}\n\nКРИТИЧЕСКИ ВАЖНО: СТРОГО следуй пожеланиям пользователя! Если пользователь просит определенный стиль, тон, манеру речи (включая мат, если указано) - ОБЯЗАТЕЛЬНО выполни это требование. Пожелания пользователя имеют МАКСИМАЛЬНЫЙ приоритет над всеми остальными инструкциями по стилю."

        # Get user information for the prompt
        try:
            user_info = bot.get_chat(user_id)
            user_first_name = user_info.first_name or ""
            user_last_name = user_info.last_name or ""
            user_full_name = f"{user_first_name} {user_last_name}".strip()
            if not user_full_name:
                user_full_name = user_info.username or "пользователь"
        except Exception as e:
            log_admin(f"{user_info_log} - Error getting user info: {e}")
            user_full_name = "пользователь"

        # Format requester information for the prompt
        requester_info = f"\nПОДКАСТ ЗАПРОСИЛ: {user_full_name}\nВ диалоге можете 1-2 раза упомянуть, что отвечаете {user_full_name} или что {user_full_name} попросил исследование, но не часто - только если это естественно вписывается в разговор.\n"

        # Format system prompt with user preferences and chat type constraints
        from utils import format_research_podcast_system_prompt
        is_private_chat = (chat_id == user_id)
        # Research podcast functionality has been removed
        log_admin(f"{user_info_log} - Research podcast functionality has been removed")
        return

        log_admin(f"{user_info_log} - Using {'private' if is_private_chat else 'group'} chat constraints for research podcast")



        dialogue_prompt = f"ПЕРВОНАЧАЛЬНЫЙ ЗАПРОС ПОЛЬЗОВАТЕЛЯ (ВЫСШИЙ ПРИОРИТЕТ): {original_query}\n\nСоздай образовательный подкаст-диалог на основе следующего исследования:\n\nТема: {topic}\n\nИсследование:\n{research_text}\n\nВАЖНО: Первоначальный запрос пользователя имеет высший приоритет - обязательно учитывай его при создании диалога, а исследование используй как дополнительную информацию."

        # Check which model to use for this group
        group_model = get_group_model_setting(chat_id)
        log_admin(f"{user_info_log} - Using model: {group_model} for research podcast generation")

        if group_model == "grok":
            # Use grok-4 via Navy API with fallback to Gemini
            # Combine system prompt and user prompt for grok
            if formatted_system_prompt:
                full_prompt = f"{formatted_system_prompt}\n\n{dialogue_prompt}"
            else:
                full_prompt = dialogue_prompt

            dialogue_text = generate_with_grok_fallback(
                prompt=full_prompt,
                chat_id=chat_id,
                system_prompt=formatted_system_prompt,
                call_type="research_podcast_generation"
            )
        else:
            # Use Gemini (default behavior) - изолируем в thread pool
            def dialogue_worker():
                return call_official_gemini_api(
                    model_name="gemini-2.5-pro",
                    history=[],
                    user_text=dialogue_prompt,
                    input_data=None,
                    system_prompt=formatted_system_prompt,
                    call_type="research_podcast_generation",
                    user_id=user_id  # Pass user_id for ultrathink setting
                )

            future = submit_task(dialogue_worker)
            dialogue_text = future.result(timeout=GENAI_REQUEST_TIMEOUT)

        if not dialogue_text or dialogue_text.startswith("ОШИБКА") or dialogue_text.startswith("Извините"):
            status_manager.show_error('general', f"Ошибка при генерации диалога: {dialogue_text[:100]}...")
            return

        # Step 2: Generate audio using TTS
        status_manager.set_stage(2)

        # Clean first line from tags and remove duplicate lines before TTS
        dialogue_text = clean_first_line_tags(dialogue_text)
        dialogue_text = remove_duplicate_dialogue_lines(dialogue_text)

        # Generate single-part research podcast (new default behavior)
        log_admin(f"{user_info_log} - Generating single-part research podcast")

        # TTS generation with retry logic
        MAX_TTS_TOTAL_RETRIES = 3  # суммарно 3 захода
        audio_data = None
        
        for tts_try in range(1, MAX_TTS_TOTAL_RETRIES + 1):
            try:
                from api_clients import call_gemini_tts_api

                # Изолируем блокирующую TTS операцию в thread pool
                def tts_worker():
                    return call_gemini_tts_api(dialogue_text, "research_podcast_tts", "anna_mikhail")

                future = submit_task(tts_worker)
                audio_data = future.result(timeout=GENAI_TTS_TIMEOUT)
                break  # 🎉 успех – выходим из цикла
            except RuntimeError as e:
                if str(e) == "TTS_FAILED_NO_AUDIO" and tts_try < MAX_TTS_TOTAL_RETRIES:
                    status_manager.set_current_stage_text(
                        "⚠️ Проблемы с озвучкой…\nПробуем другой ключ, подождите ещё немного."
                    )
                    status_manager.extend_total_duration(300)  # +5 мин
                    bot.send_chat_action(chat_id, "record_voice")
                    time.sleep(15)  # маленький back‑off
                    continue
                raise  # другие ошибки — наверх
            except Exception as tts_error:
                log_admin(f"{user_info_log} - Critical error during research TTS generation: {tts_error}", level="error")
                status_manager.show_error('general', f"Критическая ошибка при синтезе аудио: {str(tts_error)[:100]}...")
                return

        if not audio_data:
            status_manager.show_error('tts_rejected')
            return

        from utils import save_podcast_audio
        import time
        timestamp = int(time.time())
        audio_filepath = save_podcast_audio(audio_data, chat_id, timestamp, process_key=process_key)

        # Step 3: Prepare for sending audio
        status_manager.set_stage(3)

        if not audio_filepath:
            status_manager.show_error('general', "Ошибка при сохранении аудиофайла.")
            return

        # Generate improved title based on research topic using GPT-4.1
        from utils import generate_research_podcast_title
        title = generate_research_podcast_title(topic)

        # Remove podcast button from original research message and send audio
        try:
            # Remove the podcast button from the original research message
            bot.edit_message_reply_markup(
                chat_id=chat_id,
                message_id=original_message_id,
                reply_markup=None
            )
            log_admin(f"{user_info_log} - Removed podcast button from research message")
        except Exception as e_remove_button:
            log_admin(f"{user_info_log} - Error removing podcast button: {e_remove_button}")

        # Complete the process and clean up (this will also delete the status message)
        status_manager.complete()

        try:
            # Import safe caption function
            from utils import safe_caption_for_telegram
            safe_caption = safe_caption_for_telegram(f"🎙️ {title}")

            with open(audio_filepath, "rb") as audio_file:
                try:
                    # Try to send with reply first
                    bot.send_voice(
                        chat_id=chat_id,
                        voice=audio_file,
                        caption=safe_caption,
                        reply_to_message_id=original_message_id,
                        timeout=120  # Увеличиваем таймаут для больших файлов
                    )
                except Exception as voice_error:
                    if "restricted receiving of video messages" in str(voice_error):
                        # User has restricted voice messages, fallback to audio
                        log_admin(f"{user_info_log} - Voice messages restricted, sending as audio with reply")
                        audio_file.seek(0)  # Reset file pointer
                        bot.send_audio(
                            chat_id=chat_id,
                            audio=audio_file,
                            caption=safe_caption,
                            reply_to_message_id=original_message_id,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent Anna & Mikhail podcast as audio with reply")
                    else:
                        raise voice_error
                except Exception as voice_error:
                    if "restricted receiving of video messages" in str(voice_error):
                        # User has restricted voice messages, fallback to audio
                        log_admin(f"{user_info_log} - Voice messages restricted, sending as audio with reply")
                        audio_file.seek(0)  # Reset file pointer
                        bot.send_audio(
                            chat_id=chat_id,
                            audio=audio_file,
                            caption=safe_caption,
                            reply_to_message_id=original_message_id,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent Anna & Mikhail podcast as audio with reply")
                    else:
                        raise voice_error
                    log_admin(f"{user_info_log} - Successfully sent research podcast voice with reply")
                except Exception as reply_error:
                    if "message to be replied not found" in str(reply_error):
                        # Original message was deleted, send without reply
                        log_admin(f"{user_info_log} - Original message not found, sending research podcast without reply")
                        audio_file.seek(0)  # Reset file pointer
                        bot.send_voice(
                            chat_id=chat_id,
                            voice=audio_file,
                            caption=safe_caption,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent research podcast voice without reply")
                    else:
                        raise reply_error  # Re-raise if it's a different error
        except Exception as send_error:
            log_admin(f"{user_info_log} - Error sending research podcast voice: {send_error}")
            # Fallback: send text message about completion
            try:
                # Get file size for fallback message
                try:
                    file_size = os.path.getsize(audio_filepath) if audio_filepath and os.path.exists(audio_filepath) else 0
                except:
                    file_size = 0

                bot.send_message(
                    chat_id=chat_id,
                    text=f"🎙️ Подкаст исследования готов, но не удалось отправить аудиофайл.\nТема: {topic}\nРазмер: {file_size} байт",
                    reply_to_message_id=original_message_id
                )
            except Exception as fallback_error:
                if "message to be replied not found" in str(fallback_error):
                    # Send fallback message without reply
                    try:
                        file_size = os.path.getsize(audio_filepath) if audio_filepath and os.path.exists(audio_filepath) else 0
                    except:
                        file_size = 0

                    bot.send_message(
                        chat_id=chat_id,
                        text=f"🎙️ Подкаст исследования готов, но не удалось отправить аудиофайл.\nТема: {topic}\nРазмер: {file_size} байт"
                    )
                else:
                    raise fallback_error

        # Clean up temporary file
        try:
            os.remove(audio_filepath)
        except:
            pass

        # Clean up research data
        with research_podcast_data_lock:
            if research_key in research_podcast_data:
                del research_podcast_data[research_key]

        # Complete the process
        status_manager.complete()
        log_admin(f"{user_info_log} - Research podcast generation completed successfully")

    except Exception as e:
        log_admin(f"{user_info_log} - Error in research podcast generation: {e}")
        status_manager.show_error('general', f"Произошла ошибка при создании подкаста исследования: {str(e)[:100]}...")


def process_research_podcast_diana_sasha_request(user_id, chat_id, research_key, original_message_id, status_message_id, preferences_key=None):
    """
    Main function to process Diana & Sasha style research podcast generation request.
    """
    user_info_log = f"user {user_id} (chat {chat_id})"
    log_admin(f"{user_info_log} - Starting Diana & Sasha research podcast generation for research_key: '{research_key}'")

    # Очищаем данные подкаста в начале обработки
    if preferences_key:
        # Очистка данных подкаста напрямую
        log_admin(f"{user_info_log} - Cleaned up podcast preferences for key: {preferences_key}")

    # Initialize PodcastStatusManager for Diana & Sasha with process parameters
    from podcast_status_manager import PodcastStatusManager
    from config import DIANA_SASHA_PODCAST_STATUSES

    process_params = {
        'user_id': user_id,
        'research_key': research_key,
        'podcast_type': 'diana_sasha'
    }
    status_manager = PodcastStatusManager(bot, chat_id, status_message_id, 'diana_sasha', process_params)

    # Запускаем новый менеджер статусов
    status_manager.start()

    try:
        # Get research data
        from bot_globals import research_podcast_data, research_podcast_data_lock
        with research_podcast_data_lock:
            research_data = research_podcast_data.get(research_key)

        if not research_data:
            status_manager.show_error('general', "Данные исследования не найдены. Попробуйте создать подкаст сразу после получения результатов исследования.")
            return

        research_text = research_data['research_text']
        topic = research_data['topic']
        original_query = research_data.get('original_query', topic)  # Get original user query

        # Step 1: Generate podcast dialogue with Diana & Sasha style
        # Update to stage 1 (generate_dialogue)
        status_manager.start_auto_update(1, custom_messages=DIANA_SASHA_PODCAST_STATUSES)

        from api_clients import call_official_gemini_api

        # Get user information for the prompt
        try:
            user_info = bot.get_chat(user_id)
            user_first_name = user_info.first_name or ""
            user_last_name = user_info.last_name or ""
            user_full_name = f"{user_first_name} {user_last_name}".strip()
            if not user_full_name:
                user_full_name = user_info.username or "пользователь"
        except Exception as e:
            log_admin(f"{user_info_log} - Error getting user info: {e}")
            user_full_name = "пользователь"

        # Format requester information for the prompt
        requester_info = f"\nПОДКАСТ ЗАПРОСИЛ: {user_full_name}\nВ диалоге можете 1-2 раза упомянуть, что отвечаете {user_full_name} или что {user_full_name} попросил исследование, но не часто - только если это естественно вписывается в разговор.\n"

        # Format system prompt for Diana & Sasha style with chat type constraints
        from utils import format_research_podcast_system_prompt
        is_private_chat = (chat_id == user_id)
        # Research podcast functionality has been removed
        log_admin(f"{user_info_log} - Research podcast functionality has been removed")
        return

        log_admin(f"{user_info_log} - Using {'private' if is_private_chat else 'group'} chat constraints for Diana & Sasha podcast")

        dialogue_prompt = f"ПЕРВОНАЧАЛЬНЫЙ ЗАПРОС ПОЛЬЗОВАТЕЛЯ (ВЫСШИЙ ПРИОРИТЕТ): {original_query}\n\nСоздай неформальный подкаст-диалог на основе следующего исследования:\n\nТема: {topic}\n\nИсследование:\n{research_text}\n\nВАЖНО: Первоначальный запрос пользователя имеет высший приоритет - обязательно учитывай его при создании диалога, а исследование используй как дополнительную информацию."

        dialogue_text = call_official_gemini_api(
            model_name="gemini-2.5-pro",
            history=[],
            user_text=dialogue_prompt,
            input_data=None,
            system_prompt=formatted_system_prompt,
            call_type="research_podcast_diana_sasha_generation",
            user_id=user_id  # Pass user_id for ultrathink setting
        )

        if not dialogue_text or dialogue_text.startswith("ОШИБКА") or dialogue_text.startswith("Извините"):
            status_manager.show_error('general', f"Ошибка при генерации диалога: {dialogue_text[:100]}...")
            return

        log_admin(f"{user_info_log} - Generated Diana & Sasha dialogue ({len(dialogue_text)} characters, target: 10000)")

        # Clean first line from tags and remove duplicate lines before TTS
        dialogue_text = clean_first_line_tags(dialogue_text)
        dialogue_text = remove_duplicate_dialogue_lines(dialogue_text)

        # Step 2: Synthesize speech
        # Update to stage 2 (synthesize_speech)
        status_manager.start_auto_update(2, custom_messages=DIANA_SASHA_PODCAST_STATUSES)

        log_admin(f"{user_info_log} - Generating single-part Diana & Sasha podcast")

        # TTS generation with retry logic
        MAX_TTS_TOTAL_RETRIES = 3  # суммарно 3 захода
        audio_data = None
        
        for tts_try in range(1, MAX_TTS_TOTAL_RETRIES + 1):
            try:
                from api_clients import call_gemini_tts_api

                # Изолируем блокирующую TTS операцию в thread pool
                def tts_worker():
                    return call_gemini_tts_api(dialogue_text, "research_podcast_diana_sasha_tts", "diana_sasha")

                future = submit_task(tts_worker)
                audio_data = future.result(timeout=GENAI_TTS_TIMEOUT)
                break  # 🎉 успех – выходим из цикла
            except RuntimeError as e:
                if str(e) == "TTS_FAILED_NO_AUDIO" and tts_try < MAX_TTS_TOTAL_RETRIES:
                    status_manager.set_current_stage_text(
                        "⚠️ Проблемы с озвучкой…\nПробуем другой ключ, подождите ещё немного."
                    )
                    status_manager.extend_total_duration(300)  # +5 мин
                    bot.send_chat_action(chat_id, "record_voice")
                    time.sleep(15)  # маленький back‑off
                    continue
                raise  # другие ошибки — наверх
            except Exception as tts_error:
                log_admin(f"{user_info_log} - Critical error during Diana & Sasha TTS generation: {tts_error}", level="error")
                status_manager.show_error('general', f"Критическая ошибка при синтезе аудио: {str(tts_error)[:100]}...")
                return

        if not audio_data:
            status_manager.show_error('tts_rejected', "Не удалось создать аудиоподкаст исследования (возможно, контент был отклонен TTS-сервисом). Попробуйте изменить стиль или создать подкаст заново.")
            return

        from utils import save_podcast_audio
        import time
        timestamp = int(time.time())
        audio_filepath = save_podcast_audio(audio_data, chat_id, timestamp)

        # Step 3: Prepare for sending audio
        # Update to stage 3 (prepare_sending)
        status_manager.start_auto_update(3, custom_messages=DIANA_SASHA_PODCAST_STATUSES)

        import tempfile
        from utils import generate_research_podcast_title

        # Generate improved title based on research topic using GPT-4.1
        title = generate_research_podcast_title(topic)

        # Remove podcast button from original research message and send audio
        try:
            # Remove the podcast button from the original research message
            bot.edit_message_reply_markup(
                chat_id=chat_id,
                message_id=original_message_id,
                reply_markup=None
            )
            log_admin(f"{user_info_log} - Removed podcast button from research message")
        except Exception as e_remove_button:
            log_admin(f"{user_info_log} - Error removing podcast button: {e_remove_button}")

        # Complete the process (this will also delete the status message)
        status_manager.complete()

        if not audio_filepath:
            try:
                bot.send_message(
                    chat_id,
                    "🎙️ Ошибка при сохранении аудиофайла для подкаста Дианочки и Саши."
                )
            except:
                pass
            return

        # Critical check: verify Diana & Sasha file exists before attempting to send
        if not os.path.exists(audio_filepath):
            log_admin(f"{user_info_log} - CRITICAL ERROR: Diana & Sasha audio file does not exist: {audio_filepath}", level="error")
            try:
                bot.send_message(
                    chat_id,
                    "🎙️ Ошибка: не удалось создать аудиофайл подкаста Дианочки и Саши. Попробуйте позже."
                )
            except:
                pass
            return

        # Verify file is not empty
        try:
            file_size = os.path.getsize(audio_filepath)
            if file_size == 0:
                log_admin(f"{user_info_log} - CRITICAL ERROR: Diana & Sasha audio file is empty: {audio_filepath}", level="error")
                try:
                    bot.send_message(
                        chat_id,
                        "🎙️ Ошибка: создан пустой аудиофайл подкаста Дианочки и Саши. Попробуйте позже."
                    )
                except:
                    pass
                return
            log_admin(f"{user_info_log} - Diana & Sasha audio file verified: {audio_filepath} (size: {file_size} bytes)")
        except Exception as size_error:
            log_admin(f"{user_info_log} - Error checking Diana & Sasha file size: {size_error}", level="error")

        try:
            # Import safe caption function
            from utils import safe_caption_for_telegram
            safe_caption = safe_caption_for_telegram(f"🎙️ <b>{title}</b>\n\n🔞 <i>Дианочка и Саша</i>")

            with open(audio_filepath, 'rb') as audio_file:
                try:
                    bot.send_voice(
                        chat_id,
                        audio_file,
                        caption=safe_caption,
                        parse_mode="HTML",
                        reply_to_message_id=None,
                        timeout=120  # Увеличиваем таймаут для больших файлов
                    )
                except Exception as voice_error:
                    if "restricted receiving of video messages" in str(voice_error):
                        # User has restricted voice messages, fallback to audio
                        log_admin(f"{user_info_log} - Voice messages restricted, sending research podcast as audio")
                        audio_file.seek(0)  # Reset file pointer
                        bot.send_audio(
                            chat_id,
                            audio_file,
                            caption=safe_caption,
                            parse_mode="HTML",
                            reply_to_message_id=None,
                            timeout=120
                        )
                        log_admin(f"{user_info_log} - Successfully sent Diana & Sasha research podcast as audio")
                    else:
                        raise voice_error
            log_admin(f"{user_info_log} - Diana & Sasha research podcast sent successfully as voice message")
        except Exception as e_send:
            log_admin(f"{user_info_log} - Error sending Diana & Sasha research podcast voice: {e_send}")
            # Try to send as audio file instead
            try:
                # Import safe caption function
                from utils import safe_caption_for_telegram
                safe_caption = safe_caption_for_telegram(f"🎙️ <b>{title}</b>\n\n🔞 <i>Дианочка и Саша</i>")

                with open(audio_filepath, 'rb') as audio_file:
                    bot.send_audio(
                        chat_id,
                        audio_file,
                        title=title,
                        caption=safe_caption,
                        parse_mode="HTML",
                        reply_to_message_id=None,
                        timeout=120
                    )
                log_admin(f"{user_info_log} - Diana & Sasha research podcast sent as audio file")
            except Exception as e_send_audio:
                log_admin(f"{user_info_log} - Error sending Diana & Sasha research podcast audio: {e_send_audio}")

        # Clean up temporary file
        try:
            os.remove(audio_filepath)
        except:
            pass

        # Complete the process
        status_manager.complete()
        log_admin(f"{user_info_log} - Diana & Sasha research podcast generation completed successfully")

    except Exception as e:
        log_admin(f"{user_info_log} - Error in Diana & Sasha research podcast generation: {e}")
        status_manager.show_error('general', f"Произошла ошибка при создании подкаста исследования: {str(e)[:100]}...")













# --- Direct Video Processing for Private Chats ---
def process_video_direct(user_id, chat_id, file_id, message_id, caption=None):
    """
    Обрабатывает видео напрямую через Gemini 2.5 Flash без транскрипции.
    Используется только для личных чатов.
    """
    user_info_log = f"user {user_id}"
    log_admin(f"{user_info_log} - Processing video directly via Gemini 2.5 Flash")

    # Check private message limits for AI responses
    from rate_limiter import rate_limiter, format_time_remaining
    from admin_system import is_admin

    if not is_admin(user_id):  # Admins bypass all limits
        allowed, reason, wait_time = rate_limiter.check_private_limit(user_id, 'ai_response', 80)

        if not allowed:
            try:
                bot.send_message(
                    chat_id,
                    f"🤖 {reason}",
                    reply_to_message_id=message_id
                )
            except Exception as e:
                log_admin(f"{user_info_log} - Error sending AI response limit message: {e}")
            return

        # Record the AI response request
        rate_limiter.record_private_request(user_id, 'ai_response')

    # Set lightning reaction and start typing status instead of separate messages
    reaction_set = False
    typing_manager = None
    try:
        set_reaction(bot, chat_id, message_id, "⚡")
        reaction_set = True
        log_admin(f"{user_info_log} - Set ⚡ reaction on video message")

        typing_manager = TypingStatusManager(bot, chat_id)
        typing_manager.start()
        log_admin(f"{user_info_log} - Started typing status for video processing")
    except Exception as e:
        log_admin(f"{user_info_log} - Error setting reaction/typing status: {e}")

    try:
        # Get file info first to check size
        file_info = bot.get_file(file_id)

        # Check file size limit (50MB = 52,428,800 bytes)
        MAX_VIDEO_SIZE = 52428800  # 50MB in bytes
        if file_info.file_size and file_info.file_size > MAX_VIDEO_SIZE:
            size_mb = file_info.file_size / (1024 * 1024)
            error_msg = f"❌ Видео слишком большое ({size_mb:.1f} МБ). Максимальный размер: 50 МБ."
            log_admin(f"{user_info_log} - Video file too large: {size_mb:.1f}MB")

            # Stop typing and remove reaction
            if typing_manager:
                try:
                    typing_manager.stop()
                except:
                    pass
            if reaction_set:
                try:
                    remove_reaction(bot, chat_id, message_id)
                except:
                    pass

            # Send error message
            try:
                bot.send_message(
                    chat_id,
                    error_msg,
                    reply_to_message_id=message_id
                )
            except Exception as send_error:
                log_admin(f"{user_info_log} - Error sending size limit message: {send_error}")
            return

        # Download video file with memory optimization
        log_admin(f"{user_info_log} - Downloading video file ({file_info.file_size} bytes)")
        video_bytes = bot.download_file(file_info.file_path)

        # Convert to base64 with memory management
        try:
            base64_video = base64.b64encode(video_bytes).decode("utf-8")

            # Immediately clear video_bytes from memory
            del video_bytes
            import gc
            gc.collect()

            log_admin(f"{user_info_log} - Base64 encoded video size: {len(base64_video)} characters")

        except MemoryError as mem_err:
            log_admin(f"{user_info_log} - Memory error during base64 encoding for video: {mem_err}")

            # Stop typing and remove reaction
            if typing_manager:
                try:
                    typing_manager.stop()
                except:
                    pass
            if reaction_set:
                try:
                    remove_reaction(bot, chat_id, message_id)
                except:
                    pass

            # Send error message
            try:
                bot.send_message(
                    chat_id,
                    "❌ Недостаточно памяти для обработки видео файла.",
                    reply_to_message_id=message_id
                )
            except Exception as send_error:
                log_admin(f"{user_info_log} - Error sending memory error message: {send_error}")
            return

        # Determine MIME type based on file extension
        mime_type = "video/mp4"  # Default
        if file_info.file_path:
            ext = os.path.splitext(file_info.file_path)[1].lower()
            if ext == ".mov":
                mime_type = "video/quicktime"
            elif ext == ".avi":
                mime_type = "video/x-msvideo"
            elif ext == ".webm":
                mime_type = "video/webm"
            elif ext == ".mkv":
                mime_type = "video/x-matroska"

        # Prepare video data for Gemini (correct format for inline video)
        video_data = [{
            "mime_type": mime_type,
            "data": base64_video
        }]

        # Prepare user text - use caption if provided, otherwise default prompt
        if caption and caption.strip():
            user_text = caption.strip()
            log_admin(f"{user_info_log} - Using video caption as user text: '{user_text[:100]}{'...' if len(user_text) > 100 else ''}'")
        else:
            user_text = "Опиши что происходит в этом видео"
            log_admin(f"{user_info_log} - No caption provided, using default prompt")

        # Get conversation history (private chat uses user_id as context_key)
        context_key = user_id  # For private chats
        with user_conversations_lock:
            history = list(user_conversations[context_key])

        # Get system prompt for private chat
        from config import SYSTEM_PROMPT_MAIN
        from datetime import datetime
        import pytz
        moscow_tz = pytz.timezone('Europe/Moscow')
        current_datetime_msk = datetime.now(moscow_tz).strftime("%d.%m.%Y %H:%M")

        # Get user information for system prompt
        try:
            user_info = bot.get_chat(user_id)
            user_first_name = user_info.first_name or ""
            user_last_name = user_info.last_name or ""
            user_username = user_info.username or ""

            # Format user info string
            user_info_parts = []
            if user_first_name or user_last_name:
                full_name = f"{user_first_name} {user_last_name}".strip()
                user_info_parts.append(f"Имя: {full_name}")
            if user_username:
                user_info_parts.append(f"Username: @{user_username}")

            user_info_str = ", ".join(user_info_parts) if user_info_parts else "Информация недоступна"

        except Exception as e:
            log_admin(f"Error getting user info for video analysis system prompt: {e}")
            user_info_str = "Информация недоступна"

        system_instruction = SYSTEM_PROMPT_MAIN.format(
            current_datetime_msk=current_datetime_msk,
            user_info=user_info_str
        )

        # Call Gemini 2.5 Flash API with video - изолируем в thread pool
        def video_worker():
            return call_gemini_2_5_flash_api(
                history=history,
                user_text=user_text,
                input_data=video_data,
                system_prompt=system_instruction,
                call_type="video_analysis"
            )

        future = submit_task(video_worker)
        response = future.result(timeout=GENAI_REQUEST_TIMEOUT)

        # Clear video data from memory after API call
        del video_data
        del base64_video
        import gc
        gc.collect()
        log_admin(f"{user_info_log} - Cleared video data from memory after API call")

        # Stop typing status
        if typing_manager:
            try:
                typing_manager.stop()
                log_admin(f"{user_info_log} - Stopped typing status after video processing")
            except Exception as e:
                log_admin(f"{user_info_log} - Error stopping typing status: {e}")

        # Send response
        if response and response.strip():
            # Clean and send response
            cleaned_response = clean_response_text(response)

            # Parse FILE tags from the cleaned response
            final_response_text, files_to_send = parse_file_tags(cleaned_response)

            # Send response with proper formatting (no parse_mode parameter)
            final_message_id = send_long_message(
                bot,
                chat_id,
                final_response_text,
                reply_to_message_id=message_id,
                user_id=user_id
            )

            # Send parsed files if any were found
            if final_message_id and files_to_send:
                try:
                    sent_file_ids = send_parsed_files(
                        bot, chat_id, files_to_send,
                        reply_to_message_id=message_id,
                        user_id=user_id
                    )
                    if sent_file_ids:
                        log_admin(f"{user_info_log} - Successfully sent {len(sent_file_ids)} files from FILE tags (video processing)")
                except Exception as e_send_files:
                    log_admin(f"{user_info_log} - Error sending parsed files (video processing): {e_send_files}", level="error")

            # Save to conversation history
            with user_conversations_lock:
                # Add user message to history
                user_conversations[context_key].append({
                    "role": "user",
                    "parts": [{"text": f"[Видео] {user_text}"}]
                })
                # Add model response to history (use final_response_text without FILE tags)
                user_conversations[context_key].append({
                    "role": "model",
                    "parts": [{"text": final_response_text}]
                })

            # Remove lightning reaction and add celebration reaction for successful processing
            if reaction_set:
                try:
                    remove_reaction(bot, chat_id, message_id)
                    set_reaction(bot, chat_id, message_id, "🎉")
                    log_admin(f"{user_info_log} - Replaced ⚡ with 🎉 reaction after successful video processing")
                except Exception as e:
                    log_admin(f"{user_info_log} - Error updating reaction after video processing: {e}")

            log_admin(f"{user_info_log} - Video processed successfully via Gemini 2.5 Flash")
        else:
            # Stop typing and remove reaction on error
            if typing_manager:
                try:
                    typing_manager.stop()
                except:
                    pass
            if reaction_set:
                try:
                    remove_reaction(bot, chat_id, message_id)
                except:
                    pass

            error_msg = "❌ Не удалось обработать видео. Попробуйте позже."
            bot.send_message(
                chat_id,
                error_msg,
                reply_to_message_id=message_id
            )
            log_admin(f"{user_info_log} - Empty response from Gemini 2.5 Flash for video")

    except Exception as e:
        log_admin(f"{user_info_log} - Error in process_video_direct: {e}\n{traceback.format_exc()}")

        # Stop typing and remove reaction on error
        if typing_manager:
            try:
                typing_manager.stop()
            except:
                pass
        if reaction_set:
            try:
                remove_reaction(bot, chat_id, message_id)
            except:
                pass

        # Send error message
        error_msg = f"❌ Ошибка при обработке видео: {str(e)[:100]}"
        try:
            bot.send_message(
                chat_id,
                error_msg,
                reply_to_message_id=message_id
            )
        except Exception as send_error:
            log_admin(f"{user_info_log} - Error sending video error message: {send_error}")