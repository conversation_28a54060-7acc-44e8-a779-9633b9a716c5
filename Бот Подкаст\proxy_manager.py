"""
Модуль управления прокси для Gemini API.
Обеспечивает загрузку конфигурации прокси, валидацию настроек и thread-safe операции.
"""

import json
import os
import threading
import time
from typing import Dict, Optional, Tuple, Any
from urllib.parse import urlparse

# Импорт функции логирования
try:
    from bot_globals import log_admin
except ImportError:
    # Fallback если bot_globals недоступен
    def log_admin(message, level="info"):
        print(f"[{level.upper()}] {message}")


class ProxyManager:
    """
    Менеджер для управления прокси конфигурацией для Gemini API.
    Обеспечивает thread-safe операции и кэширование конфигурации.
    """

    def __init__(self, config_path: str = None):
        """
        Инициализация менеджера прокси.

        Args:
            config_path: Путь к файлу конфигурации прокси (если None, будет автоматический поиск)
        """
        self.config_path = config_path or self._find_config_file()
        self._config_cache: Optional[Dict[str, Any]] = None
        self._cache_timestamp: float = 0
        self._cache_ttl: float = 300  # 5 минут кэширования
        self._lock = threading.RLock()  # Используем RLock для возможности повторного захвата
        
        # Кэширование результатов проверки соединения
        self._connection_test_cache: Optional[bool] = None
        self._connection_test_timestamp: float = 0
        self._connection_test_ttl: float = 60  # 1 минута кэширования для проверки соединения

        log_admin(f"[ProxyManager] Initialized with config path: {self.config_path}", level="info")

    def _find_config_file(self) -> str:
        """
        Ищет файл конфигурации прокси в нескольких возможных местах.
        Приоритет отдается серверным путям, затем локальным.

        Returns:
            Путь к найденному файлу или путь по умолчанию
        """
        # Пути в порядке приоритета (серверные пути сначала)
        possible_paths = [
            # Серверные пути (высокий приоритет)
            "/home/<USER>/proxy_config.json",  # Корень контейнера
            "/home/<USER>",            # Домашняя директория в контейнере
            "/app/proxy_config.json",             # Директория приложения
            "/opt/proxy_config.json",             # Системная директория
            "/etc/proxy_config.json",             # Конфигурационная директория

            # Локальные пути относительно скрипта
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "proxy_config.json"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "proxy_config.json"),

            # Относительные пути
            "proxy_config.json",                  # Текущая директория
            "../proxy_config.json",               # Родительская директория
            "../../proxy_config.json",            # Дедушкина директория
            "../../../proxy_config.json",         # Прадедушкина директория

            # Домашняя директория пользователя
            os.path.expanduser("~/proxy_config.json"),

            # Дополнительные системные пути
            "/usr/local/etc/proxy_config.json",
            "/var/lib/proxy_config.json",
            "/tmp/proxy_config.json",
        ]

        log_admin(f"[ProxyManager] Searching for proxy config in {len(possible_paths)} locations...", level="debug")

        for i, path in enumerate(possible_paths, 1):
            try:
                if os.path.exists(path):
                    log_admin(f"[ProxyManager] Found proxy config at: {path} (priority {i})", level="info")
                    return path
            except Exception as e:
                # Игнорируем ошибки доступа к файлам (например, права доступа)
                log_admin(f"[ProxyManager] Error checking path {path}: {e}", level="debug")
                continue

        # Если файл не найден, возвращаем путь по умолчанию
        default_path = "../proxy_config.json"
        log_admin(f"[ProxyManager] Proxy config not found in any location, using default: {default_path}", level="warning")
        return default_path

    def load_config(self, force_reload: bool = False) -> Optional[Dict[str, Any]]:
        """
        Загружает конфигурацию прокси из файла с кэшированием.
        
        Args:
            force_reload: Принудительная перезагрузка конфигурации
            
        Returns:
            Словарь с конфигурацией прокси или None при ошибке
        """
        with self._lock:
            current_time = time.time()
            
            # Проверяем кэш
            if (not force_reload and 
                self._config_cache is not None and 
                (current_time - self._cache_timestamp) < self._cache_ttl):
                log_admin("[ProxyManager] Using cached proxy configuration", level="debug")
                return self._config_cache
            
            try:
                # Проверяем существование файла
                if not os.path.exists(self.config_path):
                    log_admin(f"[ProxyManager] Proxy config file not found: {self.config_path}", level="warning")
                    return None
                
                # Загружаем конфигурацию
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    config = json.load(file)
                
                # Валидируем конфигурацию
                if not self._validate_config(config):
                    log_admin("[ProxyManager] Invalid proxy configuration", level="error")
                    return None
                
                # Обновляем кэш
                self._config_cache = config
                self._cache_timestamp = current_time
                
                log_admin(f"[ProxyManager] Proxy configuration loaded successfully", level="info")
                return config
                
            except json.JSONDecodeError as e:
                log_admin(f"[ProxyManager] JSON decode error in proxy config: {e}", level="error")
                return None
            except Exception as e:
                log_admin(f"[ProxyManager] Error loading proxy config: {e}", level="error")
                return None
    
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Валидирует конфигурацию прокси.
        
        Args:
            config: Словарь с конфигурацией
            
        Returns:
            True если конфигурация валидна, False иначе
        """
        try:
            # Проверяем наличие основной секции proxy
            if 'proxy' not in config:
                log_admin("[ProxyManager] Missing 'proxy' section in config", level="error")
                return False
            
            proxy_config = config['proxy']
            
            # Проверяем обязательные поля
            required_fields = ['host', 'http_port', 'enabled']
            for field in required_fields:
                if field not in proxy_config:
                    log_admin(f"[ProxyManager] Missing required field '{field}' in proxy config", level="error")
                    return False
            
            # Валидируем типы данных
            if not isinstance(proxy_config['host'], str) or not proxy_config['host'].strip():
                log_admin("[ProxyManager] Invalid proxy host", level="error")
                return False
            
            if not isinstance(proxy_config['http_port'], int) or not (1 <= proxy_config['http_port'] <= 65535):
                log_admin("[ProxyManager] Invalid proxy port", level="error")
                return False
            
            if not isinstance(proxy_config['enabled'], bool):
                log_admin("[ProxyManager] Invalid proxy enabled flag", level="error")
                return False
            
            # Проверяем опциональные поля аутентификации
            if 'username' in proxy_config:
                if not isinstance(proxy_config['username'], str):
                    log_admin("[ProxyManager] Invalid proxy username", level="error")
                    return False
                
                # Если есть username, должен быть и password
                if 'password' not in proxy_config:
                    log_admin("[ProxyManager] Username provided but password missing", level="error")
                    return False
                
                if not isinstance(proxy_config['password'], str):
                    log_admin("[ProxyManager] Invalid proxy password", level="error")
                    return False
            
            log_admin("[ProxyManager] Proxy configuration validation passed", level="debug")
            return True
            
        except Exception as e:
            log_admin(f"[ProxyManager] Error validating proxy config: {e}", level="error")
            return False
    
    def is_enabled(self) -> bool:
        """
        Проверяет, включен ли прокси.
        
        Returns:
            True если прокси включен, False иначе
        """
        config = self.load_config()
        if config is None:
            return False
        
        return config.get('proxy', {}).get('enabled', False)
    
    def get_proxy_settings(self) -> Optional[Dict[str, Any]]:
        """
        Получает настройки прокси для HTTP клиентов.
        
        Returns:
            Словарь с настройками прокси или None если прокси отключен/недоступен
        """
        with self._lock:
            config = self.load_config()
            if config is None or not self.is_enabled():
                return None
            
            proxy_config = config['proxy']
            
            try:
                # Формируем URL прокси
                proxy_url = self._build_proxy_url(proxy_config)
                
                # Возвращаем настройки в формате для HTTP клиентов
                settings = {
                    'proxy_url': proxy_url,
                    'host': proxy_config['host'],
                    'port': proxy_config['http_port'],
                    'auth': None
                }
                
                # Добавляем аутентификацию если есть
                if 'username' in proxy_config and 'password' in proxy_config:
                    settings['auth'] = {
                        'username': proxy_config['username'],
                        'password': proxy_config['password']
                    }
                
                log_admin(f"[ProxyManager] Proxy settings prepared for host: {proxy_config['host']}:{proxy_config['http_port']}", level="debug")
                return settings
                
            except Exception as e:
                log_admin(f"[ProxyManager] Error preparing proxy settings: {e}", level="error")
                return None
    
    def _build_proxy_url(self, proxy_config: Dict[str, Any]) -> str:
        """
        Строит URL прокси из конфигурации.
        
        Args:
            proxy_config: Конфигурация прокси
            
        Returns:
            URL прокси в формате http://[username:password@]host:port
        """
        host = proxy_config['host']
        port = proxy_config['http_port']
        
        # Проверяем наличие аутентификации
        if 'username' in proxy_config and 'password' in proxy_config:
            username = proxy_config['username']
            password = proxy_config['password']
            return f"http://{username}:{password}@{host}:{port}"
        else:
            return f"http://{host}:{port}"
    
    def get_proxy_url(self) -> Optional[str]:
        """
        Получает URL прокси.
        
        Returns:
            URL прокси или None если прокси отключен/недоступен
        """
        settings = self.get_proxy_settings()
        if settings is None:
            return None
        
        return settings['proxy_url']
    
    def test_proxy_connection(self, timeout: float = 5.0, use_cache: bool = True) -> bool:
        """
        Тестирует соединение с прокси (быстрая проверка с кэшированием).
        
        Args:
            timeout: Таймаут для проверки соединения в секундах
            use_cache: Использовать ли кэшированный результат
        
        Returns:
            True если прокси доступен, False иначе
        """
        with self._lock:
            current_time = time.time()
            
            # Проверяем кэш если разрешено
            if (use_cache and 
                self._connection_test_cache is not None and 
                (current_time - self._connection_test_timestamp) < self._connection_test_ttl):
                log_admin("[ProxyManager] Using cached proxy connection test result", level="debug")
                return self._connection_test_cache
            
            settings = self.get_proxy_settings()
            if settings is None:
                self._connection_test_cache = False
                self._connection_test_timestamp = current_time
                return False
            
            try:
                import socket
                
                # Быстрая проверка TCP соединения с прокси
                start_time = time.time()
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(timeout)
                
                try:
                    result = sock.connect_ex((settings['host'], settings['port']))
                    sock.close()
                    
                    elapsed = time.time() - start_time
                    is_available = result == 0
                    
                    # Обновляем кэш
                    self._connection_test_cache = is_available
                    self._connection_test_timestamp = current_time
                    
                    if is_available:
                        log_admin(f"[ProxyManager] Proxy connection test passed ({elapsed:.2f}s)", level="debug")
                    else:
                        log_admin(f"[ProxyManager] Proxy connection test failed (code: {result})", level="warning")
                    
                    return is_available
                    
                except Exception as e:
                    sock.close()
                    log_admin(f"[ProxyManager] Proxy connection test error: {e}", level="warning")
                    self._connection_test_cache = False
                    self._connection_test_timestamp = current_time
                    return False
                    
            except Exception as e:
                log_admin(f"[ProxyManager] Error testing proxy connection: {e}", level="error")
                self._connection_test_cache = False
                self._connection_test_timestamp = current_time
                return False
    
    def is_proxy_available(self) -> bool:
        """
        Быстрая проверка доступности прокси (с кэшированием).
        
        Returns:
            True если прокси включен и доступен, False иначе
        """
        if not self.is_enabled():
            return False
        
        return self.test_proxy_connection(timeout=3.0, use_cache=True)
    
    def reload_config(self) -> bool:
        """
        Принудительно перезагружает конфигурацию прокси.

        Returns:
            True если конфигурация успешно перезагружена, False иначе
        """
        log_admin("[ProxyManager] Forcing proxy configuration reload", level="info")
        config = self.load_config(force_reload=True)
        return config is not None

    def update_config_path(self, new_path: str = None) -> bool:
        """
        Обновляет путь к файлу конфигурации и перезагружает конфигурацию.

        Args:
            new_path: Новый путь к файлу конфигурации (если None, выполняется поиск заново)

        Returns:
            True если конфигурация успешно обновлена, False иначе
        """
        with self._lock:
            old_path = self.config_path

            if new_path is None:
                # Выполняем поиск заново
                self.config_path = self._find_config_file()
            else:
                self.config_path = new_path

            log_admin(f"[ProxyManager] Config path updated from '{old_path}' to '{self.config_path}'", level="info")

            # Очищаем кэш и перезагружаем конфигурацию
            self._config_cache = None
            self._cache_timestamp = 0

            return self.reload_config()
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        Получает информацию о текущей конфигурации для отладки.
        
        Returns:
            Словарь с информацией о конфигурации
        """
        with self._lock:
            info = {
                'config_path': self.config_path,
                'config_exists': os.path.exists(self.config_path),
                'cache_valid': False,
                'enabled': False,
                'last_loaded': None
            }
            
            if self._config_cache is not None:
                current_time = time.time()
                cache_age = current_time - self._cache_timestamp
                info['cache_valid'] = cache_age < self._cache_ttl
                info['cache_age_seconds'] = cache_age
                info['last_loaded'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self._cache_timestamp))
                info['enabled'] = self.is_enabled()
            
            return info


# Глобальный экземпляр менеджера прокси
proxy_manager = ProxyManager()
