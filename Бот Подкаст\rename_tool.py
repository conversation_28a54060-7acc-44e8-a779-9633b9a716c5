# Модуль для обработки скрытого инструмента [RENAME:префикс]
# Позволяет ИИ автоматически переименовывать администраторов в групповых чатах

import re
import telebot
from bot_globals import bot, log_admin, get_bot_id  # ЭТАП 2: Добавлен импорт кэшированной функции

def process_rename_tool(response_text, message):
    """
    Обрабатывает скрытый инструмент [RENAME:префикс] в ответах ИИ
    
    Args:
        response_text (str): Текст ответа от ИИ
        message: Объект сообщения Telegram
        
    Returns:
        str: Очищенный от [RENAME:] тегов текст ответа
    """
    # Искать паттерн [RENAME:префикс]
    rename_pattern = r'\[RENAME:([^\]]+)\]'
    
    matches = re.findall(rename_pattern, response_text)
    
    if matches and message.chat.type in ['group', 'supergroup']:
        new_title = matches[0]
        
        try:
            # ЭТАП 3: Улучшенная обработка исключений для rename tool
            # Получаем ID бота с проверкой
            bot_id = get_bot_id()
            if not bot_id or bot_id == 0:
                log_admin(f"Cannot get bot ID for rename operation in chat {message.chat.id}", level="warning")
                clean_response = re.sub(rename_pattern, '', response_text)
                return clean_response

            log_admin(f"Checking bot permissions for rename in chat {message.chat.id} with bot_id={bot_id}", level="debug")
            bot_member = bot.get_chat_member(message.chat.id, bot_id)

            if bot_member.status != 'administrator' or not bot_member.can_promote_members:
                log_admin(f"Bot doesn't have admin rights in chat {message.chat.id} (status: {bot_member.status}, can_promote: {getattr(bot_member, 'can_promote_members', False)})", level="debug")
                # Удаляем [RENAME:] из ответа и возвращаем
                clean_response = re.sub(rename_pattern, '', response_text)
                return clean_response

            # Проверить статус пользователя
            user_member = bot.get_chat_member(message.chat.id, message.from_user.id)
            if user_member.status not in ['administrator', 'creator']:
                log_admin(f"User {message.from_user.id} is not admin in chat {message.chat.id} (status: {user_member.status})", level="debug")
                # Удаляем [RENAME:] из ответа и возвращаем
                clean_response = re.sub(rename_pattern, '', response_text)
                return clean_response

            # Попытаться установить новый префикс
            log_admin(f"Attempting to rename admin {message.from_user.id} to '{new_title}' in chat {message.chat.id}", level="debug")
            bot.set_chat_administrator_custom_title(
                chat_id=message.chat.id,
                user_id=message.from_user.id,
                custom_title=new_title
            )
            log_admin(f"Successfully renamed admin {message.from_user.id} to '{new_title}' in chat {message.chat.id}", level="info")

        except Exception as e:
            # ЭТАП 3: Детальная обработка ошибок rename tool
            error_type = type(e).__name__
            error_msg = str(e)

            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                log_admin(f"Timeout error in rename tool for chat {message.chat.id}: {error_type}: {error_msg}", level="error")
            elif "forbidden" in error_msg.lower() or "not enough rights" in error_msg.lower():
                log_admin(f"Permission error in rename tool for chat {message.chat.id}: {error_type}: {error_msg}", level="warning")
            elif "bad request" in error_msg.lower():
                log_admin(f"Invalid request in rename tool for chat {message.chat.id} (title: '{new_title}'): {error_type}: {error_msg}", level="warning")
            else:
                log_admin(f"Unexpected error in rename tool for chat {message.chat.id}: {error_type}: {error_msg}", level="error")
    
    # ОБЯЗАТЕЛЬНО удалить все упоминания [RENAME:] из ответа
    clean_response = re.sub(rename_pattern, '', response_text)
    return clean_response


def check_rename_permissions(message):
    """
    Проверяет, может ли бот использовать функцию переименования в данном чате
    
    Args:
        message: Объект сообщения Telegram
        
    Returns:
        dict: Словарь с информацией о правах
    """
    result = {
        'can_rename': False,
        'is_group': False,
        'bot_is_admin': False,
        'bot_can_promote': False,
        'user_is_admin': False
    }
    
    try:
        # Проверяем тип чата
        if message.chat.type in ['group', 'supergroup']:
            result['is_group'] = True

            # ЭТАП 3: Улучшенная проверка прав для rename tool
            # Получаем ID бота с проверкой
            bot_id = get_bot_id()
            if not bot_id or bot_id == 0:
                log_admin(f"Cannot get bot ID for rename permissions check in chat {message.chat.id}", level="warning")
                return result

            log_admin(f"Checking rename permissions in chat {message.chat.id} with bot_id={bot_id}", level="debug")
            bot_member = bot.get_chat_member(message.chat.id, bot_id)

            if bot_member.status == 'administrator':
                result['bot_is_admin'] = True
                if bot_member.can_promote_members:
                    result['bot_can_promote'] = True

                    # Проверяем статус пользователя
                    user_member = bot.get_chat_member(message.chat.id, message.from_user.id)
                    if user_member.status in ['administrator', 'creator']:
                        result['user_is_admin'] = True
                        result['can_rename'] = True
                        log_admin(f"Rename permissions granted for user {message.from_user.id} in chat {message.chat.id}", level="debug")
                    else:
                        log_admin(f"User {message.from_user.id} is not admin in chat {message.chat.id} (status: {user_member.status})", level="debug")
                else:
                    log_admin(f"Bot cannot promote members in chat {message.chat.id}", level="debug")
            else:
                log_admin(f"Bot is not admin in chat {message.chat.id} (status: {bot_member.status})", level="debug")

    except Exception as e:
        # ЭТАП 3: Детальная обработка ошибок проверки прав
        error_type = type(e).__name__
        error_msg = str(e)

        if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
            log_admin(f"Timeout error checking rename permissions in chat {message.chat.id}: {error_type}: {error_msg}", level="error")
        elif "forbidden" in error_msg.lower() or "not found" in error_msg.lower():
            log_admin(f"Access error checking rename permissions in chat {message.chat.id}: {error_type}: {error_msg}", level="warning")
        else:
            log_admin(f"Unexpected error checking rename permissions in chat {message.chat.id}: {error_type}: {error_msg}", level="error")
    
    return result
