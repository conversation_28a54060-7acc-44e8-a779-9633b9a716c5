"""
Утилиты для автоматического повтора запросов при получении ошибочных ответов длиной 41 символ.

Этот модуль содержит функции для обнаружения и обработки ошибочных ответов типа
"Извините, получен пустой ответ от модели." (41 символ) и автоматического повтора
запросов к API.
"""

import functools
import time
from typing import Callable, Any, Optional

from bot_globals import log_admin


def is_censorship_error(response: Any) -> bool:
    """
    Проверяет, является ли ответ ошибочным ответом длиной 41 символ.
    
    Ошибочные ответы обычно имеют вид:
    "Извините, получен пустой ответ от модели." (41 символ)
    
    Args:
        response: Ответ от API (любой тип)
        
    Returns:
        True если это ошибочный ответ длиной 41 символ, False иначе
    """
    if not isinstance(response, str):
        return False
    
    stripped_response = response.strip()
    
    # Проверяем точную длину 41 символ
    if len(stripped_response) != 41:
        return False
    
    # Дополнительная проверка на типичные ошибочные фразы
    error_patterns = [
        "Извините, получен пустой ответ от модели.",
        "Извините, ИИ дала пустой ответ"  # На случай вариаций
    ]
    
    for pattern in error_patterns:
        if stripped_response == pattern:
            return True
    
    # Если длина 41 символ, но не совпадает с известными паттернами,
    # все равно считаем это потенциальной ошибкой
    if stripped_response.startswith("Извините"):
        return True
    
    return False


def retry_on_censorship(max_attempts: int = 5):
    """
    Декоратор для автоматического повтора функций при получении ошибочных ответов длиной 41 символ.
    
    Args:
        max_attempts: Максимальное количество попыток (по умолчанию 5)
        
    Returns:
        Декоратор функции
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_response = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    # Выполняем функцию
                    response = func(*args, **kwargs)
                    last_response = response
                    
                    # Проверяем, является ли ответ ошибочным
                    if not is_censorship_error(response):
                        # Успешный ответ
                        if attempt > 1:
                            log_admin(f"[RetryUtils] Function {func.__name__} succeeded on attempt {attempt}")
                        return response
                    
                    # Ошибочный ответ - логируем и повторяем
                    log_admin(
                        f"[RetryUtils] Function {func.__name__} returned 41-char error on attempt {attempt}/{max_attempts}: "
                        f"'{response}'"
                    )
                    
                    if attempt < max_attempts:
                        # Небольшая задержка перед повтором
                        time.sleep(0.5)
                        log_admin(f"[RetryUtils] Retrying {func.__name__} (attempt {attempt + 1}/{max_attempts})")
                    
                except Exception as e:
                    # Если произошло исключение, логируем и повторяем
                    log_admin(
                        f"[RetryUtils] Function {func.__name__} raised exception on attempt {attempt}/{max_attempts}: {e}"
                    )
                    
                    if attempt < max_attempts:
                        time.sleep(0.5)
                        log_admin(f"[RetryUtils] Retrying {func.__name__} after exception (attempt {attempt + 1}/{max_attempts})")
                    else:
                        # На последней попытке пробрасываем исключение
                        raise
            
            # Все попытки исчерпаны
            log_admin(
                f"[RetryUtils] Function {func.__name__} failed after {max_attempts} attempts. "
                f"Returning last response: '{last_response}'"
            )
            return last_response
        
        return wrapper
    
    return decorator


def retry_on_censorship_async(max_attempts: int = 5):
    """
    Асинхронная версия декоратора для автоматического повтора функций при получении ошибочных ответов.
    
    Args:
        max_attempts: Максимальное количество попыток (по умолчанию 5)
        
    Returns:
        Декоратор асинхронной функции
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            last_response = None
            
            for attempt in range(1, max_attempts + 1):
                try:
                    # Выполняем асинхронную функцию
                    response = await func(*args, **kwargs)
                    last_response = response
                    
                    # Проверяем, является ли ответ ошибочным
                    if not is_censorship_error(response):
                        # Успешный ответ
                        if attempt > 1:
                            log_admin(f"[RetryUtils] Async function {func.__name__} succeeded on attempt {attempt}")
                        return response
                    
                    # Ошибочный ответ - логируем и повторяем
                    log_admin(
                        f"[RetryUtils] Async function {func.__name__} returned 41-char error on attempt {attempt}/{max_attempts}: "
                        f"'{response}'"
                    )
                    
                    if attempt < max_attempts:
                        # Небольшая задержка перед повтором
                        import asyncio
                        await asyncio.sleep(0.5)
                        log_admin(f"[RetryUtils] Retrying async {func.__name__} (attempt {attempt + 1}/{max_attempts})")
                    
                except Exception as e:
                    # Если произошло исключение, логируем и повторяем
                    log_admin(
                        f"[RetryUtils] Async function {func.__name__} raised exception on attempt {attempt}/{max_attempts}: {e}"
                    )
                    
                    if attempt < max_attempts:
                        import asyncio
                        await asyncio.sleep(0.5)
                        log_admin(f"[RetryUtils] Retrying async {func.__name__} after exception (attempt {attempt + 1}/{max_attempts})")
                    else:
                        # На последней попытке пробрасываем исключение
                        raise
            
            # Все попытки исчерпаны
            log_admin(
                f"[RetryUtils] Async function {func.__name__} failed after {max_attempts} attempts. "
                f"Returning last response: '{last_response}'"
            )
            return last_response
        
        return wrapper
    
    return decorator


def get_retry_stats() -> dict:
    """
    Возвращает статистику retry попыток.
    
    В будущем можно расширить для сбора метрик.
    
    Returns:
        Словарь со статистикой
    """
    return {
        "retry_utils_version": "1.0",
        "max_attempts_default": 5,
        "error_pattern_length": 41
    }


# Конфигурация по умолчанию
DEFAULT_MAX_ATTEMPTS = 5
DEFAULT_RETRY_DELAY = 0.5

# Для удобства экспорта
__all__ = [
    'is_censorship_error',
    'retry_on_censorship', 
    'retry_on_censorship_async',
    'get_retry_stats',
    'DEFAULT_MAX_ATTEMPTS',
    'DEFAULT_RETRY_DELAY'
]
