import re
import os
import html
import traceback
import socket

try:
    from telegraph import Telegraph
    from telegraph.exceptions import TelegraphException
    from telegraph.utils import html_to_nodes
    from bs4 import BeautifulSoup
    TELEGRAPH_SUPPORTED_LIB = True # Use a different name to avoid conflict with config
except ImportError:
    TELEGRAPH_SUPPORTED_LIB = False # Flag based on library import success

# Import config and global variables
from config import (
    TELEGRAPH_SUPPORTED,
    TELEGRAPH_TOKEN_FILE,
    TELEGRAPH_SHORT_NAME,
    MODEL_GEMINI_2_5_FLASH,
    SYSTEM_PROMPT_TELEGRAPH_TITLE,
)
from bot_globals import log_admin
import bot_globals



# Reference to the telegraph client stored in bot_globals
telegraph_client = bot_globals.telegraph_client

# Store the currently active Telegraph short name so that publish_to_telegraph
# can use the same author regardless of how setup_telegraph was called.
current_short_name = TELEGRAPH_SHORT_NAME
from api_clients import call_llm, call_gemini_2_5_flash_api


def setup_telegraph(short_name: str = TELEGRAPH_SHORT_NAME):
    """Initializes the global telegraph_client.

    Parameters
    ----------
    short_name: str
        Desired short name for the Telegraph account. If different from the
        current account short name, a new account will be created and the token
        will be saved to ``TELEGRAPH_TOKEN_FILE``.
    """
    global telegraph_client, current_short_name
    if not TELEGRAPH_SUPPORTED_LIB: # Check library availability first
        log_admin("telegra.ph libraries not available. Skipping setup.")
        return False

    # Setup proxy for Telegraph if available
    _setup_telegraph_proxy()

    access_token = None
    safe_short_name = re.sub(r'[^\w]+', '', short_name) if short_name else ''
    if not safe_short_name:
        safe_short_name = "shbot"
    if os.path.exists(TELEGRAPH_TOKEN_FILE):
        try:
            with open(TELEGRAPH_TOKEN_FILE, 'r') as f:
                access_token = f.read().strip()
            log_admin(f"loaded telegra.ph token from {TELEGRAPH_TOKEN_FILE}")
        except Exception as e:
            log_admin(f"error loading telegra.ph token: {e}")
            access_token = None

    if access_token:
        telegraph_client = Telegraph(access_token=access_token)
        bot_globals.telegraph_client = telegraph_client
        try:
            acc_info = telegraph_client.get_account_info()
            current = acc_info.get('short_name', 'unknown')
            if current != safe_short_name:
                log_admin(
                    f"telegraph short_name mismatch (have '{current}', want '{safe_short_name}'). Creating new account."
                )
                telegraph_client = Telegraph()
                bot_globals.telegraph_client = telegraph_client
                telegraph_client.create_account(short_name=safe_short_name)
                access_token = telegraph_client.get_access_token()
                with open(TELEGRAPH_TOKEN_FILE, 'w') as f:
                    f.write(access_token)
                log_admin(
                    f"created new telegra.ph account '{safe_short_name}' and saved token"
                )
            else:
                log_admin(
                    f"telegra.ph client initialized for account: {current}"
                )
            current_short_name = safe_short_name
            return True
        except TelegraphException as e:
            log_admin(
                f"telegra.ph token is invalid or expired: {e}. creating new account."
            )
            access_token = None
            try:
                os.remove(TELEGRAPH_TOKEN_FILE)
            except OSError as remove_err:
                log_admin(f"could not remove invalid token file: {remove_err}")

    if not access_token:
        try:
            telegraph_client = Telegraph()
            bot_globals.telegraph_client = telegraph_client
            telegraph_client.create_account(short_name=safe_short_name)
            access_token = telegraph_client.get_access_token()
            log_admin(f"created new telegra.ph account '{safe_short_name}'.")
            try:
                with open(TELEGRAPH_TOKEN_FILE, 'w') as f: f.write(access_token)
                log_admin(f"saved new telegra.ph token to {TELEGRAPH_TOKEN_FILE}")
                current_short_name = safe_short_name
                return True
            except Exception as e:
                log_admin(f"error saving telegra.ph token: {e}")
                current_short_name = safe_short_name
                return True
        except TelegraphException as e:
            log_admin(f"error creating telegra.ph account: {e}")
            telegraph_client = None
            bot_globals.telegraph_client = telegraph_client
            return False
        except Exception as e:
            log_admin(f"unexpected error during telegra.ph account creation: {e}")
            telegraph_client = None
            bot_globals.telegraph_client = telegraph_client
            return False
    return False # Should not reach here if logic is sound, but as safeguard

def html_to_telegraph_nodes(html_content):
    """Converts HTML-like content string to Telegraph nodes."""
    if not TELEGRAPH_SUPPORTED_LIB or not isinstance(html_content, str):
        log_admin("html_to_telegraph_nodes: Libraries not supported or input not string.")
        return []

    # The original code used Telegraph's html_to_nodes with a wrapped <p> tag.
    # Let's try to keep that structure but be cautious about the input HTML.
    # The 'clean_response_text' function ensures only <b> and <a> are supposed to be present.
    # Telegraph's html_to_nodes *should* handle <strong> (from <b>) and <a>.
    # It also converts <br/> to newlines internally.
    # Double newlines in the source text are split into paragraphs in the processing core.
    # We should probably trust the core's paragraph splitting and feed segments to the converter.
    # However, the original function tried to split by \n\n *here* and process each segment.
    # Let's refine this: assume the input `html_content` *is* a single chunk that should be parsed.
    # The core should handle multi-paragraph splitting *before* calling publish.

    # Simplified conversion attempt: Let Telegraph parse the cleaned HTML directly.
    # The cleaning should have left only allowed tags (<b> -> <strong>, <a>).
    # Telegraph's html_to_nodes handles <strong>, <em>, a, code, pre, br.
    # Our cleanup allows <b> (converted to <strong>). Double newlines become \n\n.
    # Telegraph's parser is usually good at turning simple HTML into nodes.

    try:
        # html_to_nodes expects a full HTML snippet, like "<p>...</p><p>...</p>"
        # Or just the content nodes directly.
        # Given our cleanup preserves \n\n for paragraphs, let's try feeding the cleaned HTML.
        # Double newlines in the cleaned text should result in separate <p> tags if parsed well.

        # Using BeautifulSoup might offer more control if html_to_nodes is struggling with complex cases,
        # but the original code relied primarily on html_to_nodes.
        # Let's stick to using html_to_nodes directly with the cleaned HTML string.
        # We might need to replace \n\n with <p>...</p> structure before parsing if
        # html_to_nodes doesn't interpret \n\n as paragraph breaks correctly *within* its input.
        # However, the simplest way is to just pass the cleaned text and let it parse.
        # The original code split by \n\n and wrapped *each* segment in <p>. Let's revert to that for safety.

        final_nodes = []
        paragraphs = html_content.split('\n\n')

        for paragraph_text in paragraphs:
            paragraph_text = paragraph_text.strip()
            if not paragraph_text:
                continue

            # Preserve единичные переносы строк как <br/>, чтобы точки с источниками
            # в Telegraph не склеивались в одну строку
            paragraph_text = paragraph_text.replace('\n', '<br/>')

            wrapped_paragraph = f'<p>{paragraph_text}</p>'
            nodes_from_paragraph = html_to_nodes(wrapped_paragraph)

            # html_to_nodes returns a list of nodes. If it was a single <p>, we expect one node.
            if nodes_from_paragraph and isinstance(nodes_from_paragraph, list):
                # Append the children of the <p> node, or the node itself if it's not a <p> or malformed
                if len(nodes_from_paragraph) == 1 and isinstance(nodes_from_paragraph[0], dict) and nodes_from_paragraph[0].get('tag') == 'p':
                    p_children = nodes_from_paragraph[0].get('children', [])
                    if p_children:
                        final_nodes.append({"tag": "p", "children": p_children})
                    elif paragraph_text: # Add a space if the paragraph was not empty but produced no children (e.g., only formatting)
                        final_nodes.append({"tag": "p", "children": [" "]})
                else:
                    # Handle cases where the parser produced something other than a single <p>
                    log_admin(f"html_to_telegraph_nodes: Parser output unexpected for segment '{paragraph_text[:50]}...': {nodes_from_paragraph}")
                    final_nodes.extend(nodes_from_paragraph) # Add whatever nodes were produced
            elif paragraph_text:
                # Fallback if html_to_nodes returned None or empty list for a non-empty segment
                log_admin(f"html_to_telegraph_nodes: Parser returned empty for segment '{paragraph_text[:50]}...'. Adding as plain text paragraph.")
                final_nodes.append({"tag": "p", "children": [paragraph_text]})

        # Final check: If no nodes were produced but original content wasn't empty
        if not final_nodes and html_content.strip():
            log_admin("html_to_telegraph_nodes: conversion resulted in empty nodes. Publishing as plain text in <pre>.")
            escaped_content = html.escape(html_content)
            return [{"tag": "pre", "children": [escaped_content]}]


        return final_nodes

    except Exception as e:
        log_admin(f"html_to_telegraph_nodes: unexpected error during conversion: {e}\n{traceback.format_exc()}. Publishing as plain text in <pre>.")
        escaped_content = html.escape(str(html_content))
        return [{"tag": "pre", "children": [escaped_content]}]


def clean_html_for_telegraph(html_content: str) -> str:
    """
    Cleans HTML content to leave only tags and attributes supported by Telegra.ph.

    Parameters
    ----------
    html_content: str
        The HTML content to clean.

    Returns
    -------
    str
        Cleaned HTML content.
    """
    if not TELEGRAPH_SUPPORTED_LIB or not html_content:
        return html_content

    # Supported tags and their allowed attributes in Telegra.ph
    allowed_tags = {
        'a': ['href'], 'aside': [], 'b': [], 'blockquote': [], 'br': [],
        'code': [], 'em': [], 'figcaption': [], 'figure': [], 'h3': [],
        'h4': [], 'hr': [], 'i': [], 'iframe': ['src'], 'img': ['src'],
        'li': [], 'ol': [], 'p': [], 'pre': [], 's': [], 'strong': [],
        'u': [], 'ul': [], 'video': ['src']
    }

    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        for tag in soup.find_all(True):
            if tag.name not in allowed_tags:
                tag.unwrap()  # Remove tag, keep content
            else:
                allowed_attrs = allowed_tags[tag.name]
                current_attrs = list(tag.attrs.keys())
                for attr in current_attrs:
                    if attr not in allowed_attrs:
                        del tag[attr]  # Remove not allowed attribute

        return str(soup)
    except Exception as e:
        log_admin(f"Error cleaning HTML for Telegra.ph: {e}", level="warning")
        return html_content


def generate_telegraph_title(content_preview):
    """Generates a title for a Telegraph page using Gemini 2.5 Flash."""
    log_admin("generating telegra.ph title with Gemini 2.5 Flash...")
    generated_title = call_llm(
        MODEL_GEMINI_2_5_FLASH,
        history=[],
        user_text=content_preview[:1000],
        input_data=None,
        system_prompt=SYSTEM_PROMPT_TELEGRAPH_TITLE,
        call_type="telegraph_title",
        is_private_chat=False,  # Title generation not specific to chat type
    )
    if isinstance(generated_title, str) and not generated_title.startswith("Ошибка:") and generated_title.strip():
        telegraph_title = generated_title.strip().replace("\n", " ")
        log_admin(f"generated title: '{telegraph_title}'")
    else:
        telegraph_title = "Ответ от sh"
        log_admin(f"title generation failed or empty. using default: '{telegraph_title}'. error: {generated_title}")
    return telegraph_title


def publish_to_telegraph(title, original_html_content):
    """Publishes content to Telegra.ph."""
    if not TELEGRAPH_SUPPORTED_LIB or not telegraph_client:
        log_admin("telegra.ph client not initialized or libraries not supported. cannot publish.")
        return None

    # Clean the HTML content before processing
    cleaned_html_content = clean_html_for_telegraph(original_html_content)
    if len(cleaned_html_content) < len(original_html_content):
        log_admin(f"Cleaned HTML for Telegra.ph. Original size: {len(original_html_content)}, Cleaned size: {len(cleaned_html_content)}")

    log_admin(f"converting content to telegra.ph nodes for title: '{title[:50]}...'")
    content_nodes = []
    try:
        # Use the cleaned HTML content for node conversion
        content_nodes = html_to_telegraph_nodes(cleaned_html_content)
        if not content_nodes:
            log_admin("conversion to telegra.ph nodes resulted in empty content (fallback check). publishing as plain text in <pre>.")
            escaped_content = html.escape(str(original_html_content)) # Use original content for fallback
            content_nodes = [{"tag": "pre", "children": [escaped_content]}]
    except Exception as e:
        # This catch should ideally not be hit if html_to_telegraph_nodes is robust,
        # but included as a final safeguard.
        log_admin(f"error converting html to telegra.ph nodes: {e}\n{traceback.format_exc()}. publishing as plain text in <pre>.")
        escaped_content = html.escape(str(original_html_content)) # Use original content for fallback
        content_nodes = [{"tag": "pre", "children": [escaped_content]}]

    if not content_nodes:
        log_admin("No content nodes generated after conversion. Cannot publish.")
        return None


    log_admin(f"publishing content to telegra.ph...")
    try:
        response = telegraph_client.create_page(
            title=title,
            content=content_nodes,
            author_name=current_short_name
        )
        page_url = response['url']
        log_admin(f"successfully published to telegra.ph: {page_url}")
        return page_url
    except TelegraphException as e:
        log_admin(f"error publishing to telegra.ph: {e}")
        log_admin("attempting to publish as <pre> due to TelegraphException.")
        try:
            escaped_content = html.escape(str(original_html_content)) # Escape the original content
            pre_nodes = [{"tag": "pre", "children": [escaped_content]}]
            response = telegraph_client.create_page(
                title=title + " (fallback)",
                content=pre_nodes,
                author_name=current_short_name
            )
            page_url = response['url']
            log_admin(f"successfully published fallback to telegra.ph: {page_url}")
            return page_url
        except Exception as fallback_e:
            log_admin(f"error publishing fallback to telegra.ph: {fallback_e}")
            return None


def _setup_telegraph_proxy():
    """Clear any proxy environment variables for Telegraph HTTP requests."""
    # Очищаем переменные окружения прокси для Telegraph
    os.environ.pop('HTTP_PROXY', None)
    os.environ.pop('HTTPS_PROXY', None)
