import json
import sys
import time
import urllib.request
import urllib.error

# Simple standalone test to validate OpenRouter Chat Completions payload and response.
# It mimics the example request provided by the user and logs status and body.

OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
OPENROUTER_API_KEY = "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"


def make_request(payload: dict):
    req = urllib.request.Request(
        OPENROUTER_API_URL,
        data=json.dumps(payload).encode("utf-8"),
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        },
        method="POST",
    )
    try:
        with urllib.request.urlopen(req, timeout=60) as resp:
            status = resp.status
            body = resp.read().decode("utf-8", errors="replace")
            return status, body
    except urllib.error.HTTPError as e:
        body = e.read().decode("utf-8", errors="replace") if e.fp else str(e)
        return e.code, body
    except urllib.error.URLError as e:
        return -1, f"URLError: {e}"


def test_text_only():
    payload = {
        "model": "openrouter/horizon-alpha",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Generate a minimal valid HTML page with a single heading."}
                ],
            }
        ],
    }
    status, body = make_request(payload)
    print("[TEXT_ONLY] Status:", status)
    print("[TEXT_ONLY] Body:", body[:1000])
    assert status == 200, f"Expected 200, got {status}. Body: {body}"


def test_with_image_url():
    payload = {
        "model": "openrouter/horizon-alpha",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "What is in this image?"},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
                        },
                    },
                ],
            }
        ],
    }
    status, body = make_request(payload)
    print("[IMAGE_URL] Status:", status)
    print("[IMAGE_URL] Body:", body[:1000])
    assert status == 200, f"Expected 200, got {status}. Body: {body}"


def main():
    print("== OpenRouter API smoke tests ==")
    start = time.time()

    try:
        print("\nRunning test_text_only...")
        test_text_only()

        print("\nRunning test_with_image_url...")
        test_with_image_url()

        print("\nAll tests passed in %.2fs" % (time.time() - start))
        sys.exit(0)
    except AssertionError as e:
        print("\nTEST FAILED:", e)
        sys.exit(1)
    except Exception as e:
        print("\nUNEXPECTED ERROR:", e)
        sys.exit(2)


if __name__ == "__main__":
    main()